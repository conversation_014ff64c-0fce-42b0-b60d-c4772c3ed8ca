@extends('layouts.dashboard')

@section('title', 'Program Management - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Program Management</h1>
                <p class="text-lg text-dark-gray">Manage academy programs and schedules</p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $programs->total() }} Total Programs
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('create', App\Models\Program::class)
                <a href="{{ route('programs.create') }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                        </path>
                    </svg>
                    Add New Program
                </a>
            @endcan
            <div class="flex items-center space-x-2">
                @can('export', App\Models\Program::class)
                    <button onclick="exportData('excel')" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Export Excel
                    </button>
                    <button onclick="exportData('pdf')" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                            </path>
                        </svg>
                        Export PDF
                    </button>
                @endcan
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="programManagement()" x-init="init()">
        <!-- Advanced Search & Filters -->
        @include('programs._filters')

        <!-- Statistics Cards -->
        @include('programs._stats')

        <!-- Main Content Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">All Programs</h3>
                    <p class="bank-card-subtitle">
                        Showing {{ $programs->firstItem() ?? 0 }} to {{ $programs->lastItem() ?? 0 }}
                        of {{ $programs->total() }} programs
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Bulk Actions -->
                    @can('bulkAction', App\Models\Program::class)
                        <div x-show="selectedPrograms.length > 0" x-transition class="flex items-center space-x-2">
                            <span class="text-sm text-dark-gray" x-text="`${selectedPrograms.length} selected`"></span>
                            <select x-model="bulkAction" class="form-select-bank text-sm">
                                <option value="">Bulk Actions</option>
                                <option value="activate">Activate Selected</option>
                                <option value="deactivate">Deactivate Selected</option>
                                <option value="delete">Delete Selected</option>
                            </select>
                            <button @click="executeBulkAction()" :disabled="!bulkAction" class="btn-bank btn-bank-sm">
                                Apply
                            </button>
                        </div>
                    @endcan

                    <!-- View Toggle -->
                    <div class="flex items-center bg-off-white rounded-lg p-1">
                        <button @click="viewMode = 'table'" :class="viewMode === 'table' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M3 6h18m-9 10h9"></path>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bank-card-body p-0">
                <!-- Table View -->
                <div x-show="viewMode === 'table'" x-transition>
                    @include('programs._table')
                </div>

                <!-- Grid View -->
                <div x-show="viewMode === 'grid'" x-transition>
                    @include('programs._grid')
                </div>

                <!-- Pagination -->
                @if ($programs->hasPages())
                    <div class="px-6 py-4 border-t border-light-gray">
                        {{ $programs->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function programManagement() {
            return {
                selectedPrograms: [],
                bulkAction: '',
                viewMode: localStorage.getItem('programViewMode') || 'table',

                init() {
                    this.$watch('viewMode', (value) => {
                        localStorage.setItem('programViewMode', value);
                    });
                },

                toggleProgramSelection(programId) {
                    const index = this.selectedPrograms.indexOf(programId);
                    if (index > -1) {
                        this.selectedPrograms.splice(index, 1);
                    } else {
                        this.selectedPrograms.push(programId);
                    }
                },

                selectAllPrograms() {
                    const checkboxes = document.querySelectorAll('input[name="program_ids[]"]');
                    const allSelected = this.selectedPrograms.length === checkboxes.length;

                    if (allSelected) {
                        this.selectedPrograms = [];
                    } else {
                        this.selectedPrograms = Array.from(checkboxes).map(cb => parseInt(cb.value));
                    }
                },

                async executeBulkAction() {
                    if (!this.bulkAction || this.selectedPrograms.length === 0) return;

                    const confirmed = await this.confirmBulkAction();
                    if (!confirmed) return;

                    try {
                        const response = await fetch('{{ route('programs.bulk-action') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                action: this.bulkAction,
                                program_ids: this.selectedPrograms
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while processing the request.');
                    }
                },

                confirmBulkAction() {
                    const actionText = this.bulkAction.charAt(0).toUpperCase() + this.bulkAction.slice(1);
                    return confirm(
                        `Are you sure you want to ${actionText.toLowerCase()} ${this.selectedPrograms.length} selected program(s)?`
                    );
                }
            }
        }

        function exportData(format) {
            const url = format === 'excel' ? '{{ route('programs.export.excel') }}' :
                '{{ route('programs.export.pdf') }}';
            const params = new URLSearchParams(window.location.search);
            window.open(`${url}?${params.toString()}`, '_blank');
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            alert(message);
        }
    </script>
@endpush
