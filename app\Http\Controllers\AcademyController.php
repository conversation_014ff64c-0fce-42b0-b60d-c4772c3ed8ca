<?php

namespace App\Http\Controllers;

use App\Models\Academy;
use App\Models\Branch;
use App\Models\Student;
use App\Models\Program;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class AcademyController extends Controller
{
    /**
     * Display a listing of academies with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = Academy::with(['branch', 'students', 'programs', 'payments'])
            ->withCount(['students', 'programs', 'payments']);

        // Apply branch-level filtering for non-admin users
        $user = Auth::user();
        if ($user->role === 'branch_manager') {
            $query->where('branch_id', $user->branch_id);
        } elseif ($user->role === 'academy_manager') {
            $query->where('id', $user->academy_id);
        }

        // Advanced search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->search($search);
        }

        // Branch filter
        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->active();
            } elseif ($status === 'inactive') {
                $query->inactive();
            }
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->where('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->where('created_at', '<=', $request->get('date_to') . ' 23:59:59');
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $academies = $query->paginate($perPage)->withQueryString();

        // Get branches for filter dropdown
        $branches = Branch::active()->orderBy('name')->get();

        // Calculate statistics
        $stats = [
            'total_academies' => Academy::count(),
            'active_academies' => Academy::active()->count(),
            'total_students' => Student::count(),
            'total_programs' => Program::count(),
        ];

        if ($request->ajax()) {
            return response()->json([
                'academies' => $academies,
                'stats' => $stats
            ]);
        }

        return view('academies.index', compact('academies', 'branches', 'stats'));
    }

    /**
     * Show the form for creating a new academy.
     */
    public function create(Request $request): View
    {
        Gate::authorize('create', Academy::class);

        $branches = Branch::active()->orderBy('name')->get();
        $selectedBranchId = $request->get('branch_id');

        return view('academies.create', compact('branches', 'selectedBranchId'));
    }

    /**
     * Store a newly created academy in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Academy::class);

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'address' => 'nullable|string|max:500',
            'coach_name' => 'nullable|string|max:255',
            'coach_phone' => 'nullable|string|regex:/^\+971[0-9]{9}$/',
            'status' => 'boolean',
        ], [
            'branch_id.required' => 'Please select a branch.',
            'branch_id.exists' => 'The selected branch is invalid.',
            'name.required' => 'Academy name is required.',
            'name.max' => 'Academy name cannot exceed 255 characters.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'address.max' => 'Address cannot exceed 500 characters.',
            'coach_name.max' => 'Coach name cannot exceed 255 characters.',
            'coach_phone.regex' => 'Coach phone must be a valid UAE phone number (+971XXXXXXXXX).',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $academy = Academy::create([
                'branch_id' => $request->branch_id,
                'name' => $request->name,
                'description' => $request->description,
                'address' => $request->address,
                'coach_name' => $request->coach_name,
                'coach_phone' => $request->coach_phone,
                'status' => $request->boolean('status', true),
            ]);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Academy created successfully.',
                    'academy' => $academy->load(['branch', 'students', 'programs'])
                ]);
            }

            return redirect()->route('academies.index')
                ->with('success', 'Academy created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            // Log the actual error for debugging
            Log::error('Academy creation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id(),
                'request_data' => $request->all()
            ]);

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create academy. Please try again.',
                    'error' => config('app.debug') ? $e->getMessage() : null
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to create academy. Please try again.')
                ->withInput();
        }
    }

    /**
     * Display the specified academy with detailed information.
     */
    public function show(Academy $academy): View|JsonResponse
    {
        Gate::authorize('view', $academy);

        $academy->load([
            'branch',
            'programs',
            'students.payments',
            'payments',
            'uniforms'
        ]);

        // Get academy statistics
        $stats = $academy->getStatistics();

        // Get students with details
        $students = $academy->getStudentsWithDetails();

        // Get available programs
        $programs = $academy->getAvailablePrograms();

        // Get monthly revenue data
        $monthlyRevenue = $academy->getMonthlyRevenue(12);

        if (request()->ajax()) {
            return response()->json([
                'academy' => $academy,
                'stats' => $stats,
                'students' => $students,
                'programs' => $programs,
                'monthlyRevenue' => $monthlyRevenue
            ]);
        }

        return view('academies.show', compact('academy', 'stats', 'students', 'programs', 'monthlyRevenue'));
    }

    /**
     * Show the form for editing the specified academy.
     */
    public function edit(Academy $academy): View
    {
        Gate::authorize('update', $academy);

        $branches = Branch::active()->orderBy('name')->get();

        return view('academies.edit', compact('academy', 'branches'));
    }

    /**
     * Update the specified academy in storage.
     */
    public function update(Request $request, Academy $academy): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $academy);

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'address' => 'nullable|string|max:500',
            'coach_name' => 'nullable|string|max:255',
            'coach_phone' => 'nullable|string|regex:/^\+971[0-9]{9}$/',
            'status' => 'boolean',
        ], [
            'branch_id.required' => 'Please select a branch.',
            'branch_id.exists' => 'The selected branch is invalid.',
            'name.required' => 'Academy name is required.',
            'name.max' => 'Academy name cannot exceed 255 characters.',
            'description.max' => 'Description cannot exceed 1000 characters.',
            'address.max' => 'Address cannot exceed 500 characters.',
            'coach_name.max' => 'Coach name cannot exceed 255 characters.',
            'coach_phone.regex' => 'Coach phone must be in UAE format (+971XXXXXXXXX).',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $academy->update([
                'branch_id' => $request->branch_id,
                'name' => $request->name,
                'description' => $request->description,
                'address' => $request->address,
                'coach_name' => $request->coach_name,
                'coach_phone' => $request->coach_phone,
                'status' => $request->boolean('status', true),
            ]);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Academy updated successfully.',
                    'academy' => $academy->load(['branch', 'students', 'programs'])
                ]);
            }

            return redirect()->route('academies.index')
                ->with('success', 'Academy updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update academy. Please try again.'
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to update academy. Please try again.')
                ->withInput();
        }
    }

    /**
     * Remove the specified academy from storage.
     */
    public function destroy(Academy $academy): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $academy);

        try {
            DB::beginTransaction();

            // Check if academy has associated data
            $hasStudents = $academy->students()->count() > 0;
            $hasPrograms = $academy->programs()->count() > 0;
            $hasPayments = $academy->payments()->count() > 0;

            if ($hasStudents || $hasPrograms || $hasPayments) {
                // Soft delete by deactivating instead of hard delete
                $academy->update(['status' => false]);
                $message = 'Academy has been deactivated due to existing associations.';
            } else {
                // Safe to hard delete
                $academy->delete();
                $message = 'Academy deleted successfully.';
            }

            DB::commit();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message
                ]);
            }

            return redirect()->route('academies.index')->with('success', $message);
        } catch (\Exception $e) {
            DB::rollBack();

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete academy. Please try again.'
                ], 500);
            }

            return redirect()->route('academies.index')
                ->with('error', 'Failed to delete academy. Please try again.');
        }
    }

    /**
     * Toggle academy status.
     */
    public function toggleStatus(Academy $academy): JsonResponse
    {
        Gate::authorize('update', $academy);

        try {
            $academy->update(['status' => !$academy->status]);

            return response()->json([
                'success' => true,
                'message' => 'Academy status updated successfully.',
                'status' => $academy->status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update academy status.'
            ], 500);
        }
    }

    /**
     * Handle bulk actions on academies.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'academy_ids' => 'required|array|min:1',
            'academy_ids.*' => 'exists:academies,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request data.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $academies = Academy::whereIn('id', $request->academy_ids)->get();
            $action = $request->action;
            $count = 0;

            foreach ($academies as $academy) {
                // Check authorization for each academy
                if (!Gate::allows('update', $academy)) {
                    continue;
                }

                switch ($action) {
                    case 'activate':
                        $academy->update(['status' => true]);
                        $count++;
                        break;
                    case 'deactivate':
                        $academy->update(['status' => false]);
                        $count++;
                        break;
                    case 'delete':
                        if (Gate::allows('delete', $academy)) {
                            // Check for associations before deleting
                            $hasAssociations = $academy->students()->count() > 0 ||
                                $academy->programs()->count() > 0 ||
                                $academy->payments()->count() > 0;

                            if ($hasAssociations) {
                                $academy->update(['status' => false]);
                            } else {
                                $academy->delete();
                            }
                            $count++;
                        }
                        break;
                }
            }

            DB::commit();

            $actionText = ucfirst($action);
            if ($action === 'delete') {
                $actionText = 'processed';
            }

            return response()->json([
                'success' => true,
                'message' => "{$count} academies {$actionText} successfully."
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to process bulk action. Please try again.'
            ], 500);
        }
    }

    /**
     * Export academies to Excel/CSV.
     */
    public function exportExcel(Request $request)
    {
        $query = Academy::with(['branch', 'students', 'programs', 'payments']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $query->search($request->get('search'));
        }
        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->active();
            } elseif ($status === 'inactive') {
                $query->inactive();
            }
        }

        $academies = $query->orderBy('name')->get();

        // Create CSV content
        $csvContent = "ID,Name,Branch,Description,Coach Name,Coach Phone,Students,Programs,Revenue,Status,Created At\n";

        foreach ($academies as $academy) {
            $revenue = $academy->completedPayments()->sum('amount');
            $csvContent .= sprintf(
                "%d,\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",%d,%d,%.2f,\"%s\",\"%s\"\n",
                $academy->id,
                str_replace('"', '""', $academy->name),
                str_replace('"', '""', $academy->branch->name ?? ''),
                str_replace('"', '""', $academy->description ?? ''),
                str_replace('"', '""', $academy->coach_name ?? ''),
                $academy->coach_phone ?? '',
                $academy->students_count ?? 0,
                $academy->programs_count ?? 0,
                $revenue,
                $academy->status ? 'Active' : 'Inactive',
                $academy->created_at->format('Y-m-d H:i:s')
            );
        }

        $filename = 'academies_export_' . now()->format('Y_m_d_H_i_s') . '.csv';

        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    /**
     * Export academies to PDF.
     */
    public function exportPdf(Request $request)
    {
        $query = Academy::with(['branch', 'students', 'programs', 'payments']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $query->search($request->get('search'));
        }
        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->active();
            } elseif ($status === 'inactive') {
                $query->inactive();
            }
        }

        $academies = $query->orderBy('name')->get();

        // For now, return a simple HTML view that can be printed as PDF
        return view('academies.export-pdf', compact('academies'));
    }

    /**
     * API endpoint for AJAX requests.
     */
    public function apiIndex(Request $request): JsonResponse
    {
        return $this->index($request);
    }

    /**
     * Get academy statistics for API.
     */
    public function getStatistics(): JsonResponse
    {
        $stats = [
            'total_academies' => Academy::count(),
            'active_academies' => Academy::active()->count(),
            'inactive_academies' => Academy::inactive()->count(),
            'total_students' => Student::count(),
            'total_programs' => Program::count(),
            'total_revenue' => Academy::join('payments', 'academies.id', '=', 'payments.academy_id')
                ->where('payments.status', 'completed')
                ->sum('payments.amount'),
            'pending_payments' => Academy::join('payments', 'academies.id', '=', 'payments.academy_id')
                ->where('payments.status', 'pending')
                ->sum('payments.amount'),
        ];

        return response()->json($stats);
    }
}
