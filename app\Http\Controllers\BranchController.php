<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Academy;
use App\Models\Program;
use App\Models\Student;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class BranchController extends Controller
{
    /**
     * Display a listing of branches with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        $query = Branch::with(['academies', 'students', 'users'])
            ->withCount(['academies', 'students', 'payments']);

        // Apply branch-level filtering for non-admin users
        $user = Auth::user();
        if ($user->role === 'branch_manager' || $user->role === 'academy_manager') {
            $query->where('id', $user->branch_id);
        }

        // Advanced search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%")
                    ->orWhere('address', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status') === 'active');
        }

        // Date range filters
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        // Academy count filter
        if ($request->filled('min_academies')) {
            $query->has('academies', '>=', (int)$request->get('min_academies'));
        }
        if ($request->filled('max_academies')) {
            $query->has('academies', '<=', (int)$request->get('max_academies'));
        }

        // Student count filter
        if ($request->filled('min_students')) {
            $query->has('students', '>=', (int)$request->get('min_students'));
        }
        if ($request->filled('max_students')) {
            $query->has('students', '<=', (int)$request->get('max_students'));
        }

        // Location-based filter
        if ($request->filled('location_filter')) {
            $locations = explode(',', $request->get('location_filter'));
            $query->whereIn('location', $locations);
        }

        // Advanced sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');

        $allowedSorts = ['name', 'location', 'created_at', 'updated_at', 'academies_count', 'students_count', 'payments_count'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination with per-page option
        $perPage = $request->get('per_page', 15);
        $perPage = in_array($perPage, [10, 15, 25, 50, 100]) ? $perPage : 15;

        $branches = $query->paginate($perPage)->withQueryString();

        // Enhanced statistics with filters applied
        $filteredQuery = clone $query;
        $filteredStats = [
            'filtered_count' => $filteredQuery->count(),
            'total_branches' => Branch::count(),
            'active_branches' => Branch::where('status', true)->count(),
            'inactive_branches' => Branch::where('status', false)->count(),
            'total_academies' => Academy::count(),
            'active_academies' => Academy::where('status', true)->count(),
            'total_students' => Student::count(),
            'active_students' => Student::where('status', 'active')->count(),
            'total_programs' => Program::count(),
            'active_programs' => Program::where('status', true)->count(),
            'total_revenue' => \App\Models\Payment::whereIn('status', ['completed', 'active'])->sum('amount') ?? 0,
            'pending_payments' => \App\Models\Payment::where('status', 'pending')->sum('amount') ?? 0,
            'active_rate' => Branch::count() > 0 ? round((Branch::where('status', true)->count() / Branch::count()) * 100, 1) : 0,
            'avg_students_per_branch' => Branch::count() > 0 ? round(Student::count() / Branch::count(), 1) : 0,
            'avg_revenue_per_branch' => Branch::count() > 0 ? round((\App\Models\Payment::whereIn('status', ['completed', 'active'])->sum('amount') ?? 0) / Branch::count(), 0) : 0,
        ];

        // Get unique locations for filter dropdown
        $availableLocations = Branch::distinct()->pluck('location')->filter()->sort()->values();

        // Return JSON for AJAX requests
        if ($request->ajax()) {
            return response()->json([
                'branches' => $branches,
                'stats' => $filteredStats,
                'available_locations' => $availableLocations,
                'html' => view('branches._table', compact('branches'))->render(),
                'stats_html' => view('branches._stats', compact('filteredStats'))->render()
            ]);
        }

        return view('branches.index', compact('branches', 'filteredStats', 'availableLocations'));
    }

    /**
     * Show the form for creating a new branch.
     */
    public function create(): View
    {
        $this->authorize('create', Branch::class);

        return view('branches.create');
    }

    /**
     * Store a newly created branch in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        $this->authorize('create', Branch::class);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:branches,name',
            'location' => 'required|string|max:500',
            'address' => 'required|string|max:1000',
            'email' => 'required|email|max:255|unique:branches,email',
            'status' => 'boolean'
        ], [
            'name.required' => 'Branch name is required.',
            'name.unique' => 'A branch with this name already exists.',
            'location.required' => 'Branch location is required.',
            'phone.regex' => 'Please enter a valid UAE phone number.',
            'email.unique' => 'A branch with this email already exists.',
        ]);


        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $branch = Branch::create([
                'name' => $request->name,
                'location' => $request->location,
                'address' => $request->address,
                'phone' => $request->phone,
                'email' => $request->email,
                'status' => $request->boolean('status', true),
            ]);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Branch created successfully.',
                    'branch' => $branch->load(['academies', 'students'])
                ]);
            }

            return redirect()->route('branches.index')
                ->with('success', 'Branch created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create branch. Please try again.'
                ], 500);
            }

            return back()->with('error', 'Failed to create branch. Please try again.')
                ->withInput();
        }
    }

    /**
     * Display the specified branch with detailed information.
     */
    public function show(Branch $branch): View|JsonResponse
    {
        $this->authorize('view', $branch);

        $branch->load([
            'academies.programs.students',
            'academies.programs.payments',
            'students.payments',
            'users',
            'payments'
        ]);

        // Calculate branch statistics
        $stats = [
            'total_academies' => $branch->academies->count(),
            'active_academies' => $branch->academies->where('status', true)->count(),
            'total_programs' => $branch->academies->sum(fn($academy) => $academy->programs->count()),
            'active_programs' => $branch->academies->sum(fn($academy) => $academy->programs->where('status', true)->count()),
            'total_students' => $branch->students->count(),
            'active_students' => $branch->students->where('status', 'active')->count(),
            'total_revenue' => $branch->payments->where('status', 'completed')->sum('amount'),
            'pending_payments' => $branch->payments->where('status', 'pending')->sum('amount'),
        ];

        // Get recent activities
        $recentStudents = $branch->students()
            ->latest()
            ->limit(5)
            ->get();

        $recentPayments = $branch->payments()
            ->with('student')
            ->latest()
            ->limit(5)
            ->get();

        if (request()->ajax()) {
            return response()->json([
                'branch' => $branch,
                'stats' => $stats,
                'recent_students' => $recentStudents,
                'recent_payments' => $recentPayments
            ]);
        }

        return view('branches.show', compact('branch', 'stats', 'recentStudents', 'recentPayments'));
    }

    /**
     * Show the form for editing the specified branch.
     */
    public function edit(Branch $branch): View
    {
        $this->authorize('update', $branch);

        return view('branches.edit', compact('branch'));
    }

    /**
     * Update the specified branch in storage.
     */
    public function update(Request $request, Branch $branch): RedirectResponse|JsonResponse
    {
        $this->authorize('update', $branch);

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:branches,name,' . $branch->id,
            'location' => 'required|string|max:500',
            'address' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:15|regex:/^(\+971|00971|971)?[0-9]{8,9}$/',
            'email' => 'nullable|email|max:255|unique:branches,email,' . $branch->id,
            'status' => 'boolean'
        ], [
            'name.required' => 'Branch name is required.',
            'name.unique' => 'A branch with this name already exists.',
            'location.required' => 'Branch location is required.',
            'phone.regex' => 'Please enter a valid UAE phone number.',
            'email.unique' => 'A branch with this email already exists.',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $branch->update([
                'name' => $request->name,
                'location' => $request->location,
                'address' => $request->address,
                'phone' => $request->phone,
                'email' => $request->email,
                'status' => $request->boolean('status', true),
            ]);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Branch updated successfully.',
                    'branch' => $branch->fresh()->load(['academies', 'students'])
                ]);
            }

            return redirect()->route('branches.index')
                ->with('success', 'Branch updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update branch. Please try again.'
                ], 500);
            }

            return back()->with('error', 'Failed to update branch. Please try again.')
                ->withInput();
        }
    }

    /**
     * Remove the specified branch from storage.
     */
    public function destroy(Branch $branch): RedirectResponse|JsonResponse
    {
        $this->authorize('delete', $branch);

        try {
            DB::beginTransaction();

            // Check if branch has associated data
            $hasAcademies = $branch->academies()->count() > 0;
            $hasStudents = $branch->students()->count() > 0;
            $hasPayments = $branch->payments()->count() > 0;

            if ($hasAcademies || $hasStudents || $hasPayments) {
                // Soft delete by setting status to inactive instead of hard delete
                $branch->update(['status' => false]);
                $message = 'Branch has been deactivated due to existing data associations.';
            } else {
                // Safe to hard delete
                $branch->delete();
                $message = 'Branch deleted successfully.';
            }

            DB::commit();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message
                ]);
            }

            return redirect()->route('branches.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            DB::rollBack();

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete branch. Please try again.'
                ], 500);
            }

            return back()->with('error', 'Failed to delete branch. Please try again.');
        }
    }

    /**
     * Toggle branch status (active/inactive).
     */
    public function toggleStatus(Branch $branch): JsonResponse
    {
        $this->authorize('update', $branch);

        try {
            $branch->update(['status' => !$branch->status]);

            return response()->json([
                'success' => true,
                'message' => 'Branch status updated successfully.',
                'status' => $branch->status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update branch status.'
            ], 500);
        }
    }

    /**
     * Bulk operations for branches.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $this->authorize('update', Branch::class);

        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,delete',
            'branch_ids' => 'required|array|min:1',
            'branch_ids.*' => 'exists:branches,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $branchIds = $request->get('branch_ids');
            $action = $request->get('action');
            $affectedCount = 0;

            switch ($action) {
                case 'activate':
                    $affectedCount = Branch::whereIn('id', $branchIds)->update(['status' => true]);
                    $message = "Successfully activated {$affectedCount} branches.";
                    break;

                case 'deactivate':
                    $affectedCount = Branch::whereIn('id', $branchIds)->update(['status' => false]);
                    $message = "Successfully deactivated {$affectedCount} branches.";
                    break;

                case 'delete':
                    // Check for branches with associated data
                    $branchesWithData = Branch::whereIn('id', $branchIds)
                        ->where(function ($query) {
                            $query->has('academies')
                                ->orHas('students')
                                ->orHas('payments');
                        })->get();

                    // Deactivate branches with data
                    if ($branchesWithData->count() > 0) {
                        Branch::whereIn('id', $branchesWithData->pluck('id'))
                            ->update(['status' => false]);
                    }

                    // Delete branches without data
                    $branchesWithoutData = Branch::whereIn('id', $branchIds)
                        ->whereNotIn('id', $branchesWithData->pluck('id'));

                    $deletedCount = $branchesWithoutData->count();
                    $branchesWithoutData->delete();

                    $deactivatedCount = $branchesWithData->count();

                    if ($deletedCount > 0 && $deactivatedCount > 0) {
                        $message = "Deleted {$deletedCount} branches and deactivated {$deactivatedCount} branches with associated data.";
                    } elseif ($deletedCount > 0) {
                        $message = "Successfully deleted {$deletedCount} branches.";
                    } else {
                        $message = "Deactivated {$deactivatedCount} branches due to associated data.";
                    }
                    break;
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $message,
                'affected_count' => $affectedCount
            ]);
        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to perform bulk action. Please try again.'
            ], 500);
        }
    }

    /**
     * Export branches to Excel.
     */
    public function exportExcel(Request $request)
    {
        $this->authorize('viewAny', Branch::class);

        // Apply same filters as index method
        $query = Branch::with(['academies', 'students', 'users'])
            ->withCount(['academies', 'students', 'payments']);

        // Apply filters (same logic as index method)
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('location', 'like', "%{$search}%")
                    ->orWhere('address', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status') === 'active');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        $branches = $query->orderBy('name')->get();

        // Create CSV content
        $csvContent = "ID,Name,Location,Address,Phone,Email,Status,Academies,Students,Payments,Revenue,Created At\n";

        foreach ($branches as $branch) {
            $revenue = $branch->payments->where('status', 'completed')->sum('amount');
            $csvContent .= sprintf(
                "%d,\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",%d,%d,%d,%.2f,\"%s\"\n",
                $branch->id,
                str_replace('"', '""', $branch->name),
                str_replace('"', '""', $branch->location),
                str_replace('"', '""', $branch->address ?? ''),
                $branch->phone ?? '',
                $branch->email ?? '',
                $branch->status ? 'Active' : 'Inactive',
                $branch->academies_count,
                $branch->students_count,
                $branch->payments_count,
                $revenue,
                $branch->created_at->format('Y-m-d H:i:s')
            );
        }

        $filename = 'branches_export_' . date('Y-m-d_H-i-s') . '.csv';

        return response($csvContent)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
    }

    /**
     * Export branches to PDF.
     */
    public function exportPdf(Request $request)
    {
        $this->authorize('viewAny', Branch::class);

        try {
            // Apply same filters as index method
            $query = Branch::with(['academies', 'students', 'users', 'payments'])
                ->withCount(['academies', 'students', 'payments']);

            // Apply filters (same logic as index method)
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('location', 'like', "%{$search}%")
                        ->orWhere('address', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                });
            }

            if ($request->filled('status')) {
                $query->where('status', $request->get('status') === 'active');
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->get('date_from'));
            }
            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->get('date_to'));
            }

            $branches = $query->orderBy('name')->get();

            // Calculate summary statistics
            $stats = [
                'total_branches' => $branches->count(),
                'active_branches' => $branches->where('status', true)->count(),
                'inactive_branches' => $branches->where('status', false)->count(),
                'total_academies' => $branches->sum('academies_count'),
                'total_students' => $branches->sum('students_count'),
                'total_revenue' => $branches->sum(function ($branch) {
                    return $branch->payments->where('status', 'completed')->sum('amount');
                }),
            ];

            // Generate PDF using DomPDF
            $pdf = \Barryvdh\DomPDF\Facade\Pdf::loadView('branches.export-pdf', compact('branches', 'stats'));

            // Set paper size and orientation for better table display
            $pdf->setPaper('A4', 'landscape');

            // Set PDF options for better rendering
            $pdf->setOptions([
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'DejaVu Sans',
                'dpi' => 150,
                'defaultPaperSize' => 'A4',
                'chroot' => public_path(),
            ]);

            // Generate filename with timestamp
            $filename = 'branches_report_' . now()->format('Y-m-d_H-i-s') . '.pdf';

            // Download the PDF
            return $pdf->download($filename);
        } catch (\Exception $e) {
            Log::error('Branch PDF export failed: ' . $e->getMessage());

            return redirect()
                ->back()
                ->with('error', 'Failed to export branches to PDF. Please try again.');
        }
    }

    /**
     * Get branch data for API endpoints.
     */
    public function apiIndex(Request $request): JsonResponse
    {
        $query = Branch::with(['academies', 'students'])
            ->withCount(['academies', 'students', 'payments']);

        // Apply branch-level filtering for non-admin users
        $user = Auth::user();
        if ($user->role === 'branch_manager' || $user->role === 'academy_manager') {
            $query->where('id', $user->branch_id);
        }

        $branches = $query
            ->when($request->filled('status'), function ($query) use ($request) {
                $query->where('status', $request->boolean('status'));
            })
            ->when($request->filled('search'), function ($query) use ($request) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('location', 'like', "%{$search}%");
                });
            })
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $branches
        ]);
    }

    /**
     * Get real-time statistics for dashboard.
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $stats = [
            'total_branches' => Branch::count(),
            'active_branches' => Branch::where('status', true)->count(),
            'inactive_branches' => Branch::where('status', false)->count(),
            'total_academies' => Academy::count(),
            'total_students' => Student::count(),
            'total_revenue' => \App\Models\Payment::where('status', 'completed')->sum('amount'),
            'recent_branches' => Branch::latest()->limit(5)->get(['id', 'name', 'location', 'created_at']),
        ];

        return response()->json([
            'success' => true,
            'stats' => $stats,
            'timestamp' => now()->toISOString()
        ]);
    }
}
