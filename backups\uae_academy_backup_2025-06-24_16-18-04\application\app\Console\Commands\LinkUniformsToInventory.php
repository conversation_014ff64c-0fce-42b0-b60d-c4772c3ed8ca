<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Uniform;
use App\Models\UniformInventory;

class LinkUniformsToInventory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'uniforms:link-inventory {--dry-run : Show what would be linked without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Link existing uniform orders to inventory items';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->info('Running in DRY RUN mode - no changes will be made');
        }

        $uniforms = Uniform::whereNull('uniform_inventory_id')->get();

        if ($uniforms->isEmpty()) {
            $this->info('No uniforms found that need linking to inventory.');
            return 0;
        }

        $this->info("Found {$uniforms->count()} uniforms to link to inventory items.");

        $linked = 0;
        $notLinked = 0;

        $progressBar = $this->output->createProgressBar($uniforms->count());
        $progressBar->start();

        foreach ($uniforms as $uniform) {
            // Try to find matching inventory item
            $inventoryItem = UniformInventory::where('branch_id', $uniform->branch_id)
                ->where('academy_id', $uniform->academy_id)
                ->where('size', $uniform->size)
                ->where('status', 'active')
                ->whereHas('category', function ($query) use ($uniform) {
                    $query->where('name', 'like', "%{$uniform->item}%");
                })
                ->first();

            if ($inventoryItem) {
                if (!$dryRun) {
                    $uniform->update([
                        'uniform_inventory_id' => $inventoryItem->id,
                        'sku' => $inventoryItem->sku,
                        'color' => $inventoryItem->color,
                    ]);
                }
                $linked++;

                if ($dryRun) {
                    $this->line("Would link Uniform #{$uniform->id} ({$uniform->item} - {$uniform->size}) to Inventory #{$inventoryItem->id} ({$inventoryItem->sku})");
                }
            } else {
                $notLinked++;

                if ($dryRun) {
                    $this->line("No matching inventory found for Uniform #{$uniform->id} ({$uniform->item} - {$uniform->size})");
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        if ($dryRun) {
            $this->info("DRY RUN RESULTS:");
            $this->info("- Would link: {$linked} uniforms");
            $this->info("- Cannot link: {$notLinked} uniforms");
            $this->info("Run without --dry-run to apply changes.");
        } else {
            $this->info("LINKING COMPLETED:");
            $this->info("- Successfully linked: {$linked} uniforms");
            $this->info("- Could not link: {$notLinked} uniforms");

            if ($notLinked > 0) {
                $this->warn("Some uniforms could not be linked. You may need to:");
                $this->warn("1. Create matching inventory items");
                $this->warn("2. Check item names and sizes match");
                $this->warn("3. Ensure inventory items are active");
            }
        }

        return 0;
    }
}
