{{-- UAE English Sports Academy - Translation Management --}}
@extends('layouts.dashboard')

@section('title', 'Translation Management')

@section('header')
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Translation Management</h1>
            <p class="text-sm text-gray-600 mt-1">Manage system translations for English and Arabic</p>
        </div>
        <div class="flex items-center gap-2">
            <button type="button" class="btn btn-outline" onclick="importTranslations()">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                </svg>
                Import
            </button>
            <button type="button" class="btn btn-outline" onclick="exportTranslations()">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l3-3m0 0l-3-3m3 3H9"></path>
                </svg>
                Export
            </button>
            <button type="button" class="btn btn-primary" onclick="openAddTranslationModal()">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Add Translation
            </button>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Translation Management Content -->
        <div class="w-full">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="stat-card">
                    <div class="stat-icon bg-blue-100 text-blue-600">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ $stats['total'] ?? 0 }}</div>
                        <div class="stat-label">Total Translations</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon bg-green-100 text-green-600">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ $stats['complete'] ?? 0 }}</div>
                        <div class="stat-label">Complete</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon bg-yellow-100 text-yellow-600">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                            </path>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ $stats['incomplete'] ?? 0 }}</div>
                        <div class="stat-label">Incomplete</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon bg-purple-100 text-purple-600">
                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                            </path>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number">{{ $stats['groups'] ?? 0 }}</div>
                        <div class="stat-label">Groups</div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="bank-card mb-6">
                <div class="card-body">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <label for="search" class="form-label">Search Translations</label>
                            <div class="relative">
                                <input type="text" id="search" name="search" value="{{ $search }}"
                                    class="form-control pl-10" placeholder="Search by key or text...">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="w-full md:w-48">
                            <label for="group" class="form-label">Group</label>
                            <select id="group" name="group" class="form-control">
                                @foreach ($groups as $groupOption)
                                    <option value="{{ $groupOption }}" {{ $group === $groupOption ? 'selected' : '' }}>
                                        {{ ucfirst($groupOption) }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="button" class="btn btn-primary" onclick="filterTranslations()">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Search
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Translations Table -->
            <div class="bank-card">
                <div class="card-header">
                    <h3 class="card-title">Translations</h3>
                    <div class="card-actions">
                        <span class="text-sm text-gray-600">
                            {{ $translations->count() }} translations found
                        </span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Key</th>
                                    <th>English Text</th>
                                    <th>Arabic Text</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($translations as $translation)
                                    <tr>
                                        <td>
                                            <code
                                                class="text-sm bg-gray-100 px-2 py-1 rounded">{{ $translation->key }}</code>
                                        </td>
                                        <td>
                                            <div class="max-w-xs truncate" title="{{ $translation->text_en }}">
                                                {{ $translation->text_en }}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="max-w-xs truncate" dir="rtl"
                                                title="{{ $translation->text_ar }}">
                                                {{ $translation->text_ar }}
                                            </div>
                                        </td>
                                        <td>
                                            @if ($translation->text_en && $translation->text_ar)
                                                <span class="badge badge-success">Complete</span>
                                            @else
                                                <span class="badge badge-warning">Incomplete</span>
                                            @endif
                                        </td>
                                        <td>
                                            <div class="flex items-center gap-2">
                                                <button type="button" class="btn-icon"
                                                    onclick="editTranslation({{ $translation->id }}, {{ json_encode($translation->key) }}, {{ json_encode($translation->text_en) }}, {{ json_encode($translation->text_ar) }}, {{ json_encode($translation->group) }})"
                                                    title="Edit Translation">
                                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                        </path>
                                                    </svg>
                                                </button>
                                                <button type="button" class="btn-icon text-red-600"
                                                    onclick="deleteTranslation({{ $translation->id }})"
                                                    title="Delete Translation">
                                                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                                        </path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center py-8 text-gray-500">
                                            No translations found.
                                            <button type="button" class="text-blue-600 hover:text-blue-800"
                                                onclick="openAddTranslationModal()">
                                                Add your first translation
                                            </button>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Add/Edit Translation Modal -->
    <div id="translationModal" class="modal">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="translationModalTitle">Add Translation</h3>
                    <button type="button" class="modal-close" onclick="closeTranslationModal()">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <form id="translationForm" onsubmit="saveTranslation(event)">
                    <div class="modal-body">
                        <input type="hidden" id="translation_id" name="translation_id">

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="translation_key" class="form-label required">Translation Key</label>
                                <input type="text" id="translation_key" name="key" class="form-control" required>
                                <div class="form-help">Use dot notation (e.g., dashboard.welcome)</div>
                            </div>
                            <div>
                                <label for="translation_group" class="form-label required">Group</label>
                                <select id="translation_group" name="group" class="form-control" required>
                                    @foreach ($groups as $groupOption)
                                        <option value="{{ $groupOption }}">{{ ucfirst($groupOption) }}</option>
                                    @endforeach
                                    <option value="new">Create New Group...</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label for="text_en" class="form-label required">English Text</label>
                                <textarea id="text_en" name="text_en" rows="4" class="form-control" required></textarea>
                            </div>
                            <div>
                                <label for="text_ar" class="form-label required">Arabic Text</label>
                                <textarea id="text_ar" name="text_ar" rows="4" class="form-control" dir="rtl" required></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline" onclick="closeTranslationModal()">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Translation</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Translation Management JavaScript
        let currentTranslationId = null;

        // Open Add Translation Modal
        function openAddTranslationModal() {
            currentTranslationId = null;
            document.getElementById('translationModalTitle').textContent = 'Add Translation';
            document.getElementById('translationForm').reset();
            document.getElementById('translation_id').value = '';
            document.getElementById('translationModal').classList.add('show');
        }

        // Close Translation Modal
        function closeTranslationModal() {
            document.getElementById('translationModal').classList.remove('show');
            currentTranslationId = null;
        }

        // Edit Translation
        function editTranslation(id, key, textEn, textAr, group) {
            currentTranslationId = id;
            document.getElementById('translationModalTitle').textContent = 'Edit Translation';
            document.getElementById('translation_id').value = id;
            document.getElementById('translation_key').value = key;
            document.getElementById('text_en').value = textEn;
            document.getElementById('text_ar').value = textAr;
            document.getElementById('translation_group').value = group;
            document.getElementById('translationModal').classList.add('show');
        }

        // Save Translation
        async function saveTranslation(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const data = Object.fromEntries(formData.entries());

            try {
                const response = await fetch('{{ route('settings.translations.store') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                            'content')
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                    closeTranslationModal();
                    location.reload(); // Refresh to show updated data
                } else {
                    showNotification('error', result.message);
                }
            } catch (error) {
                console.error('Error saving translation:', error);
                showNotification('error', 'Failed to save translation');
            }
        }

        // Delete Translation
        async function deleteTranslation(id) {
            if (!confirm('Are you sure you want to delete this translation?')) {
                return;
            }

            try {
                const response = await fetch('{{ route('settings.translations.delete') }}', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                            'content')
                    },
                    body: JSON.stringify({
                        id: id
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                    location.reload(); // Refresh to show updated data
                } else {
                    showNotification('error', result.message);
                }
            } catch (error) {
                console.error('Error deleting translation:', error);
                showNotification('error', 'Failed to delete translation');
            }
        }

        // Filter Translations
        function filterTranslations() {
            const search = document.getElementById('search').value;
            const group = document.getElementById('group').value;

            const url = new URL(window.location);
            url.searchParams.set('search', search);
            url.searchParams.set('group', group);

            window.location.href = url.toString();
        }

        // Export Translations
        async function exportTranslations() {
            const group = document.getElementById('group').value;

            try {
                const response = await fetch('{{ route('settings.translations.export') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                            'content')
                    },
                    body: JSON.stringify({
                        group: group
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                } else {
                    showNotification('error', result.message);
                }
            } catch (error) {
                console.error('Error exporting translations:', error);
                showNotification('error', 'Failed to export translations');
            }
        }

        // Import Translations
        async function importTranslations() {
            const group = document.getElementById('group').value;

            if (!confirm('This will import translations from language files. Continue?')) {
                return;
            }

            try {
                const response = await fetch('{{ route('settings.translations.import') }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                            'content')
                    },
                    body: JSON.stringify({
                        group: group
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                    location.reload(); // Refresh to show updated data
                } else {
                    showNotification('error', result.message);
                }
            } catch (error) {
                console.error('Error importing translations:', error);
                showNotification('error', 'Failed to import translations');
            }
        }

        // Show Notification
        function showNotification(type, message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
        <div class="notification-content">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="notification-close">×</button>
        </div>
    `;

            // Add to page
            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Handle search on Enter key
        document.getElementById('search').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                filterTranslations();
            }
        });

        // Handle group change
        document.getElementById('group').addEventListener('change', function() {
            filterTranslations();
        });

        // Handle new group creation
        document.getElementById('translation_group').addEventListener('change', function() {
            if (this.value === 'new') {
                const newGroup = prompt('Enter new group name:');
                if (newGroup) {
                    const option = document.createElement('option');
                    option.value = newGroup;
                    option.textContent = newGroup.charAt(0).toUpperCase() + newGroup.slice(1);
                    option.selected = true;
                    this.insertBefore(option, this.lastElementChild);
                } else {
                    this.value = '{{ $group }}'; // Reset to current group
                }
            }
        });

        // Close modal on outside click
        document.getElementById('translationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeTranslationModal();
            }
        });
    </script>

    <style>
        /* Enhanced Translation Management Styles */

        /* Page Layout */
        .translation-page {
            background: #f8fafc;
            min-height: 100vh;
        }

        /* Statistics Cards */
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .stat-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .stat-icon svg {
            width: 24px;
            height: 24px;
        }

        .stat-content {
            text-align: left;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            line-height: 1;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* Enhanced Bank Card */
        .bank-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            background: #fafbfc;
        }

        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .card-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Enhanced Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.625rem 1.25rem;
            border-radius: 8px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            gap: 0.5rem;
        }

        .btn svg {
            width: 16px;
            height: 16px;
        }

        .btn-primary {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
        }

        .btn-primary:hover {
            background: #2563eb;
            border-color: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .btn-outline {
            background: white;
            color: #6b7280;
            border-color: #d1d5db;
        }

        .btn-outline:hover {
            background: #f9fafb;
            color: #374151;
            border-color: #9ca3af;
        }

        /* Enhanced Action Buttons */
        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            background: white;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-icon svg {
            width: 16px;
            height: 16px;
        }

        .btn-icon:hover {
            background: #f3f4f6;
            color: #374151;
            border-color: #d1d5db;
            transform: translateY(-1px);
        }

        .btn-icon.text-red-600 {
            color: #dc2626;
        }

        .btn-icon.text-red-600:hover {
            background: #fef2f2;
            color: #b91c1c;
            border-color: #fecaca;
        }

        /* Enhanced Table */
        .table-responsive {
            overflow-x: auto;
            border-radius: 8px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .table th {
            background: #f8fafc;
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 1px solid #e5e7eb;
            white-space: nowrap;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
            vertical-align: middle;
        }

        .table tbody tr:hover {
            background-color: #f8fafc;
        }

        .table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Enhanced Form Controls */
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            background: white;
        }

        .form-control.pl-10 {
            padding-left: 2.5rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        /* Enhanced Badges */
        .badge {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 6px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .badge-success {
            background-color: #d1fae5;
            color: #065f46;
        }

        .badge-warning {
            background-color: #fef3c7;
            color: #92400e;
        }

        /* Enhanced Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(4px);
        }

        .modal.show {
            display: flex;
        }

        .modal-dialog {
            background: white;
            border-radius: 12px;
            max-width: 90vw;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .modal-lg {
            width: 800px;
        }

        .modal-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
            margin: 0;
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: none;
            background: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .modal-close:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            padding: 1.5rem;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
            background: #fafbfc;
        }

        /* Enhanced Notifications */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            padding: 1rem 1.25rem;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            animation: slideIn 0.3s ease-out;
            border: 1px solid transparent;
        }

        .notification-success {
            background-color: #10b981;
            color: white;
            border-color: #059669;
        }

        .notification-error {
            background-color: #ef4444;
            color: white;
            border-color: #dc2626;
        }

        .notification-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .notification-close {
            background: none;
            border: none;
            color: inherit;
            font-size: 1.25rem;
            cursor: pointer;
            margin-left: 1rem;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .notification-close:hover {
            opacity: 1;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }

            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Enhanced Settings Navigation */
        .settings-nav-item {
            display: flex;
            align-items: center;
            padding: 0.875rem 1.25rem;
            color: #6b7280;
            text-decoration: none;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;
        }

        .settings-nav-item:hover {
            background: #f8fafc;
            color: #374151;
        }

        .settings-nav-item.active {
            background: #eff6ff;
            color: #2563eb;
            border-right: 3px solid #3b82f6;
        }

        .settings-nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .settings-nav-icon svg {
            width: 18px;
            height: 18px;
        }

        .settings-nav-text {
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* Form Improvements */
        .form-help {
            font-size: 0.75rem;
            color: #6b7280;
            margin-top: 0.375rem;
        }

        .required::after {
            content: ' *';
            color: #ef4444;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .stat-card {
                padding: 1rem;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .card-header,
            .card-body {
                padding: 1rem;
            }

            .modal-dialog {
                margin: 1rem;
                max-width: calc(100vw - 2rem);
            }

            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.8125rem;
            }

            .table th,
            .table td {
                padding: 0.75rem 0.5rem;
            }
        }

        /* Code styling */
        code {
            background: #f1f5f9;
            color: #475569;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8125rem;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
        }

        /* Text truncation */
        .max-w-xs {
            max-width: 20rem;
        }

        .truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* RTL Support */
        [dir="rtl"] .truncate {
            direction: rtl;
            text-align: right;
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        /* Focus improvements */
        .btn:focus,
        .btn-icon:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }
    </style>
@endpush
