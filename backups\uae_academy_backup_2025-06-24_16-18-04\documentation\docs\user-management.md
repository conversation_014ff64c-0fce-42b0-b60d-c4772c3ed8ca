# User Management Module Documentation

## Overview

The User Management module provides comprehensive functionality for managing system users, roles, and access control within the UAE English Sports Academy system. This module is exclusively accessible to System Administrators and follows the established design patterns and technical specifications.

## Features

### 🔐 Role-Based Access Control
- **System Administrator**: Full access to all system features
- **Branch Manager**: Access to assigned branches and their academies
- **Academy Manager**: Access to assigned academy only

### 👥 User Management
- **User Creation**: Add new users with role-specific assignments
- **User Editing**: Update user information and role assignments
- **User Viewing**: Detailed user profiles with comprehensive information
- **Status Management**: Activate/deactivate user accounts
- **Bulk Operations**: Mass activate, deactivate, or delete users

### 🔍 Advanced Search & Filtering
- **Text Search**: Search by name, email, branch, or academy
- **Role Filtering**: Filter by user roles (admin, branch_manager, academy_manager)
- **Status Filtering**: Filter by active/inactive status
- **Branch/Academy Filtering**: Filter by specific branches or academies
- **Sorting Options**: Sort by various fields with ascending/descending order

### 📊 Statistics & Analytics
- **User Counts**: Total, active, and inactive users
- **Role Distribution**: Count of users by role type
- **Real-time Updates**: Live statistics with percentage calculations

### 📤 Export Functionality
- **Excel Export**: CSV format with filtered data
- **PDF Export**: Professional PDF reports with statistics
- **Filtered Exports**: Export respects current search/filter criteria

## Technical Implementation

### Backend Components

#### Models
- **User.php**: Enhanced model with relationships and computed properties
  - Relationships: branch, academy
  - Computed Properties: role_text, status_text, badge_classes, formatted_dates
  - Scopes: active, inactive, role, search
  - Utility Methods: getStatistics(), role checking methods

#### Controllers
- **UserController.php**: Full CRUD operations with advanced features
  - Index: Paginated listing with search/filter/sort
  - Create/Store: User creation with role-specific validation
  - Show: Detailed user profile view
  - Edit/Update: User modification with role validation
  - Destroy: Safe user deletion with constraints
  - toggleStatus: AJAX status toggling
  - bulkAction: Mass operations on multiple users
  - exportExcel/exportPdf: Data export functionality
  - getAcademiesByBranch: AJAX academy loading

#### Policies
- **UserPolicy.php**: Role-based authorization
  - Admin: Full access to all operations
  - Self-protection: Users cannot modify/delete themselves
  - Admin protection: Cannot delete last admin user

#### Validation
- **Form Requests**: Comprehensive validation rules
  - Required fields: name, email, password, role
  - Unique constraints: email validation
  - Role-specific requirements: branch/academy assignments
  - Password confirmation and strength requirements

### Frontend Components

#### Views
- **index.blade.php**: Main listing page with advanced filters and statistics
- **create.blade.php**: User creation form with role-based field visibility
- **edit.blade.php**: User editing form with current assignment display
- **show.blade.php**: Detailed user profile with role permissions
- **_table.blade.php**: Table view with sortable columns and actions
- **_grid.blade.php**: Card-based grid view with responsive design
- **_filters.blade.php**: Advanced search and filtering interface
- **_stats.blade.php**: Statistics cards with real-time data
- **export-pdf.blade.php**: Professional PDF export template

#### JavaScript Features
- **Alpine.js Integration**: Reactive components for bulk actions
- **AJAX Operations**: Real-time updates without page refresh
- **Form Validation**: Client-side validation with visual feedback
- **Dynamic Loading**: Academy loading based on branch selection
- **Notification System**: User feedback for actions
- **View Mode Toggle**: Switch between table and grid views

#### CSS Enhancements
- **Bank-style Design**: Premium aesthetic following design guidelines
- **Responsive Layout**: Mobile-first design approach
- **Action Buttons**: Consistent styling with hover effects
- **Status Badges**: Color-coded role and status indicators
- **Form Styling**: Professional form elements with validation states

## Database Schema

### Users Table Structure
```sql
- id (Primary Key)
- name (VARCHAR, UTF8MB4)
- email (VARCHAR, Unique)
- email_verified_at (TIMESTAMP, Nullable)
- password (VARCHAR, Hashed)
- role (ENUM: admin, branch_manager, academy_manager)
- branch_id (Foreign Key, Nullable)
- academy_id (Foreign Key, Nullable)
- status (BOOLEAN, Default: true)
- last_login_at (TIMESTAMP, Nullable)
- remember_token (VARCHAR, Nullable)
- created_at, updated_at (Timestamps)
```

### Relationships
- **Branch**: belongsTo relationship (nullable for admins)
- **Academy**: belongsTo relationship (nullable for admins and branch managers)

### Indexes
- **Composite Index**: (role, status) for efficient filtering
- **Composite Index**: (branch_id, academy_id) for assignment queries
- **Unique Index**: email for user identification

## Security & Authorization

### Role-based Access Control
- **Admin Only**: Complete access to user management
- **Self-Protection**: Users cannot modify their own accounts
- **Admin Protection**: Cannot delete the last active admin
- **Status Protection**: Cannot deactivate the last active admin

### Permission Matrix
| Operation | Admin | Branch Manager | Academy Manager |
|-----------|-------|----------------|-----------------|
| View All | ✅ | ❌ | ❌ |
| Create | ✅ | ❌ | ❌ |
| Update | ✅ (not self) | ❌ | ❌ |
| Delete | ✅ (not self) | ❌ | ❌ |
| Toggle Status | ✅ (not self) | ❌ | ❌ |
| Bulk Actions | ✅ | ❌ | ❌ |
| Export | ✅ | ❌ | ❌ |

## API Endpoints

### Web Routes
```php
// User Management (Admin only)
Route::middleware('role:admin')->group(function () {
    Route::resource('users', UserController::class);
    Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus']);
    Route::post('users/bulk-action', [UserController::class, 'bulkAction']);
    Route::get('users/export/excel', [UserController::class, 'exportExcel']);
    Route::get('users/export/pdf', [UserController::class, 'exportPdf']);
    Route::get('users/academies-by-branch', [UserController::class, 'getAcademiesByBranch']);
});
```

### AJAX Endpoints
- `POST /users/{user}/toggle-status` - Toggle user status
- `POST /users/bulk-action` - Bulk operations
- `GET /users/academies-by-branch` - Load academies by branch

## Usage Guide

### Creating a User
1. Navigate to User Management
2. Click "Add New User"
3. Fill in basic information (name, email, password)
4. Select user role
5. Assign branch/academy based on role
6. Set account status
7. Save user profile

### Managing Users
1. Use advanced filters to find specific users
2. View user details with comprehensive information
3. Edit user information and role assignments
4. Toggle user status for access control
5. Use bulk operations for mass updates
6. Export user data for reporting

### Role Assignment Rules
- **Admin**: No branch/academy assignment required
- **Branch Manager**: Branch assignment required
- **Academy Manager**: Both branch and academy assignment required

## Statistics & Analytics

### User Metrics
- **Total Users**: Complete count of all system users
- **Active/Inactive**: Status-based user distribution
- **Role Distribution**: Count by user roles
- **Assignment Status**: Users with/without assignments

### Real-time Updates
- **Live Statistics**: Automatic calculation and display
- **Percentage Calculations**: Relative distribution metrics
- **Visual Indicators**: Color-coded status and role badges

## Export & Reporting

### Excel Export (CSV)
- **Filtered Data**: Respects current search/filter criteria
- **Complete Information**: All user fields included
- **Timestamp**: Export generation date/time

### PDF Export
- **Professional Layout**: Branded report template
- **Statistics Summary**: Key metrics at the top
- **Detailed Table**: Complete user information
- **Print Optimized**: Proper formatting for printing

## Integration Points

### Authentication System
- **Login Tracking**: Last login timestamp recording
- **Status Enforcement**: Inactive users cannot log in
- **Role Enforcement**: Middleware-based access control

### Branch/Academy Management
- **Dynamic Loading**: Academy options based on branch selection
- **Assignment Validation**: Role-specific assignment requirements
- **Relationship Management**: Proper foreign key constraints

## Best Practices

### User Creation
- **Strong Passwords**: Enforce password complexity
- **Proper Role Assignment**: Match role to user responsibilities
- **Initial Status**: Set appropriate active/inactive status
- **Assignment Validation**: Ensure proper branch/academy assignments

### User Management
- **Regular Audits**: Review user access and assignments
- **Status Management**: Deactivate unused accounts
- **Role Reviews**: Periodic role assignment verification
- **Export Backups**: Regular user data exports

### Security Considerations
- **Admin Protection**: Always maintain at least one active admin
- **Self-Protection**: Prevent users from locking themselves out
- **Audit Trail**: Track user modifications and access
- **Password Security**: Enforce strong password policies

---

**Last Updated**: December 2024  
**Version**: 1.0  
**Module**: User Management  
**Access Level**: Admin Only  
**Dependencies**: Branch Management, Academy Management
