<?php

namespace App\Policies;

use App\Models\UniformInventory;
use App\Models\User;

class UniformInventoryPolicy
{
    /**
     * Determine whether the user can view any inventory items.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the inventory item.
     */
    public function view(User $user, UniformInventory $inventory): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can view inventory in their branch
                return $user->branch_id === $inventory->branch_id;
            case 'academy_manager':
                // Academy managers can view inventory in their academy
                return $user->academy_id === $inventory->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can create inventory items.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can update the inventory item.
     */
    public function update(User $user, UniformInventory $inventory): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can update inventory in their branch
                return $user->branch_id === $inventory->branch_id;
            case 'academy_manager':
                // Academy managers can update inventory in their academy
                return $user->academy_id === $inventory->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can delete the inventory item.
     */
    public function delete(User $user, UniformInventory $inventory): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can delete inventory in their branch (only if no stock)
                return $user->branch_id === $inventory->branch_id && 
                       $inventory->current_stock <= 0;
            case 'academy_manager':
                // Academy managers can delete inventory in their academy (only if no stock)
                return $user->academy_id === $inventory->academy_id && 
                       $inventory->current_stock <= 0;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can restore the inventory item.
     */
    public function restore(User $user, UniformInventory $inventory): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can permanently delete the inventory item.
     */
    public function forceDelete(User $user, UniformInventory $inventory): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can export inventory data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view inventory statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can adjust stock levels.
     */
    public function adjustStock(User $user, UniformInventory $inventory): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                return $user->branch_id === $inventory->branch_id;
            case 'academy_manager':
                return $user->academy_id === $inventory->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can reserve stock.
     */
    public function reserveStock(User $user, UniformInventory $inventory): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                return $user->branch_id === $inventory->branch_id;
            case 'academy_manager':
                return $user->academy_id === $inventory->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can manage inventory locations.
     */
    public function manageLocations(User $user, UniformInventory $inventory): bool
    {
        return $this->update($user, $inventory);
    }

    /**
     * Determine whether the user can view stock movements.
     */
    public function viewStockMovements(User $user, UniformInventory $inventory): bool
    {
        return $this->view($user, $inventory);
    }
}
