@extends('layouts.dashboard')

@section('title', 'Edit Branch - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Edit Branch</h1>
                <p class="text-lg text-dark-gray">Update {{ $branch->name }} information</p>
                <span class="badge-bank {{ $branch->status ? 'badge-success' : 'badge-neutral' }}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="{{ $branch->status ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                        </path>
                    </svg>
                    {{ $branch->status ? 'Active' : 'Inactive' }} Branch
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('branches.show', $branch) }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                    </path>
                </svg>
                View Details
            </a>
            <a href="{{ route('branches.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Branches
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto" x-data="branchEditForm()" x-init="init()">
        <form action="{{ route('branches.update', $branch) }}" method="POST" @submit="handleSubmit" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Branch Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="stats-card-sm">
                    <div class="stats-icon-sm bg-gradient-to-br from-leaders-red to-leaders-deep-red">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 14l9-5-9-5-9 5 9 5z"></path>
                        </svg>
                    </div>
                    <div class="stats-value-sm">{{ $branch->academies_count ?? 0 }}</div>
                    <div class="stats-label-sm">Academies</div>
                </div>
                <div class="stats-card-sm">
                    <div class="stats-icon-sm bg-gradient-to-br from-success-green to-green-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value-sm">{{ $branch->students_count ?? 0 }}</div>
                    <div class="stats-label-sm">Students</div>
                </div>
                <div class="stats-card-sm">
                    <div class="stats-icon-sm bg-gradient-to-br from-info-blue to-blue-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value-sm">{{ number_format($branch->total_revenue ?? 0, 0) }}</div>
                    <div class="stats-label-sm">Revenue (AED)</div>
                </div>
                <div class="stats-card-sm">
                    <div class="stats-icon-sm bg-gradient-to-br from-gold-yellow to-yellow-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value-sm">{{ $branch->created_at->diffInDays() }}</div>
                    <div class="stats-label-sm">Days Active</div>
                </div>
            </div>

            <!-- Main Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Branch Information</h3>
                        <p class="bank-card-subtitle">Update branch details and contact information</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Branch Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label-bank required">Branch Name</label>
                            <input type="text" id="name" name="name"
                                value="{{ old('name', $branch->name) }}"
                                class="form-input-bank @error('name') border-error-red @enderror"
                                placeholder="Enter branch name" x-model="form.name" @input="validateField('name')"
                                required>
                            @error('name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Choose a descriptive name that identifies the branch location</p>
                        </div>

                        <!-- Location -->
                        <div class="md:col-span-2">
                            <label for="location" class="form-label-bank required">Location</label>
                            <input type="text" id="location" name="location"
                                value="{{ old('location', $branch->location) }}"
                                class="form-input-bank @error('location') border-error-red @enderror"
                                placeholder="Enter location" x-model="form.location" @input="validateField('location')"
                                required>
                            @error('location')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Specify the city area or district where the branch is located</p>
                        </div>

                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label for="address" class="form-label-bank">Full Address</label>
                            <textarea id="address" name="address" rows="3"
                                class="form-textarea-bank @error('address') border-error-red @enderror" placeholder="Enter complete address"
                                x-model="form.address">{{ old('address', $branch->address) }}</textarea>
                            @error('address')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: Provide detailed address for better identification</p>
                        </div>

                        <!-- Phone Number -->
                        <div>
                            <label for="phone" class="form-label-bank">Phone Number</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-dark-gray text-sm">+971</span>
                                </div>
                                <input type="tel" id="phone" name="phone"
                                    value="{{ old('phone', $branch->phone) }}"
                                    class="form-input-bank pl-12 @error('phone') border-error-red @enderror"
                                    placeholder="50 123 4567" pattern="[0-9]{2}[0-9]{3}[0-9]{4}" x-model="form.phone"
                                    @input="validateField('phone')">
                            </div>
                            @error('phone')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">UAE phone number format: 50 123 4567</p>
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="form-label-bank">Email Address</label>
                            <input type="email" id="email" name="email"
                                value="{{ old('email', $branch->email) }}"
                                class="form-input-bank @error('email') border-error-red @enderror"
                                placeholder="<EMAIL>" x-model="form.email"
                                @input="validateField('email')">
                            @error('email')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: Contact email for this branch</p>
                        </div>

                        <!-- Status -->
                        <div class="md:col-span-2">
                            <label class="form-label-bank">Branch Status</label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="1" class="form-radio-bank"
                                        x-model="form.status"
                                        {{ old('status', $branch->status) == '1' ? 'checked' : '' }}>
                                    <span class="ml-2 text-charcoal-black">Active</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="0" class="form-radio-bank"
                                        x-model="form.status"
                                        {{ old('status', $branch->status) == '0' ? 'checked' : '' }}>
                                    <span class="ml-2 text-charcoal-black">Inactive</span>
                                </label>
                            </div>
                            <p class="form-help-bank">
                                @if ($branch->academies_count > 0 || $branch->students_count > 0)
                                    <span class="text-warning-orange">⚠️ Warning: This branch has
                                        {{ $branch->academies_count }} academies and {{ $branch->students_count }}
                                        students. Deactivating will affect operations.</span>
                                @else
                                    Set the status of the branch
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change History -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Change History</h3>
                        <p class="bank-card-subtitle">Track modifications to this branch</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-info-blue to-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                        <div>
                            <strong class="text-charcoal-black">Created:</strong>
                            <p class="text-dark-gray">{{ $branch->created_at->format('F j, Y \a\t g:i A') }}</p>
                            <p class="text-medium-gray">{{ $branch->created_at->diffForHumans() }}</p>
                        </div>
                        <div>
                            <strong class="text-charcoal-black">Last Updated:</strong>
                            <p class="text-dark-gray">{{ $branch->updated_at->format('F j, Y \a\t g:i A') }}</p>
                            <p class="text-medium-gray">{{ $branch->updated_at->diffForHumans() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <a href="{{ route('branches.show', $branch) }}" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                                View Details
                            </a>
                            <button type="button" @click="deleteBranch()"
                                class="btn-bank btn-bank-outline text-error-red border-error-red hover:bg-error-red hover:text-white">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                    </path>
                                </svg>
                                Delete Branch
                            </button>
                        </div>

                        <div class="flex items-center space-x-3">
                            <a href="{{ route('branches.index') }}" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel
                            </a>
                            <button type="submit" class="btn-bank" :disabled="isSubmitting"
                                :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    x-show="!isSubmitting">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12">
                                    </path>
                                </svg>
                                <svg class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24"
                                    x-show="isSubmitting">
                                    <circle class="opacity-25" cx="12" cy="12" r="10"
                                        stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                <span x-text="isSubmitting ? 'Updating Branch...' : 'Update Branch'"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function branchEditForm() {
            return {
                form: {
                    name: '{{ old('name', $branch->name) }}',
                    location: '{{ old('location', $branch->location) }}',
                    address: '{{ old('address', $branch->address) }}',
                    phone: '{{ old('phone', $branch->phone) }}',
                    email: '{{ old('email', $branch->email) }}',
                    status: '{{ old('status', $branch->status ? '1' : '0') }}'
                },
                isSubmitting: false,
                errors: {},

                init() {
                    // Initialize form
                },

                validateField(field) {
                    // Clear previous error
                    delete this.errors[field];

                    // Basic validation
                    switch (field) {
                        case 'name':
                            if (!this.form.name.trim()) {
                                this.errors[field] = 'Branch name is required';
                            } else if (this.form.name.length < 3) {
                                this.errors[field] = 'Branch name must be at least 3 characters';
                            }
                            break;
                        case 'location':
                            if (!this.form.location.trim()) {
                                this.errors[field] = 'Location is required';
                            }
                            break;
                        case 'email':
                            if (this.form.email && !this.isValidEmail(this.form.email)) {
                                this.errors[field] = 'Please enter a valid email address';
                            }
                            break;
                        case 'phone':
                            if (this.form.phone && !this.isValidUAEPhone(this.form.phone)) {
                                this.errors[field] = 'Please enter a valid UAE phone number';
                            }
                            break;
                    }
                },

                isValidEmail(email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                },

                isValidUAEPhone(phone) {
                    const phoneRegex = /^[0-9]{2}[0-9]{3}[0-9]{4}$/;
                    return phoneRegex.test(phone.replace(/\s/g, ''));
                },

                handleSubmit(event) {
                    this.isSubmitting = true;

                    // Validate all fields
                    this.validateField('name');
                    this.validateField('location');
                    this.validateField('email');
                    this.validateField('phone');

                    // If there are errors, prevent submission
                    if (Object.keys(this.errors).length > 0) {
                        event.preventDefault();
                        this.isSubmitting = false;
                        showNotification('error', 'Please fix the form errors before submitting.');
                        return;
                    }
                },

                async deleteBranch() {
                    const hasData =
                        {{ $branch->academies_count > 0 || $branch->students_count > 0 ? 'true' : 'false' }};
                    let confirmMessage = `Are you sure you want to delete "{{ $branch->name }}"?`;

                    if (hasData) {
                        confirmMessage +=
                            '\n\nThis branch has {{ $branch->academies_count }} academies and {{ $branch->students_count }} students. This action cannot be undone.';
                    }

                    if (!confirm(confirmMessage)) {
                        return;
                    }

                    try {
                        const response = await fetch('{{ route('branches.destroy', $branch) }}', {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.href = '{{ route('branches.index') }}';
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while deleting the branch.');
                    }
                }
            }
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            alert(message);
        }
    </script>
@endpush
