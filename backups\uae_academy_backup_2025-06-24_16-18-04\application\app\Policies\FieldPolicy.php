<?php

namespace App\Policies;

use App\Models\Field;
use App\Models\User;

class FieldPolicy
{
    /**
     * Determine whether the user can view any fields.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the field.
     */
    public function view(User $user, Field $field): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can create fields.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can update the field.
     */
    public function update(User $user, Field $field): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can delete the field.
     */
    public function delete(User $user, Field $field): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can restore the field.
     */
    public function restore(User $user, Field $field): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can permanently delete the field.
     */
    public function forceDelete(User $user, Field $field): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can export field data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view field statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can manage field reservations.
     */
    public function manageReservations(User $user, Field $field): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view field availability.
     */
    public function viewAvailability(User $user, Field $field): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }
}
