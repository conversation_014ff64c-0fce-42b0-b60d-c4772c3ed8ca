<?php

// UAE English Sports Academy - Dashboard Translations (English)
// Dashboard-specific translations and navigation

return [
    // ===== MAIN NAVIGATION =====
    'Dashboard' => 'Dashboard',
    'Branch Management' => 'Branch Management',
    'Academy Management' => 'Academy Management',
    'Venue Management' => 'Venue Management',
    'Program Management' => 'Program Management',
    'Student Management' => 'Student Management',
    'Payment Management' => 'Payment Management',
    'Uniform Management' => 'Uniform Management',
    'Field Reservations' => 'Field Reservations',
    'Manage Customers' => 'Manage Customers',
    'User Management' => 'User Management',
    'Settings' => 'Settings',

    // ===== REPORTS SECTION =====
    'Reports' => 'Reports',
    'Reports Dashboard' => 'Reports Dashboard',
    'Financial Reports' => 'Financial Reports',
    'Uniform Reports' => 'Uniform Reports',
    'Program Reports' => 'Program Reports',
    'Status Reports' => 'Status Reports',
    'Daily Reports' => 'Daily Reports',

    // ===== QUICK ACTIONS =====
    'Quick Actions' => 'Quick Actions',
    'Add Student' => 'Add Student',
    'Add Payment' => 'Add Payment',
    'Add Venue' => 'Add Venue',
    'Order Uniform' => 'Order Uniform',

    // ===== ADMINISTRATION =====
    'Administration' => 'Administration',

    // ===== DASHBOARD OVERVIEW =====
    'welcome_message' => 'Welcome to UAE English Sports Academy',
    'dashboard_overview' => 'Dashboard Overview',
    'system_status' => 'System Status',
    'recent_activity' => 'Recent Activity',
    'quick_stats' => 'Quick Statistics',
    'performance_metrics' => 'Performance Metrics',

    // ===== STATISTICS CARDS =====
    'total_branches' => 'Total Branches',
    'total_academies' => 'Total Academies',
    'total_programs' => 'Total Programs',
    'total_students' => 'Total Students',
    'total_payments' => 'Total Payments',
    'total_uniforms' => 'Total Uniforms',
    'total_users' => 'Total Users',
    'active_branches' => 'Active Branches',
    'active_academies' => 'Active Academies',
    'active_programs' => 'Active Programs',
    'active_students' => 'Active Students',
    'pending_payments' => 'Pending Payments',
    'pending_uniforms' => 'Pending Uniforms',
    'monthly_revenue' => 'Monthly Revenue',
    'yearly_revenue' => 'Yearly Revenue',
    'new_registrations' => 'New Registrations',
    'recent_payments' => 'Recent Payments',

    // ===== DASHBOARD WIDGETS =====
    'revenue_chart' => 'Revenue Chart',
    'enrollment_trends' => 'Enrollment Trends',
    'payment_status' => 'Payment Status',
    'branch_performance' => 'Branch Performance',
    'academy_performance' => 'Academy Performance',
    'program_popularity' => 'Program Popularity',
    'student_demographics' => 'Student Demographics',
    'uniform_orders' => 'Uniform Orders',
    'recent_activities' => 'Recent Activities',
    'upcoming_events' => 'Upcoming Events',
    'notifications_panel' => 'Notifications',
    'alerts_panel' => 'Alerts',

    // ===== TIME PERIODS =====
    'today' => 'Today',
    'this_week' => 'This Week',
    'this_month' => 'This Month',
    'this_year' => 'This Year',
    'last_7_days' => 'Last 7 Days',
    'last_30_days' => 'Last 30 Days',
    'last_90_days' => 'Last 90 Days',
    'last_year' => 'Last Year',
    'custom_range' => 'Custom Range',

    // ===== DASHBOARD ACTIONS =====
    'refresh_data' => 'Refresh Data',
    'export_report' => 'Export Report',
    'view_details' => 'View Details',
    'manage_settings' => 'Manage Settings',
    'system_backup' => 'System Backup',
    'maintenance_mode' => 'Maintenance Mode',
    'user_activity' => 'User Activity',
    'system_logs' => 'System Logs',

    // ===== DASHBOARD MESSAGES =====
    'data_updated' => 'Data updated successfully',
    'loading_dashboard' => 'Loading dashboard...',
    'no_data_available' => 'No data available for the selected period',
    'dashboard_error' => 'Error loading dashboard data',
    'refresh_required' => 'Please refresh to see latest data',
    'maintenance_notice' => 'System maintenance scheduled',
    'backup_completed' => 'System backup completed successfully',
    'welcome_back' => 'Welcome back',
    'good_morning' => 'Good morning',
    'good_afternoon' => 'Good afternoon',
    'good_evening' => 'Good evening',

    // ===== DASHBOARD FILTERS =====
    'filter_by_branch' => 'Filter by Branch',
    'filter_by_academy' => 'Filter by Academy',
    'filter_by_program' => 'Filter by Program',
    'filter_by_status' => 'Filter by Status',
    'filter_by_date' => 'Filter by Date',
    'filter_by_user' => 'Filter by User',
    'all_branches' => 'All Branches',
    'all_academies' => 'All Academies',
    'all_programs' => 'All Programs',
    'all_statuses' => 'All Statuses',
    'all_users' => 'All Users',

    // ===== DASHBOARD CHARTS =====
    'revenue_by_month' => 'Revenue by Month',
    'students_by_program' => 'Students by Program',
    'payments_by_status' => 'Payments by Status',
    'uniforms_by_status' => 'Uniforms by Status',
    'branches_by_region' => 'Branches by Region',
    'growth_rate' => 'Growth Rate',
    'conversion_rate' => 'Conversion Rate',
    'retention_rate' => 'Retention Rate',

    // ===== DASHBOARD TABLES =====
    'recent_students' => 'Recent Students',
    'recent_payments' => 'Recent Payments',
    'recent_uniforms' => 'Recent Uniform Orders',
    'top_programs' => 'Top Programs',
    'top_branches' => 'Top Branches',
    'top_academies' => 'Top Academies',
    'pending_approvals' => 'Pending Approvals',
    'overdue_payments' => 'Overdue Payments',

    // ===== DASHBOARD NOTIFICATIONS =====
    'new_student_registered' => 'New student registered',
    'payment_received' => 'Payment received',
    'uniform_order_placed' => 'Uniform order placed',
    'branch_activated' => 'Branch activated',
    'academy_created' => 'Academy created',
    'program_updated' => 'Program updated',
    'user_logged_in' => 'User logged in',
    'system_updated' => 'System updated',
    'backup_reminder' => 'Backup reminder',
    'maintenance_reminder' => 'Maintenance reminder',

    // ===== DASHBOARD SHORTCUTS =====
    'keyboard_shortcuts' => 'Keyboard Shortcuts',
    'create_student' => 'Create Student (Ctrl+N)',
    'search_students' => 'Search Students (Ctrl+F)',
    'view_reports' => 'View Reports (Ctrl+R)',
    'system_settings' => 'System Settings (Ctrl+S)',
    'help_center' => 'Help Center (F1)',
    'logout' => 'Logout (Ctrl+L)',

    // ===== DASHBOARD HELP =====
    'help_getting_started' => 'Getting Started',
    'help_user_guide' => 'User Guide',
    'help_video_tutorials' => 'Video Tutorials',
    'help_faq' => 'Frequently Asked Questions',
    'help_contact_support' => 'Contact Support',
    'help_system_requirements' => 'System Requirements',
    'help_troubleshooting' => 'Troubleshooting',

    // ===== DASHBOARD PREFERENCES =====
    'dashboard_preferences' => 'Dashboard Preferences',
    'customize_dashboard' => 'Customize Dashboard',
    'widget_settings' => 'Widget Settings',
    'notification_settings' => 'Notification Settings',
    'display_settings' => 'Display Settings',
    'language_settings' => 'Language Settings',
    'theme_settings' => 'Theme Settings',
    'layout_settings' => 'Layout Settings',

    // ===== DASHBOARD EXPORT =====
    'export_dashboard' => 'Export Dashboard',
    'export_to_pdf' => 'Export to PDF',
    'export_to_excel' => 'Export to Excel',
    'export_to_csv' => 'Export to CSV',
    'export_summary' => 'Export Summary',
    'export_detailed' => 'Export Detailed Report',
    'export_custom' => 'Export Custom Report',

    // ===== DASHBOARD SEARCH =====
    'search_dashboard' => 'Search Dashboard',
    'search_students' => 'Search Students',
    'search_payments' => 'Search Payments',
    'search_branches' => 'Search Branches',
    'search_academies' => 'Search Academies',
    'search_programs' => 'Search Programs',
    'search_uniforms' => 'Search Uniforms',
    'search_users' => 'Search Users',
    'search_placeholder' => 'Type to search...',
    'search_results' => 'Search Results',
    'no_search_results' => 'No search results found',

    // ===== DASHBOARD CALENDAR =====
    'calendar_view' => 'Calendar View',
    'upcoming_events' => 'Upcoming Events',
    'scheduled_payments' => 'Scheduled Payments',
    'program_schedules' => 'Program Schedules',
    'maintenance_schedules' => 'Maintenance Schedules',
    'holiday_calendar' => 'Holiday Calendar',
    'academic_calendar' => 'Academic Calendar',

    // ===== DASHBOARD PERFORMANCE =====
    'performance_overview' => 'Performance Overview',
    'system_performance' => 'System Performance',
    'database_performance' => 'Database Performance',
    'user_activity_stats' => 'User Activity Statistics',
    'response_time' => 'Response Time',
    'uptime_percentage' => 'Uptime Percentage',
    'error_rate' => 'Error Rate',
    'success_rate' => 'Success Rate',

    // ===== MISSING TRANSLATIONS FOR DASHBOARD =====
    'active_growing' => 'Active & Growing',
    'excellence_sports' => 'Excellence in Sports',
    'future_champions' => 'Future Champions',
    'view_all' => 'View All',
    'welcome_back' => 'Welcome Back!',
    'Order Uniform' => 'Order Uniform',
    'Quick Actions' => 'Quick Actions',
    'quick_stats' => 'Quick Statistics',
    'Add Student' => 'Add Student',
    'Add Payment' => 'Add Payment',
    'view_details' => 'View Details',
];
