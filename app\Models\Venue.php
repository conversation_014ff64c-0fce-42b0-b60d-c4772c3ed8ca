<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Venue extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'code',
        'description',
        'description_ar',
        'address',
        'address_ar',
        'city',
        'country',
        'phone',
        'email',
        'manager_name',
        'manager_phone',
        'manager_email',
        'latitude',
        'longitude',
        'operating_hours',
        'facilities',
        'hourly_rate_base',
        'currency',
        'vat_applicable',
        'vat_rate',
        'status',
        'notes',
        'notes_ar',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'operating_hours' => 'array',
        'facilities' => 'array',
        'hourly_rate_base' => 'decimal:2',
        'vat_rate' => 'decimal:2',
        'status' => 'boolean',
        'vat_applicable' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'localized_name',
        'localized_description',
        'localized_address',
        'localized_notes',
        'formatted_hourly_rate',
        'status_text',
        'status_badge_class',
        'fields_count',
        'active_fields_count',
        'total_reservations',
        'today_reservations',
        'formatted_phone',
        'formatted_manager_phone',
        'coordinates_text',
    ];

    /**
     * Get the fields for this venue.
     */
    public function fields(): HasMany
    {
        return $this->hasMany(Field::class);
    }

    /**
     * Get the active fields for this venue.
     */
    public function activeFields(): HasMany
    {
        return $this->fields()->where('status', true);
    }

    /**
     * Get the reservations for this venue.
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get the reservation payments for this venue.
     */
    public function reservationPayments(): HasMany
    {
        return $this->hasMany(ReservationPayment::class);
    }

    // Computed Properties

    /**
     * Get localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        return app()->getLocale() === 'ar' && $this->name_ar ? $this->name_ar : $this->name;
    }

    /**
     * Get localized description based on current locale.
     */
    public function getLocalizedDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->description_ar ? $this->description_ar : $this->description;
    }

    /**
     * Get localized address based on current locale.
     */
    public function getLocalizedAddressAttribute(): string
    {
        return app()->getLocale() === 'ar' && $this->address_ar ? $this->address_ar : $this->address;
    }

    /**
     * Get localized notes based on current locale.
     */
    public function getLocalizedNotesAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->notes_ar ? $this->notes_ar : $this->notes;
    }

    /**
     * Get formatted hourly rate.
     */
    public function getFormattedHourlyRateAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->hourly_rate_base, 2);
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status ? 'Active' : 'Inactive';
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return $this->status ? 'badge-success' : 'badge-danger';
    }

    /**
     * Get fields count.
     */
    public function getFieldsCountAttribute(): int
    {
        return $this->fields()->count();
    }

    /**
     * Get active fields count.
     */
    public function getActiveFieldsCountAttribute(): int
    {
        return $this->activeFields()->count();
    }

    /**
     * Get total reservations count.
     */
    public function getTotalReservationsAttribute(): int
    {
        return $this->reservations()->count();
    }

    /**
     * Get today's reservations count.
     */
    public function getTodayReservationsAttribute(): int
    {
        return $this->reservations()->whereDate('reservation_date', today())->count();
    }

    /**
     * Get formatted phone number.
     */
    public function getFormattedPhoneAttribute(): ?string
    {
        return $this->phone ? $this->formatUAEPhone($this->phone) : null;
    }

    /**
     * Get formatted manager phone number.
     */
    public function getFormattedManagerPhoneAttribute(): ?string
    {
        return $this->manager_phone ? $this->formatUAEPhone($this->manager_phone) : null;
    }

    /**
     * Get coordinates as text.
     */
    public function getCoordinatesTextAttribute(): ?string
    {
        if ($this->latitude && $this->longitude) {
            return "{$this->latitude}, {$this->longitude}";
        }
        return null;
    }

    // Query Scopes

    /**
     * Scope to get only active venues.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', true);
    }

    /**
     * Scope to search venues.
     */
    public function scopeSearch(Builder $query, ?string $search): Builder
    {
        if (!$search) {
            return $query;
        }

        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('name_ar', 'like', "%{$search}%")
                ->orWhere('code', 'like', "%{$search}%")
                ->orWhere('city', 'like', "%{$search}%")
                ->orWhere('address', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to filter by city.
     */
    public function scopeByCity(Builder $query, string $city): Builder
    {
        return $query->where('city', $city);
    }

    // Utility Methods

    /**
     * Format UAE phone number.
     */
    private function formatUAEPhone(string $phone): string
    {
        // Format +971XXXXXXXXX to +971 XX XXX XXXX
        if (preg_match('/^\+971(\d{9})$/', $phone, $matches)) {
            $number = $matches[1];
            return '+971 ' . substr($number, 0, 2) . ' ' . substr($number, 2, 3) . ' ' . substr($number, 5);
        }
        return $phone;
    }

    /**
     * Generate unique venue code.
     */
    public static function generateCode(string $name): string
    {
        $baseCode = strtoupper(str_replace(' ', '_', $name));
        $counter = 1;
        $code = $baseCode . '_001';

        while (self::where('code', $code)->exists()) {
            $counter++;
            $code = $baseCode . '_' . str_pad($counter, 3, '0', STR_PAD_LEFT);
        }

        return $code;
    }

    /**
     * Get venue statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_fields' => $this->fields()->count(),
            'active_fields' => $this->activeFields()->count(),
            'total_reservations' => $this->reservations()->count(),
            'confirmed_reservations' => $this->reservations()->where('status', 'confirmed')->count(),
            'today_reservations' => $this->reservations()->whereDate('reservation_date', today())->count(),
            'this_month_reservations' => $this->reservations()->whereMonth('reservation_date', now()->month)->count(),
            'total_revenue' => $this->reservationPayments()->where('status', 'completed')->sum('total_amount'),
            'this_month_revenue' => $this->reservationPayments()
                ->where('status', 'completed')
                ->whereMonth('payment_date', now()->month)
                ->sum('total_amount'),
        ];
    }
}
