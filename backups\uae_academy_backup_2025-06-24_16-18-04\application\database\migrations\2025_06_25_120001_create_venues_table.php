<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('venues', function (Blueprint $table) {
            $table->id();
            $table->string('name')->charset('utf8mb4'); // e.g., "Leaders"
            $table->string('name_ar')->charset('utf8mb4')->nullable(); // Arabic name
            $table->string('code')->unique(); // e.g., "LEADERS_001"
            $table->text('description')->charset('utf8mb4')->nullable();
            $table->text('description_ar')->charset('utf8mb4')->nullable();
            $table->text('address')->charset('utf8mb4');
            $table->text('address_ar')->charset('utf8mb4')->nullable();
            $table->string('city')->charset('utf8mb4');
            $table->string('country')->default('UAE');
            $table->string('phone', 15)->nullable(); // UAE format: +971XXXXXXXXX
            $table->string('email')->nullable();
            $table->string('manager_name')->charset('utf8mb4')->nullable();
            $table->string('manager_phone', 15)->nullable();
            $table->string('manager_email')->nullable();
            $table->decimal('latitude', 10, 8)->nullable(); // GPS coordinates
            $table->decimal('longitude', 11, 8)->nullable();
            $table->json('operating_hours')->nullable(); // Daily operating hours
            $table->json('facilities')->nullable(); // Available facilities (parking, cafe, etc.)
            $table->decimal('hourly_rate_base', 8, 2)->default(0); // Base hourly rate in AED
            $table->string('currency', 3)->default('AED');
            $table->boolean('vat_applicable')->default(true);
            $table->decimal('vat_rate', 5, 2)->default(5.00); // VAT percentage
            $table->boolean('status')->default(true); // active/inactive
            $table->text('notes')->charset('utf8mb4')->nullable();
            $table->text('notes_ar')->charset('utf8mb4')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'city']);
            $table->index('name');
            $table->index('code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('venues');
    }
};
