<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;

class Setting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'type',
        'category',
        'label',
        'description',
        'validation_rules',
        'options',
        'is_public',
        'is_encrypted',
        'sort_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'validation_rules' => 'array',
        'options' => 'array',
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Cache key prefix for settings
     */
    const CACHE_PREFIX = 'setting_';

    /**
     * Cache duration in seconds (24 hours)
     */
    const CACHE_DURATION = 86400;

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        // Clear cache when settings are modified
        static::saved(function ($setting) {
            Cache::forget(self::CACHE_PREFIX . $setting->key);
            Cache::forget('settings_all');
            Cache::forget('settings_public');
        });

        static::deleted(function ($setting) {
            Cache::forget(self::CACHE_PREFIX . $setting->key);
            Cache::forget('settings_all');
            Cache::forget('settings_public');
        });
    }

    /**
     * Get a setting value by key
     *
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public static function get(string $key, $default = null)
    {
        $cacheKey = self::CACHE_PREFIX . $key;

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($key, $default) {
            $setting = self::where('key', $key)->first();

            if (!$setting) {
                return $default;
            }

            return $setting->getCastedValue();
        });
    }

    /**
     * Set a setting value
     *
     * @param string $key
     * @param mixed $value
     * @param string $type
     * @return bool
     */
    public static function set(string $key, $value, string $type = 'string'): bool
    {
        $setting = self::firstOrNew(['key' => $key]);

        $setting->value = $setting->is_encrypted ? Crypt::encryptString($value) : $value;
        $setting->type = $type;

        return $setting->save();
    }

    /**
     * Get all settings grouped by category
     *
     * @param bool $publicOnly
     * @return array
     */
    public static function getAllGrouped(bool $publicOnly = false): array
    {
        $cacheKey = $publicOnly ? 'settings_public' : 'settings_all';

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($publicOnly) {
            $query = self::orderBy('category')->orderBy('sort_order');

            if ($publicOnly) {
                $query->where('is_public', true);
            }

            return $query->get()->groupBy('category')->toArray();
        });
    }

    /**
     * Get settings by category
     *
     * @param string $category
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByCategory(string $category)
    {
        return self::where('category', $category)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Get the casted value based on type
     *
     * @return mixed
     */
    public function getCastedValue()
    {
        $value = $this->is_encrypted ? Crypt::decryptString($this->value) : $this->value;

        return match ($this->type) {
            'boolean' => (bool) $value,
            'integer' => (int) $value,
            'decimal', 'float' => (float) $value,
            'json' => json_decode($value, true),
            'array' => is_array($value) ? $value : json_decode($value, true),
            default => $value,
        };
    }

    /**
     * Get the raw value (for forms)
     *
     * @return mixed
     */
    public function getRawValue()
    {
        if ($this->is_encrypted) {
            return Crypt::decryptString($this->value);
        }

        if ($this->type === 'json' || $this->type === 'array') {
            return json_decode($this->value, true);
        }

        return $this->value;
    }

    /**
     * Set the value with proper casting
     *
     * @param mixed $value
     * @return void
     */
    public function setCastedValue($value): void
    {
        $processedValue = match ($this->type) {
            'boolean' => $value ? '1' : '0',
            'json', 'array' => is_string($value) ? $value : json_encode($value),
            default => (string) $value,
        };

        $this->value = $this->is_encrypted ? Crypt::encryptString($processedValue) : $processedValue;
    }

    /**
     * Get available categories
     *
     * @return array
     */
    public static function getCategories(): array
    {
        return [
            'general' => 'General Settings',
            'academy' => 'Academy Settings',
            'payment' => 'Payment Settings',
            'notification' => 'Notification Settings',
            'security' => 'Security Settings',
            'system' => 'System Settings',
            'translation' => 'Translation Management',
        ];
    }

    /**
     * Get available types
     *
     * @return array
     */
    public static function getTypes(): array
    {
        return [
            'string' => 'Text',
            'textarea' => 'Long Text',
            'integer' => 'Number',
            'decimal' => 'Decimal',
            'boolean' => 'Yes/No',
            'select' => 'Dropdown',
            'json' => 'JSON Data',
            'file' => 'File Upload',
        ];
    }

    /**
     * Validate setting value against rules
     *
     * @param mixed $value
     * @return bool
     */
    public function validateValue($value): bool
    {
        if (!$this->validation_rules) {
            return true;
        }

        $validator = \Validator::make(
            ['value' => $value],
            ['value' => $this->validation_rules]
        );

        return $validator->passes();
    }

    /**
     * Get formatted value for display
     *
     * @return string
     */
    public function getFormattedValueAttribute(): string
    {
        $value = $this->getCastedValue();

        return match ($this->type) {
            'boolean' => $value ? 'Yes' : 'No',
            'json', 'array' => is_array($value) ? implode(', ', $value) : json_encode($value),
            'file' => $value ? basename($value) : 'No file',
            default => (string) $value,
        };
    }

    /**
     * Scope for public settings
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for settings by category
     */
    public function scopeCategory($query, string $category)
    {
        return $query->where('category', $category);
    }
}
