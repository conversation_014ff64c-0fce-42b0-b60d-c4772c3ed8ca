<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Uniform;
use App\Models\Student;

class UniformSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if ($this->command) {
            $this->command->info('Creating uniforms...');
        }

        // Get all students
        $students = Student::with(['branch', 'academy'])->get();

        if ($students->isEmpty()) {
            throw new \Exception('No students found. Please run StudentSeeder first.');
        }

        $uniformTypes = ['jersey', 'shorts', 'tracksuit', 'cap', 'socks'];
        $sizes = ['XS', 'S', 'M', 'L', 'XL'];
        $colors = ['Red', 'Blue', 'Green', 'Black', 'White'];
        $statuses = ['ordered', 'processing', 'ready', 'delivered', 'cancelled'];
        $branchStatuses = ['pending', 'received', 'delivered'];
        $officeStatuses = ['pending', 'received', 'delivered'];

        foreach ($students as $student) {
            // Create 1-2 uniform items per student (60% get uniforms)
            if (rand(1, 10) <= 6) {
                $uniformCount = rand(1, 2);

                for ($i = 0; $i < $uniformCount; $i++) {
                    $uniformType = $uniformTypes[array_rand($uniformTypes)];
                    $size = $sizes[array_rand($sizes)];
                    $color = $colors[array_rand($colors)];
                    $price = rand(50, 120);
                    $quantity = rand(1, 2);
                    $orderDate = now()->subDays(rand(1, 90));
                    $deliveryDate = $orderDate->copy()->addDays(rand(3, 21));

                    $uniformData = [
                        'student_id' => $student->id,
                        'branch_id' => $student->branch_id,
                        'academy_id' => $student->academy_id,
                        'item' => $uniformType,
                        'size' => $size,
                        'color' => $color,
                        'quantity' => $quantity,
                        'amount' => $price,
                        'currency' => 'AED',
                        'order_date' => $orderDate->format('Y-m-d'),
                        'delivery_date' => $deliveryDate->format('Y-m-d'),
                        'status' => $statuses[array_rand($statuses)],
                        'branch_status' => $branchStatuses[array_rand($branchStatuses)],
                        'office_status' => $officeStatuses[array_rand($officeStatuses)],
                        'payment_method' => ['cash', 'card', 'bank_transfer'][rand(0, 2)],
                        'reference_number' => 'UNI' . str_pad($student->id * 100 + $i, 6, '0', STR_PAD_LEFT),
                        'description' => ucfirst($uniformType) . ' in ' . $color . ' - Size ' . $size,
                        'note' => 'Seeded uniform data for ' . $student->full_name,
                    ];

                    Uniform::create($uniformData);
                }
            }
        }

        if ($this->command) {
            $uniformCount = Uniform::count();
            $this->command->info("Uniforms created successfully. Total: {$uniformCount}");
        }
    }
}
