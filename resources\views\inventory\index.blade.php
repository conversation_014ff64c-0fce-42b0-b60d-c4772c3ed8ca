@extends('layouts.dashboard')

@section('title', __('Inventory Management'))

@section('header')
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-charcoal-black">{{ __('Inventory Management') }}</h1>
            <p class="mt-1 text-sm text-dark-gray">
                {{ __('Manage uniform inventory items, stock levels, and reorder points') }}</p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-leaders-red/10 text-leaders-red">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                {{ $stats['total_items'] ?? 0 }} {{ __('Total Items') }}
            </span>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('inventory.create') }}" class="btn-bank">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                </svg>
                {{ __('Add Inventory Item') }}
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="stats-card scale-in" style="animation-delay: 0.1s;">
                <div class="stats-icon bg-gradient-to-br from-blue-500 to-blue-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_items'] ?? 0 }}</div>
                <div class="stats-label">{{ __('Total Items') }}</div>
            </div>

            <div class="stats-card scale-in" style="animation-delay: 0.2s;">
                <div class="stats-icon bg-gradient-to-br from-green-500 to-green-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['active_items'] ?? 0 }}</div>
                <div class="stats-label">{{ __('Active Items') }}</div>
            </div>

            <div class="stats-card scale-in" style="animation-delay: 0.3s;">
                <div class="stats-icon bg-gradient-to-br from-yellow-500 to-yellow-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['low_stock_items'] ?? 0 }}</div>
                <div class="stats-label">{{ __('Low Stock Items') }}</div>
            </div>

            <div class="stats-card scale-in" style="animation-delay: 0.4s;">
                <div class="stats-icon bg-gradient-to-br from-purple-500 to-purple-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">AED {{ number_format($stats['total_stock_value'] ?? 0, 2) }}</div>
                <div class="stats-label">{{ __('Total Stock Value') }}</div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Search & Filter') }}</h3>
            </div>
            <div class="bank-card-body">
                <form method="GET" action="{{ route('inventory.index') }}"
                    class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <div>
                        <label for="search" class="form-label">{{ __('Search') }}</label>
                        <input type="text" class="form-input" id="search" name="search"
                            value="{{ request('search') }}" placeholder="{{ __('Search by name, SKU, or barcode') }}">
                    </div>

                    <div>
                        <label for="category_id" class="form-label">{{ __('Category') }}</label>
                        <select class="form-select" id="category_id" name="category_id">
                            <option value="">{{ __('All Categories') }}</option>
                            @foreach ($categories as $category)
                                <option value="{{ $category->id }}"
                                    {{ request('category_id') == $category->id ? 'selected' : '' }}>
                                    {{ $category->localized_name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label for="status" class="form-label">{{ __('Status') }}</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">{{ __('All Status') }}</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>
                                {{ __('Active') }}</option>
                            <option value="low_stock" {{ request('status') == 'low_stock' ? 'selected' : '' }}>
                                {{ __('Low Stock') }}</option>
                            <option value="out_of_stock" {{ request('status') == 'out_of_stock' ? 'selected' : '' }}>
                                {{ __('Out of Stock') }}</option>
                            <option value="needs_reorder" {{ request('status') == 'needs_reorder' ? 'selected' : '' }}>
                                {{ __('Needs Reorder') }}</option>
                        </select>
                    </div>

                    <div>
                        <label for="size" class="form-label">{{ __('Size') }}</label>
                        <select class="form-select" id="size" name="size">
                            <option value="">{{ __('All Sizes') }}</option>
                            <option value="XS" {{ request('size') == 'XS' ? 'selected' : '' }}>XS</option>
                            <option value="S" {{ request('size') == 'S' ? 'selected' : '' }}>S</option>
                            <option value="M" {{ request('size') == 'M' ? 'selected' : '' }}>M</option>
                            <option value="L" {{ request('size') == 'L' ? 'selected' : '' }}>L</option>
                            <option value="XL" {{ request('size') == 'XL' ? 'selected' : '' }}>XL</option>
                            <option value="XXL" {{ request('size') == 'XXL' ? 'selected' : '' }}>XXL</option>
                        </select>
                    </div>

                    <div>
                        <label class="form-label">&nbsp;</label>
                        <div class="flex space-x-2">
                            <button type="submit" class="btn-bank">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                {{ __('Search') }}
                            </button>
                            <a href="{{ route('inventory.index') }}" class="btn-bank-outline">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                {{ __('Clear') }}
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Inventory Table -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div class="flex items-center justify-between w-full">
                    <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Inventory Items') }}</h3>
                    <div class="flex items-center space-x-2">
                        <button class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white"
                            onclick="exportData('excel')">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                            {{ __('Export Excel') }}
                        </button>
                        <button class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white"
                            onclick="exportData('pdf')">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                                </path>
                            </svg>
                            {{ __('Export PDF') }}
                        </button>
                    </div>
                </div>
            </div>
            <div class="bank-card-body p-0">
                <div class="overflow-x-auto">
                    <table class="table-bank" id="inventoryTable" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>{{ __('SKU') }}</th>
                                <th>{{ __('Name') }}</th>
                                <th>{{ __('Category') }}</th>
                                <th>{{ __('Size') }}</th>
                                <th>{{ __('Color') }}</th>
                                <th>{{ __('Current Stock') }}</th>
                                <th>{{ __('Available') }}</th>
                                <th>{{ __('Status') }}</th>
                                <th>{{ __('Selling Price') }}</th>
                                <th>{{ __('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($inventory as $item)
                                <tr>
                                    <td>
                                        <strong>{{ $item->sku }}</strong>
                                        @if ($item->barcode)
                                            <br><small class="text-muted">{{ $item->barcode }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <div>{{ $item->localized_name }}</div>
                                        @if ($item->brand)
                                            <small class="text-muted">{{ $item->brand }}</small>
                                        @endif
                                    </td>
                                    <td>
                                        <span
                                            class="badge bg-secondary">{{ $item->category->localized_name ?? 'N/A' }}</span>
                                    </td>
                                    <td>{{ $item->size }}</td>
                                    <td>
                                        @if ($item->color)
                                            <span class="badge bg-light text-dark">{{ $item->localized_color }}</span>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $item->current_stock }}</strong>
                                        @if ($item->reserved_stock > 0)
                                            <br><small class="text-warning">({{ $item->reserved_stock }} reserved)</small>
                                        @endif
                                    </td>
                                    <td>{{ $item->available_stock }}</td>
                                    <td>
                                        <span class="badge {{ $item->stock_status_badge_class }}">
                                            {{ $item->stock_status_text }}
                                        </span>
                                    </td>
                                    <td>{{ $item->formatted_selling_price }}</td>
                                    <td>
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ route('inventory.show', $item) }}"
                                                class="btn-bank-outline btn-sm" title="{{ __('View') }}">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                    </path>
                                                </svg>
                                            </a>
                                            @if (Route::has('inventory.edit'))
                                                <a href="{{ route('inventory.edit', $item) }}"
                                                    class="btn-bank-outline btn-sm" title="{{ __('Edit') }}">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                        </path>
                                                    </svg>
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="10" class="text-center py-12">
                                        <div class="text-dark-gray">
                                            <svg class="w-16 h-16 mx-auto mb-4 text-medium-gray" fill="none"
                                                stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4">
                                                </path>
                                            </svg>
                                            <p class="text-lg font-medium mb-4">{{ __('No inventory items found') }}</p>
                                            <a href="{{ route('inventory.create') }}" class="btn-bank">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                </svg>
                                                {{ __('Add First Item') }}
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if ($inventory->hasPages())
                    <div class="px-6 py-4 border-t border-light-gray">
                        {{ $inventory->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            /* Force white text on table headers */
            .table-bank thead {
                background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
            }

            .table-bank thead th {
                color: white !important;
                font-weight: 600 !important;
                font-size: 0.75rem !important;
                text-transform: uppercase !important;
                letter-spacing: 0.1em !important;
                padding: 1rem !important;
                border-bottom: 2px solid #e53e3e !important;
                background: transparent !important;
            }

            .table-bank thead th * {
                color: white !important;
                background: transparent !important;
            }

            /* Ensure rounded corners */
            .table-bank thead th:first-child {
                border-top-left-radius: 0.5rem !important;
            }

            .table-bank thead th:last-child {
                border-top-right-radius: 0.5rem !important;
            }

            /* Enhanced table styling */
            .table-bank {
                border-radius: 0.5rem !important;
                overflow: hidden !important;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
            }

            .table-bank tbody tr:hover {
                background-color: #f9fafb !important;
                transform: translateY(-1px) !important;
                transition: all 0.2s ease-in-out !important;
            }

            /* Force white text on red buttons */
            button[class*="red"],
            button[class*="Red"],
            .btn[class*="red"],
            .btn[class*="Red"],
            .btn-bank,
            .btn-primary,
            .btn-danger,
            .btn-leaders {
                color: white !important;
            }

            .btn-bank *,
            .btn-primary *,
            .btn-danger *,
            .btn-leaders * {
                color: white !important;
            }

            .btn-bank svg,
            .btn-primary svg,
            .btn-danger svg,
            .btn-leaders svg {
                color: white !important;
                fill: white !important;
                stroke: white !important;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            function exportData(format) {
                const params = new URLSearchParams(window.location.search);
                const url = format === 'excel' ?
                    '{{ route('inventory.export.excel') }}' :
                    '{{ route('inventory.export.pdf') }}';

                window.open(url + '?' + params.toString(), '_blank');
            }

            // Ensure table headers and buttons have correct text colors
            document.addEventListener('DOMContentLoaded', function() {
                // Fix table headers
                const tableHeaders = document.querySelectorAll('.table-bank thead th');
                tableHeaders.forEach(header => {
                    header.style.setProperty('color', 'white', 'important');
                    header.style.setProperty('background', 'transparent', 'important');

                    const childElements = header.querySelectorAll('*');
                    childElements.forEach(child => {
                        child.style.setProperty('color', 'white', 'important');
                        child.style.setProperty('background', 'transparent', 'important');
                    });
                });

                // Fix button text colors
                const buttons = document.querySelectorAll('button, .btn');
                buttons.forEach(button => {
                    const hasRedClass = button.className.includes('red') ||
                        button.className.includes('Red') ||
                        button.className.includes('primary') ||
                        button.className.includes('danger') ||
                        button.className.includes('leaders') ||
                        button.className.includes('btn-bank');

                    if (hasRedClass) {
                        button.style.setProperty('color', 'white', 'important');

                        const childElements = button.querySelectorAll('*');
                        childElements.forEach(child => {
                            child.style.setProperty('color', 'white', 'important');
                        });

                        const svgElements = button.querySelectorAll('svg');
                        svgElements.forEach(svg => {
                            svg.style.setProperty('color', 'white', 'important');
                            svg.style.setProperty('fill', 'white', 'important');
                            svg.style.setProperty('stroke', 'white', 'important');
                        });
                    }
                });
            });
        </script>
    @endpush
@endsection
