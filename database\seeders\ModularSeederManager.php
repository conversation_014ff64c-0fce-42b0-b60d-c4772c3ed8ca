<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ModularSeederManager extends Seeder
{
    /**
     * Seeder dependency map
     */
    private array $dependencies = [
        UserSeeder::class => [],
        BranchSeeder::class => [UserSeeder::class],
        AcademySeeder::class => [BranchSeeder::class],
        ProgramSeeder::class => [AcademySeeder::class],
        StudentSeeder::class => [ProgramSeeder::class],
        PaymentSeeder::class => [StudentSeeder::class],
        UniformSeeder::class => [StudentSeeder::class],
    ];

    /**
     * Executed seeders tracking
     */
    private array $executed = [];

    /**
     * Run the database seeds with dependency resolution
     */
    public function run(): void
    {
        $this->command->info('🌱 Starting Modular Database Seeding...');
        $this->command->newLine();

        // Validate dependencies
        $this->validateDependencies();

        // Execute seeders in dependency order
        foreach ($this->dependencies as $seederClass => $deps) {
            $this->executeSeeder($seederClass);
        }

        $this->command->newLine();
        $this->command->info('✅ Modular seeding completed successfully!');
        
        // Run data integrity validation
        $this->validateDataIntegrity();
    }

    /**
     * Execute a seeder with dependency checking
     */
    private function executeSeeder(string $seederClass): void
    {
        // Skip if already executed
        if (in_array($seederClass, $this->executed)) {
            return;
        }

        // Execute dependencies first
        foreach ($this->dependencies[$seederClass] as $dependency) {
            $this->executeSeeder($dependency);
        }

        // Execute the seeder
        $this->command->info("🔄 Running {$seederClass}...");
        
        try {
            $seeder = new $seederClass();
            $seeder->setCommand($this->command);
            $seeder->run();
            
            $this->executed[] = $seederClass;
            $this->command->info("✅ {$seederClass} completed");
            
        } catch (\Exception $e) {
            $this->command->error("❌ {$seederClass} failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Validate dependency configuration
     */
    private function validateDependencies(): void
    {
        foreach ($this->dependencies as $seederClass => $deps) {
            // Check if seeder class exists
            if (!class_exists($seederClass)) {
                throw new \Exception("Seeder class {$seederClass} does not exist");
            }

            // Check if dependencies exist
            foreach ($deps as $dependency) {
                if (!class_exists($dependency)) {
                    throw new \Exception("Dependency {$dependency} for {$seederClass} does not exist");
                }
                
                if (!array_key_exists($dependency, $this->dependencies)) {
                    throw new \Exception("Dependency {$dependency} is not registered in the dependency map");
                }
            }
        }

        // Check for circular dependencies
        $this->checkCircularDependencies();
    }

    /**
     * Check for circular dependencies
     */
    private function checkCircularDependencies(): void
    {
        $visited = [];
        $recursionStack = [];

        foreach (array_keys($this->dependencies) as $seederClass) {
            if ($this->hasCircularDependency($seederClass, $visited, $recursionStack)) {
                throw new \Exception("Circular dependency detected involving {$seederClass}");
            }
        }
    }

    /**
     * Recursive function to detect circular dependencies
     */
    private function hasCircularDependency(string $seederClass, array &$visited, array &$recursionStack): bool
    {
        $visited[$seederClass] = true;
        $recursionStack[$seederClass] = true;

        foreach ($this->dependencies[$seederClass] as $dependency) {
            if (!isset($visited[$dependency])) {
                if ($this->hasCircularDependency($dependency, $visited, $recursionStack)) {
                    return true;
                }
            } elseif (isset($recursionStack[$dependency])) {
                return true;
            }
        }

        unset($recursionStack[$seederClass]);
        return false;
    }

    /**
     * Validate data integrity after seeding
     */
    private function validateDataIntegrity(): void
    {
        $this->command->info('🔍 Validating data integrity...');
        
        try {
            // Run our data integrity validator
            $validator = new DataIntegrityValidator();
            $validator->setCommand($this->command);
            $validator->run();
            
        } catch (\Exception $e) {
            $this->command->warn('Data integrity validation failed: ' . $e->getMessage());
        }
    }

    /**
     * Get seeding statistics
     */
    public function getStatistics(): array
    {
        return [
            'total_seeders' => count($this->dependencies),
            'executed_seeders' => count($this->executed),
            'success_rate' => count($this->executed) / count($this->dependencies) * 100,
            'executed_list' => $this->executed,
        ];
    }

    /**
     * Reset execution state (useful for testing)
     */
    public function reset(): void
    {
        $this->executed = [];
    }

    /**
     * Add a seeder to the dependency map
     */
    public function addSeeder(string $seederClass, array $dependencies = []): void
    {
        $this->dependencies[$seederClass] = $dependencies;
    }

    /**
     * Remove a seeder from the dependency map
     */
    public function removeSeeder(string $seederClass): void
    {
        unset($this->dependencies[$seederClass]);
        
        // Remove from other seeders' dependencies
        foreach ($this->dependencies as $class => $deps) {
            $this->dependencies[$class] = array_filter($deps, fn($dep) => $dep !== $seederClass);
        }
    }

    /**
     * Get dependency order for all seeders
     */
    public function getDependencyOrder(): array
    {
        $order = [];
        $temp = $this->executed;
        $this->executed = [];
        
        foreach ($this->dependencies as $seederClass => $deps) {
            $this->addToOrder($seederClass, $order);
        }
        
        $this->executed = $temp;
        return $order;
    }

    /**
     * Helper method to build dependency order
     */
    private function addToOrder(string $seederClass, array &$order): void
    {
        if (in_array($seederClass, $order)) {
            return;
        }

        foreach ($this->dependencies[$seederClass] as $dependency) {
            $this->addToOrder($dependency, $order);
        }

        $order[] = $seederClass;
    }
}
