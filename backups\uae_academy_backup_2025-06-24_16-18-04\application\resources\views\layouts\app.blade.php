<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Sports Academy')</title>

    <!-- Favicon (Using Logo) -->
    <link rel="icon" type="image/jpeg" href="{{ asset('images/logo.jpg') }}">
    <link rel="apple-touch-icon" href="{{ asset('images/logo.jpg') }}">

    <!-- IBM Plex Sans Fonts (As per UI Guide) -->
    <link rel="preload"
        href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap"
        as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload"
        href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap"
        as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Fallback for non-JS users -->
    <noscript>
        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap">
        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap">
    </noscript>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Pro -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">

    <!-- Animate.css for smooth animations -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Additional Styles -->
    @stack('styles')

    <!-- UAE English Sports Academy Design System (Per UI Guide) -->
    <style>
        :root {
            /* Brand Colors (From Logo) */
            --leaders-red: #E53E3E;
            --deep-red: #C53030;
            --gold-yellow: #D69E2E;

            /* Neutral Foundation */
            --pure-white: #FFFFFF;
            --off-white: #FAFAFA;
            --light-gray: #F7FAFC;
            --medium-gray: #E2E8F0;
            --dark-gray: #4A5568;
            --charcoal-black: #1A202C;

            /* Status Colors */
            --success-green: #38A169;
            --warning-orange: #DD6B20;
            --error-red: #E53E3E;
            --info-blue: #3182CE;

            /* Typography (IBM Plex Sans) */
            --font-family-primary: 'IBM Plex Sans', system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            --font-family-arabic: 'IBM Plex Sans Arabic', 'IBM Plex Sans', system-ui, sans-serif;

            /* Shadows (Per UI Guide) */
            --shadow-card: 0 2px 4px rgba(0, 0, 0, 0.1);
            --shadow-hover: 0 4px 8px rgba(0, 0, 0, 0.15);

            /* Border Radius (Per UI Guide) */
            --radius-card: 8px;
            --radius-button: 6px;

            /* Spacing System (8px Grid) */
            --space-xs: 0.25rem;
            /* 4px */
            --space-sm: 0.5rem;
            /* 8px */
            --space-md: 1rem;
            /* 16px */
            --space-lg: 1.5rem;
            /* 24px */
            --space-xl: 2rem;
            /* 32px */
            --space-xxl: 3rem;
            /* 48px */
        }

        /* Global Styles (Per UI Guide) */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family-primary);
            background-color: var(--off-white);
            min-height: 100vh;
            font-weight: 400;
            line-height: 1.6;
            color: var(--charcoal-black);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Arabic Text Support */
        [lang="ar"],
        .arabic-text {
            font-family: var(--font-family-arabic);
            direction: rtl;
        }

        /* Navigation Logo Styles */
        .navbar-logo {
            max-height: 50px;
            width: auto;
            object-fit: contain;
            transition: all 0.3s ease;
        }

        .navbar-logo:hover {
            transform: scale(1.05);
        }

        .navbar-brand-text {
            color: var(--charcoal-black);
            font-family: var(--font-family-primary);
            font-weight: 600;
            text-decoration: none;
        }

        .navbar-brand-text:hover {
            color: var(--leaders-red);
            text-decoration: none;
        }

        /* Card Components (Per UI Guide) */
        .card,
        .premium-card {
            background: var(--pure-white);
            border: 1px solid var(--medium-gray);
            border-radius: var(--radius-card);
            box-shadow: var(--shadow-card);
            padding: var(--space-lg);
            transition: all 0.2s ease-in-out;
        }

        .card:hover,
        .premium-card:hover {
            box-shadow: var(--shadow-hover);
            transform: translateY(-1px);
        }

        /* Button Hierarchy (Per UI Guide) */
        .btn-primary,
        .btn-premium {
            background-color: var(--leaders-red);
            border-color: var(--leaders-red);
            border-radius: var(--radius-button);
            height: 42px;
            font-family: var(--font-family-primary);
            font-weight: 500;
            transition: all 0.2s ease-in-out;
        }

        .btn-primary:hover,
        .btn-premium:hover {
            background-color: var(--deep-red);
            border-color: var(--deep-red);
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: transparent;
            border: 1px solid var(--leaders-red);
            color: var(--leaders-red);
            border-radius: var(--radius-button);
            height: 42px;
            font-family: var(--font-family-primary);
            font-weight: 500;
        }

        .btn-secondary:hover {
            background-color: var(--leaders-red);
            color: var(--pure-white);
        }

        /* Form Elements (Per UI Guide) */
        .form-control,
        .form-control-premium {
            height: 42px;
            border: 1px solid var(--light-gray);
            border-radius: var(--radius-button);
            padding: 0 var(--space-md);
            font-family: var(--font-family-primary);
            font-weight: 400;
            transition: all 0.2s ease-in-out;
        }

        .form-control:focus,
        .form-control-premium:focus {
            border-color: var(--leaders-red);
            box-shadow: 0 0 0 0.2rem rgba(229, 62, 62, 0.25);
            outline: none;
        }

        .form-control.is-invalid {
            border-color: var(--error-red);
        }

        .form-control.is-valid {
            border-color: var(--success-green);
        }

        /* Premium Typography */
        .text-premium {
            font-family: var(--font-secondary);
            font-weight: 600;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-uae {
            color: var(--uae-red);
            font-weight: 700;
        }

        /* Animations */
        .fade-in-up {
            animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .scale-in {
            animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }

            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* GLOBAL BUTTON RED BACKGROUND OVERRIDE - HIGHEST PRIORITY */
        .btn-bank-outline,
        button.btn-bank-outline,
        a.btn-bank-outline,
        .btn-bank.btn-bank-outline,
        .btn-bank-sm,
        button.btn-bank-sm,
        a.btn-bank-sm,
        .btn-bank.btn-bank-sm,
        .btn-bank-secondary,
        button.btn-bank-secondary,
        a.btn-bank-secondary,
        .btn-bank.btn-bank-secondary {
            background: linear-gradient(135deg, #E53E3E 0%, #B91C1C 100%) !important;
            color: white !important;
            border: 1px solid #B91C1C !important;
            text-decoration: none !important;
        }

        .btn-bank-outline:hover,
        button.btn-bank-outline:hover,
        a.btn-bank-outline:hover,
        .btn-bank.btn-bank-outline:hover,
        .btn-bank-sm:hover,
        button.btn-bank-sm:hover,
        a.btn-bank-sm:hover,
        .btn-bank.btn-bank-sm:hover,
        .btn-bank-secondary:hover,
        button.btn-bank-secondary:hover,
        a.btn-bank-secondary:hover,
        .btn-bank.btn-bank-secondary:hover {
            background: linear-gradient(135deg, #B91C1C 0%, #991b1b 100%) !important;
            border-color: #991b1b !important;
            color: white !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        }

        /* All button children get white text */
        .btn-bank-outline *,
        button.btn-bank-outline *,
        a.btn-bank-outline *,
        .btn-bank.btn-bank-outline *,
        .btn-bank-sm *,
        button.btn-bank-sm *,
        a.btn-bank-sm *,
        .btn-bank.btn-bank-sm *,
        .btn-bank-secondary *,
        button.btn-bank-secondary *,
        a.btn-bank-secondary *,
        .btn-bank.btn-bank-secondary * {
            color: white !important;
            background: transparent !important;
        }

        /* All button SVGs get white styling */
        .btn-bank-outline svg,
        button.btn-bank-outline svg,
        a.btn-bank-outline svg,
        .btn-bank.btn-bank-outline svg,
        .btn-bank-sm svg,
        button.btn-bank-sm svg,
        a.btn-bank-sm svg,
        .btn-bank.btn-bank-sm svg,
        .btn-bank-secondary svg,
        button.btn-bank-secondary svg,
        a.btn-bank-secondary svg,
        .btn-bank.btn-bank-secondary svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }

        /* Nuclear option - catch any button with bank classes */
        button[class*="btn-bank"],
        a[class*="btn-bank"],
        [class*="btn-bank"] {
            background: linear-gradient(135deg, #E53E3E 0%, #B91C1C 100%) !important;
            color: white !important;
            border: 1px solid #B91C1C !important;
        }

        button[class*="btn-bank"]:hover,
        a[class*="btn-bank"]:hover,
        [class*="btn-bank"]:hover {
            background: linear-gradient(135deg, #B91C1C 0%, #991b1b 100%) !important;
            border-color: #991b1b !important;
            color: white !important;
        }

        button[class*="btn-bank"] *,
        a[class*="btn-bank"] *,
        [class*="btn-bank"] * {
            color: white !important;
            background: transparent !important;
        }

        button[class*="btn-bank"] svg,
        a[class*="btn-bank"] svg,
        [class*="btn-bank"] svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }
    </style>
</head>

<body class="font-sans antialiased">
    <div class="min-h-screen bg-gray-100">
        @include('layouts.navigation')

        <!-- Page Heading -->
        @hasSection('header')
            <header class="bg-white shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    @yield('header')
                </div>
            </header>
        @endif

        <!-- Page Content -->
        <main>
            @yield('content')
        </main>
    </div>

    <!-- Bootstrap JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Additional Scripts -->
    @stack('scripts')
</body>

</html>
