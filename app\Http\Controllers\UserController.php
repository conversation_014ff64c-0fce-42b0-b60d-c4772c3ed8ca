<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Branch;
use App\Models\Academy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): View|JsonResponse
    {
        $this->authorize('viewAny', User::class);

        $query = User::with(['branch', 'academy']);

        // Apply search filter
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Apply role filter
        if ($request->filled('role')) {
            $query->role($request->role);
        }

        // Apply status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        // Apply branch filter
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Apply academy filter
        if ($request->filled('academy_id')) {
            $query->where('academy_id', $request->academy_id);
        }

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        $users = $query->paginate(15)->withQueryString();

        // Get statistics
        $stats = User::getStatistics();

        // Get filter options
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        if ($request->ajax()) {
            return response()->json([
                'users' => $users,
                'stats' => $stats,
            ]);
        }

        return view('users.index', compact('users', 'stats', 'branches', 'academies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $this->authorize('create', User::class);

        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        return view('users.create', compact('branches', 'academies'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $this->authorize('create', User::class);

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'in:admin,branch_manager,academy_manager'],
            'branch_id' => ['nullable', 'exists:branches,id'],
            'academy_id' => ['nullable', 'exists:academies,id'],
            'status' => ['boolean'],
        ]);

        // Validate role-specific requirements
        if ($validated['role'] === 'branch_manager' && !$validated['branch_id']) {
            return back()->withErrors(['branch_id' => 'Branch is required for Branch Manager role.']);
        }

        if ($validated['role'] === 'academy_manager' && (!$validated['branch_id'] || !$validated['academy_id'])) {
            return back()->withErrors([
                'branch_id' => 'Branch is required for Academy Manager role.',
                'academy_id' => 'Academy is required for Academy Manager role.',
            ]);
        }

        // Clear branch/academy for admin role
        if ($validated['role'] === 'admin') {
            $validated['branch_id'] = null;
            $validated['academy_id'] = null;
        }

        $validated['password'] = Hash::make($validated['password']);
        $validated['status'] = $request->boolean('status', true);

        $user = User::create($validated);

        return redirect()->route('users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user): View|JsonResponse
    {
        $this->authorize('view', $user);

        $user->load(['branch', 'academy']);

        if (request()->ajax()) {
            return response()->json(['user' => $user]);
        }

        return view('users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user): View
    {
        $this->authorize('update', $user);

        $user->load(['branch', 'academy']);
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        return view('users.edit', compact('user', 'branches', 'academies'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $this->authorize('update', $user);

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'lowercase', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'password' => ['nullable', 'confirmed', Rules\Password::defaults()],
            'role' => ['required', 'in:admin,branch_manager,academy_manager'],
            'branch_id' => ['nullable', 'exists:branches,id'],
            'academy_id' => ['nullable', 'exists:academies,id'],
            'status' => ['boolean'],
        ]);

        // Validate role-specific requirements
        if ($validated['role'] === 'branch_manager' && !$validated['branch_id']) {
            return back()->withErrors(['branch_id' => 'Branch is required for Branch Manager role.']);
        }

        if ($validated['role'] === 'academy_manager' && (!$validated['branch_id'] || !$validated['academy_id'])) {
            return back()->withErrors([
                'branch_id' => 'Branch is required for Academy Manager role.',
                'academy_id' => 'Academy is required for Academy Manager role.',
            ]);
        }

        // Clear branch/academy for admin role
        if ($validated['role'] === 'admin') {
            $validated['branch_id'] = null;
            $validated['academy_id'] = null;
        }

        // Only update password if provided
        if (empty($validated['password'])) {
            unset($validated['password']);
        } else {
            $validated['password'] = Hash::make($validated['password']);
        }

        $validated['status'] = $request->boolean('status', true);

        $user->update($validated);

        return redirect()->route('users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user): RedirectResponse
    {
        $this->authorize('delete', $user);

        $user->delete();

        return redirect()->route('users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Toggle user status.
     */
    public function toggleStatus(User $user): JsonResponse
    {
        $this->authorize('toggleStatus', $user);

        $user->update(['status' => !$user->status]);

        return response()->json([
            'success' => true,
            'message' => 'User status updated successfully.',
            'status' => $user->status,
        ]);
    }

    /**
     * Get academies by branch for AJAX requests.
     */
    public function getAcademiesByBranch(Request $request): JsonResponse
    {
        $branchId = $request->get('branch_id');

        if (!$branchId) {
            return response()->json(['academies' => []]);
        }

        $academies = Academy::where('branch_id', $branchId)
            ->where('status', true)
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json(['academies' => $academies]);
    }

    /**
     * Handle bulk actions on users.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $this->authorize('bulkAction', User::class);

        $validated = $request->validate([
            'action' => ['required', 'in:activate,deactivate,delete'],
            'user_ids' => ['required', 'array'],
            'user_ids.*' => ['exists:users,id'],
        ]);

        $userIds = $validated['user_ids'];
        $action = $validated['action'];

        // Prevent actions on current user
        $currentUserId = Auth::id();
        if (in_array($currentUserId, $userIds)) {
            return response()->json([
                'success' => false,
                'message' => 'You cannot perform bulk actions on your own account.',
            ]);
        }

        try {
            DB::beginTransaction();

            $count = 0;
            foreach ($userIds as $userId) {
                $user = User::find($userId);
                if (!$user) continue;

                switch ($action) {
                    case 'activate':
                        try {
                            $this->authorize('toggleStatus', $user);
                            $user->update(['status' => true]);
                            $count++;
                        } catch (\Exception) {
                            // Skip unauthorized actions
                        }
                        break;
                    case 'deactivate':
                        try {
                            $this->authorize('toggleStatus', $user);
                            $user->update(['status' => false]);
                            $count++;
                        } catch (\Exception) {
                            // Skip unauthorized actions
                        }
                        break;
                    case 'delete':
                        try {
                            $this->authorize('delete', $user);
                            $user->delete();
                            $count++;
                        } catch (\Exception) {
                            // Skip unauthorized actions
                        }
                        break;
                }
            }

            DB::commit();

            $actionText = match ($action) {
                'activate' => 'activated',
                'deactivate' => 'deactivated',
                'delete' => 'deleted',
            };

            return response()->json([
                'success' => true,
                'message' => "{$count} user(s) {$actionText} successfully.",
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while processing the bulk action: ' . $exception->getMessage(),
            ]);
        }
    }

    /**
     * Export users to Excel.
     */
    public function exportExcel(Request $request)
    {
        $this->authorize('export', User::class);

        // This will be implemented with Laravel Excel package
        // For now, return a simple CSV export
        $query = User::with(['branch', 'academy']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $query->search($request->search);
        }
        if ($request->filled('role')) {
            $query->role($request->role);
        }
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        $users = $query->get();

        $filename = 'users_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function () use ($users) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID',
                'Name',
                'Email',
                'Role',
                'Branch',
                'Academy',
                'Status',
                'Created At',
                'Last Login'
            ]);

            // CSV data
            foreach ($users as $user) {
                fputcsv($file, [
                    $user->id,
                    $user->name,
                    $user->email,
                    $user->role_text,
                    $user->branch?->name ?? 'N/A',
                    $user->academy?->name ?? 'N/A',
                    $user->status_text,
                    $user->formatted_created_at,
                    $user->last_login_text,
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export users to PDF.
     */
    public function exportPdf(Request $request)
    {
        $this->authorize('export', User::class);

        $query = User::with(['branch', 'academy']);

        // Apply same filters as index
        if ($request->filled('search')) {
            $query->search($request->search);
        }
        if ($request->filled('role')) {
            $query->role($request->role);
        }
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->inactive();
            }
        }

        $users = $query->get();
        $stats = User::getStatistics();

        return view('users.export-pdf', compact('users', 'stats'));
    }

    /**
     * Get user statistics for API.
     */
    public function getStatistics(): JsonResponse
    {
        $this->authorize('viewAny', User::class);

        $stats = User::getStatistics();

        return response()->json(['stats' => $stats]);
    }
}
