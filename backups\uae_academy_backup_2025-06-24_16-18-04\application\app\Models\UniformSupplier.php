<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class UniformSupplier extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'code',
        'contact_person',
        'email',
        'phone',
        'phone_secondary',
        'address',
        'address_ar',
        'city',
        'country',
        'tax_number',
        'trade_license',
        'payment_terms',
        'credit_limit',
        'currency',
        'lead_time_days',
        'minimum_order_amount',
        'status',
        'rating',
        'notes',
        'notes_ar',
    ];

    protected $casts = [
        'credit_limit' => 'decimal:2',
        'minimum_order_amount' => 'decimal:2',
        'lead_time_days' => 'integer',
        'rating' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'localized_name',
        'localized_address',
        'localized_notes',
        'formatted_credit_limit',
        'formatted_minimum_order',
        'status_text',
        'status_badge_class',
        'payment_terms_text',
        'formatted_phone',
        'total_purchase_orders',
        'total_purchase_amount',
        'average_delivery_time',
        'performance_score',
    ];

    /**
     * Get the purchase orders for this supplier.
     */
    public function purchaseOrders(): HasMany
    {
        return $this->hasMany(UniformPurchaseOrder::class);
    }

    /**
     * Get the inventory items supplied by this supplier.
     */
    public function inventoryItems(): HasMany
    {
        return $this->hasMany(UniformInventory::class);
    }

    // Computed Properties

    /**
     * Get localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->name_ar) ? $this->name_ar : $this->name;
    }

    /**
     * Get localized address based on current locale.
     */
    public function getLocalizedAddressAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->address_ar) ? $this->address_ar : $this->address;
    }

    /**
     * Get localized notes based on current locale.
     */
    public function getLocalizedNotesAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->notes_ar) ? $this->notes_ar : $this->notes;
    }

    /**
     * Get formatted credit limit in AED.
     */
    public function getFormattedCreditLimitAttribute(): string
    {
        return 'AED ' . number_format($this->credit_limit, 2);
    }

    /**
     * Get formatted minimum order amount in AED.
     */
    public function getFormattedMinimumOrderAttribute(): string
    {
        return 'AED ' . number_format($this->minimum_order_amount, 2);
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'suspended' => 'Suspended',
            default => 'Unknown'
        };
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'active' => 'bg-green-100 text-green-800',
            'inactive' => 'bg-gray-100 text-gray-800',
            'suspended' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get payment terms text.
     */
    public function getPaymentTermsTextAttribute(): string
    {
        return match ($this->payment_terms) {
            'cash' => 'Cash on Delivery',
            'net_15' => 'Net 15 Days',
            'net_30' => 'Net 30 Days',
            'net_45' => 'Net 45 Days',
            'net_60' => 'Net 60 Days',
            default => 'Net 30 Days'
        };
    }

    /**
     * Get formatted phone number.
     */
    public function getFormattedPhoneAttribute(): ?string
    {
        if (empty($this->phone)) {
            return null;
        }
        
        // Format UAE phone numbers
        if (str_starts_with($this->phone, '+971')) {
            return $this->phone;
        } elseif (str_starts_with($this->phone, '971')) {
            return '+' . $this->phone;
        } elseif (str_starts_with($this->phone, '0')) {
            return '+971' . substr($this->phone, 1);
        } else {
            return '+971' . $this->phone;
        }
    }

    /**
     * Get total number of purchase orders.
     */
    public function getTotalPurchaseOrdersAttribute(): int
    {
        return $this->purchaseOrders()->count();
    }

    /**
     * Get total purchase amount.
     */
    public function getTotalPurchaseAmountAttribute(): float
    {
        return $this->purchaseOrders()->sum('total_amount');
    }

    /**
     * Get average delivery time in days.
     */
    public function getAverageDeliveryTimeAttribute(): ?float
    {
        $orders = $this->purchaseOrders()
            ->whereNotNull('actual_delivery_date')
            ->whereNotNull('order_date')
            ->get();

        if ($orders->isEmpty()) {
            return null;
        }

        $totalDays = $orders->sum(function ($order) {
            return $order->order_date->diffInDays($order->actual_delivery_date);
        });

        return round($totalDays / $orders->count(), 1);
    }

    /**
     * Get performance score based on delivery time and rating.
     */
    public function getPerformanceScoreAttribute(): float
    {
        $ratingScore = $this->rating * 20; // Convert 5-point scale to 100-point
        
        $deliveryScore = 100;
        if ($this->average_delivery_time !== null) {
            $expectedDays = $this->lead_time_days;
            $actualDays = $this->average_delivery_time;
            
            if ($actualDays <= $expectedDays) {
                $deliveryScore = 100;
            } else {
                $deliveryScore = max(0, 100 - (($actualDays - $expectedDays) * 5));
            }
        }
        
        return round(($ratingScore + $deliveryScore) / 2, 1);
    }

    // Query Scopes

    /**
     * Scope to get only active suppliers.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to search suppliers.
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('name_ar', 'like', "%{$search}%")
              ->orWhere('code', 'like', "%{$search}%")
              ->orWhere('contact_person', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%");
        });
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by rating.
     */
    public function scopeByRating(Builder $query, float $minRating): Builder
    {
        return $query->where('rating', '>=', $minRating);
    }

    // Utility Methods

    /**
     * Generate next supplier code.
     */
    public static function generateCode(): string
    {
        $lastSupplier = self::orderBy('id', 'desc')->first();
        $nextNumber = $lastSupplier ? (int) substr($lastSupplier->code, 3) + 1 : 1;
        
        return 'SUP' . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Get supplier statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_orders' => $this->purchaseOrders()->count(),
            'completed_orders' => $this->purchaseOrders()->where('status', 'received')->count(),
            'pending_orders' => $this->purchaseOrders()->whereIn('status', ['approved', 'ordered', 'partial'])->count(),
            'total_amount' => $this->purchaseOrders()->sum('total_amount'),
            'average_order_value' => $this->purchaseOrders()->avg('total_amount') ?? 0,
            'on_time_delivery_rate' => $this->calculateOnTimeDeliveryRate(),
            'inventory_items' => $this->inventoryItems()->count(),
        ];
    }

    /**
     * Calculate on-time delivery rate.
     */
    private function calculateOnTimeDeliveryRate(): float
    {
        $completedOrders = $this->purchaseOrders()
            ->whereNotNull('actual_delivery_date')
            ->whereNotNull('expected_delivery_date')
            ->get();

        if ($completedOrders->isEmpty()) {
            return 0;
        }

        $onTimeOrders = $completedOrders->filter(function ($order) {
            return $order->actual_delivery_date <= $order->expected_delivery_date;
        });

        return round(($onTimeOrders->count() / $completedOrders->count()) * 100, 1);
    }
}
