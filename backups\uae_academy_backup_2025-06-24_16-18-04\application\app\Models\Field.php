<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class Field extends Model
{
    protected $fillable = [
        'venue_id',
        'name',
        'name_ar',
        'code',
        'type',
        'description',
        'description_ar',
        'surface_type',
        'dimensions',
        'capacity',
        'hourly_rate',
        'peak_hour_rate',
        'peak_hours',
        'weekend_rate',
        'equipment_included',
        'amenities',
        'lighting_available',
        'air_conditioning',
        'covered',
        'available_from',
        'available_to',
        'unavailable_days',
        'minimum_booking_hours',
        'maximum_booking_hours',
        'advance_booking_days',
        'requires_deposit',
        'deposit_percentage',
        'currency',
        'status',
        'maintenance_notes',
        'last_maintenance_date',
        'next_maintenance_date',
        'notes',
        'notes_ar',
    ];

    protected $casts = [
        'dimensions' => 'array',
        'peak_hours' => 'array',
        'equipment_included' => 'array',
        'amenities' => 'array',
        'unavailable_days' => 'array',
        'hourly_rate' => 'decimal:2',
        'peak_hour_rate' => 'decimal:2',
        'weekend_rate' => 'decimal:2',
        'deposit_percentage' => 'decimal:2',
        'capacity' => 'integer',
        'minimum_booking_hours' => 'integer',
        'maximum_booking_hours' => 'integer',
        'advance_booking_days' => 'integer',
        'lighting_available' => 'boolean',
        'air_conditioning' => 'boolean',
        'covered' => 'boolean',
        'requires_deposit' => 'boolean',
        'status' => 'boolean',
        'available_from' => 'datetime:H:i',
        'available_to' => 'datetime:H:i',
        'last_maintenance_date' => 'date',
        'next_maintenance_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'localized_name',
        'localized_description',
        'localized_notes',
        'formatted_hourly_rate',
        'formatted_peak_hour_rate',
        'formatted_weekend_rate',
        'status_text',
        'status_badge_class',
        'type_text',
        'surface_type_text',
        'availability_text',
        'features_list',
        'reservations_count',
        'today_reservations',
        'maintenance_status',
        'is_available_now',
    ];

    /**
     * Get the venue that owns this field.
     */
    public function venue(): BelongsTo
    {
        return $this->belongsTo(Venue::class);
    }

    /**
     * Get the reservations for this field.
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    // Computed Properties

    /**
     * Get localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        return app()->getLocale() === 'ar' && $this->name_ar ? $this->name_ar : $this->name;
    }

    /**
     * Get localized description based on current locale.
     */
    public function getLocalizedDescriptionAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->description_ar ? $this->description_ar : $this->description;
    }

    /**
     * Get localized notes based on current locale.
     */
    public function getLocalizedNotesAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->notes_ar ? $this->notes_ar : $this->notes;
    }

    /**
     * Get formatted hourly rate.
     */
    public function getFormattedHourlyRateAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->hourly_rate, 2);
    }

    /**
     * Get formatted peak hour rate.
     */
    public function getFormattedPeakHourRateAttribute(): ?string
    {
        return $this->peak_hour_rate ? $this->currency . ' ' . number_format($this->peak_hour_rate, 2) : null;
    }

    /**
     * Get formatted weekend rate.
     */
    public function getFormattedWeekendRateAttribute(): ?string
    {
        return $this->weekend_rate ? $this->currency . ' ' . number_format($this->weekend_rate, 2) : null;
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status ? 'Active' : 'Inactive';
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return $this->status ? 'badge-success' : 'badge-danger';
    }

    /**
     * Get field type text.
     */
    public function getTypeTextAttribute(): string
    {
        return match ($this->type) {
            'football' => 'Football',
            'basketball' => 'Basketball',
            'tennis' => 'Tennis',
            'volleyball' => 'Volleyball',
            'multipurpose' => 'Multi-purpose',
            default => ucfirst($this->type),
        };
    }

    /**
     * Get surface type text.
     */
    public function getSurfaceTypeTextAttribute(): string
    {
        return $this->surface_type ? ucfirst($this->surface_type) : 'Not specified';
    }

    /**
     * Get availability text.
     */
    public function getAvailabilityTextAttribute(): string
    {
        return $this->available_from->format('H:i') . ' - ' . $this->available_to->format('H:i');
    }

    /**
     * Get features list.
     */
    public function getFeaturesListAttribute(): array
    {
        $features = [];
        
        if ($this->lighting_available) $features[] = 'Lighting';
        if ($this->air_conditioning) $features[] = 'Air Conditioning';
        if ($this->covered) $features[] = 'Covered';
        
        if ($this->amenities) {
            $features = array_merge($features, $this->amenities);
        }
        
        return $features;
    }

    /**
     * Get reservations count.
     */
    public function getReservationsCountAttribute(): int
    {
        return $this->reservations()->count();
    }

    /**
     * Get today's reservations count.
     */
    public function getTodayReservationsAttribute(): int
    {
        return $this->reservations()->whereDate('reservation_date', today())->count();
    }

    /**
     * Get maintenance status.
     */
    public function getMaintenanceStatusAttribute(): string
    {
        if (!$this->next_maintenance_date) {
            return 'No maintenance scheduled';
        }

        $daysUntilMaintenance = now()->diffInDays($this->next_maintenance_date, false);
        
        if ($daysUntilMaintenance < 0) {
            return 'Maintenance overdue';
        } elseif ($daysUntilMaintenance <= 7) {
            return 'Maintenance due soon';
        } else {
            return 'Maintenance scheduled';
        }
    }

    /**
     * Check if field is available now.
     */
    public function getIsAvailableNowAttribute(): bool
    {
        if (!$this->status) {
            return false;
        }

        $now = now();
        $currentTime = $now->format('H:i:s');
        
        // Check if current time is within availability hours
        if ($currentTime < $this->available_from->format('H:i:s') || 
            $currentTime > $this->available_to->format('H:i:s')) {
            return false;
        }

        // Check if today is an unavailable day
        if ($this->unavailable_days && in_array(strtolower($now->format('l')), $this->unavailable_days)) {
            return false;
        }

        // Check if there's a current reservation
        $currentReservation = $this->reservations()
            ->where('reservation_date', $now->toDateString())
            ->where('start_time', '<=', $currentTime)
            ->where('end_time', '>', $currentTime)
            ->where('status', 'confirmed')
            ->exists();

        return !$currentReservation;
    }

    // Query Scopes

    /**
     * Scope to get only active fields.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', true);
    }

    /**
     * Scope to filter by venue.
     */
    public function scopeByVenue(Builder $query, int $venueId): Builder
    {
        return $query->where('venue_id', $venueId);
    }

    /**
     * Scope to filter by field type.
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to search fields.
     */
    public function scopeSearch(Builder $query, ?string $search): Builder
    {
        if (!$search) {
            return $query;
        }

        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('name_ar', 'like', "%{$search}%")
                ->orWhere('code', 'like', "%{$search}%")
                ->orWhere('type', 'like', "%{$search}%")
                ->orWhere('surface_type', 'like', "%{$search}%");
        });
    }

    // Utility Methods

    /**
     * Generate unique field code.
     */
    public static function generateCode(int $venueId, string $name): string
    {
        $venue = Venue::find($venueId);
        $venueCode = $venue ? $venue->code : 'VENUE';
        $fieldCode = strtoupper(str_replace(' ', '_', $name));
        
        $counter = 1;
        $code = $venueCode . '_' . $fieldCode;

        while (self::where('code', $code)->exists()) {
            $counter++;
            $code = $venueCode . '_' . $fieldCode . '_' . $counter;
        }

        return $code;
    }

    /**
     * Check if field is available for a specific time slot.
     */
    public function isAvailableForTimeSlot(string $date, string $startTime, string $endTime, ?int $excludeReservationId = null): bool
    {
        if (!$this->status) {
            return false;
        }

        // Check if the time slot is within field availability hours
        if ($startTime < $this->available_from->format('H:i:s') || 
            $endTime > $this->available_to->format('H:i:s')) {
            return false;
        }

        // Check for conflicting reservations
        $query = $this->reservations()
            ->where('reservation_date', $date)
            ->where('status', '!=', 'cancelled')
            ->where(function ($q) use ($startTime, $endTime) {
                $q->where(function ($subQ) use ($startTime, $endTime) {
                    // New booking starts during existing booking
                    $subQ->where('start_time', '<=', $startTime)
                         ->where('end_time', '>', $startTime);
                })->orWhere(function ($subQ) use ($startTime, $endTime) {
                    // New booking ends during existing booking
                    $subQ->where('start_time', '<', $endTime)
                         ->where('end_time', '>=', $endTime);
                })->orWhere(function ($subQ) use ($startTime, $endTime) {
                    // New booking completely contains existing booking
                    $subQ->where('start_time', '>=', $startTime)
                         ->where('end_time', '<=', $endTime);
                });
            });

        if ($excludeReservationId) {
            $query->where('id', '!=', $excludeReservationId);
        }

        return !$query->exists();
    }

    /**
     * Get field statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_reservations' => $this->reservations()->count(),
            'confirmed_reservations' => $this->reservations()->where('status', 'confirmed')->count(),
            'completed_reservations' => $this->reservations()->where('status', 'completed')->count(),
            'cancelled_reservations' => $this->reservations()->where('status', 'cancelled')->count(),
            'today_reservations' => $this->reservations()->whereDate('reservation_date', today())->count(),
            'this_week_reservations' => $this->reservations()
                ->whereBetween('reservation_date', [now()->startOfWeek(), now()->endOfWeek()])
                ->count(),
            'this_month_reservations' => $this->reservations()
                ->whereMonth('reservation_date', now()->month)
                ->count(),
            'utilization_rate' => $this->calculateUtilizationRate(),
        ];
    }

    /**
     * Calculate field utilization rate for current month.
     */
    private function calculateUtilizationRate(): float
    {
        $totalHoursInMonth = now()->daysInMonth * 
            ($this->available_to->diffInHours($this->available_from));
        
        $bookedHours = $this->reservations()
            ->whereMonth('reservation_date', now()->month)
            ->where('status', '!=', 'cancelled')
            ->sum('duration_hours');

        return $totalHoursInMonth > 0 ? ($bookedHours / $totalHoursInMonth) * 100 : 0;
    }
}
