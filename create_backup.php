<?php

/**
 * UAE English Sports Academy - Complete System Backup Script
 * Creates a comprehensive backup from A to Z of the entire system
 * 
 * Date: June 25, 2025
 * Version: 1.0.0
 */

echo "🏆 UAE English Sports Academy - Complete System Backup\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Configuration
$projectPath = '/Users/<USER>/Sites/uae_english_sports_academy';
$backupDir = $projectPath . '/backups';
$timestamp = date('Y-m-d_H-i-s');
$backupName = "uae_academy_backup_{$timestamp}";
$fullBackupPath = $backupDir . '/' . $backupName;

// Create backup directory
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
    echo "✅ Created backup directory: {$backupDir}\n";
}

if (!is_dir($fullBackupPath)) {
    mkdir($fullBackupPath, 0755, true);
    echo "✅ Created backup folder: {$backupName}\n";
}

echo "\n📁 Starting comprehensive backup process...\n\n";

// 1. Database Backup
echo "1️⃣ Creating Database Backup...\n";
$dbBackupPath = $fullBackupPath . '/database';
mkdir($dbBackupPath, 0755, true);

// Export database structure and data
$dbName = 'uae_english_sports_academy_db';
$dbUser = 'root';
$dbPassword = '';
$dbHost = '127.0.0.1';

$sqlFile = $dbBackupPath . '/database_backup.sql';
$mysqldumpCmd = "mysqldump -h {$dbHost} -u {$dbUser} --single-transaction --routines --triggers {$dbName} > {$sqlFile}";

exec($mysqldumpCmd, $output, $returnCode);
if ($returnCode === 0) {
    echo "   ✅ Database exported to: database_backup.sql\n";
} else {
    echo "   ❌ Database export failed\n";
}

// 2. Application Files Backup
echo "\n2️⃣ Backing up Application Files...\n";
$appBackupPath = $fullBackupPath . '/application';
mkdir($appBackupPath, 0755, true);

// Core application directories to backup
$appDirectories = [
    'app' => 'Application logic, models, controllers, middleware',
    'config' => 'Configuration files',
    'database' => 'Migrations, seeders, factories',
    'resources' => 'Views, assets, translations',
    'routes' => 'Route definitions',
    'public' => 'Public assets, images, compiled assets',
    'storage/app' => 'Application storage',
    'bootstrap' => 'Bootstrap files'
];

foreach ($appDirectories as $dir => $description) {
    $sourceDir = $projectPath . '/' . $dir;
    $targetDir = $appBackupPath . '/' . $dir;
    
    if (is_dir($sourceDir)) {
        $copyCmd = "cp -R '{$sourceDir}' '{$targetDir}'";
        exec($copyCmd);
        echo "   ✅ Copied {$dir}/ - {$description}\n";
    }
}

// 3. Configuration Files Backup
echo "\n3️⃣ Backing up Configuration Files...\n";
$configBackupPath = $fullBackupPath . '/configuration';
mkdir($configBackupPath, 0755, true);

$configFiles = [
    '.env' => 'Environment configuration',
    '.env.example' => 'Environment template',
    'composer.json' => 'PHP dependencies',
    'composer.lock' => 'PHP dependency lock file',
    'package.json' => 'Node.js dependencies',
    'package-lock.json' => 'Node.js dependency lock file',
    'vite.config.js' => 'Vite build configuration',
    'tailwind.config.js' => 'Tailwind CSS configuration',
    '.gitignore' => 'Git ignore rules',
    'artisan' => 'Laravel Artisan CLI'
];

foreach ($configFiles as $file => $description) {
    $sourceFile = $projectPath . '/' . $file;
    $targetFile = $configBackupPath . '/' . $file;
    
    if (file_exists($sourceFile)) {
        copy($sourceFile, $targetFile);
        echo "   ✅ Copied {$file} - {$description}\n";
    }
}

// 4. Documentation Backup
echo "\n4️⃣ Backing up Documentation...\n";
$docsBackupPath = $fullBackupPath . '/documentation';
mkdir($docsBackupPath, 0755, true);

// Find all markdown files
$markdownFiles = glob($projectPath . '/*.md');
$docsDir = $projectPath . '/docs';

foreach ($markdownFiles as $mdFile) {
    $fileName = basename($mdFile);
    copy($mdFile, $docsBackupPath . '/' . $fileName);
    echo "   ✅ Copied {$fileName}\n";
}

if (is_dir($docsDir)) {
    $copyCmd = "cp -R '{$docsDir}' '{$docsBackupPath}/docs'";
    exec($copyCmd);
    echo "   ✅ Copied docs/ directory\n";
}

// 5. Create backup manifest
echo "\n5️⃣ Creating Backup Manifest...\n";
$manifest = [
    'backup_info' => [
        'name' => $backupName,
        'created_at' => date('Y-m-d H:i:s'),
        'version' => '1.0.0',
        'system' => 'UAE English Sports Academy',
        'backup_type' => 'Complete System Backup'
    ],
    'database' => [
        'name' => $dbName,
        'backup_file' => 'database/database_backup.sql',
        'tables_included' => 'All tables with data'
    ],
    'application' => [
        'directories' => array_keys($appDirectories),
        'total_files' => 'All application files'
    ],
    'configuration' => [
        'files' => array_keys($configFiles),
        'environment' => 'Development configuration included'
    ]
];

file_put_contents($fullBackupPath . '/BACKUP_MANIFEST.json', json_encode($manifest, JSON_PRETTY_PRINT));
echo "   ✅ Created backup manifest\n";

echo "\n🎉 Backup completed successfully!\n";
echo "📍 Backup location: {$fullBackupPath}\n";
echo "📊 Backup size: " . formatBytes(getDirSize($fullBackupPath)) . "\n";

// Helper functions
function getDirSize($dir) {
    $size = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }
    return $size;
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}

echo "\n📋 Next Steps:\n";
echo "1. Verify backup integrity\n";
echo "2. Test restoration process\n";
echo "3. Store backup in secure location\n";
echo "4. Document backup for team reference\n";

echo "\n✨ Backup process completed at " . date('Y-m-d H:i:s') . "\n";
