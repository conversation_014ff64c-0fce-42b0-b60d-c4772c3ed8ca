<!-- Desktop Table View -->
<div class="hidden lg:block overflow-x-auto">
    <table class="table-bank">
        <thead>
            <tr>
                @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                    <th class="w-12">
                        <input type="checkbox" @change="selectAllUniforms()"
                            :checked="selectedUniforms.length === document.querySelectorAll('input[name=\'uniform_ids[]\']')
                                .length && document.querySelectorAll('input[name=\'uniform_ids[]\']').length > 0"
                            class="form-checkbox-bank">
                    </th>
                @endif
                <th>ID</th>
                <th>Student</th>
                <th>Branch</th>
                <th>Academy</th>
                <th>Item</th>
                <th>Size</th>
                <th>Quantity</th>
                <th>Amount (AED)</th>
                <th>Status</th>
                <th>Order Date</th>
                <th>Delivery Date</th>
                <th class="actions-column">Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($uniforms as $uniform)
                <tr class="table-row-bank">
                    @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                        <td>
                            <input type="checkbox" name="uniform_ids[]" value="{{ $uniform->id }}"
                                @change="toggleUniformSelection({{ $uniform->id }})"
                                :checked="selectedUniforms.includes({{ $uniform->id }})" class="form-checkbox-bank">
                        </td>
                    @endif
                    <td>
                        <div class="font-mono text-sm text-dark-gray">
                            #{{ str_pad($uniform->id, 4, '0', STR_PAD_LEFT) }}
                        </div>
                        @if ($uniform->reference_number)
                            <div class="text-xs text-medium-gray">{{ $uniform->reference_number }}</div>
                        @endif
                    </td>
                    <td>
                        <div class="flex items-center space-x-3">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">
                                    {{ strtoupper(substr($uniform->student->full_name, 0, 2)) }}
                                </span>
                            </div>
                            <div>
                                <div class="font-semibold">
                                    <a href="{{ route('students.show', $uniform->student) }}"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                        style="color: #dc2626 !important;">
                                        {{ $uniform->student->full_name }}
                                    </a>
                                </div>
                                <div class="text-sm text-gray-600">{{ $uniform->student->email }}</div>
                                <div class="text-xs text-gray-500">{{ $uniform->student->formatted_phone }}</div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="font-medium">
                            <a href="{{ route('branches.show', $uniform->branch) }}"
                                class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                style="color: #dc2626 !important;">
                                {{ $uniform->branch->name }}
                            </a>
                        </div>
                    </td>
                    <td>
                        <div class="font-medium">
                            <a href="{{ route('academies.show', $uniform->academy) }}"
                                class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                style="color: #dc2626 !important;">
                                {{ $uniform->academy->name }}
                            </a>
                        </div>
                    </td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <span class="badge-bank badge-info">{{ ucfirst($uniform->item) }}</span>
                        </div>
                    </td>
                    <td>
                        <span class="badge-bank badge-neutral">{{ $uniform->size }}</span>
                    </td>
                    <td>
                        <div class="text-center">
                            <span class="font-semibold text-charcoal-black">{{ $uniform->quantity }}</span>
                        </div>
                    </td>
                    <td>
                        <div class="text-right">
                            <div class="font-semibold text-success-green">
                                {{ $uniform->formatted_total_amount }}
                            </div>
                            @if ($uniform->quantity > 1)
                                <div class="text-sm text-dark-gray">
                                    {{ $uniform->formatted_amount }} each
                                </div>
                            @endif
                        </div>
                    </td>
                    <td>
                        <button @click="toggleStatus({{ $uniform->id }})"
                            class="badge-bank {{ $uniform->status_badge_class }} cursor-pointer hover:opacity-80 transition-opacity">
                            {{ $uniform->status_text }}
                        </button>
                        <div class="text-xs text-medium-gray mt-1">
                            Branch: {{ $uniform->branch_status_text }}
                        </div>
                        <div class="text-xs text-medium-gray">
                            Office: {{ $uniform->office_status_text }}
                        </div>
                    </td>
                    <td>
                        <div class="text-sm text-charcoal-black">{{ $uniform->formatted_order_date }}</div>
                        <div class="text-xs text-medium-gray">
                            {{ $uniform->order_date->diffForHumans() }}
                        </div>
                    </td>
                    <td>
                        @if ($uniform->delivery_date)
                            <div class="text-sm text-charcoal-black">{{ $uniform->formatted_delivery_date }}</div>
                            <div class="text-xs text-medium-gray">
                                {{ $uniform->delivery_date->diffForHumans() }}
                            </div>
                        @else
                            <span class="text-sm text-medium-gray">Not set</span>
                        @endif
                    </td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <!-- View Button -->
                            <a href="{{ route('uniforms.show', $uniform) }}"
                                class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white"
                                title="View Details">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                            </a>

                            <!-- Edit Button -->
                            @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                                <button @click="$dispatch('open-modal', 'edit-uniform-{{ $uniform->id }}')"
                                    class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white"
                                    title="Edit Order">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </button>
                            @endif

                            <!-- Delete Button (Admin only) -->
                            @if (auth()->user()->role === 'admin')
                                <button @click="deleteUniform({{ $uniform->id }})"
                                    class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white"
                                    title="Delete Order">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endif
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="13" class="text-center py-12">
                        <div class="flex flex-col items-center justify-center space-y-4">
                            <svg class="w-16 h-16 text-medium-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                            <div class="text-lg font-medium text-dark-gray">No uniform orders found</div>
                            <div class="text-sm text-medium-gray">
                                @if (request()->hasAny([
                                        'search',
                                        'branch_id',
                                        'academy_id',
                                        'status',
                                        'size',
                                        'item',
                                        'payment_method',
                                        'date_from',
                                        'date_to',
                                    ]))
                                    Try adjusting your search criteria or
                                    <a href="{{ route('uniforms.index') }}"
                                        class="text-leaders-red hover:underline">clear all filters</a>
                                @else
                                    Get started by creating your first uniform order
                                @endif
                            </div>
                            @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                                <button @click="$dispatch('open-modal', 'create-uniform')" class="btn-bank mt-4">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                                        </path>
                                    </svg>
                                    Create First Order
                                </button>
                            @endif
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<!-- Mobile Card View -->
<div class="lg:hidden space-y-4 p-4">
    @forelse($uniforms as $uniform)
        <div class="bg-white border border-medium-gray rounded-xl shadow-sm overflow-hidden">
            <!-- Card Header -->
            <div class="p-4 border-b border-light-gray">
                <div class="flex items-start justify-between">
                    <div class="flex items-center space-x-3">
                        @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                            <input type="checkbox" name="uniform_ids[]" value="{{ $uniform->id }}"
                                @change="toggleUniformSelection({{ $uniform->id }})"
                                :checked="selectedUniforms.includes({{ $uniform->id }})" class="form-checkbox-bank">
                        @endif
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                            <span class="text-white font-semibold">
                                {{ strtoupper(substr($uniform->student->full_name, 0, 2)) }}
                            </span>
                        </div>
                        <div>
                            <div class="font-semibold">
                                <a href="{{ route('students.show', $uniform->student) }}"
                                    class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                    style="color: #dc2626 !important;">
                                    {{ $uniform->student->full_name }}
                                </a>
                            </div>
                            <div class="text-sm text-gray-600">Order
                                #{{ str_pad($uniform->id, 4, '0', STR_PAD_LEFT) }}</div>
                            @if ($uniform->reference_number)
                                <div class="text-xs text-gray-500">{{ $uniform->reference_number }}</div>
                            @endif
                        </div>
                    </div>
                    <button @click="toggleStatus({{ $uniform->id }})"
                        class="badge-bank {{ $uniform->status_badge_class }} cursor-pointer">
                        {{ $uniform->status_text }}
                    </button>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-4 space-y-3">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <div class="text-xs text-medium-gray uppercase tracking-wide">Item & Size</div>
                        <div class="font-medium text-charcoal-black">{{ ucfirst($uniform->item) }} -
                            {{ $uniform->size }}</div>
                        <div class="text-sm text-dark-gray">Qty: {{ $uniform->quantity }}</div>
                    </div>
                    <div class="text-right">
                        <div class="text-xs text-medium-gray uppercase tracking-wide">Amount</div>
                        <div class="font-semibold text-success-green">{{ $uniform->formatted_total_amount }}</div>
                        @if ($uniform->quantity > 1)
                            <div class="text-sm text-dark-gray">{{ $uniform->formatted_amount }} each</div>
                        @endif
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <div class="text-xs text-gray-500 uppercase tracking-wide">Branch</div>
                        <div class="text-sm">
                            <a href="{{ route('branches.show', $uniform->branch) }}"
                                class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                style="color: #dc2626 !important;">
                                {{ $uniform->branch->name }}
                            </a>
                        </div>
                    </div>
                    <div>
                        <div class="text-xs text-gray-500 uppercase tracking-wide">Academy</div>
                        <div class="text-sm">
                            <a href="{{ route('academies.show', $uniform->academy) }}"
                                class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                style="color: #dc2626 !important;">
                                {{ $uniform->academy->name }}
                            </a>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <div class="text-xs text-medium-gray uppercase tracking-wide">Order Date</div>
                        <div class="text-sm text-charcoal-black">{{ $uniform->formatted_order_date }}</div>
                    </div>
                    <div>
                        <div class="text-xs text-medium-gray uppercase tracking-wide">Delivery Date</div>
                        <div class="text-sm text-charcoal-black">
                            {{ $uniform->delivery_date ? $uniform->formatted_delivery_date : 'Not set' }}
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <div class="text-xs text-medium-gray uppercase tracking-wide">Branch Status</div>
                        <span
                            class="badge-bank badge-sm {{ $uniform->branch_status === 'delivered' ? 'badge-success' : ($uniform->branch_status === 'received' ? 'badge-warning' : 'badge-neutral') }}">
                            {{ $uniform->branch_status_text }}
                        </span>
                    </div>
                    <div>
                        <div class="text-xs text-medium-gray uppercase tracking-wide">Office Status</div>
                        <span
                            class="badge-bank badge-sm {{ $uniform->office_status === 'delivered' ? 'badge-success' : ($uniform->office_status === 'received' ? 'badge-warning' : 'badge-neutral') }}">
                            {{ $uniform->office_status_text }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Card Actions -->
            <div class="px-4 py-3 bg-light-gray border-t border-medium-gray">
                <div class="flex items-center justify-between">
                    <div class="text-xs text-medium-gray">
                        Created {{ $uniform->created_at->diffForHumans() }}
                    </div>
                    <div class="flex items-center space-x-2">
                        <!-- View Button -->
                        <a href="{{ route('uniforms.show', $uniform) }}"
                            class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
                            View
                        </a>

                        <!-- Edit Button -->
                        @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                            <button @click="$dispatch('open-modal', 'edit-uniform-{{ $uniform->id }}')"
                                class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
                                Edit
                            </button>
                        @endif

                        <!-- Delete Button (Admin only) -->
                        @if (auth()->user()->role === 'admin')
                            <button @click="deleteUniform({{ $uniform->id }})"
                                class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
                                Delete
                            </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="text-center py-12">
            <div class="flex flex-col items-center justify-center space-y-4">
                <svg class="w-16 h-16 text-medium-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <div class="text-lg font-medium text-dark-gray">No uniform orders found</div>
                <div class="text-sm text-medium-gray text-center">
                    @if (request()->hasAny([
                            'search',
                            'branch_id',
                            'academy_id',
                            'status',
                            'size',
                            'item',
                            'payment_method',
                            'date_from',
                            'date_to',
                        ]))
                        Try adjusting your search criteria or
                        <a href="{{ route('uniforms.index') }}" class="text-leaders-red hover:underline">clear all
                            filters</a>
                    @else
                        Get started by creating your first uniform order
                    @endif
                </div>
                @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                    <button @click="$dispatch('open-modal', 'create-uniform')" class="btn-bank mt-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                            </path>
                        </svg>
                        Create First Order
                    </button>
                @endif
            </div>
        </div>
    @endforelse
</div>
