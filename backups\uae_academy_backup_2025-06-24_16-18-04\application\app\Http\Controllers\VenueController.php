<?php

namespace App\Http\Controllers;

use App\Models\Venue;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class VenueController extends Controller
{
    /**
     * Display a listing of venues.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Venue::class);

        $query = Venue::with(['fields'])
            ->withCount(['fields', 'reservations', 'reservationPayments']);

        // Search filter
        if ($request->filled('search')) {
            $query->search($request->get('search'));
        }

        // City filter
        if ($request->filled('city')) {
            $query->byCity($request->get('city'));
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->get('status') === 'active') {
                $query->active();
            } else {
                $query->where('status', false);
            }
        }

        // Sort
        $sortBy = $request->get('sort_by', 'name');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $venues = $query->paginate(15)->withQueryString();

        // Get filter options
        $cities = Venue::distinct()->pluck('city')->filter()->sort();

        // Calculate statistics
        $stats = [
            'total_venues' => Venue::count(),
            'active_venues' => Venue::active()->count(),
            'total_fields' => \App\Models\Field::count(),
            'total_reservations' => \App\Models\Reservation::count(),
            'total_revenue' => \App\Models\ReservationPayment::where('status', 'completed')->sum('total_amount'),
        ];

        if ($request->ajax()) {
            return response()->json([
                'venues' => $venues,
                'stats' => $stats
            ]);
        }

        return view('venues.index', compact('venues', 'cities', 'stats'));
    }

    /**
     * Show the form for creating a new venue.
     */
    public function create(): View
    {
        Gate::authorize('create', Venue::class);

        return view('venues.create');
    }

    /**
     * Store a newly created venue.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Venue::class);

        $validator = validator($request->all(), [
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'address' => 'required|string',
            'address_ar' => 'nullable|string',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'phone' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:15',
            'manager_email' => 'nullable|email|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'operating_hours' => 'nullable|array',
            'facilities' => 'nullable|array',
            'hourly_rate_base' => 'required|numeric|min:0',
            'vat_applicable' => 'boolean',
            'vat_rate' => 'required|numeric|min:0|max:100',
            'status' => 'boolean',
            'notes' => 'nullable|string',
            'notes_ar' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();

            // Generate unique code
            $data['code'] = Venue::generateCode($data['name']);
            $data['currency'] = 'AED';

            $venue = Venue::create($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Venue created successfully',
                    'venue' => $venue->load(['fields'])
                ]);
            }

            return redirect()->route('venues.index')
                ->with('success', 'Venue created successfully');

        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error creating venue: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Error creating venue: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified venue.
     */
    public function show(Venue $venue): View|JsonResponse
    {
        Gate::authorize('view', $venue);

        $venue->load([
            'fields.reservations',
            'reservations.customer',
            'reservations.field',
            'reservationPayments'
        ]);

        // Get recent reservations
        $recentReservations = $venue->reservations()
            ->with(['customer', 'field'])
            ->orderBy('reservation_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->limit(10)
            ->get();

        // Get statistics
        $stats = $venue->getStatistics();

        if (request()->ajax()) {
            return response()->json([
                'venue' => $venue,
                'recent_reservations' => $recentReservations,
                'stats' => $stats
            ]);
        }

        return view('venues.show', compact('venue', 'recentReservations', 'stats'));
    }

    /**
     * Show the form for editing the specified venue.
     */
    public function edit(Venue $venue): View
    {
        Gate::authorize('update', $venue);

        return view('venues.edit', compact('venue'));
    }

    /**
     * Update the specified venue.
     */
    public function update(Request $request, Venue $venue): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $venue);

        $validator = validator($request->all(), [
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'address' => 'required|string',
            'address_ar' => 'nullable|string',
            'city' => 'required|string|max:255',
            'country' => 'required|string|max:255',
            'phone' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'manager_name' => 'nullable|string|max:255',
            'manager_phone' => 'nullable|string|max:15',
            'manager_email' => 'nullable|email|max:255',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'operating_hours' => 'nullable|array',
            'facilities' => 'nullable|array',
            'hourly_rate_base' => 'required|numeric|min:0',
            'vat_applicable' => 'boolean',
            'vat_rate' => 'required|numeric|min:0|max:100',
            'status' => 'boolean',
            'notes' => 'nullable|string',
            'notes_ar' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();

            // Update code if name changed
            if ($data['name'] !== $venue->name) {
                $data['code'] = Venue::generateCode($data['name']);
            }

            $venue->update($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Venue updated successfully',
                    'venue' => $venue->load(['fields'])
                ]);
            }

            return redirect()->route('venues.index')
                ->with('success', 'Venue updated successfully');

        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating venue: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Error updating venue: ' . $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified venue.
     */
    public function destroy(Venue $venue): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $venue);

        try {
            // Check if venue has active reservations
            $activeReservations = $venue->reservations()
                ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
                ->count();

            if ($activeReservations > 0) {
                $message = 'Cannot delete venue with active reservations';
                
                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 422);
                }
                
                return back()->with('error', $message);
            }

            $venue->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Venue deleted successfully'
                ]);
            }

            return redirect()->route('venues.index')
                ->with('success', 'Venue deleted successfully');

        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error deleting venue: ' . $e->getMessage()
                ], 500);
            }

            return back()->with('error', 'Error deleting venue: ' . $e->getMessage());
        }
    }

    /**
     * Toggle venue status.
     */
    public function toggleStatus(Venue $venue): JsonResponse
    {
        Gate::authorize('update', $venue);

        try {
            $venue->status = !$venue->status;
            $venue->save();

            return response()->json([
                'success' => true,
                'message' => 'Venue status updated successfully',
                'status' => $venue->status
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating venue status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get venue statistics for API.
     */
    public function getStatistics(Venue $venue): JsonResponse
    {
        Gate::authorize('view', $venue);

        return response()->json($venue->getStatistics());
    }

    /**
     * Export venues to Excel.
     */
    public function exportExcel(Request $request): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        Gate::authorize('export', Venue::class);

        // Implementation for Excel export would go here
        // For now, return a simple response
        return response()->download('path/to/venues.xlsx');
    }

    /**
     * Export venues to PDF.
     */
    public function exportPdf(Request $request): \Symfony\Component\HttpFoundation\Response
    {
        Gate::authorize('export', Venue::class);

        // Implementation for PDF export would go here
        // For now, return a simple response
        return response()->make('PDF content', 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="venues.pdf"'
        ]);
    }
}
