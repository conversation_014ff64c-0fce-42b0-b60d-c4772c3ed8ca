<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('academy_id')->constrained()->onDelete('cascade');
            $table->decimal('amount', 10, 2); // AED with 2 decimal places
            $table->decimal('discount', 10, 2)->default(0); // AED discount
            $table->string('currency', 3)->default('AED');
            $table->enum('payment_method', ['card', 'cash', 'bank_transfer'])->default('cash');
            $table->date('payment_date');
            $table->date('start_date');
            $table->date('end_date');
            $table->enum('status', ['active', 'expired', 'pending', 'cancelled'])->default('active');
            $table->string('reset_num')->nullable(); // auto-generated
            $table->time('class_time_from')->nullable();
            $table->time('class_time_to')->nullable();
            $table->boolean('renewal')->default(false);
            $table->text('note')->charset('utf8mb4')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['student_id', 'status']);
            $table->index(['branch_id', 'academy_id']);
            $table->index('payment_date');
            $table->index(['start_date', 'end_date']);
            $table->index('amount');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
