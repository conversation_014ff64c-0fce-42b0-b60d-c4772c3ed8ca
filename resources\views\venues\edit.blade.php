@extends('layouts.dashboard')

@section('title', 'Edit Venue - LEADERS SPORTS SERVICES')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Edit Venue</h1>
                <p class="text-lg text-dark-gray">Update {{ $venue->localized_name }} information</p>
                <span class="badge-bank {{ $venue->status_badge_class }}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="{{ $venue->status ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                        </path>
                    </svg>
                    {{ $venue->status_text }} Venue
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('venues.show', $venue) }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                    </path>
                </svg>
                View Details
            </a>
            <a href="{{ route('venues.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Venues
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto" x-data="venueEditForm()" x-init="init()">
        <form action="{{ route('venues.update', $venue) }}" method="POST" @submit="handleSubmit" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Venue Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="stats-card-sm">
                    <div class="stats-icon-sm bg-gradient-to-br from-leaders-red to-leaders-deep-red">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value-sm">{{ $venue->fields_count ?? 0 }}</div>
                    <div class="stats-label-sm">Fields</div>
                </div>
                <div class="stats-card-sm">
                    <div class="stats-icon-sm bg-gradient-to-br from-success-green to-green-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value-sm">{{ $venue->total_reservations ?? 0 }}</div>
                    <div class="stats-label-sm">Reservations</div>
                </div>
                <div class="stats-card-sm">
                    <div class="stats-icon-sm bg-gradient-to-br from-info-blue to-blue-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value-sm">{{ $venue->formatted_hourly_rate }}</div>
                    <div class="stats-label-sm">Base Rate</div>
                </div>
                <div class="stats-card-sm">
                    <div class="stats-icon-sm bg-gradient-to-br from-gold-yellow to-yellow-600">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value-sm">{{ $venue->created_at->diffInDays() }}</div>
                    <div class="stats-label-sm">Days Active</div>
                </div>
            </div>

            <!-- Basic Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Basic Information</h3>
                        <p class="bank-card-subtitle">Update venue's basic details</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Venue Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label-bank required">Venue Name</label>
                            <input type="text" id="name" name="name" value="{{ old('name', $venue->name) }}"
                                class="form-input-bank @error('name') border-error-red @enderror"
                                placeholder="Enter venue name" x-model="form.name" @input="validateField('name')"
                                required>
                            @error('name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Choose a descriptive name for the sports venue</p>
                        </div>

                        <!-- Venue Name (Arabic) -->
                        <div class="md:col-span-2">
                            <label for="name_ar" class="form-label-bank">Venue Name (Arabic)</label>
                            <input type="text" id="name_ar" name="name_ar"
                                value="{{ old('name_ar', $venue->name_ar) }}"
                                class="form-input-bank @error('name_ar') border-error-red @enderror"
                                placeholder="أدخل اسم المكان بالعربية" x-model="form.name_ar" dir="rtl">
                            @error('name_ar')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: Arabic name for the venue</p>
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="description" class="form-label-bank">Description</label>
                            <textarea id="description" name="description" rows="3"
                                class="form-textarea-bank @error('description') border-error-red @enderror"
                                placeholder="Enter venue description and facilities overview" x-model="form.description">{{ old('description', $venue->description) }}</textarea>
                            @error('description')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Provide a brief description of the venue and its facilities</p>
                        </div>

                        <!-- Description (Arabic) -->
                        <div class="md:col-span-2">
                            <label for="description_ar" class="form-label-bank">Description (Arabic)</label>
                            <textarea id="description_ar" name="description_ar" rows="3"
                                class="form-textarea-bank @error('description_ar') border-error-red @enderror"
                                placeholder="أدخل وصف المكان بالعربية" x-model="form.description_ar" dir="rtl">{{ old('description_ar', $venue->description_ar) }}</textarea>
                            @error('description_ar')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: Arabic description of the venue</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Location Information</h3>
                        <p class="bank-card-subtitle">Venue address and location details</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label for="address" class="form-label-bank required">Address</label>
                            <textarea id="address" name="address" rows="3"
                                class="form-textarea-bank @error('address') border-error-red @enderror"
                                placeholder="Enter complete address including building, street, and landmarks" x-model="form.address" required>{{ old('address', $venue->address) }}</textarea>
                            @error('address')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Provide detailed address for easy location</p>
                        </div>

                        <!-- Address (Arabic) -->
                        <div class="md:col-span-2">
                            <label for="address_ar" class="form-label-bank">Address (Arabic)</label>
                            <textarea id="address_ar" name="address_ar" rows="3"
                                class="form-textarea-bank @error('address_ar') border-error-red @enderror" placeholder="أدخل العنوان بالعربية"
                                x-model="form.address_ar" dir="rtl">{{ old('address_ar', $venue->address_ar) }}</textarea>
                            @error('address_ar')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: Arabic address</p>
                        </div>

                        <!-- City -->
                        <div>
                            <label for="city" class="form-label-bank required">City</label>
                            <input type="text" id="city" name="city" value="{{ old('city', $venue->city) }}"
                                class="form-input-bank @error('city') border-error-red @enderror"
                                placeholder="Enter city (e.g., Dubai, Abu Dhabi)" x-model="form.city"
                                @input="validateField('city')" required>
                            @error('city')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Country -->
                        <div>
                            <label for="country" class="form-label-bank required">Country</label>
                            <select id="country" name="country"
                                class="form-select-bank @error('country') border-error-red @enderror"
                                x-model="form.country" required>
                                <option value="">Select country</option>
                                <option value="UAE" {{ old('country', $venue->country) == 'UAE' ? 'selected' : '' }}>
                                    United Arab Emirates</option>
                                <option value="Saudi Arabia"
                                    {{ old('country', $venue->country) == 'Saudi Arabia' ? 'selected' : '' }}>Saudi Arabia
                                </option>
                                <option value="Qatar" {{ old('country', $venue->country) == 'Qatar' ? 'selected' : '' }}>
                                    Qatar</option>
                                <option value="Kuwait"
                                    {{ old('country', $venue->country) == 'Kuwait' ? 'selected' : '' }}>Kuwait</option>
                                <option value="Bahrain"
                                    {{ old('country', $venue->country) == 'Bahrain' ? 'selected' : '' }}>Bahrain</option>
                                <option value="Oman" {{ old('country', $venue->country) == 'Oman' ? 'selected' : '' }}>
                                    Oman</option>
                            </select>
                            @error('country')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Coordinates -->
                        <div>
                            <label for="latitude" class="form-label-bank">Latitude</label>
                            <input type="number" id="latitude" name="latitude"
                                value="{{ old('latitude', $venue->latitude) }}"
                                class="form-input-bank @error('latitude') border-error-red @enderror"
                                placeholder="25.2048" step="any" min="-90" max="90"
                                x-model="form.latitude">
                            @error('latitude')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: GPS latitude coordinate</p>
                        </div>

                        <div>
                            <label for="longitude" class="form-label-bank">Longitude</label>
                            <input type="number" id="longitude" name="longitude"
                                value="{{ old('longitude', $venue->longitude) }}"
                                class="form-input-bank @error('longitude') border-error-red @enderror"
                                placeholder="55.2708" step="any" min="-180" max="180"
                                x-model="form.longitude">
                            @error('longitude')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: GPS longitude coordinate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Contact Information</h3>
                        <p class="bank-card-subtitle">Venue and manager contact details</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Venue Phone -->
                        <div>
                            <label for="phone" class="form-label-bank">Venue Phone</label>
                            <x-uae-phone-input name="phone" value="{{ old('phone', $venue->phone) }}"
                                class="@error('phone') border-error-red @enderror" placeholder="*********" />
                            @error('phone')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Enter 9 digits after +971 (e.g., *********)</p>
                        </div>

                        <!-- Venue Email -->
                        <div>
                            <label for="email" class="form-label-bank">Venue Email</label>
                            <input type="email" id="email" name="email"
                                value="{{ old('email', $venue->email) }}"
                                class="form-input-bank @error('email') border-error-red @enderror"
                                placeholder="<EMAIL>" x-model="form.email"
                                @input="validateField('email')">
                            @error('email')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: Contact email for this venue</p>
                        </div>

                        <!-- Manager Name -->
                        <div>
                            <label for="manager_name" class="form-label-bank">Manager Name</label>
                            <input type="text" id="manager_name" name="manager_name"
                                value="{{ old('manager_name', $venue->manager_name) }}"
                                class="form-input-bank @error('manager_name') border-error-red @enderror"
                                placeholder="Enter manager's full name" x-model="form.manager_name">
                            @error('manager_name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Manager Phone -->
                        <div>
                            <label for="manager_phone" class="form-label-bank">Manager Phone</label>
                            <x-uae-phone-input name="manager_phone"
                                value="{{ old('manager_phone', $venue->manager_phone) }}"
                                class="@error('manager_phone') border-error-red @enderror" placeholder="*********" />
                            @error('manager_phone')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Manager Email -->
                        <div class="md:col-span-2">
                            <label for="manager_email" class="form-label-bank">Manager Email</label>
                            <input type="email" id="manager_email" name="manager_email"
                                value="{{ old('manager_email', $venue->manager_email) }}"
                                class="form-input-bank @error('manager_email') border-error-red @enderror"
                                placeholder="<EMAIL>" x-model="form.manager_email">
                            @error('manager_email')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing & Configuration Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Pricing & Configuration</h3>
                        <p class="bank-card-subtitle">Base rates and VAT settings</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Base Hourly Rate -->
                        <div>
                            <label for="hourly_rate_base" class="form-label-bank required">Base Hourly Rate (AED)</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-dark-gray text-sm">AED</span>
                                </div>
                                <input type="number" id="hourly_rate_base" name="hourly_rate_base"
                                    value="{{ old('hourly_rate_base', $venue->hourly_rate_base) }}"
                                    class="form-input-bank pl-12 @error('hourly_rate_base') border-error-red @enderror"
                                    placeholder="100.00" step="0.01" min="0" x-model="form.hourly_rate_base"
                                    required>
                            </div>
                            @error('hourly_rate_base')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Base rate per hour for field bookings</p>
                        </div>

                        <!-- VAT Applicable -->
                        <div>
                            <label class="form-label-bank">VAT Applicable</label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" name="vat_applicable" value="1" class="form-radio-bank"
                                        x-model="form.vat_applicable"
                                        {{ old('vat_applicable', $venue->vat_applicable ? '1' : '0') == '1' ? 'checked' : '' }}>
                                    <span class="ml-2 text-charcoal-black">Yes</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="vat_applicable" value="0" class="form-radio-bank"
                                        x-model="form.vat_applicable"
                                        {{ old('vat_applicable', $venue->vat_applicable ? '1' : '0') == '0' ? 'checked' : '' }}>
                                    <span class="ml-2 text-charcoal-black">No</span>
                                </label>
                            </div>
                            @error('vat_applicable')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- VAT Rate -->
                        <div x-show="form.vat_applicable === '1'">
                            <label for="vat_rate" class="form-label-bank required">VAT Rate (%)</label>
                            <div class="relative">
                                <input type="number" id="vat_rate" name="vat_rate"
                                    value="{{ old('vat_rate', $venue->vat_rate) }}"
                                    class="form-input-bank pr-8 @error('vat_rate') border-error-red @enderror"
                                    placeholder="5.00" step="0.01" min="0" max="100"
                                    x-model="form.vat_rate">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-dark-gray text-sm">%</span>
                                </div>
                            </div>
                            @error('vat_rate')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">VAT percentage (e.g., 5% for UAE)</p>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="form-label-bank">Venue Status</label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="1" class="form-radio-bank"
                                        x-model="form.status"
                                        {{ old('status', $venue->status ? '1' : '0') == '1' ? 'checked' : '' }}>
                                    <span class="ml-2 text-charcoal-black">Active</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="0" class="form-radio-bank"
                                        x-model="form.status"
                                        {{ old('status', $venue->status ? '1' : '0') == '0' ? 'checked' : '' }}>
                                    <span class="ml-2 text-charcoal-black">Inactive</span>
                                </label>
                            </div>
                            <p class="form-help-bank">
                                @if ($venue->fields_count > 0 || $venue->total_reservations > 0)
                                    <span class="text-warning-orange">⚠️ Warning: This venue has
                                        {{ $venue->fields_count }} fields and {{ $venue->total_reservations }}
                                        reservations. Deactivating will affect operations.</span>
                                @else
                                    Set the status of the venue
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Additional Information</h3>
                        <p class="bank-card-subtitle">Operating hours, facilities, and notes</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Notes -->
                        <div>
                            <label for="notes" class="form-label-bank">Notes</label>
                            <textarea id="notes" name="notes" rows="4"
                                class="form-textarea-bank @error('notes') border-error-red @enderror"
                                placeholder="Enter any additional notes about the venue" x-model="form.notes">{{ old('notes', $venue->notes) }}</textarea>
                            @error('notes')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Notes (Arabic) -->
                        <div>
                            <label for="notes_ar" class="form-label-bank">Notes (Arabic)</label>
                            <textarea id="notes_ar" name="notes_ar" rows="4"
                                class="form-textarea-bank @error('notes_ar') border-error-red @enderror"
                                placeholder="أدخل ملاحظات إضافية بالعربية" x-model="form.notes_ar" dir="rtl">{{ old('notes_ar', $venue->notes_ar) }}</textarea>
                            @error('notes_ar')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change History -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Change History</h3>
                        <p class="bank-card-subtitle">Track modifications to this venue</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-info-blue to-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                        <div>
                            <strong class="text-charcoal-black">Created:</strong>
                            <p class="text-dark-gray">{{ $venue->created_at->format('F j, Y \a\t g:i A') }}</p>
                            <p class="text-medium-gray">{{ $venue->created_at->diffForHumans() }}</p>
                        </div>
                        <div>
                            <strong class="text-charcoal-black">Last Updated:</strong>
                            <p class="text-dark-gray">{{ $venue->updated_at->format('F j, Y \a\t g:i A') }}</p>
                            <p class="text-medium-gray">{{ $venue->updated_at->diffForHumans() }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <a href="{{ route('venues.show', $venue) }}" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                                View Details
                            </a>
                            @can('delete', $venue)
                                <button type="button" @click="deleteVenue()"
                                    class="btn-bank btn-bank-outline text-error-red border-error-red hover:bg-error-red hover:text-white">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                    Delete Venue
                                </button>
                            @endcan
                        </div>

                        <div class="flex items-center space-x-3">
                            <a href="{{ route('venues.index') }}" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel
                            </a>
                            <button type="submit" class="btn-bank" :disabled="isSubmitting"
                                :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    x-show="!isSubmitting">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12">
                                    </path>
                                </svg>
                                <svg class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24"
                                    x-show="isSubmitting">
                                    <circle class="opacity-25" cx="12" cy="12" r="10"
                                        stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                <span x-text="isSubmitting ? 'Updating Venue...' : 'Update Venue'"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function venueEditForm() {
            return {
                form: {
                    name: '{{ old('name', $venue->name) }}',
                    name_ar: '{{ old('name_ar', $venue->name_ar) }}',
                    description: '{{ old('description', $venue->description) }}',
                    description_ar: '{{ old('description_ar', $venue->description_ar) }}',
                    address: '{{ old('address', $venue->address) }}',
                    address_ar: '{{ old('address_ar', $venue->address_ar) }}',
                    city: '{{ old('city', $venue->city) }}',
                    country: '{{ old('country', $venue->country) }}',
                    phone: '{{ old('phone', str_replace('+971', '', $venue->phone)) }}',
                    email: '{{ old('email', $venue->email) }}',
                    manager_name: '{{ old('manager_name', $venue->manager_name) }}',
                    manager_phone: '{{ old('manager_phone', str_replace('+971', '', $venue->manager_phone)) }}',
                    manager_email: '{{ old('manager_email', $venue->manager_email) }}',
                    latitude: '{{ old('latitude', $venue->latitude) }}',
                    longitude: '{{ old('longitude', $venue->longitude) }}',
                    hourly_rate_base: '{{ old('hourly_rate_base', $venue->hourly_rate_base) }}',
                    vat_applicable: '{{ old('vat_applicable', $venue->vat_applicable ? '1' : '0') }}',
                    vat_rate: '{{ old('vat_rate', $venue->vat_rate) }}',
                    status: '{{ old('status', $venue->status ? '1' : '0') }}',
                    notes: '{{ old('notes', $venue->notes) }}',
                    notes_ar: '{{ old('notes_ar', $venue->notes_ar) }}'
                },
                isSubmitting: false,
                errors: {},

                init() {
                    // Initialize form
                },

                validateField(field) {
                    // Clear previous error
                    delete this.errors[field];

                    // Basic validation
                    switch (field) {
                        case 'name':
                            if (!this.form.name.trim()) {
                                this.errors[field] = 'Venue name is required';
                            } else if (this.form.name.length < 3) {
                                this.errors[field] = 'Venue name must be at least 3 characters';
                            }
                            break;
                        case 'city':
                            if (!this.form.city.trim()) {
                                this.errors[field] = 'City is required';
                            }
                            break;
                        case 'email':
                            if (this.form.email && !this.isValidEmail(this.form.email)) {
                                this.errors[field] = 'Please enter a valid email address';
                            }
                            break;
                        case 'phone':
                            if (this.form.phone && !this.isValidUAEPhone(this.form.phone)) {
                                this.errors[field] = 'Please enter a valid UAE phone number';
                            }
                            break;
                    }
                },

                isValidEmail(email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                },

                isValidUAEPhone(phone) {
                    const phoneRegex = /^[0-9]{2}[0-9]{3}[0-9]{4}$/;
                    return phoneRegex.test(phone.replace(/\s/g, ''));
                },

                handleSubmit(event) {
                    this.isSubmitting = true;

                    // Validate all fields
                    this.validateField('name');
                    this.validateField('city');
                    this.validateField('email');
                    this.validateField('phone');

                    // If there are errors, prevent submission
                    if (Object.keys(this.errors).length > 0) {
                        event.preventDefault();
                        this.isSubmitting = false;
                        showNotification('error', 'Please fix the form errors before submitting.');
                        return;
                    }
                },

                async deleteVenue() {
                    const hasData =
                        {{ $venue->fields_count > 0 || $venue->total_reservations > 0 ? 'true' : 'false' }};
                    let confirmMessage = `Are you sure you want to delete "{{ $venue->localized_name }}"?`;

                    if (hasData) {
                        confirmMessage +=
                            '\n\nThis venue has {{ $venue->fields_count }} fields and {{ $venue->total_reservations }} reservations. This action cannot be undone.';
                    }

                    if (!confirm(confirmMessage)) {
                        return;
                    }

                    try {
                        const response = await fetch('{{ route('venues.destroy', $venue) }}', {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.href = '{{ route('venues.index') }}';
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while deleting the venue.');
                    }
                }
            }
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            if (type === 'success') {
                alert('Success: ' + message);
            } else {
                alert('Error: ' + message);
            }
        }
    </script>
@endpush
