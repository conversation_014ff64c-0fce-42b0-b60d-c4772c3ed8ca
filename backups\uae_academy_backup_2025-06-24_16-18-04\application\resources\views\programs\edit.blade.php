@extends('layouts.dashboard')

@section('title', 'Edit Program - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Edit Program</h1>
                <p class="text-lg text-dark-gray">Update program details and settings</p>
                <span class="badge-bank badge-info">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    ID: #{{ $program->id }}
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('programs.show', $program) }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                    </path>
                </svg>
                View Program
            </a>
            <a href="{{ route('programs.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Programs
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto" x-data="programEditForm()" x-init="init()">
        <form method="POST" action="{{ route('programs.update', $program) }}" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Basic Information</h3>
                        <p class="bank-card-subtitle">Update the program's basic details</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Program Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label-bank">Program Name <span
                                    class="text-error-red">*</span></label>
                            <input type="text" name="name" id="name" value="{{ old('name', $program->name) }}"
                                class="form-input-bank @error('name') border-error-red @enderror"
                                placeholder="Enter program name" required>
                            @error('name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Branch Selection -->
                        <div>
                            <label for="branch_id" class="form-label-bank">Branch <span
                                    class="text-error-red">*</span></label>
                            <select name="branch_id" id="branch_id" @change="onBranchChange($event)"
                                class="form-select-bank @error('branch_id') border-error-red @enderror" required>
                                <option value="">Select Branch</option>
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ old('branch_id', $program->academy->branch_id) == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('branch_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Academy Selection -->
                        <div>
                            <label for="academy_id" class="form-label-bank">Academy <span
                                    class="text-error-red">*</span></label>
                            <select name="academy_id" id="academy_id"
                                class="form-select-bank @error('academy_id') border-error-red @enderror" required>
                                <option value="">Select Academy</option>
                                @foreach ($academies as $academy)
                                    <option value="{{ $academy->id }}"
                                        {{ old('academy_id', $program->academy_id) == $academy->id ? 'selected' : '' }}
                                        data-branch-id="{{ $academy->branch_id }}">
                                        {{ $academy->name }} ({{ $academy->branch->name }})
                                    </option>
                                @endforeach
                            </select>
                            @error('academy_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="description" class="form-label-bank">Description</label>
                            <textarea name="description" id="description" rows="3"
                                class="form-textarea-bank @error('description') border-error-red @enderror"
                                placeholder="Enter program description (optional)">{{ old('description', $program->description) }}</textarea>
                            @error('description')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule & Pricing Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Schedule & Pricing</h3>
                        <p class="bank-card-subtitle">Update program schedule and pricing details</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-6">
                        <!-- Days Selection -->
                        <div>
                            <label class="form-label-bank">Program Days <span class="text-error-red">*</span></label>
                            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3 mt-2">
                                @php
                                    $days = [
                                        'SUN' => 'Sunday',
                                        'MON' => 'Monday',
                                        'TUE' => 'Tuesday',
                                        'WED' => 'Wednesday',
                                        'THU' => 'Thursday',
                                        'FRI' => 'Friday',
                                        'SAT' => 'Saturday',
                                    ];
                                    // Decode JSON string to array if it's a string, otherwise use as is
$programDays = $program->days ?? [];
if (is_string($programDays)) {
    $programDays = json_decode($programDays, true) ?? [];
}
$selectedDays = old('days', $programDays);
                                @endphp
                                @foreach ($days as $value => $label)
                                    <label
                                        class="flex items-center p-3 border border-medium-gray rounded-lg cursor-pointer hover:bg-off-white transition-colors duration-200 {{ in_array($value, $selectedDays) ? 'bg-leaders-red bg-opacity-10 border-leaders-red' : '' }}">
                                        <input type="checkbox" name="days[]" value="{{ $value }}"
                                            {{ in_array($value, $selectedDays) ? 'checked' : '' }}
                                            class="form-checkbox text-leaders-red border-medium-gray rounded focus:ring-leaders-red focus:ring-offset-0">
                                        <span class="ml-2 text-sm font-medium text-dark-gray">{{ $label }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('days')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Time Schedule -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="start_time" class="form-label-bank">Start Time</label>
                                <input type="time" name="start_time" id="start_time"
                                    value="{{ old('start_time', $program->start_time ? $program->start_time->format('H:i') : '') }}"
                                    class="form-input-bank @error('start_time') border-error-red @enderror">
                                @error('start_time')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="end_time" class="form-label-bank">End Time</label>
                                <input type="time" name="end_time" id="end_time"
                                    value="{{ old('end_time', $program->end_time ? $program->end_time->format('H:i') : '') }}"
                                    class="form-input-bank @error('end_time') border-error-red @enderror">
                                @error('end_time')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Classes and Pricing -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="classes" class="form-label-bank">Number of Classes <span
                                        class="text-error-red">*</span></label>
                                <input type="number" name="classes" id="classes"
                                    value="{{ old('classes', $program->classes) }}" min="1" max="50"
                                    class="form-input-bank @error('classes') border-error-red @enderror" required>
                                @error('classes')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="price" class="form-label-bank">Price <span
                                        class="text-error-red">*</span></label>
                                <div class="relative">
                                    <input type="number" name="price" id="price"
                                        value="{{ old('price', $program->price) }}" min="0" step="0.01"
                                        class="form-input-bank pr-12 @error('price') border-error-red @enderror"
                                        placeholder="0.00" required>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-dark-gray text-sm">AED</span>
                                    </div>
                                </div>
                                @error('price')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="max_students" class="form-label-bank">Max Students</label>
                                <input type="number" name="max_students" id="max_students"
                                    value="{{ old('max_students', $program->max_students) }}" min="1"
                                    max="1000"
                                    class="form-input-bank @error('max_students') border-error-red @enderror"
                                    placeholder="Unlimited">
                                @error('max_students')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="form-label-bank">Status</label>
                            <div class="flex items-center space-x-6 mt-2">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="1"
                                        {{ old('status', $program->status ? '1' : '0') == '1' ? 'checked' : '' }}
                                        class="form-radio text-leaders-red border-medium-gray focus:ring-leaders-red focus:ring-offset-0">
                                    <span class="ml-2 text-sm font-medium text-dark-gray">Active</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="0"
                                        {{ old('status', $program->status ? '1' : '0') == '0' ? 'checked' : '' }}
                                        class="form-radio text-leaders-red border-medium-gray focus:ring-leaders-red focus:ring-offset-0">
                                    <span class="ml-2 text-sm font-medium text-dark-gray">Inactive</span>
                                </label>
                            </div>
                            @error('status')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-dark-gray">
                            <p>Last updated: {{ $program->updated_at->format('M d, Y \a\t g:i A') }}</p>
                            <p>Created: {{ $program->created_at->format('M d, Y \a\t g:i A') }}</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <a href="{{ route('programs.show', $program) }}" class="btn-bank btn-bank-outline">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12">
                                    </path>
                                </svg>
                                Cancel
                            </a>
                            <button type="submit" class="btn-bank">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7">
                                    </path>
                                </svg>
                                Update Program
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function programEditForm() {
            return {
                init() {
                    // Initialize form functionality
                    this.filterAcademiesByBranch();
                },

                onBranchChange(event) {
                    this.filterAcademiesByBranch(event.target.value);
                },

                filterAcademiesByBranch(branchId = null) {
                    const academySelect = document.getElementById('academy_id');
                    const academyOptions = academySelect.querySelectorAll('option');

                    academyOptions.forEach(option => {
                        if (option.value === '') {
                            option.style.display = 'block';
                            return;
                        }

                        const optionBranchId = option.dataset.branchId;
                        if (!branchId || optionBranchId === branchId) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                            if (option.selected) {
                                option.selected = false;
                                academySelect.value = '';
                            }
                        }
                    });
                }
            }
        }
    </script>
@endpush
