{{-- Settings Input Partial - Renders different input types based on setting type --}}

@switch($setting->type)
    @case('string')
        <input type="text" id="{{ $setting->key }}" name="{{ $setting->key }}"
            value="{{ old($setting->key, $setting->getRawValue()) }}"
            class="form-control @error($setting->key) is-invalid @enderror" @if (in_array('required', $setting->validation_rules ?? [])) required @endif>
    @break

    @case('textarea')
        <textarea id="{{ $setting->key }}" name="{{ $setting->key }}" rows="4"
            class="form-control @error($setting->key) is-invalid @enderror" @if (in_array('required', $setting->validation_rules ?? [])) required @endif>{{ old($setting->key, $setting->getRawValue()) }}</textarea>
    @break

    @case('integer')
    @case('decimal')
        <input type="number" id="{{ $setting->key }}" name="{{ $setting->key }}"
            value="{{ old($setting->key, $setting->getRawValue()) }}"
            class="form-control @error($setting->key) is-invalid @enderror"
            @if ($setting->type === 'decimal') step="0.01" @endif @if (in_array('required', $setting->validation_rules ?? [])) required @endif>
    @break

    @case('boolean')
        <div class="checkbox-wrapper">
            <input type="hidden" name="{{ $setting->key }}" value="0">
            <label for="{{ $setting->key }}" class="checkbox-label">
                <input type="checkbox" id="{{ $setting->key }}" name="{{ $setting->key }}" value="1"
                    class="checkbox-input" @if (old($setting->key, $setting->getRawValue())) checked @endif>
                <span class="checkbox-text">
                    {{ $setting->getRawValue() ? 'Enabled' : 'Disabled' }}
                </span>
            </label>
        </div>
    @break

    @case('select')
        <select id="{{ $setting->key }}" name="{{ $setting->key }}"
            class="form-control @error($setting->key) is-invalid @enderror" @if (in_array('required', $setting->validation_rules ?? [])) required @endif>
            @if (!in_array('required', $setting->validation_rules ?? []))
                <option value="">-- Select Option --</option>
            @endif
            @if ($setting->options)
                @foreach ($setting->options as $value => $label)
                    <option value="{{ $value }}" @if (old($setting->key, $setting->getRawValue()) == $value) selected @endif>
                        {{ $label }}
                    </option>
                @endforeach
            @endif
        </select>
    @break

    @case('json')
    @case('array')
        @if ($setting->options)
            {{-- Multi-select with checkboxes --}}
            <div class="multi-select" onchange="updateMultiSelect(this)">
                @php
                    $currentValues = old($setting->key, $setting->getRawValue()) ?? [];
                    if (is_string($currentValues)) {
                        $currentValues = json_decode($currentValues, true) ?? [];
                    }
                @endphp
                @foreach ($setting->options as $value => $label)
                    <div class="multi-select-option">
                        <input type="checkbox" id="{{ $setting->key }}_{{ $value }}" value="{{ $value }}"
                            class="multi-select-checkbox" @if (in_array($value, $currentValues)) checked @endif>
                        <label for="{{ $setting->key }}_{{ $value }}">{{ $label }}</label>
                    </div>
                @endforeach
            </div>
            <input type="hidden" name="{{ $setting->key }}" value="{{ old($setting->key, json_encode($currentValues)) }}">
        @else
            {{-- JSON textarea --}}
            <textarea id="{{ $setting->key }}" name="{{ $setting->key }}" rows="6"
                class="form-control @error($setting->key) is-invalid @enderror font-mono text-sm" placeholder='{"key": "value"}'
                @if (in_array('required', $setting->validation_rules ?? [])) required @endif>{{ old($setting->key, is_array($setting->getRawValue()) ? json_encode($setting->getRawValue(), JSON_PRETTY_PRINT) : $setting->getRawValue()) }}</textarea>
            <div class="mt-2 text-sm text-gray-500">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                Enter valid JSON format
            </div>
        @endif
    @break

    @case('file')
        <div class="file-upload">
            <input type="file" id="{{ $setting->key }}" name="{{ $setting->key }}" class="file-upload-input"
                @if (str_contains(implode('|', $setting->validation_rules ?? []), 'image')) accept="image/*" @endif>
            <div class="file-upload-button">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <span class="file-upload-text">Choose file</span>
            </div>
        </div>
        @if ($setting->getRawValue())
            <div class="mt-2 text-sm text-gray-600">
                <strong>Current file:</strong>
                @if (str_contains(implode('|', $setting->validation_rules ?? []), 'image'))
                    <div class="mt-2">
                        <img src="{{ asset('storage/' . $setting->getRawValue()) }}" alt="Current {{ $setting->label }}"
                            class="max-w-xs max-h-32 rounded border">
                    </div>
                @else
                    <a href="{{ asset('storage/' . $setting->getRawValue()) }}" target="_blank"
                        class="text-blue-600 hover:text-blue-800">
                        {{ basename($setting->getRawValue()) }}
                    </a>
                @endif
            </div>
        @endif
    @break

    @default
        <input type="text" id="{{ $setting->key }}" name="{{ $setting->key }}"
            value="{{ old($setting->key, $setting->getRawValue()) }}"
            class="form-control @error($setting->key) is-invalid @enderror" @if (in_array('required', $setting->validation_rules ?? [])) required @endif>
@endswitch

{{-- Validation Rules Display (for development) --}}
@if (config('app.debug') && $setting->validation_rules)
    <div class="mt-1 text-xs text-gray-400">
        <strong>Validation:</strong> {{ implode(', ', $setting->validation_rules) }}
    </div>
@endif
