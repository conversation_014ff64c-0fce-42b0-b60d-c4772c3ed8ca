<?php

namespace App\Console\Commands;

use App\Models\Payment;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ExpirePayments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payments:expire {--dry-run : Show what would be expired without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Expire active payments that have passed their end date and update student status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting payment expiration process...');

        $dryRun = $this->option('dry-run');

        // Find all active payments that should be expired
        $paymentsToExpire = Payment::query()->shouldBeExpired()->with(['student', 'academy', 'branch'])->get();

        if ($paymentsToExpire->isEmpty()) {
            $this->info('No payments found that need to be expired.');
            return 0;
        }

        $this->info("Found {$paymentsToExpire->count()} payment(s) to expire:");

        $expiredCount = 0;
        $studentsUpdated = [];

        foreach ($paymentsToExpire as $payment) {
            $studentName = $payment->student->full_name ?? 'Unknown';
            $academyName = $payment->academy->name ?? 'Unknown';
            $endDate = $payment->end_date->format('Y-m-d');

            $this->line("- Payment ID: {$payment->id} | Student: {$studentName} | Academy: {$academyName} | End Date: {$endDate}");

            if (!$dryRun) {
                // Mark payment as expired
                $payment->markAsExpired();
                $expiredCount++;

                // Update student status to inactive if they have no other active payments
                $student = $payment->student;
                $activePaymentsCount = $student->payments()->active()->count();

                if ($activePaymentsCount === 0 && $student->status === 'active') {
                    $student->status = 'inactive';
                    $student->save();
                    $studentsUpdated[] = $student->full_name;

                    Log::info("Student status updated to inactive", [
                        'student_id' => $student->id,
                        'student_name' => $student->full_name,
                        'reason' => 'No active payments remaining'
                    ]);
                }

                Log::info("Payment expired automatically", [
                    'payment_id' => $payment->id,
                    'student_id' => $payment->student_id,
                    'student_name' => $studentName,
                    'academy_name' => $academyName,
                    'end_date' => $endDate
                ]);
            }
        }

        if ($dryRun) {
            $this->warn('DRY RUN: No changes were made. Use without --dry-run to actually expire payments.');
        } else {
            $this->info("Successfully expired {$expiredCount} payment(s).");

            if (!empty($studentsUpdated)) {
                $this->info("Updated " . count($studentsUpdated) . " student(s) to inactive status:");
                foreach ($studentsUpdated as $studentName) {
                    $this->line("- {$studentName}");
                }
            }
        }

        return 0;
    }
}
