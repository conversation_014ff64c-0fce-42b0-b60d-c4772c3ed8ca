<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('uniform_purchase_order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('uniform_purchase_order_id')->constrained()->onDelete('cascade');
            $table->foreignId('uniform_inventory_id')->constrained('uniform_inventory')->onDelete('cascade');

            // Item Details
            $table->string('item_name'); // Snapshot of item name at time of order
            $table->string('item_sku'); // Snapshot of SKU
            $table->string('size');
            $table->string('color')->nullable();
            $table->text('description')->nullable();

            // Quantities
            $table->integer('quantity_ordered'); // Original quantity ordered
            $table->integer('quantity_received')->default(0); // Quantity actually received
            $table->integer('quantity_pending')->default(0); // Still pending delivery
            $table->integer('quantity_cancelled')->default(0); // Cancelled quantity

            // Pricing
            $table->decimal('unit_cost', 10, 2); // Cost per unit in AED
            $table->decimal('total_cost', 12, 2); // quantity_ordered * unit_cost
            $table->decimal('discount_amount', 10, 2)->default(0); // Item-level discount
            $table->decimal('discount_percentage', 5, 2)->default(0); // Item-level discount %
            $table->decimal('net_cost', 12, 2); // After discount

            // Status
            $table->enum('status', ['pending', 'partial', 'received', 'cancelled'])->default('pending');

            // Receiving Information
            $table->date('expected_date')->nullable();
            $table->date('received_date')->nullable();
            $table->text('receiving_notes')->nullable();

            // Quality Control
            $table->integer('quantity_accepted')->default(0);
            $table->integer('quantity_rejected')->default(0);
            $table->text('rejection_reason')->nullable();

            $table->timestamps();

            // Indexes
            $table->index(['uniform_purchase_order_id', 'status'], 'upo_items_po_status_idx');
            $table->index(['uniform_inventory_id', 'status'], 'upo_items_inv_status_idx');
            $table->index(['item_sku', 'size', 'color'], 'upo_items_sku_size_color_idx');
            $table->index('status', 'upo_items_status_idx');
            $table->index('received_date', 'upo_items_received_date_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('uniform_purchase_order_items');
    }
};
