/* UAE English Sports Academy - RTL Enhancements */
/* Premium RTL Support with Commercial Translation Quality */

/* ===== RTL FOUNDATION ===== */

/* Enhanced RTL Base Styles */
[dir="rtl"] {
    direction: rtl;
    text-align: right;
    font-family: var(--font-family-arabic);
}

[dir="rtl"] * {
    direction: rtl;
}

/* Arabic Typography Excellence */
[lang="ar"],
[dir="rtl"] .arabic-text,
[dir="rtl"] body {
    font-family: var(--font-family-arabic);
    line-height: 1.8; /* Optimized for Arabic readability */
    letter-spacing: 0.02em; /* Subtle spacing for Arabic */
}

/* ===== LAYOUT STRUCTURE RTL ===== */

/* Header RTL Adjustments */
[dir="rtl"] .header {
    direction: rtl;
}

[dir="rtl"] .header-brand {
    margin-right: 0;
    margin-left: auto;
}

[dir="rtl"] .header-actions {
    margin-left: 0;
    margin-right: auto;
}

/* Sidebar RTL Excellence */
[dir="rtl"] .sidebar {
    right: 0;
    left: auto;
    border-right: none;
    border-left: 1px solid var(--medium-gray);
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

[dir="rtl"] .main-content {
    margin-right: var(--sidebar-width);
    margin-left: 0;
}

[dir="rtl"] .sidebar-collapsed .main-content {
    margin-right: var(--sidebar-collapsed-width);
    margin-left: 0;
}

/* ===== NAVIGATION RTL ===== */

/* Navigation Items RTL - FIXED FOR PROPER ICON POSITIONING */
[dir="rtl"] .nav-item {
    direction: rtl;
}

[dir="rtl"] .nav-link {
    text-align: right;
    padding-right: var(--space-lg);
    padding-left: var(--space-md);
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

[dir="rtl"] .nav-text {
    text-align: right;
    margin-right: var(--space-md);
    order: 1;
}

[dir="rtl"] .nav-icon {
    margin-right: 0;
    margin-left: 0;
    order: 2;
}

/* Specific nav-item RTL support for proper icon positioning */
.nav-item[dir="rtl"] .nav-link {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    text-align: right;
}

.nav-item[dir="rtl"] .nav-text {
    margin-right: var(--space-md);
    text-align: right;
    order: 1;
}

.nav-item[dir="rtl"] .nav-icon {
    margin-right: 0;
    margin-left: 0;
    order: 2;
}

/* Force RTL icon positioning with higher specificity */
html[dir="rtl"] .nav-item .nav-link,
body[dir="rtl"] .nav-item .nav-link,
[dir="rtl"] .sidebar .nav-item .nav-link {
    display: flex !important;
    flex-direction: row !important;
    justify-content: flex-end !important;
    text-align: right !important;
}

html[dir="rtl"] .nav-item .nav-text,
body[dir="rtl"] .nav-item .nav-text,
[dir="rtl"] .sidebar .nav-item .nav-text {
    margin-right: var(--space-md) !important;
    margin-left: 0 !important;
    order: 1 !important;
    text-align: right !important;
}

html[dir="rtl"] .nav-item .nav-icon,
body[dir="rtl"] .nav-item .nav-icon,
[dir="rtl"] .sidebar .nav-item .nav-icon {
    margin-right: 0 !important;
    margin-left: 0 !important;
    order: 2 !important;
}

/* ULTIMATE RTL SIDEBAR ICON FIX - Force icon to RIGHT side in RTL */
html[dir="rtl"] .sidebar .nav-link,
body[dir="rtl"] .sidebar .nav-link,
[dir="rtl"] .sidebar .nav-link,
html[dir="rtl"] .nav-item .nav-link,
body[dir="rtl"] .nav-item .nav-link,
[dir="rtl"] .nav-item .nav-link,
html[dir="rtl"] .nav-item[dir="rtl"] .nav-link,
body[dir="rtl"] .nav-item[dir="rtl"] .nav-link,
[dir="rtl"] .nav-item[dir="rtl"] .nav-link {
    display: flex !important;
    flex-direction: row-reverse !important;
    justify-content: flex-start !important;
    align-items: center !important;
    text-align: right !important;
}

/* Icon positioning - should be on RIGHT in RTL */
html[dir="rtl"] .sidebar .nav-icon,
body[dir="rtl"] .sidebar .nav-icon,
[dir="rtl"] .sidebar .nav-icon,
html[dir="rtl"] .nav-item .nav-icon,
body[dir="rtl"] .nav-item .nav-icon,
[dir="rtl"] .nav-item .nav-icon,
html[dir="rtl"] .nav-item[dir="rtl"] .nav-icon,
body[dir="rtl"] .nav-item[dir="rtl"] .nav-icon,
[dir="rtl"] .nav-item[dir="rtl"] .nav-icon {
    margin-left: var(--space-md) !important;
    margin-right: 0 !important;
    flex-shrink: 0 !important;
    order: 1 !important;
}

/* Text positioning - should be on LEFT in RTL */
html[dir="rtl"] .sidebar .nav-text,
body[dir="rtl"] .sidebar .nav-text,
[dir="rtl"] .sidebar .nav-text,
html[dir="rtl"] .nav-item .nav-text,
body[dir="rtl"] .nav-item .nav-text,
[dir="rtl"] .nav-item .nav-text,
html[dir="rtl"] .nav-item[dir="rtl"] .nav-text,
body[dir="rtl"] .nav-item[dir="rtl"] .nav-text,
[dir="rtl"] .nav-item[dir="rtl"] .nav-text {
    margin-left: 0 !important;
    margin-right: 0 !important;
    text-align: right !important;
    flex: 1 !important;
    order: 2 !important;
}

/* NUCLEAR OPTION - FORCE RTL ICON POSITIONING */
html[dir="rtl"] a.nav-link {
    display: flex !important;
    flex-direction: row-reverse !important;
}

html[dir="rtl"] a.nav-link svg.nav-icon {
    margin-left: 16px !important;
    margin-right: 0 !important;
}

html[dir="rtl"] a.nav-link span.nav-text {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Active Navigation State RTL */
[dir="rtl"] .nav-link.active {
    border-right: none;
    border-left: 3px solid var(--leaders-red);
}

[dir="rtl"] .nav-link.active::before {
    right: auto;
    left: 0;
}

/* ===== COMPONENTS RTL ===== */

/* Bank Cards RTL */
[dir="rtl"] .bank-card {
    text-align: right;
}

[dir="rtl"] .bank-card .card-header {
    text-align: right;
}

[dir="rtl"] .bank-card .card-title {
    text-align: right;
}

/* Statistics Cards RTL */
[dir="rtl"] .stats-card {
    text-align: right;
}

[dir="rtl"] .stats-card .stats-icon {
    margin-right: 0;
    margin-left: var(--space-md);
    order: 2;
}

[dir="rtl"] .stats-card .stats-content {
    order: 1;
}

[dir="rtl"] .stats-value {
    text-align: right;
}

[dir="rtl"] .stats-label {
    text-align: right;
}

/* ===== TABLES RTL ===== */

/* Table RTL Excellence */
[dir="rtl"] .table-bank {
    direction: rtl;
}

[dir="rtl"] .table-bank th,
[dir="rtl"] .table-bank td {
    text-align: right;
}

[dir="rtl"] .table-bank th:first-child,
[dir="rtl"] .table-bank td:first-child {
    border-radius: 0 var(--radius-card) var(--radius-card) 0;
}

[dir="rtl"] .table-bank th:last-child,
[dir="rtl"] .table-bank td:last-child {
    border-radius: var(--radius-card) 0 0 var(--radius-card);
}

/* Table Actions RTL */
[dir="rtl"] .table-actions {
    text-align: left; /* Actions stay left-aligned for consistency */
}

[dir="rtl"] .table-actions .btn {
    margin-left: 0;
    margin-right: var(--space-xs);
}

[dir="rtl"] .table-actions .btn:last-child {
    margin-right: 0;
}

/* ===== FORMS RTL ===== */

/* Form Groups RTL */
[dir="rtl"] .form-group {
    text-align: right;
}

[dir="rtl"] .form-label {
    text-align: right;
    margin-right: 0;
    margin-left: var(--space-xs);
}

[dir="rtl"] .form-control {
    text-align: right;
    padding-right: var(--space-md);
    padding-left: var(--space-sm);
}

[dir="rtl"] .form-control::placeholder {
    text-align: right;
}

/* Input Groups RTL */
[dir="rtl"] .input-group {
    direction: rtl;
}

[dir="rtl"] .input-group-text {
    border-radius: 0 var(--radius-button) var(--radius-button) 0;
}

[dir="rtl"] .input-group .form-control {
    border-radius: var(--radius-button) 0 0 var(--radius-button);
}

/* Select Elements RTL */
[dir="rtl"] select.form-control {
    background-position: left 0.75rem center;
    padding-left: 2.5rem;
    padding-right: 0.75rem;
}

/* ===== BUTTONS RTL ===== */

/* Button Groups RTL */
[dir="rtl"] .btn-group {
    direction: rtl;
}

[dir="rtl"] .btn-group .btn:first-child {
    border-radius: 0 var(--radius-button) var(--radius-button) 0;
}

[dir="rtl"] .btn-group .btn:last-child {
    border-radius: var(--radius-button) 0 0 var(--radius-button);
}

/* Button Icons RTL */
[dir="rtl"] .btn .icon {
    margin-right: 0;
    margin-left: var(--space-xs);
    order: 2;
}

[dir="rtl"] .btn .text {
    order: 1;
}

/* ===== MODALS RTL ===== */

/* Modal RTL */
[dir="rtl"] .modal-header {
    text-align: right;
}

[dir="rtl"] .modal-title {
    text-align: right;
}

[dir="rtl"] .modal-body {
    text-align: right;
}

[dir="rtl"] .modal-footer {
    justify-content: flex-start;
}

[dir="rtl"] .modal-footer .btn {
    margin-left: 0;
    margin-right: var(--space-sm);
}

[dir="rtl"] .modal-footer .btn:last-child {
    margin-right: 0;
}

/* Close Button RTL */
[dir="rtl"] .btn-close {
    margin-left: auto;
    margin-right: 0;
}

/* ===== RESPONSIVE RTL ===== */

/* Mobile RTL Adjustments */
@media (max-width: 1024px) {
    [dir="rtl"] .sidebar {
        transform: translateX(100%);
        right: 0;
        left: auto;
    }

    [dir="rtl"] .sidebar.mobile-open {
        transform: translateX(0);
    }

    [dir="rtl"] .main-content {
        margin-right: 0;
        margin-left: 0;
    }

    [dir="rtl"] .mobile-overlay {
        direction: rtl;
    }
}

/* Tablet RTL Adjustments */
@media (max-width: 768px) {
    [dir="rtl"] .stats-grid {
        direction: rtl;
    }

    [dir="rtl"] .table-responsive {
        direction: rtl;
    }
}

/* ===== ANIMATIONS RTL ===== */

/* Slide Animations RTL */
[dir="rtl"] .slide-in-right {
    animation: slideInLeft 0.3s ease-out;
}

[dir="rtl"] .slide-in-left {
    animation: slideInRight 0.3s ease-out;
}

/* Fade Animations RTL */
[dir="rtl"] .fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* ===== UTILITIES RTL ===== */

/* Text Alignment RTL */
[dir="rtl"] .text-left {
    text-align: right !important;
}

[dir="rtl"] .text-right {
    text-align: left !important;
}

/* Margin/Padding RTL */
[dir="rtl"] .mr-auto {
    margin-right: 0 !important;
    margin-left: auto !important;
}

[dir="rtl"] .ml-auto {
    margin-left: 0 !important;
    margin-right: auto !important;
}

[dir="rtl"] .pr-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
}

[dir="rtl"] .pl-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

/* Float RTL */
[dir="rtl"] .float-left {
    float: right !important;
}

[dir="rtl"] .float-right {
    float: left !important;
}

/* ===== PREMIUM ENHANCEMENTS ===== */

/* Arabic Number Formatting */
[dir="rtl"] .currency-amount {
    font-family: 'IBM Plex Sans', monospace; /* Keep numbers in Latin for clarity */
    direction: ltr;
    display: inline-block;
}

/* Status Badges RTL */
[dir="rtl"] .badge {
    text-align: center;
}

/* Breadcrumbs RTL */
[dir="rtl"] .breadcrumb {
    direction: rtl;
}

[dir="rtl"] .breadcrumb-item + .breadcrumb-item::before {
    content: "\\";
    transform: scaleX(-1);
}

/* Tooltips RTL */
[dir="rtl"] .tooltip {
    direction: rtl;
}

/* Dropdowns RTL */
[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

[dir="rtl"] .dropdown-item {
    text-align: right;
}
