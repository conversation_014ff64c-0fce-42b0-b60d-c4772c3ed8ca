@extends('layouts.dashboard')

@section('title', 'Add New Payment')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Add New Payment</h1>
                <p class="text-lg text-dark-gray">Create a new payment record for a student</p>
                <span class="badge-bank badge-info">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    New Payment Form
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('payments.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Payments
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto">
        <form method="POST" action="{{ route('payments.store') }}" class="space-y-6" x-data="{ submitting: false }"
            @submit="submitting = true">
            @csrf

            @include('payments._form')

            <!-- Form Actions -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button type="submit" class="btn-bank" :disabled="submitting">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    x-show="!submitting">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7"></path>
                                </svg>
                                <svg class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24" x-show="submitting"
                                    style="display: none;">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                <span x-text="submitting ? 'Creating Payment...' : 'Create Payment'"></span>
                            </button>

                            <button type="button" class="btn-bank btn-bank-outline" onclick="window.history.back()">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel
                            </button>
                        </div>

                        <div class="text-sm text-dark-gray">
                            <span class="text-leaders-red">*</span> Required fields
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-calculate end date when start date changes
            const startDateInput = document.getElementById('start_date');
            const endDateInput = document.getElementById('end_date');

            if (startDateInput && endDateInput) {
                startDateInput.addEventListener('change', function() {
                    if (this.value && !endDateInput.value) {
                        // Auto-set end date to 30 days after start date
                        const startDate = new Date(this.value);
                        startDate.setDate(startDate.getDate() + 30);
                        endDateInput.value = startDate.toISOString().split('T')[0];
                    }
                });
            }

            // Validate class times
            const timeFromInput = document.getElementById('class_time_from');
            const timeToInput = document.getElementById('class_time_to');

            if (timeFromInput && timeToInput) {
                function validateTimes() {
                    if (timeFromInput.value && timeToInput.value) {
                        if (timeFromInput.value >= timeToInput.value) {
                            timeToInput.setCustomValidity('End time must be after start time');
                        } else {
                            timeToInput.setCustomValidity('');
                        }
                    }
                }

                timeFromInput.addEventListener('change', validateTimes);
                timeToInput.addEventListener('change', validateTimes);
            }

            // Form validation before submit
            const form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const amount = parseFloat(document.getElementById('amount').value || 0);
                    const discount = parseFloat(document.getElementById('discount').value || 0);

                    if (discount > amount) {
                        e.preventDefault();
                        alert('Discount cannot be greater than the payment amount.');
                        return false;
                    }

                    const startDate = new Date(document.getElementById('start_date').value);
                    const endDate = new Date(document.getElementById('end_date').value);

                    if (startDate >= endDate) {
                        e.preventDefault();
                        alert('End date must be after start date.');
                        return false;
                    }
                });
            }
        });
    </script>
@endpush
