@extends('layouts.dashboard')

@section('title', 'Create New Field')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Create New Field</h1>
                <p class="text-lg text-dark-gray">Add a new sports field to the system</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('fields.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Fields
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto" x-data="fieldForm()" x-init="init()">
        <form action="{{ route('fields.store') }}" method="POST" @submit="handleSubmit" class="space-y-6">
            @csrf

            <!-- Basic Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                    <p class="bank-card-subtitle">Field name, venue, and type details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Venue Selection -->
                        <div class="md:col-span-2">
                            <label for="venue_id" class="form-label-bank required">Venue</label>
                            <select id="venue_id" name="venue_id" class="form-select-bank @error('venue_id') border-error-red @enderror" 
                                x-model="form.venue_id" @change="onVenueChange" required>
                                <option value="">Select Venue</option>
                                @foreach ($venues as $venue)
                                    <option value="{{ $venue->id }}" 
                                        {{ (old('venue_id') == $venue->id || ($selectedVenue && $selectedVenue->id == $venue->id)) ? 'selected' : '' }}>
                                        {{ $venue->localized_name }} - {{ $venue->city }}
                                    </option>
                                @endforeach
                            </select>
                            @error('venue_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Field Name -->
                        <div>
                            <label for="name" class="form-label-bank required">Field Name</label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}"
                                class="form-input-bank @error('name') border-error-red @enderror"
                                placeholder="e.g., Field A, Main Court" x-model="form.name" required>
                            @error('name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Field Name (Arabic) -->
                        <div>
                            <label for="name_ar" class="form-label-bank">Field Name (Arabic)</label>
                            <input type="text" id="name_ar" name="name_ar" value="{{ old('name_ar') }}"
                                class="form-input-bank @error('name_ar') border-error-red @enderror"
                                placeholder="اسم الملعب بالعربية" x-model="form.name_ar" dir="rtl">
                            @error('name_ar')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Field Type -->
                        <div>
                            <label for="type" class="form-label-bank required">Field Type</label>
                            <select id="type" name="type" class="form-select-bank @error('type') border-error-red @enderror" 
                                x-model="form.type" required>
                                <option value="">Select Type</option>
                                <option value="football" {{ old('type') == 'football' ? 'selected' : '' }}>Football</option>
                                <option value="basketball" {{ old('type') == 'basketball' ? 'selected' : '' }}>Basketball</option>
                                <option value="tennis" {{ old('type') == 'tennis' ? 'selected' : '' }}>Tennis</option>
                                <option value="volleyball" {{ old('type') == 'volleyball' ? 'selected' : '' }}>Volleyball</option>
                                <option value="multipurpose" {{ old('type') == 'multipurpose' ? 'selected' : '' }}>Multipurpose</option>
                            </select>
                            @error('type')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Surface Type -->
                        <div>
                            <label for="surface_type" class="form-label-bank">Surface Type</label>
                            <select id="surface_type" name="surface_type" class="form-select-bank @error('surface_type') border-error-red @enderror" 
                                x-model="form.surface_type">
                                <option value="">Select Surface</option>
                                <option value="grass" {{ old('surface_type') == 'grass' ? 'selected' : '' }}>Natural Grass</option>
                                <option value="artificial_grass" {{ old('surface_type') == 'artificial_grass' ? 'selected' : '' }}>Artificial Grass</option>
                                <option value="concrete" {{ old('surface_type') == 'concrete' ? 'selected' : '' }}>Concrete</option>
                                <option value="rubber" {{ old('surface_type') == 'rubber' ? 'selected' : '' }}>Rubber</option>
                                <option value="clay" {{ old('surface_type') == 'clay' ? 'selected' : '' }}>Clay</option>
                                <option value="hardcourt" {{ old('surface_type') == 'hardcourt' ? 'selected' : '' }}>Hard Court</option>
                            </select>
                            @error('surface_type')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="description" class="form-label-bank">Description</label>
                            <textarea id="description" name="description" rows="3"
                                class="form-textarea-bank @error('description') border-error-red @enderror"
                                placeholder="Field description and features..." x-model="form.description">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description (Arabic) -->
                        <div class="md:col-span-2">
                            <label for="description_ar" class="form-label-bank">Description (Arabic)</label>
                            <textarea id="description_ar" name="description_ar" rows="3"
                                class="form-textarea-bank @error('description_ar') border-error-red @enderror"
                                placeholder="وصف الملعب والمميزات..." x-model="form.description_ar" dir="rtl">{{ old('description_ar') }}</textarea>
                            @error('description_ar')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing & Booking -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Pricing & Booking Settings</h3>
                    <p class="bank-card-subtitle">Hourly rates and booking constraints</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- Hourly Rate -->
                        <div>
                            <label for="hourly_rate" class="form-label-bank required">Hourly Rate (AED)</label>
                            <input type="number" id="hourly_rate" name="hourly_rate" value="{{ old('hourly_rate') }}"
                                class="form-input-bank @error('hourly_rate') border-error-red @enderror"
                                placeholder="100" step="0.01" min="0" x-model="form.hourly_rate" required>
                            @error('hourly_rate')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Peak Hour Rate -->
                        <div>
                            <label for="peak_hour_rate" class="form-label-bank">Peak Hour Rate (AED)</label>
                            <input type="number" id="peak_hour_rate" name="peak_hour_rate" value="{{ old('peak_hour_rate') }}"
                                class="form-input-bank @error('peak_hour_rate') border-error-red @enderror"
                                placeholder="150" step="0.01" min="0" x-model="form.peak_hour_rate">
                            @error('peak_hour_rate')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Weekend Rate -->
                        <div>
                            <label for="weekend_rate" class="form-label-bank">Weekend Rate (AED)</label>
                            <input type="number" id="weekend_rate" name="weekend_rate" value="{{ old('weekend_rate') }}"
                                class="form-input-bank @error('weekend_rate') border-error-red @enderror"
                                placeholder="120" step="0.01" min="0" x-model="form.weekend_rate">
                            @error('weekend_rate')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Minimum Booking Hours -->
                        <div>
                            <label for="minimum_booking_hours" class="form-label-bank">Minimum Booking (Hours)</label>
                            <input type="number" id="minimum_booking_hours" name="minimum_booking_hours" value="{{ old('minimum_booking_hours', 1) }}"
                                class="form-input-bank @error('minimum_booking_hours') border-error-red @enderror"
                                min="1" max="24" x-model="form.minimum_booking_hours">
                            @error('minimum_booking_hours')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Maximum Booking Hours -->
                        <div>
                            <label for="maximum_booking_hours" class="form-label-bank">Maximum Booking (Hours)</label>
                            <input type="number" id="maximum_booking_hours" name="maximum_booking_hours" value="{{ old('maximum_booking_hours', 8) }}"
                                class="form-input-bank @error('maximum_booking_hours') border-error-red @enderror"
                                min="1" max="24" x-model="form.maximum_booking_hours">
                            @error('maximum_booking_hours')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Advance Booking Days -->
                        <div>
                            <label for="advance_booking_days" class="form-label-bank">Advance Booking (Days)</label>
                            <input type="number" id="advance_booking_days" name="advance_booking_days" value="{{ old('advance_booking_days', 30) }}"
                                class="form-input-bank @error('advance_booking_days') border-error-red @enderror"
                                min="1" max="365" x-model="form.advance_booking_days">
                            @error('advance_booking_days')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Buttons -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('fields.index') }}" class="btn-bank btn-bank-outline">
                    Cancel
                </a>
                <button type="submit" class="btn-bank" :disabled="loading">
                    <span x-show="!loading">Create Field</span>
                    <span x-show="loading" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating...
                    </span>
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function fieldForm() {
            return {
                loading: false,
                form: {
                    venue_id: '{{ $selectedVenue ? $selectedVenue->id : old('venue_id') }}',
                    name: '{{ old('name') }}',
                    name_ar: '{{ old('name_ar') }}',
                    type: '{{ old('type') }}',
                    surface_type: '{{ old('surface_type') }}',
                    description: '{{ old('description') }}',
                    description_ar: '{{ old('description_ar') }}',
                    hourly_rate: '{{ old('hourly_rate') }}',
                    peak_hour_rate: '{{ old('peak_hour_rate') }}',
                    weekend_rate: '{{ old('weekend_rate') }}',
                    minimum_booking_hours: '{{ old('minimum_booking_hours', 1) }}',
                    maximum_booking_hours: '{{ old('maximum_booking_hours', 8) }}',
                    advance_booking_days: '{{ old('advance_booking_days', 30) }}'
                },

                init() {
                    // Initialize form
                },

                onVenueChange() {
                    // Handle venue selection change
                },

                handleSubmit(event) {
                    this.loading = true;
                }
            }
        }
    </script>
@endpush
