<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Branch;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Branch>
 */
class BranchFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $cities = ['Dubai', 'Abu Dhabi', 'Sharjah', 'Ajman', 'Ras Al Khaimah', '<PERSON><PERSON><PERSON><PERSON>', 'Umm Al Quwain'];
        $city = fake()->randomElement($cities);
        
        return [
            'name' => $city . ' ' . fake()->randomElement(['Sports Center', 'Academy', 'Training Center']),
            'location' => $city . ', UAE',
            'phone' => '+971' . fake()->numerify('5########'),
            'email' => strtolower(str_replace(' ', '.', $city)) . '@uaeacademy.com',
            'address' => fake()->streetAddress() . ', ' . $city . ', UAE',
            'status' => true,
        ];
    }

    /**
     * Indicate that the branch is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }
}
