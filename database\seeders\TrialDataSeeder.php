<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Program;
use App\Models\Student;
use App\Models\Payment;
use App\Models\Uniform;
use App\Models\UniformInventory;
use App\Models\UniformCategory;
use App\Models\UniformSupplier;
use App\Models\Customer;
use App\Models\Venue;
use App\Models\Field;
use App\Models\Reservation;

use Carbon\Carbon;

class TrialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating trial data for UAE English Sports Academy...');

        // Create test users
        $userSeeder = new UserSeeder();
        $userSeeder->createTestUsers();

        // Create branches
        $branches = $this->createBranches();

        // Create academies
        $academies = $this->createAcademies($branches);

        // Create programs
        $programs = $this->createPrograms($academies);

        // Create uniform categories and suppliers
        $categories = $this->createUniformCategories();
        $suppliers = $this->createUniformSuppliers();

        // Create inventory items
        $inventoryItems = $this->createInventoryItems($branches, $academies, $categories, $suppliers);

        // Create students
        $students = $this->createStudents($branches, $academies, $programs);

        // Create payments
        $this->createPayments($students, $branches, $academies, $programs);

        // Create uniform orders
        $this->createUniformOrders($students, $branches, $academies);

        // Create customers
        $customers = $this->createCustomers();

        // Create venues and fields
        $venues = $this->createVenues();
        $fields = $this->createFields($venues);

        // Create reservations
        $this->createReservations($customers, $venues, $fields);

        $this->command->info('Trial data created successfully!');
    }



    private function createBranches(): array
    {
        $branches = [
            [
                'name' => 'Dubai Main Branch',
                'address' => 'Sheikh Zayed Road, Dubai',
                'phone' => '+971501234567',
                'email' => '<EMAIL>',
                'status' => true,
            ],
            [
                'name' => 'Abu Dhabi Branch',
                'address' => 'Corniche Road, Abu Dhabi',
                'phone' => '+971501234568',
                'email' => '<EMAIL>',
                'status' => true,
            ],
            [
                'name' => 'Sharjah Branch',
                'address' => 'Al Qasba, Sharjah',
                'phone' => '+971501234569',
                'email' => '<EMAIL>',
                'status' => true,
            ],
        ];

        $createdBranches = [];
        foreach ($branches as $branchData) {
            $createdBranches[] = Branch::firstOrCreate(['name' => $branchData['name']], $branchData);
        }

        return $createdBranches;
    }

    private function createAcademies(array $branches): array
    {
        $academies = [
            [
                'name' => 'Football Academy',
                'name_ar' => 'أكاديمية كرة القدم',
                'description' => 'Professional football training for all ages',
                'description_ar' => 'تدريب كرة قدم احترافي لجميع الأعمار',
                'phone' => '+971501234570',
                'email' => '<EMAIL>',
                'address' => 'Sports Complex A',
                'address_ar' => 'المجمع الرياضي أ',
                'status' => true,
            ],
            [
                'name' => 'Basketball Academy',
                'name_ar' => 'أكاديمية كرة السلة',
                'description' => 'Basketball training and development',
                'description_ar' => 'تدريب وتطوير كرة السلة',
                'phone' => '+971501234571',
                'email' => '<EMAIL>',
                'address' => 'Sports Complex B',
                'address_ar' => 'المجمع الرياضي ب',
                'status' => true,
            ],
            [
                'name' => 'Swimming Academy',
                'name_ar' => 'أكاديمية السباحة',
                'description' => 'Swimming lessons and competitive training',
                'description_ar' => 'دروس السباحة والتدريب التنافسي',
                'phone' => '+971501234572',
                'email' => '<EMAIL>',
                'address' => 'Aquatic Center',
                'address_ar' => 'المركز المائي',
                'status' => true,
            ],
        ];

        $createdAcademies = [];
        foreach ($branches as $branch) {
            foreach ($academies as $academyData) {
                $academyData['branch_id'] = $branch->id;
                $academyData['name'] = $academyData['name'] . ' - ' . $branch->name;
                $createdAcademies[] = Academy::create($academyData);
            }
        }

        return $createdAcademies;
    }

    private function createPrograms(array $academies): array
    {
        $programs = [
            [
                'name' => 'Beginner Program',
                'name_ar' => 'برنامج المبتدئين',
                'description' => 'Introduction to sports fundamentals',
                'description_ar' => 'مقدمة في أساسيات الرياضة',
                'duration_months' => 3,
                'price' => 500.00,
                'max_students' => 20,
                'min_age' => 6,
                'max_age' => 12,
                'status' => true,
            ],
            [
                'name' => 'Intermediate Program',
                'name_ar' => 'البرنامج المتوسط',
                'description' => 'Advanced skills development',
                'description_ar' => 'تطوير المهارات المتقدمة',
                'duration_months' => 6,
                'price' => 800.00,
                'max_students' => 15,
                'min_age' => 10,
                'max_age' => 16,
                'status' => true,
            ],
            [
                'name' => 'Advanced Program',
                'name_ar' => 'البرنامج المتقدم',
                'description' => 'Professional level training',
                'description_ar' => 'تدريب على المستوى المهني',
                'duration_months' => 12,
                'price' => 1200.00,
                'max_students' => 10,
                'min_age' => 14,
                'max_age' => 25,
                'status' => true,
            ],
        ];

        $createdPrograms = [];
        foreach ($academies as $academy) {
            foreach ($programs as $programData) {
                $programData['academy_id'] = $academy->id;
                $programData['branch_id'] = $academy->branch_id;
                $createdPrograms[] = Program::create($programData);
            }
        }

        return $createdPrograms;
    }

    private function createUniformCategories(): array
    {
        $categories = [
            [
                'name' => 'Jerseys',
                'name_ar' => 'القمصان',
                'code' => 'JER',
                'description' => 'Sports jerseys and shirts',
                'description_ar' => 'قمصان وتيشيرتات رياضية',
                'status' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Shorts',
                'name_ar' => 'الشورتات',
                'code' => 'SHO',
                'description' => 'Sports shorts and pants',
                'description_ar' => 'شورتات وبناطيل رياضية',
                'status' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Footwear',
                'name_ar' => 'الأحذية',
                'code' => 'FOO',
                'description' => 'Sports shoes and boots',
                'description_ar' => 'أحذية وبوتات رياضية',
                'status' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Accessories',
                'name_ar' => 'الإكسسوارات',
                'code' => 'ACC',
                'description' => 'Sports accessories and equipment',
                'description_ar' => 'إكسسوارات ومعدات رياضية',
                'status' => true,
                'sort_order' => 4,
            ],
        ];

        $createdCategories = [];
        foreach ($categories as $categoryData) {
            $createdCategories[] = UniformCategory::firstOrCreate(['code' => $categoryData['code']], $categoryData);
        }

        return $createdCategories;
    }

    private function createUniformSuppliers(): array
    {
        $suppliers = [
            [
                'name' => 'Nike UAE',
                'name_ar' => 'نايك الإمارات',
                'contact_person' => 'John Smith',
                'email' => '<EMAIL>',
                'phone' => '+971501234580',
                'address' => 'Dubai Investment Park',
                'address_ar' => 'مجمع دبي للاستثمار',
                'status' => true,
            ],
            [
                'name' => 'Adidas Middle East',
                'name_ar' => 'أديداس الشرق الأوسط',
                'contact_person' => 'Sarah Ahmed',
                'email' => '<EMAIL>',
                'phone' => '+971501234581',
                'address' => 'Abu Dhabi Commercial Center',
                'address_ar' => 'مركز أبوظبي التجاري',
                'status' => true,
            ],
            [
                'name' => 'Puma Sports',
                'name_ar' => 'بوما سبورتس',
                'contact_person' => 'Mohammed Al Ali',
                'email' => '<EMAIL>',
                'phone' => '+971501234582',
                'address' => 'Sharjah Industrial Area',
                'address_ar' => 'المنطقة الصناعية بالشارقة',
                'status' => true,
            ],
        ];

        $createdSuppliers = [];
        foreach ($suppliers as $supplierData) {
            $createdSuppliers[] = UniformSupplier::firstOrCreate(['email' => $supplierData['email']], $supplierData);
        }

        return $createdSuppliers;
    }

    private function createInventoryItems(array $branches, array $academies, array $categories, array $suppliers): array
    {
        $items = [
            ['name' => 'Football Jersey Home', 'size' => 'M', 'color' => 'Red', 'cost_price' => 50, 'selling_price' => 75, 'current_stock' => 100],
            ['name' => 'Football Jersey Away', 'size' => 'L', 'color' => 'Blue', 'cost_price' => 50, 'selling_price' => 75, 'current_stock' => 80],
            ['name' => 'Basketball Jersey', 'size' => 'XL', 'color' => 'White', 'cost_price' => 45, 'selling_price' => 70, 'current_stock' => 60],
            ['name' => 'Swimming Shorts', 'size' => 'S', 'color' => 'Black', 'cost_price' => 30, 'selling_price' => 45, 'current_stock' => 120],
            ['name' => 'Sports Shoes', 'size' => '42', 'color' => 'White', 'cost_price' => 80, 'selling_price' => 120, 'current_stock' => 40],
            ['name' => 'Training Shorts', 'size' => 'M', 'color' => 'Navy', 'cost_price' => 25, 'selling_price' => 40, 'current_stock' => 150],
        ];

        $createdItems = [];
        foreach ($branches as $branch) {
            foreach ($academies as $academy) {
                if ($academy->branch_id !== $branch->id) continue;

                foreach ($items as $index => $itemData) {
                    $category = $categories[$index % count($categories)];
                    $supplier = $suppliers[$index % count($suppliers)];

                    $itemData['branch_id'] = $branch->id;
                    $itemData['academy_id'] = $academy->id;
                    $itemData['uniform_category_id'] = $category->id;
                    $itemData['uniform_supplier_id'] = $supplier->id;
                    $itemData['sku'] = $category->code . '-' . strtoupper(substr($itemData['size'], 0, 2)) . '-' . substr($itemData['color'], 0, 2) . '-' . str_pad($index + 1, 3, '0', STR_PAD_LEFT);
                    $itemData['minimum_stock'] = 10;
                    $itemData['maximum_stock'] = 200;
                    $itemData['reorder_quantity'] = 50;
                    $itemData['available_stock'] = $itemData['current_stock'];
                    $itemData['currency'] = 'AED';
                    $itemData['status'] = 'active';
                    $itemData['brand'] = 'UAE Sports Academy';
                    $itemData['location'] = 'Warehouse A';

                    $createdItems[] = UniformInventory::create($itemData);
                }
            }
        }

        return $createdItems;
    }

    private function createStudents(array $branches, array $academies, array $programs): array
    {
        $students = [
            ['first_name' => 'Ahmed', 'last_name' => 'Al Mansouri', 'email' => '<EMAIL>', 'phone' => '+971501234590', 'gender' => 'male', 'birth_date' => '2010-05-15'],
            ['first_name' => 'Fatima', 'last_name' => 'Al Zahra', 'email' => '<EMAIL>', 'phone' => '+971501234591', 'gender' => 'female', 'birth_date' => '2012-08-22'],
            ['first_name' => 'Omar', 'last_name' => 'Al Shamsi', 'email' => '<EMAIL>', 'phone' => '+971501234592', 'gender' => 'male', 'birth_date' => '2009-12-10'],
            ['first_name' => 'Aisha', 'last_name' => 'Al Blooshi', 'email' => '<EMAIL>', 'phone' => '+971501234593', 'gender' => 'female', 'birth_date' => '2011-03-18'],
            ['first_name' => 'Khalid', 'last_name' => 'Al Rashid', 'email' => '<EMAIL>', 'phone' => '+971501234594', 'gender' => 'male', 'birth_date' => '2008-07-25'],
        ];

        $createdStudents = [];
        foreach ($programs as $program) {
            foreach ($students as $index => $studentData) {
                $studentData['branch_id'] = $program->branch_id;
                $studentData['academy_id'] = $program->academy_id;
                $studentData['program_id'] = $program->id;
                $studentData['student_id'] = 'STU' . str_pad(($index + 1) * $program->id, 6, '0', STR_PAD_LEFT);
                $studentData['nationality'] = 'UAE';
                $studentData['emergency_contact_name'] = 'Parent Name';
                $studentData['emergency_contact_phone'] = '+971501234500';
                $studentData['emergency_contact_relationship'] = 'Parent';
                $studentData['enrollment_date'] = Carbon::now()->subDays(rand(30, 365));
                $studentData['status'] = 'active';

                $createdStudents[] = Student::create($studentData);
            }
        }

        return $createdStudents;
    }

    private function createPayments(array $students, array $branches, array $academies, array $programs): void
    {
        foreach ($students as $student) {
            $program = collect($programs)->firstWhere('id', $student->program_id);

            Payment::create([
                'student_id' => $student->id,
                'branch_id' => $student->branch_id,
                'academy_id' => $student->academy_id,
                'program_id' => $student->program_id,
                'payment_number' => 'PAY' . str_pad($student->id, 6, '0', STR_PAD_LEFT),
                'amount' => $program->price,
                'vat_amount' => $program->price * 0.05,
                'total_amount' => $program->price * 1.05,
                'payment_date' => Carbon::now()->subDays(rand(1, 30)),
                'payment_method' => collect(['cash', 'card', 'bank_transfer'])->random(),
                'payment_type' => 'fresh',
                'status' => collect(['active', 'expired'])->random(),
                'currency' => 'AED',
                'notes' => 'Trial payment data',
            ]);
        }
    }

    private function createUniformOrders(array $students, array $branches, array $academies): void
    {
        foreach (array_slice($students, 0, 10) as $student) {
            Uniform::create([
                'student_id' => $student->id,
                'branch_id' => $student->branch_id,
                'academy_id' => $student->academy_id,
                'reference_number' => 'UNI' . str_pad($student->id, 6, '0', STR_PAD_LEFT),
                'uniform_type' => collect(['jersey', 'shorts', 'shoes', 'complete_set'])->random(),
                'size' => collect(['S', 'M', 'L', 'XL'])->random(),
                'color' => collect(['Red', 'Blue', 'White', 'Black'])->random(),
                'quantity' => rand(1, 3),
                'unit_price' => rand(50, 150),
                'amount' => rand(50, 450),
                'order_date' => Carbon::now()->subDays(rand(1, 60)),
                'delivery_date' => Carbon::now()->addDays(rand(1, 14)),
                'status' => collect(['ordered', 'processing', 'ready', 'delivered'])->random(),
                'currency' => 'AED',
                'notes' => 'Trial uniform order',
            ]);
        }
    }

    private function createCustomers(): array
    {
        $customers = [
            ['full_name' => 'Mohammed Al Ahmed', 'email' => '<EMAIL>', 'phone' => '+971501234600', 'gender' => 'male', 'nationality' => 'UAE'],
            ['full_name' => 'Sara Al Mansoori', 'email' => '<EMAIL>', 'phone' => '+971501234601', 'gender' => 'female', 'nationality' => 'UAE'],
            ['full_name' => 'Ali Al Rashid', 'email' => '<EMAIL>', 'phone' => '+971501234602', 'gender' => 'male', 'nationality' => 'UAE'],
            ['full_name' => 'Noura Al Blooshi', 'email' => '<EMAIL>', 'phone' => '+971501234603', 'gender' => 'female', 'nationality' => 'UAE'],
        ];

        $createdCustomers = [];
        foreach ($customers as $index => $customerData) {
            $customerData['customer_number'] = 'CUS' . str_pad($index + 1, 6, '0', STR_PAD_LEFT);
            $customerData['first_name'] = explode(' ', $customerData['full_name'])[0];
            $customerData['last_name'] = explode(' ', $customerData['full_name'])[1] . ' ' . explode(' ', $customerData['full_name'])[2];
            $customerData['customer_type'] = 'individual';
            $customerData['preferred_language'] = 'en';
            $customerData['preferred_contact_method'] = 'phone';
            $customerData['registration_date'] = Carbon::now()->subDays(rand(30, 365));
            $customerData['status'] = true;
            $customerData['vip_status'] = false;

            $createdCustomers[] = Customer::create($customerData);
        }

        return $createdCustomers;
    }

    private function createVenues(): array
    {
        $venues = [
            [
                'name' => 'Leaders Sports Complex',
                'name_ar' => 'مجمع ليدرز الرياضي',
                'address' => 'Al Wasl Road, Dubai',
                'address_ar' => 'شارع الوصل، دبي',
                'phone' => '+971501234610',
                'email' => '<EMAIL>',
                'capacity' => 500,
                'facilities' => 'Football fields, Basketball courts, Swimming pool',
                'status' => true,
            ],
            [
                'name' => 'Champions Arena',
                'name_ar' => 'ساحة الأبطال',
                'address' => 'Sheikh Mohammed Bin Rashid Boulevard, Dubai',
                'address_ar' => 'شارع الشيخ محمد بن راشد، دبي',
                'phone' => '+971501234611',
                'email' => '<EMAIL>',
                'capacity' => 300,
                'facilities' => 'Indoor courts, Training rooms',
                'status' => true,
            ],
        ];

        $createdVenues = [];
        foreach ($venues as $venueData) {
            $createdVenues[] = Venue::firstOrCreate(['name' => $venueData['name']], $venueData);
        }

        return $createdVenues;
    }

    private function createFields(array $venues): array
    {
        $fields = [
            ['name' => 'Field X', 'type' => 'football', 'hourly_rate' => 200],
            ['name' => 'Field Y', 'type' => 'football', 'hourly_rate' => 200],
            ['name' => 'Field Z', 'type' => 'basketball', 'hourly_rate' => 150],
            ['name' => 'Court A', 'type' => 'basketball', 'hourly_rate' => 150],
            ['name' => 'Court B', 'type' => 'tennis', 'hourly_rate' => 180],
        ];

        $createdFields = [];
        foreach ($venues as $venue) {
            foreach ($fields as $index => $fieldData) {
                $fieldData['venue_id'] = $venue->id;
                $fieldData['field_number'] = 'F' . str_pad($index + 1, 3, '0', STR_PAD_LEFT);
                $fieldData['status'] = true;
                $fieldData['currency'] = 'AED';

                $createdFields[] = Field::create($fieldData);
            }
        }

        return $createdFields;
    }

    private function createReservations(array $customers, array $venues, array $fields): void
    {
        foreach (array_slice($customers, 0, 5) as $index => $customer) {
            $field = $fields[array_rand($fields)];
            $venue = collect($venues)->firstWhere('id', $field->venue_id);

            $startDate = Carbon::now()->addDays(rand(1, 30));
            $duration = rand(1, 3);

            Reservation::create([
                'customer_id' => $customer->id,
                'venue_id' => $venue->id,
                'field_id' => $field->id,
                'reservation_number' => 'RES' . str_pad($index + 1, 6, '0', STR_PAD_LEFT),
                'reservation_date' => $startDate->toDateString(),
                'start_time' => $startDate->format('H:i:s'),
                'end_time' => $startDate->addHours($duration)->format('H:i:s'),
                'duration_hours' => $duration,
                'hourly_rate' => $field->hourly_rate,
                'total_amount' => $field->hourly_rate * $duration,
                'status' => collect(['confirmed', 'pending', 'cancelled'])->random(),
                'currency' => 'AED',
                'notes' => 'Trial reservation data',
            ]);
        }
    }
}
