<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Academy;
use App\Models\Program;
use App\Models\Student;
use App\Models\Payment;
use App\Models\Uniform;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Display the main reports dashboard.
     */
    public function index(): View
    {
        // Check if user has permission to view reports
        if (!Auth::check() || !in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager'])) {
            abort(403, 'Unauthorized access to reports.');
        }

        // Get overview statistics
        $stats = $this->getOverviewStatistics();

        // Get recent activity
        $recentActivity = $this->getRecentActivity();

        return view('reports.index', compact('stats', 'recentActivity'));
    }

    /**
     * Display financial reports.
     */
    public function financial(Request $request): View|JsonResponse
    {
        // Check if user has permission to view financial reports
        if (!Auth::check() || !in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager'])) {
            abort(403, 'Unauthorized access to financial reports.');
        }

        $filters = $this->getFilters($request);

        // Get financial data
        $financialData = $this->getFinancialData($filters);

        // Get branches and academies for filters
        $branches = Branch::active()->get();
        $academies = Academy::active()->get();

        if ($request->ajax()) {
            return response()->json($financialData);
        }

        return view('reports.financial', compact('financialData', 'branches', 'academies', 'filters'));
    }

    /**
     * Display uniform reports.
     */
    public function uniform(Request $request): View|JsonResponse
    {
        // Check if user has permission to view uniform reports
        if (!Auth::check() || !in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager'])) {
            abort(403, 'Unauthorized access to uniform reports.');
        }

        $filters = $this->getFilters($request);

        // Get uniform data
        $uniformData = $this->getUniformData($filters);

        // Get branches and academies for filters
        $branches = Branch::active()->get();
        $academies = Academy::active()->get();

        if ($request->ajax()) {
            return response()->json($uniformData);
        }

        return view('reports.uniform', compact('uniformData', 'branches', 'academies', 'filters'));
    }

    /**
     * Display program reports.
     */
    public function program(Request $request): View|JsonResponse
    {
        // Check if user has permission to view program reports
        if (!Auth::check() || !in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager'])) {
            abort(403, 'Unauthorized access to program reports.');
        }

        $filters = $this->getFilters($request);

        // Get program data
        $programData = $this->getProgramData($filters);

        // Get branches and academies for filters
        $branches = Branch::active()->get();
        $academies = Academy::active()->get();

        if ($request->ajax()) {
            return response()->json($programData);
        }

        return view('reports.program', compact('programData', 'branches', 'academies', 'filters'));
    }

    /**
     * Display status reports.
     */
    public function status(Request $request): View|JsonResponse
    {
        // Check if user has permission to view status reports
        if (!Auth::check() || !in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager'])) {
            abort(403, 'Unauthorized access to status reports.');
        }

        $filters = $this->getFilters($request);

        // Get status data
        $statusData = $this->getStatusData($filters);

        // Get branches and academies for filters
        $branches = Branch::active()->get();
        $academies = Academy::active()->get();

        if ($request->ajax()) {
            return response()->json($statusData);
        }

        return view('reports.status', compact('statusData', 'branches', 'academies', 'filters'));
    }

    /**
     * Display daily reports.
     */
    public function daily(Request $request): View|JsonResponse
    {
        // Check if user has permission to view daily reports
        if (!Auth::check() || !in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager'])) {
            abort(403, 'Unauthorized access to daily reports.');
        }

        $filters = $this->getFilters($request);

        // Get daily data
        $dailyData = $this->getDailyData($filters);

        // Get branches and academies for filters
        $branches = Branch::active()->get();
        $academies = Academy::active()->get();

        if ($request->ajax()) {
            return response()->json($dailyData);
        }

        return view('reports.daily', compact('dailyData', 'branches', 'academies', 'filters'));
    }

    /**
     * Get overview statistics for dashboard.
     */
    private function getOverviewStatistics(): array
    {
        $user = Auth::user();

        // Base queries with role-based filtering
        $academyQuery = Academy::query();
        $studentQuery = Student::query();
        $paymentQuery = Payment::query();
        $uniformQuery = Uniform::query();

        // Apply role-based filtering
        if ($user->role === 'branch_manager') {
            $academyQuery->where('branch_id', $user->branch_id);
            $studentQuery->where('branch_id', $user->branch_id);
            $paymentQuery->where('branch_id', $user->branch_id);
            $uniformQuery->where('branch_id', $user->branch_id);
        } elseif ($user->role === 'academy_manager') {
            $academyQuery->where('id', $user->academy_id);
            $studentQuery->where('academy_id', $user->academy_id);
            $paymentQuery->where('academy_id', $user->academy_id);
            $uniformQuery->where('academy_id', $user->academy_id);
        }

        return [
            'total_revenue' => $paymentQuery->where('status', 'completed')->sum('amount'),
            'pending_payments' => $paymentQuery->where('status', 'pending')->sum('amount'),
            'total_students' => $studentQuery->count(),
            'active_students' => $studentQuery->where('status', 'active')->count(),
            'total_uniforms' => $uniformQuery->count(),
            'pending_uniforms' => $uniformQuery->where('branch_status', 'pending')->count(),
            'total_programs' => Program::whereIn('academy_id', $academyQuery->pluck('id'))->count(),
            'active_programs' => Program::whereIn('academy_id', $academyQuery->pluck('id'))->where('status', true)->count(),
        ];
    }

    /**
     * Get recent activity for dashboard.
     */
    private function getRecentActivity(): array
    {
        $user = Auth::user();

        // Get recent payments
        $recentPayments = Payment::with(['student', 'branch', 'academy'])
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->latest()
            ->limit(5)
            ->get();

        // Get recent uniform orders
        $recentUniforms = Uniform::with(['student', 'branch', 'academy'])
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->latest()
            ->limit(5)
            ->get();

        // Get recent student registrations
        $recentStudents = Student::with(['branch', 'academy'])
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->latest()
            ->limit(5)
            ->get();

        return [
            'payments' => $recentPayments,
            'uniforms' => $recentUniforms,
            'students' => $recentStudents,
        ];
    }

    /**
     * Get filters from request.
     */
    private function getFilters(Request $request): array
    {
        return [
            'branch_id' => $request->get('branch_id'),
            'academy_id' => $request->get('academy_id'),
            'date_from' => $request->get('date_from', Carbon::now()->subMonth()->format('Y-m-d')),
            'date_to' => $request->get('date_to', Carbon::now()->format('Y-m-d')),
            'status' => $request->get('status'),
            'payment_method' => $request->get('payment_method'),
            'period' => $request->get('period', 'month'), // day, week, month, year
        ];
    }

    /**
     * Get financial data for reports.
     */
    private function getFinancialData(array $filters): array
    {
        $user = Auth::user();

        // Base payment query with role-based filtering
        $paymentQuery = Payment::with(['student', 'branch', 'academy'])
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->where('branch_id', $filters['branch_id']))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']))
            ->when($filters['date_from'], fn($q) => $q->whereDate('payment_date', '>=', $filters['date_from']))
            ->when($filters['date_to'], fn($q) => $q->whereDate('payment_date', '<=', $filters['date_to']))
            ->when($filters['status'], fn($q) => $q->where('status', $filters['status']))
            ->when($filters['payment_method'], fn($q) => $q->where('payment_method', $filters['payment_method']));

        // Summary statistics
        $totalRevenue = $paymentQuery->clone()->where('status', 'completed')->sum('amount');
        $totalDiscount = $paymentQuery->clone()->where('status', 'completed')->sum('discount');
        $pendingAmount = $paymentQuery->clone()->where('status', 'pending')->sum('amount');
        $totalPayments = $paymentQuery->clone()->count();

        // Payment method breakdown
        $paymentMethods = $paymentQuery->clone()
            ->where('status', 'completed')
            ->select('payment_method', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('payment_method')
            ->get();

        // Branch comparison
        $branchComparison = $paymentQuery->clone()
            ->where('status', 'completed')
            ->select('branch_id', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->with('branch:id,name')
            ->groupBy('branch_id')
            ->get();

        // Academy comparison
        $academyComparison = $paymentQuery->clone()
            ->where('status', 'completed')
            ->select('academy_id', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->with('academy:id,name')
            ->groupBy('academy_id')
            ->get();

        // Monthly trend (last 12 months)
        $monthlyTrend = $paymentQuery->clone()
            ->where('status', 'completed')
            ->where('payment_date', '>=', Carbon::now()->subYear())
            ->select(
                DB::raw('YEAR(payment_date) as year'),
                DB::raw('MONTH(payment_date) as month'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        // Recent payments
        $recentPayments = $paymentQuery->clone()
            ->latest('payment_date')
            ->limit(10)
            ->get();

        return [
            'summary' => [
                'total_revenue' => $totalRevenue,
                'total_discount' => $totalDiscount,
                'net_revenue' => $totalRevenue - $totalDiscount,
                'pending_amount' => $pendingAmount,
                'total_payments' => $totalPayments,
                'average_payment' => $totalPayments > 0 ? $totalRevenue / $totalPayments : 0,
            ],
            'payment_methods' => $paymentMethods,
            'branch_comparison' => $branchComparison,
            'academy_comparison' => $academyComparison,
            'monthly_trend' => $monthlyTrend,
            'recent_payments' => $recentPayments,
        ];
    }

    /**
     * Get uniform data for reports.
     */
    private function getUniformData(array $filters): array
    {
        $user = Auth::user();

        // Base uniform query with role-based filtering
        $uniformQuery = Uniform::with(['student', 'branch', 'academy'])
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->where('branch_id', $filters['branch_id']))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']))
            ->when($filters['date_from'], fn($q) => $q->whereDate('order_date', '>=', $filters['date_from']))
            ->when($filters['date_to'], fn($q) => $q->whereDate('order_date', '<=', $filters['date_to']));

        // Summary statistics
        $totalOrders = $uniformQuery->clone()->count();
        $totalAmount = $uniformQuery->clone()->sum('amount');
        $pendingBranch = $uniformQuery->clone()->where('branch_status', 'pending')->count();
        $pendingOffice = $uniformQuery->clone()->where('office_status', 'pending')->count();

        // Size breakdown
        $sizeBreakdown = $uniformQuery->clone()
            ->select('size', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('size')
            ->orderBy('count', 'desc')
            ->get();

        // Status breakdown
        $branchStatusBreakdown = $uniformQuery->clone()
            ->select('branch_status', DB::raw('COUNT(*) as count'))
            ->groupBy('branch_status')
            ->get();

        $officeStatusBreakdown = $uniformQuery->clone()
            ->select('office_status', DB::raw('COUNT(*) as count'))
            ->groupBy('office_status')
            ->get();

        // Branch comparison
        $branchComparison = $uniformQuery->clone()
            ->select('branch_id', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->with('branch:id,name')
            ->groupBy('branch_id')
            ->get();

        // Recent orders
        $recentOrders = $uniformQuery->clone()
            ->latest('order_date')
            ->limit(10)
            ->get();

        return [
            'summary' => [
                'total_orders' => $totalOrders,
                'total_amount' => $totalAmount,
                'pending_branch' => $pendingBranch,
                'pending_office' => $pendingOffice,
                'average_order' => $totalOrders > 0 ? $totalAmount / $totalOrders : 0,
            ],
            'size_breakdown' => $sizeBreakdown,
            'branch_status_breakdown' => $branchStatusBreakdown,
            'office_status_breakdown' => $officeStatusBreakdown,
            'branch_comparison' => $branchComparison,
            'recent_orders' => $recentOrders,
        ];
    }

    /**
     * Get program data for reports.
     */
    private function getProgramData(array $filters): array
    {
        $user = Auth::user();

        // Base program query with role-based filtering
        $programQuery = Program::with(['academy.branch'])
            ->when($user->role === 'branch_manager', fn($q) => $q->whereHas('academy', fn($sq) => $sq->where('branch_id', $user->branch_id)))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->whereHas('academy', fn($sq) => $sq->where('branch_id', $filters['branch_id'])))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']));

        // Summary statistics
        $totalPrograms = $programQuery->clone()->count();
        $activePrograms = $programQuery->clone()->where('status', true)->count();
        $totalCapacity = $programQuery->clone()->sum('max_students');
        $totalEnrolled = Student::whereIn('academy_id', $programQuery->clone()->pluck('academy_id'))->count();

        // Enrollment by program
        $programEnrollment = $programQuery->clone()
            ->withCount('students')
            ->get()
            ->map(function ($program) {
                return [
                    'id' => $program->id,
                    'name' => $program->name,
                    'academy_name' => $program->academy->name,
                    'branch_name' => $program->academy->branch->name,
                    'max_students' => $program->max_students,
                    'enrolled_students' => $program->students_count,
                    'enrollment_percentage' => $program->max_students > 0 ? ($program->students_count / $program->max_students) * 100 : 0,
                    'price' => $program->price,
                    'status' => $program->status,
                ];
            });

        // Popular programs (by enrollment)
        $popularPrograms = $programEnrollment->sortByDesc('enrolled_students')->take(10);

        // Revenue by program
        $programRevenue = $programQuery->clone()
            ->select('programs.*')
            ->leftJoin('payments', 'payments.academy_id', '=', 'programs.academy_id')
            ->where('payments.status', 'completed')
            ->selectRaw('programs.*, SUM(payments.amount) as total_revenue')
            ->groupBy('programs.id')
            ->orderByDesc('total_revenue')
            ->get();

        // Days breakdown
        $daysBreakdown = $programQuery->clone()
            ->get()
            ->flatMap(function ($program) {
                return $program->days ?? [];
            })
            ->countBy()
            ->map(function ($count, $day) {
                return ['day' => $day, 'count' => $count];
            })
            ->values();

        return [
            'summary' => [
                'total_programs' => $totalPrograms,
                'active_programs' => $activePrograms,
                'total_capacity' => $totalCapacity,
                'total_enrolled' => $totalEnrolled,
                'utilization_rate' => $totalCapacity > 0 ? ($totalEnrolled / $totalCapacity) * 100 : 0,
            ],
            'program_enrollment' => $programEnrollment,
            'popular_programs' => $popularPrograms,
            'program_revenue' => $programRevenue,
            'days_breakdown' => $daysBreakdown,
        ];
    }

    /**
     * Get status data for reports.
     */
    private function getStatusData(array $filters): array
    {
        $user = Auth::user();

        // Student status breakdown
        $studentQuery = Student::query()
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->where('branch_id', $filters['branch_id']))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']));

        $studentStatus = $studentQuery->clone()
            ->select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();

        // Payment status breakdown
        $paymentQuery = Payment::query()
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->where('branch_id', $filters['branch_id']))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']));

        $paymentStatus = $paymentQuery->clone()
            ->select('status', DB::raw('COUNT(*) as count'), DB::raw('SUM(amount) as total'))
            ->groupBy('status')
            ->get();

        // Uniform status breakdown
        $uniformQuery = Uniform::query()
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->where('branch_id', $filters['branch_id']))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']));

        $uniformBranchStatus = $uniformQuery->clone()
            ->select('branch_status', DB::raw('COUNT(*) as count'))
            ->groupBy('branch_status')
            ->get();

        $uniformOfficeStatus = $uniformQuery->clone()
            ->select('office_status', DB::raw('COUNT(*) as count'))
            ->groupBy('office_status')
            ->get();

        // Program status breakdown
        $programQuery = Program::query()
            ->when($user->role === 'branch_manager', fn($q) => $q->whereHas('academy', fn($sq) => $sq->where('branch_id', $user->branch_id)))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->whereHas('academy', fn($sq) => $sq->where('branch_id', $filters['branch_id'])))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']));

        $programStatus = $programQuery->clone()
            ->select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();

        return [
            'student_status' => $studentStatus,
            'payment_status' => $paymentStatus,
            'uniform_branch_status' => $uniformBranchStatus,
            'uniform_office_status' => $uniformOfficeStatus,
            'program_status' => $programStatus,
        ];
    }

    /**
     * Get daily data for reports.
     */
    private function getDailyData(array $filters): array
    {
        $user = Auth::user();
        $date = $filters['date_to'] ?? Carbon::now()->format('Y-m-d');

        // Daily registrations
        $dailyRegistrations = Student::query()
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->where('branch_id', $filters['branch_id']))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']))
            ->whereDate('created_at', $date)
            ->with(['branch', 'academy'])
            ->get();

        // Daily payments
        $dailyPayments = Payment::query()
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->where('branch_id', $filters['branch_id']))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']))
            ->whereDate('payment_date', $date)
            ->with(['student', 'branch', 'academy'])
            ->get();

        // Daily uniform orders
        $dailyUniforms = Uniform::query()
            ->when($user->role === 'branch_manager', fn($q) => $q->where('branch_id', $user->branch_id))
            ->when($user->role === 'academy_manager', fn($q) => $q->where('academy_id', $user->academy_id))
            ->when($filters['branch_id'], fn($q) => $q->where('branch_id', $filters['branch_id']))
            ->when($filters['academy_id'], fn($q) => $q->where('academy_id', $filters['academy_id']))
            ->whereDate('order_date', $date)
            ->with(['student', 'branch', 'academy'])
            ->get();

        // Daily summary
        $summary = [
            'total_registrations' => $dailyRegistrations->count(),
            'total_payments' => $dailyPayments->count(),
            'total_payment_amount' => $dailyPayments->sum('amount'),
            'total_uniforms' => $dailyUniforms->count(),
            'total_uniform_amount' => $dailyUniforms->sum('amount'),
        ];

        return [
            'summary' => $summary,
            'registrations' => $dailyRegistrations,
            'payments' => $dailyPayments,
            'uniforms' => $dailyUniforms,
            'date' => $date,
        ];
    }
}
