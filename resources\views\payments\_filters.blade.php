<!-- Advanced Search & Filters -->
<div class="bank-card bg-gradient-to-br from-white to-gray-50 border-2 border-gray-200">
    <div
        class="bank-card-header bg-gradient-to-r from-leaders-red to-leaders-deep-red text-white rounded-t-lg -mx-6 -mt-6 px-6 py-4 mb-6">
        <div>
            <h3 class="text-lg font-semibold text-white">Search & Filters</h3>
            <p class="text-red-100 text-sm">Find payments using advanced search criteria</p>
        </div>
        <button @click="showFilters = !showFilters"
            class="bg-white/20 hover:bg-white/30 text-white border border-white/30 px-4 py-2 rounded-lg transition-all duration-200 flex items-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z">
                </path>
            </svg>
            <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
        </button>
    </div>

    <div x-show="showFilters" x-transition class="space-y-6">
        <form method="GET" action="{{ route('payments.index') }}" class="space-y-6">
            <!-- Search Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-dark-gray mb-2">Search</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                        <input type="text" name="search" id="search" value="{{ request('search') }}"
                            placeholder="Search by reference number, student name, description..."
                            class="form-input pl-10">
                    </div>
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-dark-gray mb-2">Status</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Completed
                        </option>
                        <option value="pending" {{ request('status') === 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="failed" {{ request('status') === 'failed' ? 'selected' : '' }}>Failed</option>
                        <option value="refunded" {{ request('status') === 'refunded' ? 'selected' : '' }}>Refunded
                        </option>
                        <option value="cancelled" {{ request('status') === 'cancelled' ? 'selected' : '' }}>Cancelled
                        </option>
                    </select>
                </div>
            </div>

            <!-- Branch & Academy Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="branch_id" class="block text-sm font-medium text-dark-gray mb-2">Branch</label>
                    <select name="branch_id" id="branch_id" class="form-select" onchange="loadAcademies(this.value)">
                        <option value="">All Branches</option>
                        @foreach ($branches as $branch)
                            <option value="{{ $branch->id }}"
                                {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="academy_id" class="block text-sm font-medium text-dark-gray mb-2">Academy</label>
                    <select name="academy_id" id="academy_id" class="form-select">
                        <option value="">All Academies</option>
                        @foreach ($academies as $academy)
                            <option value="{{ $academy->id }}"
                                {{ request('academy_id') == $academy->id ? 'selected' : '' }}>
                                {{ $academy->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Payment Method, Type & Amount Range Row -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="payment_method" class="block text-sm font-medium text-dark-gray mb-2">Payment
                        Method</label>
                    <select name="payment_method" id="payment_method" class="form-select">
                        <option value="">All Methods</option>
                        <option value="cash" {{ request('payment_method') === 'cash' ? 'selected' : '' }}>Cash
                        </option>
                        <option value="card" {{ request('payment_method') === 'card' ? 'selected' : '' }}>
                            Credit/Debit Card</option>
                        <option value="bank_transfer"
                            {{ request('payment_method') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer
                        </option>
                        <option value="online" {{ request('payment_method') === 'online' ? 'selected' : '' }}>Online
                            Payment</option>
                        <option value="cheque" {{ request('payment_method') === 'cheque' ? 'selected' : '' }}>Cheque
                        </option>
                    </select>
                </div>
                <div>
                    <label for="payment_type" class="block text-sm font-medium text-dark-gray mb-2">Payment Type</label>
                    <select name="payment_type" id="payment_type" class="form-select">
                        <option value="">All Types</option>
                        <option value="new_entry" {{ request('payment_type') === 'new_entry' ? 'selected' : '' }}>New
                            Entry (طالب جديد)</option>
                        <option value="renewal" {{ request('payment_type') === 'renewal' ? 'selected' : '' }}>Renewal
                            (تجديد)</option>
                        <option value="regular" {{ request('payment_type') === 'regular' ? 'selected' : '' }}>Regular
                            (دفعة عادية)</option>
                    </select>
                </div>
                <div>
                    <label for="min_amount" class="block text-sm font-medium text-dark-gray mb-2">Min Amount
                        (AED)</label>
                    <input type="number" name="min_amount" id="min_amount" value="{{ request('min_amount') }}"
                        placeholder="0.00" step="0.01" min="0" class="form-input">
                </div>
                <div>
                    <label for="max_amount" class="block text-sm font-medium text-dark-gray mb-2">Max Amount
                        (AED)</label>
                    <input type="number" name="max_amount" id="max_amount" value="{{ request('max_amount') }}"
                        placeholder="999999.99" step="0.01" min="0" class="form-input">
                </div>
            </div>

            <!-- Date Range Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="start_date" class="block text-sm font-medium text-dark-gray mb-2">Payment Date
                        From</label>
                    <input type="date" name="start_date" id="start_date" value="{{ request('start_date') }}"
                        class="form-input">
                </div>
                <div>
                    <label for="end_date" class="block text-sm font-medium text-dark-gray mb-2">Payment Date
                        To</label>
                    <input type="date" name="end_date" id="end_date" value="{{ request('end_date') }}"
                        class="form-input">
                </div>
            </div>

            <!-- Sorting & Per Page Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label for="sort_by" class="block text-sm font-medium text-dark-gray mb-2">Sort By</label>
                    <select name="sort_by" id="sort_by" class="form-select">
                        <option value="created_at" {{ request('sort_by') === 'created_at' ? 'selected' : '' }}>Created
                            Date</option>
                        <option value="payment_date" {{ request('sort_by') === 'payment_date' ? 'selected' : '' }}>
                            Payment Date</option>
                        <option value="amount" {{ request('sort_by') === 'amount' ? 'selected' : '' }}>Amount</option>
                        <option value="status" {{ request('sort_by') === 'status' ? 'selected' : '' }}>Status</option>
                        <option value="reference_number"
                            {{ request('sort_by') === 'reference_number' ? 'selected' : '' }}>Reference Number</option>
                    </select>
                </div>
                <div>
                    <label for="sort_order" class="block text-sm font-medium text-dark-gray mb-2">Sort Order</label>
                    <select name="sort_order" id="sort_order" class="form-select">
                        <option value="desc" {{ request('sort_order') === 'desc' ? 'selected' : '' }}>Descending
                        </option>
                        <option value="asc" {{ request('sort_order') === 'asc' ? 'selected' : '' }}>Ascending
                        </option>
                    </select>
                </div>
                <div>
                    <label for="per_page" class="block text-sm font-medium text-dark-gray mb-2">Per Page</label>
                    <select name="per_page" id="per_page" class="form-select">
                        <option value="15" {{ request('per_page') == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-4 border-t border-light-gray">
                <div class="flex items-center space-x-3">
                    <button type="submit" class="btn-bank">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search Payments
                    </button>
                    <a href="{{ route('payments.index') }}" class="btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                            </path>
                        </svg>
                        Reset Filters
                    </a>
                </div>
                <div class="text-sm text-dark-gray">
                    Showing {{ $payments->firstItem() ?? 0 }} to {{ $payments->lastItem() ?? 0 }} of
                    {{ $payments->total() }} results
                </div>
            </div>
        </form>
    </div>
</div>

@push('styles')
    <style>
        /* Enhanced Filter Styling */
        .filter-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: #1a202c !important;
            margin-bottom: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .filter-input,
        .filter-select {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            color: #1a202c !important;
            background-color: #ffffff;
            transition: all 0.2s ease-in-out;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .filter-input:focus,
        .filter-select:focus {
            outline: none;
            border-color: #e53e3e;
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
            background-color: #ffffff;
        }

        .filter-input::placeholder {
            color: #a0aec0;
        }

        /* Dark gray text override */
        .text-dark-gray {
            color: #1a202c !important;
        }

        /* Form input and select overrides */
        .form-input,
        .form-select {
            color: #1a202c !important;
            background-color: #ffffff !important;
            border: 2px solid #e2e8f0 !important;
            padding: 0.875rem 1rem !important;
            border-radius: 0.5rem !important;
            font-size: 0.875rem !important;
            transition: all 0.2s ease-in-out !important;
        }

        .form-input:focus,
        .form-select:focus {
            border-color: #e53e3e !important;
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1) !important;
            outline: none !important;
        }

        .form-input::placeholder {
            color: #a0aec0 !important;
        }

        /* Enhanced button styling */
        .btn-bank {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%) !important;
            color: white !important;
            border: none !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 0.5rem !important;
            font-weight: 600 !important;
            transition: all 0.2s ease-in-out !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            text-decoration: none !important;
        }

        .btn-bank:hover {
            background: linear-gradient(135deg, #c53030 0%, #9b2c2c 100%) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3) !important;
            color: white !important;
            text-decoration: none !important;
        }

        .btn-bank-outline {
            background: transparent !important;
            color: #e53e3e !important;
            border: 2px solid #e53e3e !important;
            padding: 0.75rem 1.5rem !important;
            border-radius: 0.5rem !important;
            font-weight: 600 !important;
            transition: all 0.2s ease-in-out !important;
            display: inline-flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            text-decoration: none !important;
        }

        .btn-bank-outline:hover {
            background: #e53e3e !important;
            color: white !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3) !important;
            text-decoration: none !important;
        }

        /* Enhanced border and spacing */
        .border-light-gray {
            border-color: #e2e8f0 !important;
        }

        /* Results text styling */
        .text-dark-gray {
            color: #4a5568 !important;
            font-weight: 500 !important;
        }

        /* Grid gap improvements */
        .gap-4 {
            gap: 1.5rem !important;
        }

        .gap-6 {
            gap: 2rem !important;
        }

        /* Enhanced card styling */
        .bank-card {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
            border: 1px solid #e2e8f0 !important;
        }
    </style>
@endpush

@push('scripts')
    <script>
        async function loadAcademies(branchId) {
            const academySelect = document.getElementById('academy_id');

            // Clear current options except "All Academies"
            academySelect.innerHTML = '<option value="">All Academies</option>';

            if (!branchId) return;

            try {
                const response = await fetch(`{{ route('payments.academies-by-branch') }}?branch_id=${branchId}`);
                const data = await response.json();

                data.academies.forEach(academy => {
                    const option = document.createElement('option');
                    option.value = academy.id;
                    option.textContent = academy.name;
                    academySelect.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading academies:', error);
            }
        }
    </script>
@endpush
