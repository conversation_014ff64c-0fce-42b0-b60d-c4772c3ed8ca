@extends('layouts.dashboard')

@section('title', __('Add Inventory Item'))

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ __('Add Inventory Item') }}</h1>
                <p class="text-lg text-dark-gray">{{ __('Create a new uniform inventory item') }}</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('inventory.index') }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                {{ __('Back to Inventory') }}
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="inventoryCreateForm()" x-init="init()">

        <!-- Create Form -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Item Information') }}</h3>
                    </div>
                    <div class="bank-card-body">
                        <form method="POST" action="{{ route('inventory.store') }}" class="space-y-6">
                            @csrf

                            <!-- Basic Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="branch_id" class="form-label">{{ __('Branch') }} <span
                                            class="text-red-500">*</span></label>
                                    <select class="form-select @error('branch_id') border-red-500 @enderror" id="branch_id"
                                        name="branch_id" required>
                                        <option value="">{{ __('Select Branch') }}</option>
                                        @foreach ($branches as $branch)
                                            <option value="{{ $branch->id }}"
                                                {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('branch_id')
                                        <div class="form-error">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div>
                                    <label for="academy_id" class="form-label">{{ __('Academy') }} <span
                                            class="text-red-500">*</span></label>
                                    <select class="form-select @error('academy_id') border-red-500 @enderror"
                                        id="academy_id" name="academy_id" required>
                                        <option value="">{{ __('Select Academy') }}</option>
                                        @foreach ($academies as $academy)
                                            <option value="{{ $academy->id }}" data-branch="{{ $academy->branch_id }}"
                                                {{ old('academy_id') == $academy->id ? 'selected' : '' }}>
                                                {{ $academy->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('academy_id')
                                        <div class="form-error">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Quick Actions for Categories and Suppliers -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                <h6 class="flex items-center text-blue-800 font-medium mb-3">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                    </svg>
                                    {{ __('Quick Actions') }}
                                </h6>
                                <div class="flex flex-wrap gap-2">
                                    <button type="button" @click="$dispatch('open-modal', 'create-category')"
                                        class="btn-bank-sm">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Add Category
                                    </button>
                                    <button type="button" @click="$dispatch('open-modal', 'create-supplier')"
                                        class="btn-bank-sm">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Add Supplier
                                    </button>
                                    <button type="button" @click="showExistingItems = !showExistingItems"
                                        class="btn-bank-outline-sm">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                        </svg>
                                        Browse Existing Items
                                    </button>
                                </div>
                            </div>

                            <!-- Existing Items Browser -->
                            <div x-show="showExistingItems" x-transition
                                class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
                                <h6 class="text-gray-800 font-medium mb-3">{{ __('Select from Existing Items') }}</h6>
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-60 overflow-y-auto">
                                    <!-- This will be populated via AJAX -->
                                    <div id="existing-items-list" class="col-span-full">
                                        <div class="text-center py-4">
                                            <button type="button" @click="loadExistingItems()"
                                                class="btn-bank-outline-sm">
                                                Load Existing Items
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="uniform_category_id" class="form-label">{{ __('Category') }} <span
                                            class="text-danger">*</span></label>
                                    <div class="flex items-center space-x-2">
                                        <select
                                            class="form-select @error('uniform_category_id') is-invalid @enderror flex-1"
                                            id="uniform_category_id" name="uniform_category_id" required>
                                            <option value="">{{ __('Select Category') }}</option>
                                            @foreach ($categories as $category)
                                                <option value="{{ $category->id }}"
                                                    {{ old('uniform_category_id') == $category->id ? 'selected' : '' }}>
                                                    {{ $category->localized_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <button type="button" @click="refreshCategories()" class="btn-bank-outline-sm"
                                            title="Refresh Categories">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                    @error('uniform_category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="uniform_supplier_id" class="form-label">{{ __('Supplier') }}</label>
                                    <div class="flex items-center space-x-2">
                                        <select
                                            class="form-select @error('uniform_supplier_id') is-invalid @enderror flex-1"
                                            id="uniform_supplier_id" name="uniform_supplier_id">
                                            <option value="">{{ __('Select Supplier') }}</option>
                                            @foreach ($suppliers as $supplier)
                                                <option value="{{ $supplier->id }}"
                                                    {{ old('uniform_supplier_id') == $supplier->id ? 'selected' : '' }}>
                                                    {{ $supplier->localized_name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        <button type="button" @click="refreshSuppliers()" class="btn-bank-outline-sm"
                                            title="Refresh Suppliers">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                                </path>
                                            </svg>
                                        </button>
                                    </div>
                                    @error('uniform_supplier_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">{{ __('Item Name') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                        id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="name_ar" class="form-label">{{ __('Item Name (Arabic)') }}</label>
                                    <input type="text" class="form-control @error('name_ar') is-invalid @enderror"
                                        id="name_ar" name="name_ar" value="{{ old('name_ar') }}">
                                    @error('name_ar')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Product Details -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="size" class="form-label">{{ __('Size') }} <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select @error('size') is-invalid @enderror" id="size"
                                        name="size" required>
                                        <option value="">{{ __('Select Size') }}</option>
                                        <option value="XS" {{ old('size') == 'XS' ? 'selected' : '' }}>XS</option>
                                        <option value="S" {{ old('size') == 'S' ? 'selected' : '' }}>S</option>
                                        <option value="M" {{ old('size') == 'M' ? 'selected' : '' }}>M</option>
                                        <option value="L" {{ old('size') == 'L' ? 'selected' : '' }}>L</option>
                                        <option value="XL" {{ old('size') == 'XL' ? 'selected' : '' }}>XL</option>
                                        <option value="XXL" {{ old('size') == 'XXL' ? 'selected' : '' }}>XXL</option>
                                    </select>
                                    @error('size')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="color" class="form-label">{{ __('Color') }}</label>
                                    <input type="text" class="form-control @error('color') is-invalid @enderror"
                                        id="color" name="color" value="{{ old('color') }}">
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="brand" class="form-label">{{ __('Brand') }}</label>
                                    <input type="text" class="form-control @error('brand') is-invalid @enderror"
                                        id="brand" name="brand" value="{{ old('brand', 'UAE Sports Academy') }}">
                                    @error('brand')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Stock Information -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="current_stock" class="form-label">{{ __('Current Stock') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number"
                                        class="form-control @error('current_stock') is-invalid @enderror"
                                        id="current_stock" name="current_stock" value="{{ old('current_stock', 0) }}"
                                        min="0" required>
                                    @error('current_stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-3">
                                    <label for="minimum_stock" class="form-label">{{ __('Minimum Stock') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number"
                                        class="form-control @error('minimum_stock') is-invalid @enderror"
                                        id="minimum_stock" name="minimum_stock" value="{{ old('minimum_stock', 10) }}"
                                        min="0" required>
                                    @error('minimum_stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-3">
                                    <label for="maximum_stock" class="form-label">{{ __('Maximum Stock') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number"
                                        class="form-control @error('maximum_stock') is-invalid @enderror"
                                        id="maximum_stock" name="maximum_stock" value="{{ old('maximum_stock', 100) }}"
                                        min="1" required>
                                    @error('maximum_stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-3">
                                    <label for="reorder_quantity" class="form-label">{{ __('Reorder Quantity') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number"
                                        class="form-control @error('reorder_quantity') is-invalid @enderror"
                                        id="reorder_quantity" name="reorder_quantity"
                                        value="{{ old('reorder_quantity', 50) }}" min="1" required>
                                    @error('reorder_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Pricing -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="cost_price" class="form-label">{{ __('Cost Price (AED)') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number" step="0.01"
                                        class="form-control @error('cost_price') is-invalid @enderror" id="cost_price"
                                        name="cost_price" value="{{ old('cost_price') }}" min="0" required>
                                    @error('cost_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="selling_price" class="form-label">{{ __('Selling Price (AED)') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number" step="0.01"
                                        class="form-control @error('selling_price') is-invalid @enderror"
                                        id="selling_price" name="selling_price" value="{{ old('selling_price') }}"
                                        min="0" required>
                                    @error('selling_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Location -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="location" class="form-label">{{ __('Location') }}</label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror"
                                        id="location" name="location" value="{{ old('location', 'Warehouse A') }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="shelf" class="form-label">{{ __('Shelf') }}</label>
                                    <input type="text" class="form-control @error('shelf') is-invalid @enderror"
                                        id="shelf" name="shelf" value="{{ old('shelf') }}">
                                    @error('shelf')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="bin" class="form-label">{{ __('Bin') }}</label>
                                    <input type="text" class="form-control @error('bin') is-invalid @enderror"
                                        id="bin" name="bin" value="{{ old('bin') }}">
                                    @error('bin')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label for="notes" class="form-label">{{ __('Notes') }}</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes') }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="flex items-center space-x-4 pt-6 border-t border-light-gray">
                                <button type="submit" class="btn-bank">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12">
                                        </path>
                                    </svg>
                                    {{ __('Create Inventory Item') }}
                                </button>
                                <a href="{{ route('inventory.index') }}" class="btn-bank-outline">
                                    {{ __('Cancel') }}
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Help Panel -->
            <div>
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Help & Tips') }}</h3>
                    </div>
                    <div class="bank-card-body space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h6 class="flex items-center text-blue-800 font-medium mb-2">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ __('Quick Tips') }}
                            </h6>
                            <ul class="text-blue-700 space-y-1 text-sm">
                                <li>• {{ __('SKU will be auto-generated if not provided') }}</li>
                                <li>• {{ __('Set minimum stock to get low stock alerts') }}</li>
                                <li>• {{ __('Reorder quantity suggests how much to purchase') }}</li>
                                <li>• {{ __('Location helps track where items are stored') }}</li>
                            </ul>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h6 class="flex items-center text-yellow-800 font-medium mb-2">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                                    </path>
                                </svg>
                                {{ __('Important') }}
                            </h6>
                            <p class="text-yellow-700 text-sm">
                                {{ __('Make sure the branch and academy combination is correct as this cannot be changed later.') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            // Enhanced inventory form functionality
            function inventoryCreateForm() {
                return {
                    showExistingItems: false,
                    existingItems: [],

                    init() {
                        this.initBranchFilter();
                        this.initPriceCalculation();
                    },

                    initBranchFilter() {
                        // Filter academies based on selected branch
                        document.getElementById('branch_id').addEventListener('change', function() {
                            const branchId = this.value;
                            const academySelect = document.getElementById('academy_id');
                            const academyOptions = academySelect.querySelectorAll('option');

                            academyOptions.forEach(option => {
                                if (option.value === '') {
                                    option.style.display = 'block';
                                    return;
                                }

                                const optionBranchId = option.getAttribute('data-branch');
                                if (branchId === '' || optionBranchId === branchId) {
                                    option.style.display = 'block';
                                } else {
                                    option.style.display = 'none';
                                }
                            });

                            // Reset academy selection if current selection is not valid for new branch
                            const currentAcademyOption = academySelect.querySelector(
                                `option[value="${academySelect.value}"]`);
                            if (currentAcademyOption && currentAcademyOption.style.display === 'none') {
                                academySelect.value = '';
                            }
                        });
                    },

                    initPriceCalculation() {
                        // Auto-calculate selling price based on cost price (50% markup)
                        document.getElementById('cost_price').addEventListener('input', function() {
                            const costPrice = parseFloat(this.value) || 0;
                            const sellingPriceField = document.getElementById('selling_price');

                            if (costPrice > 0 && sellingPriceField.value === '') {
                                const suggestedPrice = (costPrice * 1.5).toFixed(2);
                                sellingPriceField.value = suggestedPrice;
                            }
                        });
                    },

                    async loadExistingItems() {
                        try {
                            const response = await fetch('/api/inventory/items');
                            const data = await response.json();

                            if (data.success) {
                                this.displayExistingItems(data.items);
                            } else {
                                showNotification('error', 'Failed to load existing items');
                            }
                        } catch (error) {
                            showNotification('error', 'Error loading existing items');
                        }
                    },

                    displayExistingItems(items) {
                        const container = document.getElementById('existing-items-list');
                        if (items.length === 0) {
                            container.innerHTML =
                                '<div class="col-span-full text-center py-4 text-gray-500">No existing items found</div>';
                            return;
                        }

                        const itemsHtml = items.map(item => `
                            <div class="bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:bg-gray-50 transition-colors"
                                 onclick="selectExistingItem(${item.id})">
                                <div class="font-medium text-sm">${item.name}</div>
                                <div class="text-xs text-gray-500">${item.sku} - ${item.size}</div>
                                <div class="text-xs text-gray-600">Stock: ${item.current_stock}</div>
                                <div class="text-xs text-green-600">AED ${item.selling_price}</div>
                            </div>
                        `).join('');

                        container.innerHTML = itemsHtml;
                    },

                    async refreshCategories() {
                        try {
                            const response = await fetch('/api/categories');
                            const data = await response.json();

                            if (data.success) {
                                const select = document.getElementById('uniform_category_id');
                                const currentValue = select.value;

                                // Clear existing options except the first one
                                select.innerHTML = '<option value="">{{ __('Select Category') }}</option>';

                                // Add new options
                                data.categories.forEach(category => {
                                    const option = new Option(category.localized_name, category.id);
                                    if (category.id == currentValue) {
                                        option.selected = true;
                                    }
                                    select.appendChild(option);
                                });

                                showNotification('success', 'Categories refreshed');
                            }
                        } catch (error) {
                            showNotification('error', 'Error refreshing categories');
                        }
                    },

                    async refreshSuppliers() {
                        try {
                            const response = await fetch('/api/suppliers');
                            const data = await response.json();

                            if (data.success) {
                                const select = document.getElementById('uniform_supplier_id');
                                const currentValue = select.value;

                                // Clear existing options except the first one
                                select.innerHTML = '<option value="">{{ __('Select Supplier') }}</option>';

                                // Add new options
                                data.suppliers.forEach(supplier => {
                                    const option = new Option(supplier.localized_name, supplier.id);
                                    if (supplier.id == currentValue) {
                                        option.selected = true;
                                    }
                                    select.appendChild(option);
                                });

                                showNotification('success', 'Suppliers refreshed');
                            }
                        } catch (error) {
                            showNotification('error', 'Error refreshing suppliers');
                        }
                    }
                }
            }

            // Function to select an existing item and populate form
            async function selectExistingItem(itemId) {
                try {
                    const response = await fetch(`/api/inventory/items/${itemId}`);
                    const data = await response.json();

                    if (data.success) {
                        const item = data.item;

                        // Populate form fields with existing item data
                        document.getElementById('branch_id').value = item.branch_id;
                        document.getElementById('academy_id').value = item.academy_id;
                        document.getElementById('uniform_category_id').value = item.uniform_category_id;
                        document.getElementById('uniform_supplier_id').value = item.uniform_supplier_id || '';
                        document.getElementById('name').value = item.name;
                        document.getElementById('name_ar').value = item.name_ar || '';
                        document.getElementById('size').value = item.size;
                        document.getElementById('color').value = item.color || '';
                        document.getElementById('brand').value = item.brand || '';
                        document.getElementById('cost_price').value = item.cost_price;
                        document.getElementById('selling_price').value = item.selling_price;
                        document.getElementById('location').value = item.location || '';
                        document.getElementById('shelf').value = item.shelf || '';
                        document.getElementById('bin').value = item.bin || '';

                        // Reset stock fields to 0 for new entry
                        document.getElementById('current_stock').value = 0;
                        document.getElementById('minimum_stock').value = item.minimum_stock;
                        document.getElementById('maximum_stock').value = item.maximum_stock;
                        document.getElementById('reorder_quantity').value = item.reorder_quantity;

                        // Trigger branch change to filter academies
                        document.getElementById('branch_id').dispatchEvent(new Event('change'));

                        showNotification('success', 'Item details populated from existing item');

                        // Hide existing items panel
                        Alpine.store('inventoryForm').showExistingItems = false;
                    }
                } catch (error) {
                    showNotification('error', 'Error loading item details');
                }
            }

            // Initialize form on page load
            document.addEventListener('DOMContentLoaded', function() {
                const branchSelect = document.getElementById('branch_id');
                if (branchSelect.value) {
                    branchSelect.dispatchEvent(new Event('change'));
                }
            });
        </script>
    @endpush
@endsection
