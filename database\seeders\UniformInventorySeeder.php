<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\UniformCategory;
use App\Models\UniformSupplier;
use App\Models\Branch;
use App\Models\Academy;

class UniformInventorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Uniform Categories
        $categories = [
            [
                'name' => 'Jersey',
                'name_ar' => 'قميص',
                'code' => 'JER',
                'description' => 'Sports jerseys and shirts',
                'description_ar' => 'قمصان رياضية',
                'sort_order' => 1,
            ],
            [
                'name' => 'Shorts',
                'name_ar' => 'شورت',
                'code' => 'SHO',
                'description' => 'Sports shorts and pants',
                'description_ar' => 'شورتات رياضية',
                'sort_order' => 2,
            ],
            [
                'name' => 'Socks',
                'name_ar' => 'جوارب',
                'code' => 'SOC',
                'description' => 'Sports socks',
                'description_ar' => 'جوارب رياضية',
                'sort_order' => 3,
            ],
            [
                'name' => 'Tracksuit',
                'name_ar' => 'بدلة رياضية',
                'code' => 'TRA',
                'description' => 'Complete tracksuit sets',
                'description_ar' => 'بدلات رياضية كاملة',
                'sort_order' => 4,
            ],
            [
                'name' => 'Jacket',
                'name_ar' => 'جاكيت',
                'code' => 'JAC',
                'description' => 'Sports jackets and hoodies',
                'description_ar' => 'جاكيتات رياضية',
                'sort_order' => 5,
            ],
            [
                'name' => 'Cap',
                'name_ar' => 'قبعة',
                'code' => 'CAP',
                'description' => 'Sports caps and hats',
                'description_ar' => 'قبعات رياضية',
                'sort_order' => 6,
            ],
            [
                'name' => 'Sports Bag',
                'name_ar' => 'حقيبة رياضية',
                'code' => 'BAG',
                'description' => 'Sports bags and backpacks',
                'description_ar' => 'حقائب رياضية',
                'sort_order' => 7,
            ],
            [
                'name' => 'Complete Set',
                'name_ar' => 'طقم كامل',
                'code' => 'SET',
                'description' => 'Complete uniform sets',
                'description_ar' => 'أطقم كاملة',
                'sort_order' => 8,
            ],
        ];

        foreach ($categories as $category) {
            UniformCategory::firstOrCreate(['code' => $category['code']], $category);
        }

        // Create Uniform Suppliers
        $suppliers = [
            [
                'name' => 'Emirates Sports Gear LLC',
                'name_ar' => 'شركة الإمارات للمعدات الرياضية',
                'code' => 'SUP001',
                'contact_person' => 'Ahmed Al Mansouri',
                'email' => '<EMAIL>',
                'phone' => '+971501234567',
                'address' => 'Dubai Industrial City, Dubai',
                'address_ar' => 'مدينة دبي الصناعية، دبي',
                'city' => 'Dubai',
                'country' => 'UAE',
                'tax_number' => '100123456700003',
                'trade_license' => 'DIC-123456',
                'payment_terms' => 'net_30',
                'credit_limit' => 50000.00,
                'lead_time_days' => 14,
                'minimum_order_amount' => 1000.00,
                'rating' => 4.5,
                'notes' => 'Reliable supplier with good quality products',
            ],
            [
                'name' => 'Gulf Athletic Wear Trading',
                'name_ar' => 'تجارة الملابس الرياضية الخليجية',
                'code' => 'SUP002',
                'contact_person' => 'Fatima Al Zahra',
                'email' => '<EMAIL>',
                'phone' => '+971507654321',
                'address' => 'Sharjah Industrial Area, Sharjah',
                'address_ar' => 'المنطقة الصناعية، الشارقة',
                'city' => 'Sharjah',
                'country' => 'UAE',
                'tax_number' => '100987654300003',
                'trade_license' => 'SIA-789012',
                'payment_terms' => 'net_15',
                'credit_limit' => 30000.00,
                'lead_time_days' => 10,
                'minimum_order_amount' => 500.00,
                'rating' => 4.2,
                'notes' => 'Fast delivery and competitive prices',
            ],
            [
                'name' => 'Al Noor Sports Equipment',
                'name_ar' => 'معدات النور الرياضية',
                'code' => 'SUP003',
                'contact_person' => 'Mohammed Al Rashid',
                'email' => '<EMAIL>',
                'phone' => '+971509876543',
                'address' => 'Abu Dhabi Industrial Zone, Abu Dhabi',
                'address_ar' => 'المنطقة الصناعية، أبوظبي',
                'city' => 'Abu Dhabi',
                'country' => 'UAE',
                'tax_number' => '100456789100003',
                'trade_license' => 'ADIZ-345678',
                'payment_terms' => 'net_45',
                'credit_limit' => 75000.00,
                'lead_time_days' => 21,
                'minimum_order_amount' => 2000.00,
                'rating' => 4.8,
                'notes' => 'Premium quality supplier, higher prices but excellent quality',
            ],
        ];

        foreach ($suppliers as $supplier) {
            UniformSupplier::firstOrCreate(['code' => $supplier['code']], $supplier);
        }

        // Get branches and academies for inventory items
        $branches = Branch::all();
        $academies = Academy::all();
        $categories = UniformCategory::all();
        $suppliers = UniformSupplier::all();

        if ($branches->isEmpty() || $academies->isEmpty()) {
            $this->command->info('No branches or academies found. Skipping inventory item creation.');
            return;
        }

        // Create sample inventory items
        $sizes = ['XS', 'S', 'M', 'L', 'XL', 'XXL'];
        $colors = [
            ['name' => 'Red', 'name_ar' => 'أحمر'],
            ['name' => 'Blue', 'name_ar' => 'أزرق'],
            ['name' => 'White', 'name_ar' => 'أبيض'],
            ['name' => 'Black', 'name_ar' => 'أسود'],
            ['name' => 'Green', 'name_ar' => 'أخضر'],
        ];

        $inventoryItems = [];
        $skuCounter = 1;

        foreach ($branches as $branch) {
            foreach ($academies->where('branch_id', $branch->id) as $academy) {
                foreach ($categories as $category) {
                    foreach ($sizes as $size) {
                        foreach ($colors as $color) {
                            $supplier = $suppliers->random();
                            $costPrice = rand(25, 100);
                            $sellingPrice = $costPrice * 1.5; // 50% markup

                            $inventoryItems[] = [
                                'branch_id' => $branch->id,
                                'academy_id' => $academy->id,
                                'uniform_category_id' => $category->id,
                                'uniform_supplier_id' => $supplier->id,
                                'sku' => $category->code . '-' . $size . '-' . substr($color['name'], 0, 2) . '-' . str_pad($skuCounter++, 3, '0', STR_PAD_LEFT),
                                'name' => $category->name . ' - ' . $size . ' - ' . $color['name'],
                                'name_ar' => $category->name_ar . ' - ' . $size . ' - ' . $color['name_ar'],
                                'description' => 'High quality ' . strtolower($category->name) . ' in ' . strtolower($color['name']) . ' color',
                                'description_ar' => $category->description_ar . ' عالية الجودة باللون ' . $color['name_ar'],
                                'size' => $size,
                                'color' => $color['name'],
                                'color_ar' => $color['name_ar'],
                                'material' => 'Polyester',
                                'brand' => 'UAE Sports Academy',
                                'current_stock' => rand(10, 100),
                                'reserved_stock' => 0,
                                'minimum_stock' => 10,
                                'maximum_stock' => 200,
                                'reorder_quantity' => 50,
                                'cost_price' => $costPrice,
                                'selling_price' => $sellingPrice,
                                'markup_percentage' => 50.0,
                                'currency' => 'AED',
                                'location' => 'Warehouse A',
                                'shelf' => 'A' . rand(1, 10),
                                'bin' => 'B' . rand(1, 20),
                                'status' => 'active',
                                'is_trackable' => true,
                                'allow_backorder' => false,
                                'supplier_sku' => 'SUP-' . $category->code . '-' . $size . '-' . rand(1000, 9999),
                                'supplier_price' => $costPrice * 0.8, // Supplier gives us 20% discount
                                'supplier_lead_time' => $supplier->lead_time_days,
                                'supplier_minimum_order' => 10,
                                'notes' => 'Standard academy uniform item',
                                'notes_ar' => 'عنصر الزي المدرسي القياسي',
                                'created_at' => now(),
                                'updated_at' => now(),
                            ];
                        }
                    }
                }
            }
        }

        // Calculate available stock for each item
        foreach ($inventoryItems as &$item) {
            $item['available_stock'] = $item['current_stock'] - $item['reserved_stock'];
        }

        // Insert inventory items in chunks to avoid memory issues
        $chunks = array_chunk($inventoryItems, 100);
        foreach ($chunks as $chunk) {
            DB::table('uniform_inventory')->insert($chunk);
        }

        $this->command->info('Uniform inventory system seeded successfully!');
        $this->command->info('Created:');
        $this->command->info('- ' . count($categories) . ' uniform categories');
        $this->command->info('- ' . count($suppliers) . ' suppliers');
        $this->command->info('- ' . count($inventoryItems) . ' inventory items');
    }
}
