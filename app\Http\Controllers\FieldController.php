<?php

namespace App\Http\Controllers;

use App\Models\Field;
use App\Models\Venue;
use App\Models\Reservation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class FieldController extends Controller
{
    /**
     * Display a listing of fields with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Field::class);

        $query = Field::with(['venue', 'reservations'])
            ->withCount(['reservations']);

        // Search filter
        if ($request->filled('search')) {
            $query->search($request->get('search'));
        }

        // Venue filter
        if ($request->filled('venue_id')) {
            $query->where('venue_id', $request->get('venue_id'));
        }

        // Type filter
        if ($request->filled('type')) {
            $query->where('type', $request->get('type'));
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->get('status') === 'active') {
                $query->where('status', true);
            } else {
                $query->where('status', false);
            }
        }

        // Surface type filter
        if ($request->filled('surface_type')) {
            $query->where('surface_type', $request->get('surface_type'));
        }

        // Availability filter
        if ($request->filled('available_now')) {
            $query->where('status', true);
            // Additional logic for current availability can be added here
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        $fields = $query->paginate(15)->withQueryString();

        // Get venues for filter dropdown
        $venues = Venue::active()->orderBy('name')->get();

        if ($request->ajax()) {
            return response()->json([
                'fields' => $fields,
                'html' => view('fields.partials.table', compact('fields'))->render()
            ]);
        }

        return view('fields.index', compact('fields', 'venues'));
    }

    /**
     * Show the form for creating a new field.
     */
    public function create(Request $request): View
    {
        Gate::authorize('create', Field::class);

        $venues = Venue::active()->orderBy('name')->get();

        // Get selected venue if venue_id is provided
        $selectedVenue = null;
        if ($request->filled('venue_id')) {
            $selectedVenue = Venue::find($request->get('venue_id'));
        }

        return view('fields.create', compact('venues', 'selectedVenue'));
    }

    /**
     * Store a newly created field in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Field::class);

        $validator = Validator::make($request->all(), [
            'venue_id' => 'required|exists:venues,id',
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'type' => 'required|in:football,basketball,tennis,volleyball,multipurpose',
            'description' => 'nullable|string|max:1000',
            'description_ar' => 'nullable|string|max:1000',
            'surface_type' => 'nullable|string|max:100',
            'dimensions' => 'nullable|array',
            'dimensions.length' => 'nullable|numeric|min:0',
            'dimensions.width' => 'nullable|numeric|min:0',
            'dimensions.area' => 'nullable|numeric|min:0',
            'capacity' => 'nullable|integer|min:1',
            'hourly_rate' => 'required|numeric|min:0',
            'peak_hour_rate' => 'nullable|numeric|min:0',
            'peak_hours' => 'nullable|array',
            'weekend_rate' => 'nullable|numeric|min:0',
            'equipment_included' => 'nullable|array',
            'amenities' => 'nullable|array',
            'lighting_available' => 'boolean',
            'air_conditioning' => 'boolean',
            'covered' => 'boolean',
            'available_from' => 'required|date_format:H:i',
            'available_to' => 'required|date_format:H:i|after:available_from',
            'unavailable_days' => 'nullable|array',
            'minimum_booking_hours' => 'required|integer|min:1',
            'maximum_booking_hours' => 'required|integer|min:1|gte:minimum_booking_hours',
            'advance_booking_days' => 'required|integer|min:1',
            'requires_deposit' => 'boolean',
            'deposit_percentage' => 'nullable|numeric|min:0|max:100',
            'currency' => 'required|string|size:3',
            'maintenance_notes' => 'nullable|string|max:1000',
            'last_maintenance_date' => 'nullable|date|before_or_equal:today',
            'next_maintenance_date' => 'nullable|date|after:today',
            'notes' => 'nullable|string|max:1000',
            'notes_ar' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $validator->validated();

            // Generate field code
            $data['code'] = Field::generateCode($data['venue_id'], $data['name']);
            $data['status'] = true;

            $field = Field::create($data);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Field created successfully',
                    'field' => $field
                ]);
            }

            return redirect()->route('fields.show', $field)
                ->with('success', 'Field created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating field: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error creating field'
                ], 500);
            }

            return back()->with('error', 'Error creating field')->withInput();
        }
    }

    /**
     * Display the specified field.
     */
    public function show(Field $field, Request $request): View|JsonResponse
    {
        Gate::authorize('view', $field);

        $field->load([
            'venue',
            'reservations.customer',
            'reservations.reservationPayments'
        ]);

        // Get recent reservations
        $recentReservations = $field->reservations()
            ->with(['customer'])
            ->orderBy('reservation_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->limit(10)
            ->get();

        // Get today's reservations
        $todayReservations = $field->reservations()
            ->with(['customer'])
            ->whereDate('reservation_date', today())
            ->orderBy('start_time')
            ->get();

        // Get statistics
        $stats = $field->getStatistics();

        if ($request->ajax()) {
            return response()->json([
                'field' => $field,
                'recent_reservations' => $recentReservations,
                'today_reservations' => $todayReservations,
                'stats' => $stats
            ]);
        }

        return view('fields.show', compact('field', 'recentReservations', 'todayReservations', 'stats'));
    }

    /**
     * Show the form for editing the specified field.
     */
    public function edit(Field $field): View
    {
        Gate::authorize('update', $field);

        $venues = Venue::active()->orderBy('name')->get();

        return view('fields.edit', compact('field', 'venues'));
    }

    /**
     * Update the specified field in storage.
     */
    public function update(Request $request, Field $field): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $field);

        $validator = Validator::make($request->all(), [
            'venue_id' => 'required|exists:venues,id',
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'type' => 'required|in:football,basketball,tennis,volleyball,multipurpose',
            'description' => 'nullable|string|max:1000',
            'description_ar' => 'nullable|string|max:1000',
            'surface_type' => 'nullable|string|max:100',
            'dimensions' => 'nullable|array',
            'dimensions.length' => 'nullable|numeric|min:0',
            'dimensions.width' => 'nullable|numeric|min:0',
            'dimensions.area' => 'nullable|numeric|min:0',
            'capacity' => 'nullable|integer|min:1',
            'hourly_rate' => 'required|numeric|min:0',
            'peak_hour_rate' => 'nullable|numeric|min:0',
            'peak_hours' => 'nullable|array',
            'weekend_rate' => 'nullable|numeric|min:0',
            'equipment_included' => 'nullable|array',
            'amenities' => 'nullable|array',
            'lighting_available' => 'boolean',
            'air_conditioning' => 'boolean',
            'covered' => 'boolean',
            'available_from' => 'required|date_format:H:i',
            'available_to' => 'required|date_format:H:i|after:available_from',
            'unavailable_days' => 'nullable|array',
            'minimum_booking_hours' => 'required|integer|min:1',
            'maximum_booking_hours' => 'required|integer|min:1|gte:minimum_booking_hours',
            'advance_booking_days' => 'required|integer|min:1',
            'requires_deposit' => 'boolean',
            'deposit_percentage' => 'nullable|numeric|min:0|max:100',
            'currency' => 'required|string|size:3',
            'maintenance_notes' => 'nullable|string|max:1000',
            'last_maintenance_date' => 'nullable|date|before_or_equal:today',
            'next_maintenance_date' => 'nullable|date|after:today',
            'notes' => 'nullable|string|max:1000',
            'notes_ar' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $validator->validated();

            // Regenerate code if name or venue changed
            if ($data['name'] !== $field->name || $data['venue_id'] !== $field->venue_id) {
                $data['code'] = Field::generateCode($data['venue_id'], $data['name']);
            }

            $field->update($data);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Field updated successfully',
                    'field' => $field->fresh()
                ]);
            }

            return redirect()->route('fields.show', $field)
                ->with('success', 'Field updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating field: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating field'
                ], 500);
            }

            return back()->with('error', 'Error updating field')->withInput();
        }
    }

    /**
     * Remove the specified field from storage.
     */
    public function destroy(Field $field): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $field);

        try {
            // Check if field has active reservations
            $activeReservations = $field->reservations()
                ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
                ->count();

            if ($activeReservations > 0) {
                $message = 'Cannot delete field with active reservations';

                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 422);
                }

                return back()->with('error', $message);
            }

            $field->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Field deleted successfully'
                ]);
            }

            return redirect()->route('fields.index')
                ->with('success', 'Field deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting field: ' . $e->getMessage());

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error deleting field'
                ], 500);
            }

            return back()->with('error', 'Error deleting field');
        }
    }

    /**
     * Toggle field status (active/inactive).
     */
    public function toggleStatus(Field $field): JsonResponse
    {
        Gate::authorize('update', $field);

        try {
            $field->update(['status' => !$field->status]);

            return response()->json([
                'success' => true,
                'message' => 'Field status updated successfully',
                'status' => $field->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling field status: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error updating field status'
            ], 500);
        }
    }

    /**
     * Get field availability for a specific date range.
     */
    public function getAvailability(Field $field, Request $request): JsonResponse
    {
        Gate::authorize('view', $field);

        $validator = Validator::make($request->all(), [
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $date = $request->get('date');
        $startTime = $request->get('start_time');
        $endTime = $request->get('end_time');

        try {
            // Get existing reservations for the date
            $reservations = $field->reservations()
                ->whereDate('reservation_date', $date)
                ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
                ->orderBy('start_time')
                ->get(['start_time', 'end_time', 'status']);

            // Check if specific time slot is available
            $isAvailable = true;
            if ($startTime && $endTime) {
                $isAvailable = $field->isAvailableForTimeSlot($date, $startTime, $endTime);
            }

            return response()->json([
                'success' => true,
                'field' => $field->only(['id', 'name', 'available_from', 'available_to']),
                'date' => $date,
                'reservations' => $reservations,
                'is_available' => $isAvailable,
                'availability_hours' => [
                    'from' => $field->available_from->format('H:i'),
                    'to' => $field->available_to->format('H:i')
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting field availability: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error getting field availability'
            ], 500);
        }
    }

    /**
     * Get field statistics.
     */
    public function getStatistics(Field $field): JsonResponse
    {
        Gate::authorize('view', $field);

        try {
            $stats = $field->getStatistics();

            return response()->json($stats);
        } catch (\Exception $e) {
            Log::error('Error getting field statistics: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error getting field statistics'
            ], 500);
        }
    }

    /**
     * Export fields to Excel.
     */
    public function exportExcel(Request $request): \Symfony\Component\HttpFoundation\Response
    {
        Gate::authorize('export', Field::class);

        // Implementation for Excel export would go here
        // For now, return a simple response
        return response()->make('Excel content', 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="fields.xlsx"'
        ]);
    }

    /**
     * Export fields to PDF.
     */
    public function exportPdf(Request $request): \Symfony\Component\HttpFoundation\Response
    {
        Gate::authorize('export', Field::class);

        // Implementation for PDF export would go here
        // For now, return a simple response
        return response()->make('PDF content', 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="fields.pdf"'
        ]);
    }

    /**
     * API endpoint for field availability (AJAX requests).
     */
    public function apiAvailability(Field $field, Request $request): JsonResponse
    {
        Gate::authorize('view', $field);

        $validator = Validator::make($request->all(), [
            'date' => 'required|date|after_or_equal:today',
            'duration' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $date = $request->get('date');
        $duration = $request->get('duration', 1);

        try {
            // Get available time slots for the date
            $availableSlots = $this->getAvailableTimeSlots($field, $date, $duration);

            return response()->json([
                'success' => true,
                'field' => $field->only(['id', 'name', 'hourly_rate']),
                'date' => $date,
                'duration' => $duration,
                'available_slots' => $availableSlots
            ]);
        } catch (\Exception $e) {
            Log::error('Error getting field availability slots: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error getting availability slots'
            ], 500);
        }
    }

    /**
     * Get available time slots for a field on a specific date.
     */
    private function getAvailableTimeSlots(Field $field, string $date, int $duration): array
    {
        $availableSlots = [];

        // Get field availability hours
        $startTime = Carbon::createFromFormat('H:i:s', $field->available_from->format('H:i:s'));
        $endTime = Carbon::createFromFormat('H:i:s', $field->available_to->format('H:i:s'));

        // Get existing reservations for the date
        $reservations = $field->reservations()
            ->whereDate('reservation_date', $date)
            ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
            ->orderBy('start_time')
            ->get(['start_time', 'end_time']);

        // Generate time slots
        $currentTime = $startTime->copy();
        while ($currentTime->copy()->addHours($duration)->lte($endTime)) {
            $slotStart = $currentTime->format('H:i');
            $slotEnd = $currentTime->copy()->addHours($duration)->format('H:i');

            // Check if slot conflicts with existing reservations
            $isAvailable = true;
            foreach ($reservations as $reservation) {
                $reservationStart = Carbon::createFromFormat('H:i:s', $reservation->start_time);
                $reservationEnd = Carbon::createFromFormat('H:i:s', $reservation->end_time);

                if ($currentTime->lt($reservationEnd) && $currentTime->copy()->addHours($duration)->gt($reservationStart)) {
                    $isAvailable = false;
                    break;
                }
            }

            if ($isAvailable) {
                $availableSlots[] = [
                    'start_time' => $slotStart,
                    'end_time' => $slotEnd,
                    'duration' => $duration
                ];
            }

            $currentTime->addHour();
        }

        return $availableSlots;
    }
}
