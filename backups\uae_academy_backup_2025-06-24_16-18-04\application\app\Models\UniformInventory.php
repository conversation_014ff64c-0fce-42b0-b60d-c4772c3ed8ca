<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class UniformInventory extends Model
{
    protected $table = 'uniform_inventory';
    protected $fillable = [
        'branch_id',
        'academy_id',
        'uniform_category_id',
        'uniform_supplier_id',
        'sku',
        'name',
        'name_ar',
        'description',
        'description_ar',
        'size',
        'color',
        'color_ar',
        'material',
        'brand',
        'model',
        'barcode',
        'current_stock',
        'reserved_stock',
        'available_stock',
        'minimum_stock',
        'maximum_stock',
        'reorder_quantity',
        'cost_price',
        'selling_price',
        'markup_percentage',
        'currency',
        'location',
        'shelf',
        'bin',
        'status',
        'is_trackable',
        'allow_backorder',
        'last_restocked_at',
        'last_sold_at',
        'supplier_sku',
        'supplier_price',
        'supplier_lead_time',
        'supplier_minimum_order',
        'attributes',
        'notes',
        'notes_ar',
    ];

    protected $casts = [
        'current_stock' => 'integer',
        'reserved_stock' => 'integer',
        'available_stock' => 'integer',
        'minimum_stock' => 'integer',
        'maximum_stock' => 'integer',
        'reorder_quantity' => 'integer',
        'cost_price' => 'decimal:2',
        'selling_price' => 'decimal:2',
        'markup_percentage' => 'decimal:2',
        'supplier_price' => 'decimal:2',
        'supplier_lead_time' => 'integer',
        'supplier_minimum_order' => 'integer',
        'is_trackable' => 'boolean',
        'allow_backorder' => 'boolean',
        'last_restocked_at' => 'date',
        'last_sold_at' => 'date',
        'attributes' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'localized_name',
        'localized_description',
        'localized_color',
        'localized_notes',
        'formatted_cost_price',
        'formatted_selling_price',
        'formatted_supplier_price',
        'stock_status',
        'stock_status_text',
        'stock_status_badge_class',
        'needs_reorder',
        'reorder_suggestion',
        'stock_value',
        'formatted_stock_value',
        'profit_margin',
        'turnover_rate',
        'days_since_last_sale',
        'full_location',
    ];

    /**
     * Get the branch that owns this inventory item.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns this inventory item.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the category for this inventory item.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(UniformCategory::class, 'uniform_category_id');
    }

    /**
     * Get the supplier for this inventory item.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(UniformSupplier::class, 'uniform_supplier_id');
    }

    /**
     * Get the stock movements for this inventory item.
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(UniformStockMovement::class);
    }

    /**
     * Get the uniform orders for this inventory item.
     */
    public function uniformOrders(): HasMany
    {
        return $this->hasMany(Uniform::class);
    }

    /**
     * Get the purchase order items for this inventory item.
     */
    public function purchaseOrderItems(): HasMany
    {
        return $this->hasMany(UniformPurchaseOrderItem::class);
    }

    // Computed Properties

    /**
     * Get localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->name_ar) ? $this->name_ar : $this->name;
    }

    /**
     * Get localized description based on current locale.
     */
    public function getLocalizedDescriptionAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->description_ar) ? $this->description_ar : $this->description;
    }

    /**
     * Get localized color based on current locale.
     */
    public function getLocalizedColorAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->color_ar) ? $this->color_ar : $this->color;
    }

    /**
     * Get localized notes based on current locale.
     */
    public function getLocalizedNotesAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->notes_ar) ? $this->notes_ar : $this->notes;
    }

    /**
     * Get formatted cost price in AED.
     */
    public function getFormattedCostPriceAttribute(): string
    {
        return 'AED ' . number_format($this->cost_price, 2);
    }

    /**
     * Get formatted selling price in AED.
     */
    public function getFormattedSellingPriceAttribute(): string
    {
        return 'AED ' . number_format($this->selling_price, 2);
    }

    /**
     * Get formatted supplier price in AED.
     */
    public function getFormattedSupplierPriceAttribute(): string
    {
        return 'AED ' . number_format($this->supplier_price ?? 0, 2);
    }

    /**
     * Get stock status based on current stock levels.
     */
    public function getStockStatusAttribute(): string
    {
        if ($this->current_stock <= 0) {
            return 'out_of_stock';
        } elseif ($this->current_stock <= $this->minimum_stock) {
            return 'low_stock';
        } elseif ($this->current_stock >= $this->maximum_stock) {
            return 'overstock';
        } else {
            return 'in_stock';
        }
    }

    /**
     * Get stock status text.
     */
    public function getStockStatusTextAttribute(): string
    {
        return match ($this->stock_status) {
            'out_of_stock' => 'Out of Stock',
            'low_stock' => 'Low Stock',
            'overstock' => 'Overstock',
            'in_stock' => 'In Stock',
            default => 'Unknown'
        };
    }

    /**
     * Get stock status badge class for UI.
     */
    public function getStockStatusBadgeClassAttribute(): string
    {
        return match ($this->stock_status) {
            'out_of_stock' => 'bg-red-100 text-red-800',
            'low_stock' => 'bg-yellow-100 text-yellow-800',
            'overstock' => 'bg-blue-100 text-blue-800',
            'in_stock' => 'bg-green-100 text-green-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Check if item needs reordering.
     */
    public function getNeedsReorderAttribute(): bool
    {
        return $this->current_stock <= $this->minimum_stock;
    }

    /**
     * Get reorder suggestion quantity.
     */
    public function getReorderSuggestionAttribute(): int
    {
        if (!$this->needs_reorder) {
            return 0;
        }

        $suggestedQuantity = $this->reorder_quantity;
        $shortfall = $this->minimum_stock - $this->current_stock;

        return max($suggestedQuantity, $shortfall);
    }

    /**
     * Get total stock value.
     */
    public function getStockValueAttribute(): float
    {
        return $this->current_stock * $this->cost_price;
    }

    /**
     * Get formatted stock value in AED.
     */
    public function getFormattedStockValueAttribute(): string
    {
        return 'AED ' . number_format($this->stock_value, 2);
    }

    /**
     * Get profit margin percentage.
     */
    public function getProfitMarginAttribute(): float
    {
        if ($this->cost_price <= 0) {
            return 0;
        }

        return round((($this->selling_price - $this->cost_price) / $this->cost_price) * 100, 2);
    }

    /**
     * Get turnover rate (sales per month).
     */
    public function getTurnoverRateAttribute(): float
    {
        $salesLastMonth = $this->stockMovements()
            ->where('movement_type', 'sale')
            ->where('movement_date', '>=', now()->subMonth())
            ->sum('quantity');

        return abs($salesLastMonth); // Make positive
    }

    /**
     * Get days since last sale.
     */
    public function getDaysSinceLastSaleAttribute(): ?int
    {
        return $this->last_sold_at ? $this->last_sold_at->diffInDays(now()) : null;
    }

    /**
     * Get full location string.
     */
    public function getFullLocationAttribute(): string
    {
        $parts = array_filter([$this->location, $this->shelf, $this->bin]);
        return implode(' - ', $parts) ?: 'Not specified';
    }

    // Query Scopes

    /**
     * Scope to get only active inventory items.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get items that need reordering.
     */
    public function scopeNeedsReorder(Builder $query): Builder
    {
        return $query->whereColumn('current_stock', '<=', 'minimum_stock');
    }

    /**
     * Scope to get out of stock items.
     */
    public function scopeOutOfStock(Builder $query): Builder
    {
        return $query->where('current_stock', '<=', 0);
    }

    /**
     * Scope to get low stock items.
     */
    public function scopeLowStock(Builder $query): Builder
    {
        return $query->whereColumn('current_stock', '<=', 'minimum_stock')
            ->where('current_stock', '>', 0);
    }

    /**
     * Scope to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope to filter by category.
     */
    public function scopeByCategory(Builder $query, int $categoryId): Builder
    {
        return $query->where('uniform_category_id', $categoryId);
    }

    /**
     * Scope to filter by supplier.
     */
    public function scopeBySupplier(Builder $query, int $supplierId): Builder
    {
        return $query->where('uniform_supplier_id', $supplierId);
    }

    /**
     * Scope to search inventory items.
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('name_ar', 'like', "%{$search}%")
                ->orWhere('sku', 'like', "%{$search}%")
                ->orWhere('barcode', 'like', "%{$search}%")
                ->orWhere('brand', 'like', "%{$search}%")
                ->orWhere('model', 'like', "%{$search}%");
        });
    }
}
