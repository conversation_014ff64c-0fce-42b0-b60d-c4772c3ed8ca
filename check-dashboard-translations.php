<?php

// UAE English Sports Academy - Dashboard Translation Checker
// This script specifically checks dashboard translations

echo "🔍 Dashboard Translation Checker\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// Load dashboard translations
$enDashboard = include 'resources/lang/en/dashboard.php';
$arDashboard = include 'resources/lang/ar/dashboard.php';

echo "📊 Dashboard Translation Status:\n";
echo "  English: " . count($enDashboard) . " keys\n";
echo "  Arabic: " . count($arDashboard) . " keys\n\n";

// Keys used in dashboard.blade.php
$dashboardKeys = [
    'welcome_back',
    'active_growing', 
    'excellence_sports',
    'future_champions',
    'total_branches',
    'total_academies', 
    'total_students',
    'monthly_revenue',
    'recent_students',
    'new_registrations',
    'recent_payments',
    'view_all',
    'Quick Actions',
    'Add Student',
    'Add Payment', 
    'Order Uniform',
    'view_details'
];

echo "🔍 Checking Dashboard Keys:\n";

$missingEn = [];
$missingAr = [];

foreach ($dashboardKeys as $key) {
    $enExists = isset($enDashboard[$key]);
    $arExists = isset($arDashboard[$key]);
    
    $enStatus = $enExists ? "✅" : "❌";
    $arStatus = $arExists ? "✅" : "❌";
    
    echo "  {$key}: EN {$enStatus} | AR {$arStatus}\n";
    
    if (!$enExists) {
        $missingEn[] = $key;
    }
    if (!$arExists) {
        $missingAr[] = $key;
    }
}

echo "\n";

if (!empty($missingEn)) {
    echo "❌ Missing English Translations:\n";
    foreach ($missingEn as $key) {
        $suggestion = ucwords(str_replace('_', ' ', $key));
        echo "  '{$key}' => '{$suggestion}',\n";
    }
    echo "\n";
}

if (!empty($missingAr)) {
    echo "❌ Missing Arabic Translations:\n";
    foreach ($missingAr as $key) {
        echo "  '{$key}' => 'TODO: Add Arabic translation',\n";
    }
    echo "\n";
}

// Check common translations used in dashboard
$enCommon = include 'resources/lang/en/common.php';
$arCommon = include 'resources/lang/ar/common.php';

$commonKeys = [
    'name',
    'academy', 
    'join_date',
    'status',
    'student',
    'amount',
    'date',
    'active',
    'inactive',
    'pending',
    'not_available',
    'no_students_found',
    'no_payments_found'
];

echo "🔍 Checking Common Keys Used in Dashboard:\n";

foreach ($commonKeys as $key) {
    $enExists = isset($enCommon[$key]);
    $arExists = isset($arCommon[$key]);
    
    $enStatus = $enExists ? "✅" : "❌";
    $arStatus = $arExists ? "✅" : "❌";
    
    echo "  {$key}: EN {$enStatus} | AR {$arStatus}\n";
    
    if (!$enExists) {
        echo "    Missing EN: '{$key}' => '" . ucwords(str_replace('_', ' ', $key)) . "',\n";
    }
    if (!$arExists) {
        echo "    Missing AR: '{$key}' => 'TODO: Add Arabic translation',\n";
    }
}

echo "\n🎯 Summary:\n";
if (empty($missingEn) && empty($missingAr)) {
    echo "✅ All dashboard translations are properly defined!\n";
} else {
    echo "⚠️  Found missing translations that need to be added.\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Dashboard translation check completed!\n";
