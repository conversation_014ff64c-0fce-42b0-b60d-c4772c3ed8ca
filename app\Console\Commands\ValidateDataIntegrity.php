<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Student;
use App\Models\Payment;
use App\Models\Program;
use App\Models\Uniform;
use Illuminate\Support\Facades\DB;

class ValidateDataIntegrity extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'db:validate-integrity {--fix : Attempt to fix issues automatically}';

    /**
     * The console command description.
     */
    protected $description = 'Validate database integrity and detect relationship mismatches';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Starting Data Integrity Validation...');
        $this->newLine();
        
        $issues = [];
        $fixMode = $this->option('fix');
        
        // Check user duplicates
        $issues = array_merge($issues, $this->checkUserDuplicates($fixMode));
        
        // Check foreign key relationships
        $issues = array_merge($issues, $this->checkForeignKeyIntegrity($fixMode));
        
        // Check data relationship consistency
        $issues = array_merge($issues, $this->checkDataRelationships($fixMode));
        
        // Check orphaned records
        $issues = array_merge($issues, $this->checkOrphanedRecords($fixMode));
        
        // Check for missing required data
        $issues = array_merge($issues, $this->checkMissingData($fixMode));
        
        // Report results
        $this->reportResults($issues);
        
        return count(array_filter($issues, fn($issue) => str_contains($issue, '🚨'))) > 0 ? 1 : 0;
    }
    
    private function checkUserDuplicates(bool $fix = false): array
    {
        $issues = [];
        
        $duplicateEmails = User::select('email')
            ->groupBy('email')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('email');
            
        foreach ($duplicateEmails as $email) {
            $users = User::where('email', $email)->get();
            $count = $users->count();
            $issues[] = "🚨 DUPLICATE USER: Email '{$email}' has {$count} records";
            
            if ($fix && $count > 1) {
                // Keep the first user, delete the rest
                $keepUser = $users->first();
                $deleteUsers = $users->skip(1);
                
                foreach ($deleteUsers as $user) {
                    $user->delete();
                    $this->warn("   ✅ Deleted duplicate user ID {$user->id}");
                }
                
                $issues[] = "   ✅ Fixed: Kept user ID {$keepUser->id}, deleted " . ($count - 1) . " duplicates";
            }
        }
        
        return $issues;
    }
    
    private function checkForeignKeyIntegrity(bool $fix = false): array
    {
        $issues = [];
        
        // Check users with invalid branch_id
        $invalidUserBranches = User::whereNotNull('branch_id')
            ->whereNotExists(function ($query) {
                $query->select('id')
                    ->from('branches')
                    ->whereColumn('branches.id', 'users.branch_id');
            })->get();
            
        if ($invalidUserBranches->count() > 0) {
            $issues[] = "🚨 FK VIOLATION: {$invalidUserBranches->count()} users have invalid branch_id";
            
            if ($fix) {
                foreach ($invalidUserBranches as $user) {
                    $user->update(['branch_id' => null]);
                }
                $issues[] = "   ✅ Fixed: Set branch_id to null for invalid users";
            }
        }
        
        // Check payments with invalid student_id
        $invalidPaymentStudents = Payment::whereNotExists(function ($query) {
            $query->select('id')
                ->from('students')
                ->whereColumn('students.id', 'payments.student_id');
        })->get();
        
        if ($invalidPaymentStudents->count() > 0) {
            $issues[] = "🚨 FK VIOLATION: {$invalidPaymentStudents->count()} payments have invalid student_id";
            
            if ($fix) {
                foreach ($invalidPaymentStudents as $payment) {
                    $payment->delete();
                }
                $issues[] = "   ✅ Fixed: Deleted payments with invalid student_id";
            }
        }
        
        return $issues;
    }
    
    private function checkDataRelationships(bool $fix = false): array
    {
        $issues = [];
        
        $studentCount = Student::count();
        $paymentCount = Payment::count();
        $uniformCount = Uniform::count();
        
        $issues[] = "📊 DATA COUNTS: {$studentCount} students, {$paymentCount} payments, {$uniformCount} uniforms";
        
        // Check for students without payments
        $studentsWithoutPayments = Student::whereDoesntHave('payments')->get();
        if ($studentsWithoutPayments->count() > 0) {
            $issues[] = "⚠️  DATA MISMATCH: {$studentsWithoutPayments->count()} students have no payments";
            
            if ($fix) {
                foreach ($studentsWithoutPayments as $student) {
                    // Create a basic payment for the student
                    $program = Program::where('academy_id', $student->academy_id)->first();
                    if ($program) {
                        Payment::create([
                            'student_id' => $student->id,
                            'branch_id' => $student->branch_id,
                            'academy_id' => $student->academy_id,
                            'program_id' => $program->id,
                            'amount' => $program->price ?? 300,
                            'payment_method' => 'cash',
                            'payment_date' => now(),
                            'status' => 'active',
                            'payment_type' => 'fresh',
                            'currency' => 'AED',
                            'notes' => 'Auto-generated payment for data integrity',
                        ]);
                    }
                }
                $issues[] = "   ✅ Fixed: Created payments for students without payments";
            }
        }
        
        // Check for multiple payments per student
        $studentsWithMultiplePayments = Student::has('payments', '>', 1)->count();
        if ($studentsWithMultiplePayments > 0) {
            $issues[] = "ℹ️  INFO: {$studentsWithMultiplePayments} students have multiple payments";
        }
        
        return $issues;
    }
    
    private function checkOrphanedRecords(bool $fix = false): array
    {
        $issues = [];
        
        // Check for academies without branches
        $orphanedAcademies = Academy::whereDoesntHave('branch')->get();
        if ($orphanedAcademies->count() > 0) {
            $issues[] = "🚨 ORPHANED: {$orphanedAcademies->count()} academies have no valid branch";
            
            if ($fix) {
                foreach ($orphanedAcademies as $academy) {
                    $academy->delete();
                }
                $issues[] = "   ✅ Fixed: Deleted orphaned academies";
            }
        }
        
        // Check for programs without academies
        $orphanedPrograms = Program::whereDoesntHave('academy')->get();
        if ($orphanedPrograms->count() > 0) {
            $issues[] = "🚨 ORPHANED: {$orphanedPrograms->count()} programs have no valid academy";
            
            if ($fix) {
                foreach ($orphanedPrograms as $program) {
                    $program->delete();
                }
                $issues[] = "   ✅ Fixed: Deleted orphaned programs";
            }
        }
        
        return $issues;
    }
    
    private function checkMissingData(bool $fix = false): array
    {
        $issues = [];
        
        // Check for branches without academies
        $branchesWithoutAcademies = Branch::whereDoesntHave('academies')->count();
        if ($branchesWithoutAcademies > 0) {
            $issues[] = "⚠️  MISSING DATA: {$branchesWithoutAcademies} branches have no academies";
        }
        
        // Check for academies without programs
        $academiesWithoutPrograms = Academy::whereDoesntHave('programs')->count();
        if ($academiesWithoutPrograms > 0) {
            $issues[] = "⚠️  MISSING DATA: {$academiesWithoutPrograms} academies have no programs";
        }
        
        return $issues;
    }
    
    private function reportResults(array $issues): void
    {
        $this->newLine();
        $this->info('📋 DATA INTEGRITY VALIDATION RESULTS');
        $this->info('=====================================');
        
        if (empty($issues)) {
            $this->info('✅ No data integrity issues found!');
        } else {
            foreach ($issues as $issue) {
                if (str_contains($issue, '🚨')) {
                    $this->error($issue);
                } elseif (str_contains($issue, '⚠️')) {
                    $this->warn($issue);
                } elseif (str_contains($issue, '✅')) {
                    $this->info($issue);
                } else {
                    $this->line($issue);
                }
            }
            
            $criticalIssues = array_filter($issues, fn($issue) => str_contains($issue, '🚨'));
            $warningIssues = array_filter($issues, fn($issue) => str_contains($issue, '⚠️'));
            $fixedIssues = array_filter($issues, fn($issue) => str_contains($issue, '✅'));
            
            $this->newLine();
            $this->info('SUMMARY:');
            $this->info('- ' . count($criticalIssues) . ' critical issues');
            $this->info('- ' . count($warningIssues) . ' warnings');
            $this->info('- ' . count($fixedIssues) . ' fixes applied');
        }
        
        $this->newLine();
    }
}
