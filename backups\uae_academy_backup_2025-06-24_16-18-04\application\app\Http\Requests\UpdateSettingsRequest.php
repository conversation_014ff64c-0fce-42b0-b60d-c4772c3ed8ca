<?php

namespace App\Http\Requests;

use App\Models\Setting;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Gate;

class UpdateSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Gate::allows('update', Setting::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $category = $this->route('category', 'general');
        $settings = Setting::getByCategory($category);
        $rules = [];

        foreach ($settings as $setting) {
            if ($setting->validation_rules) {
                $rules[$setting->key] = $setting->validation_rules;
            }
        }

        return $rules;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        $category = $this->route('category', 'general');
        $settings = Setting::getByCategory($category);
        $attributes = [];

        foreach ($settings as $setting) {
            $attributes[$setting->key] = $setting->label;
        }

        return $attributes;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            '*.required' => 'The :attribute field is required.',
            '*.string' => 'The :attribute must be a string.',
            '*.integer' => 'The :attribute must be an integer.',
            '*.numeric' => 'The :attribute must be a number.',
            '*.boolean' => 'The :attribute must be true or false.',
            '*.email' => 'The :attribute must be a valid email address.',
            '*.url' => 'The :attribute must be a valid URL.',
            '*.image' => 'The :attribute must be an image.',
            '*.mimes' => 'The :attribute must be a file of type: :values.',
            '*.max' => 'The :attribute may not be greater than :max.',
            '*.min' => 'The :attribute must be at least :min.',
            '*.in' => 'The selected :attribute is invalid.',
            '*.array' => 'The :attribute must be an array.',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        if ($this->ajax()) {
            throw new \Illuminate\Validation\ValidationException($validator, response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422));
        }

        parent::failedValidation($validator);
    }
}
