@extends('layouts.dashboard')

@section('title', 'Uniform Reports - UAE English Sports Academy')

@section('header')
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Uniform Reports</h1>
            <p class="text-gray-600 mt-1">Order tracking and uniform status management</p>
        </div>
        <div class="flex items-center gap-3">
            <button onclick="exportToPDF()" class="btn-bank-outline">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export PDF
            </button>
            <button onclick="exportToExcel()" class="btn-bank-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export Excel
            </button>
        </div>
    </div>
@endsection

@section('content')
    <!-- Filters -->
    @include('reports._filters')

    <!-- Uniform Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Orders -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Orders</p>
                    <p class="text-2xl font-bold text-blue-600">{{ number_format($uniformData['summary']['total_orders']) }}</p>
                </div>
                <div class="p-3 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Total Amount -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Amount</p>
                    <p class="text-2xl font-bold text-green-600">AED {{ number_format($uniformData['summary']['total_amount'], 2) }}</p>
                </div>
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Pending Branch -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Pending at Branch</p>
                    <p class="text-2xl font-bold text-orange-600">{{ number_format($uniformData['summary']['pending_branch']) }}</p>
                </div>
                <div class="p-3 bg-orange-100 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Pending Office -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Pending at Office</p>
                    <p class="text-2xl font-bold text-red-600">{{ number_format($uniformData['summary']['pending_office']) }}</p>
                </div>
                <div class="p-3 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analysis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Size Breakdown -->
        <div class="bank-card">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Size Distribution</h3>
            <div class="space-y-4">
                @foreach($uniformData['size_breakdown'] as $size)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-3 bg-blue-500"></div>
                            <span class="text-sm font-medium">Size {{ $size->size }}</span>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-semibold">{{ $size->count }} orders</p>
                            <p class="text-xs text-gray-500">AED {{ number_format($size->total, 2) }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Branch Status Breakdown -->
        <div class="bank-card">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Branch Status</h3>
            <div class="space-y-4">
                @foreach($uniformData['branch_status_breakdown'] as $status)
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-3 
                                {{ $status->branch_status === 'pending' ? 'bg-orange-500' : '' }}
                                {{ $status->branch_status === 'received' ? 'bg-blue-500' : '' }}
                                {{ $status->branch_status === 'delivered' ? 'bg-green-500' : '' }}">
                            </div>
                            <span class="text-sm font-medium">{{ ucfirst($status->branch_status) }}</span>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-semibold">{{ $status->count }} orders</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Office Status Breakdown -->
    <div class="bank-card mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Office Status Distribution</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            @foreach($uniformData['office_status_breakdown'] as $status)
                <div class="text-center p-4 bg-gray-50 rounded-lg">
                    <div class="text-2xl font-bold 
                        {{ $status->office_status === 'pending' ? 'text-orange-600' : '' }}
                        {{ $status->office_status === 'received' ? 'text-blue-600' : '' }}
                        {{ $status->office_status === 'delivered' ? 'text-green-600' : '' }}">
                        {{ $status->count }}
                    </div>
                    <div class="text-sm font-medium text-gray-600 mt-1">{{ ucfirst($status->office_status) }}</div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Branch Comparison -->
    <div class="bank-card mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Orders by Branch</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Orders</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($uniformData['branch_comparison'] as $branch)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $branch->branch->name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ number_format($branch->count) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-green-600">AED {{ number_format($branch->total, 2) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">AED {{ number_format($branch->count > 0 ? $branch->total / $branch->count : 0, 2) }}</div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="bank-card">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Recent Uniform Orders</h3>
            <a href="{{ route('uniforms.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View All</a>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Academy</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Office Status</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($uniformData['recent_orders'] as $uniform)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $uniform->student->full_name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $uniform->academy->name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $uniform->size }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-green-600">AED {{ number_format($uniform->amount, 2) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $uniform->order_date->format('M d, Y') }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $uniform->branch_status === 'pending' ? 'bg-orange-100 text-orange-800' : '' }}
                                    {{ $uniform->branch_status === 'received' ? 'bg-blue-100 text-blue-800' : '' }}
                                    {{ $uniform->branch_status === 'delivered' ? 'bg-green-100 text-green-800' : '' }}">
                                    {{ ucfirst($uniform->branch_status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $uniform->office_status === 'pending' ? 'bg-orange-100 text-orange-800' : '' }}
                                    {{ $uniform->office_status === 'received' ? 'bg-blue-100 text-blue-800' : '' }}
                                    {{ $uniform->office_status === 'delivered' ? 'bg-green-100 text-green-800' : '' }}">
                                    {{ ucfirst($uniform->office_status) }}
                                </span>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open(`{{ route('reports.uniform') }}?${params.toString()}`, '_blank');
}

function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open(`{{ route('reports.uniform') }}?${params.toString()}`, '_blank');
}
</script>
@endpush
