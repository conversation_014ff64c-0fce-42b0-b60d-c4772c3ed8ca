<!-- Advanced Search & Filters -->
<div class="bank-card">
    <div class="bank-card-header">
        <h3 class="bank-card-title">Search & Filters</h3>
        <button @click="showFilters = !showFilters"
            class="btn-bank btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                </path>
            </svg>
            <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
        </button>
    </div>

    <div class="bank-card-body" x-show="showFilters" x-transition x-data="{ showFilters: true }">
        <form method="GET" action="{{ route('students.index') }}" class="space-y-4">
            <!-- Search Fields Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Mobile Number Search -->
                <div>
                    <label class="form-label-bank">Search by Mobile Number</label>
                    <div class="mobile-input-container relative">
                        <!-- UAE Flag and Country Code Prefix -->
                        <div class="absolute inset-y-0 left-0 flex items-center z-10 pointer-events-none">
                            <div
                                class="country-code-prefix flex items-center px-4 py-2 rounded-l-md h-full bg-gray-50 border-r border-gray-300">
                                <!-- UAE Flag -->
                                <svg xmlns="http://www.w3.org/2000/svg" shape-rendering="geometricPrecision"
                                    text-rendering="geometricPrecision" image-rendering="optimizeQuality"
                                    fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 512 512"
                                    class="w-6 h-4 mr-3 flex-shrink-0">
                                    <g fill-rule="nonzero">
                                        <path fill="#4D4D4D"
                                            d="M256-.001c70.684 0 134.69 28.664 181.013 74.988C483.337 121.31 512.001 185.316 512.001 256c0 70.684-28.664 134.69-74.988 181.013C390.69 483.337 326.684 512.001 256 512.001c-70.676 0-134.689-28.664-181.013-74.988C28.663 390.69 0 326.676 0 256c0-70.684 28.663-134.69 74.987-181.013C121.311 28.663 185.316-.001 256-.001z" />
                                        <path fill="#fff"
                                            d="M256.002 19.596c65.277 0 124.382 26.466 167.162 69.243 42.777 42.779 69.243 101.884 69.243 167.162s-26.466 124.383-69.246 167.16c-42.777 42.779-101.882 69.246-167.159 69.246-65.279 0-124.384-26.467-167.163-69.243-42.777-42.78-69.243-101.885-69.243-167.163S46.062 131.618 88.839 88.839c42.779-42.777 101.884-69.243 167.163-69.243z" />
                                        <path fill="#00732F"
                                            d="M458.658 179.96H53.345C84.13 97.955 163.242 39.594 256.002 39.594c92.756 0 171.871 58.361 202.656 140.366z" />
                                        <path
                                            d="M51.468 326.835h409.067c-29.338 84.727-109.826 145.574-204.533 145.574-94.711 0-175.193-60.847-204.534-145.574z" />
                                        <path fill="red"
                                            d="M162.9 60.598v390.806C89.979 416.598 39.594 342.186 39.594 256.001c0-86.188 50.385-160.597 123.306-195.403z" />
                                    </g>
                                </svg>
                                <!-- Country Code -->
                                <span class="text-sm font-bold text-red-700 whitespace-nowrap">+971</span>
                            </div>
                        </div>
                        <!-- Mobile Number Input -->
                        <input type="text" name="search" value="{{ request('search') }}" placeholder=""
                            class="form-input-bank font-mono text-lg"
                            style="padding-left: 9rem !important; padding-right: 1rem;" maxlength="11"
                            x-data="mobileInput()" @input="formatMobileInput($event)"
                            @keyup="formatMobileInput($event)">
                    </div>
                    <p class="text-xs text-gray-500 mt-1">
                        <span class="inline-flex items-center">
                            Enter UAE mobile number (9 digits): 50 123 4567
                        </span>
                    </p>
                </div>

                <!-- Name Search -->
                <div>
                    <label class="form-label-bank">Search by Name</label>
                    <input type="text" name="name_search" value="{{ request('name_search') }}"
                        class="form-input-bank" placeholder="Enter student name">
                    <p class="text-xs text-gray-500 mt-1">
                        Search by first name, last name, or full name
                    </p>
                </div>

                <!-- Results Per Page -->
                <div>
                    <label class="form-label-bank">Results Per Page</label>
                    <select name="per_page" class="form-select-bank">
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15 per page
                        </option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25 per page</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50 per page</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100 per page</option>
                    </select>
                </div>
            </div>

            <!-- Filter Fields Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Branch Filter -->
                <div>
                    <label class="form-label-bank">Branch</label>
                    <select name="branch_id" class="form-select-bank">
                        <option value="">All Branches</option>
                        @foreach ($branches as $branch)
                            <option value="{{ $branch->id }}"
                                {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }} - {{ $branch->location }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Academy Filter -->
                <div>
                    <label class="form-label-bank">Academy</label>
                    <select name="academy_id" class="form-select-bank">
                        <option value="">All Academies</option>
                        @foreach ($academies as $academy)
                            <option value="{{ $academy->id }}"
                                {{ request('academy_id') == $academy->id ? 'selected' : '' }}>
                                {{ $academy->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Program Filter -->
                <div>
                    <label class="form-label-bank">Program</label>
                    <select name="program_id" class="form-select-bank">
                        <option value="">All Programs</option>
                        @foreach ($programs as $program)
                            <option value="{{ $program->id }}"
                                {{ request('program_id') == $program->id ? 'selected' : '' }}>
                                {{ $program->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button type="submit" class="btn-bank">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                    <a href="{{ route('students.index') }}" class="btn-bank btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                            </path>
                        </svg>
                        Clear
                    </a>
                </div>

                <!-- Active Filters Display -->
                <div class="flex flex-wrap items-center gap-2 text-sm">
                    @if (request('search'))
                        <div class="flex items-center px-3 py-1 bg-red-50 border border-red-200 rounded-full">
                            <svg class="w-4 h-4 text-red-600 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                </path>
                            </svg>
                            <span class="font-medium text-red-700">Phone:</span>
                            <span class="font-mono text-red-800 ml-1">+971 {{ request('search') }}</span>
                        </div>
                    @endif

                    @if (request('name_search'))
                        <div class="flex items-center px-3 py-1 bg-blue-50 border border-blue-200 rounded-full">
                            <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                </path>
                            </svg>
                            <span class="font-medium text-blue-700">Name:</span>
                            <span class="text-blue-800 ml-1">{{ request('name_search') }}</span>
                        </div>
                    @endif

                    @if (request('branch_id'))
                        <div class="flex items-center px-3 py-1 bg-green-50 border border-green-200 rounded-full">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                </path>
                            </svg>
                            <span class="font-medium text-green-700">Branch:</span>
                            <span
                                class="text-green-800 ml-1">{{ $branches->find(request('branch_id'))->name ?? 'Unknown' }}</span>
                        </div>
                    @endif

                    @if (request('academy_id'))
                        <div class="flex items-center px-3 py-1 bg-purple-50 border border-purple-200 rounded-full">
                            <svg class="w-4 h-4 text-purple-600 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                </path>
                            </svg>
                            <span class="font-medium text-purple-700">Academy:</span>
                            <span
                                class="text-purple-800 ml-1">{{ $academies->find(request('academy_id'))->name ?? 'Unknown' }}</span>
                        </div>
                    @endif

                    @if (request('program_id'))
                        <div class="flex items-center px-3 py-1 bg-orange-50 border border-orange-200 rounded-full">
                            <svg class="w-4 h-4 text-orange-600 mr-2" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z">
                                </path>
                            </svg>
                            <span class="font-medium text-orange-700">Program:</span>
                            <span
                                class="text-orange-800 ml-1">{{ $programs->find(request('program_id'))->name ?? 'Unknown' }}</span>
                        </div>
                    @endif
                </div>
            </div>
        </form>
    </div>
</div>

@push('styles')
    <style>
        .uae-flag {
            background: linear-gradient(to bottom,
                    #ff0000 0%, #ff0000 25%,
                    #ffffff 25%, #ffffff 50%,
                    #000000 50%, #000000 75%,
                    #00ff00 75%, #00ff00 100%);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .mobile-input-container {
            position: relative;
            overflow: hidden;
        }

        .country-code-prefix {
            background: linear-gradient(135deg, #fee2e2 0%, #dcfce7 100%);
            border-right: 2px solid #dc2626;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }
    </style>
@endpush

@push('scripts')
    <script>
        function mobileInput() {
            return {
                formatMobileInput(event) {
                    let value = event.target.value;

                    // Remove any non-numeric characters and spaces
                    value = value.replace(/[^\d]/g, '');

                    // Limit to 9 digits
                    if (value.length > 9) {
                        value = value.substring(0, 9);
                    }

                    // Format as XX XXX XXXX
                    let formatted = '';
                    if (value.length > 0) {
                        formatted = value.substring(0, 2);
                        if (value.length > 2) {
                            formatted += ' ' + value.substring(2, 5);
                            if (value.length > 5) {
                                formatted += ' ' + value.substring(5, 9);
                            }
                        }
                    }

                    // Update the input value
                    event.target.value = formatted;
                }
            }
        }
    </script>
@endpush

@push('scripts')
    <script>
        function branchFilter() {
            return {
                onBranchChange(event) {
                    const branchId = event.target.value;
                    const academySelect = document.getElementById('academy-filter');
                    const academyOptions = academySelect.querySelectorAll('option[data-branch]');

                    // Show/hide academy options based on selected branch
                    academyOptions.forEach(option => {
                        if (!branchId || option.dataset.branch === branchId) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                        }
                    });

                    // Reset academy selection if current selection is not valid for new branch
                    if (branchId && academySelect.value) {
                        const selectedOption = academySelect.querySelector(`option[value="${academySelect.value}"]`);
                        if (selectedOption && selectedOption.dataset.branch !== branchId) {
                            academySelect.value = '';
                        }
                    }
                }
            }
        }
    </script>
@endpush
