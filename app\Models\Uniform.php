<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Schema;

class Uniform extends Model
{
    protected $fillable = [
        'student_id',
        'branch_id',
        'academy_id',
        'uniform_inventory_id',
        'order_date',
        'size',
        'amount',
        'currency',
        'branch_status',
        'office_status',
        'payment_method',
        'note',
        'item',
        'sku',
        'color',
        'quantity',
        'delivery_date',
        'status',
        'fulfillment_status',
        'stock_reserved',
        'stock_reserved_at',
        'stock_released_at',
        'fulfilled_by',
        'fulfilled_at',
        'fulfillment_notes',
    ];

    protected $casts = [
        'order_date' => 'date',
        'delivery_date' => 'date',
        'amount' => 'decimal:2',
        'quantity' => 'integer',
        'stock_reserved' => 'boolean',
        'stock_reserved_at' => 'datetime',
        'stock_released_at' => 'datetime',
        'fulfilled_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_amount',
        'total_amount',
        'formatted_total_amount',
        'status_text',
        'status_badge_class',
        'formatted_order_date',
        'formatted_delivery_date',
        'branch_status_text',
        'office_status_text',
        'size_display',
        'method_text',
        'fulfillment_status_text',
        'fulfillment_status_badge_class',
        'can_reserve_stock',
        'can_fulfill',
        'inventory_available',
        'stock_status_text',
    ];

    /**
     * Get the student that owns the uniform.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the branch that owns the uniform.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the uniform.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the inventory item for this uniform order.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(UniformInventory::class, 'uniform_inventory_id');
    }

    /**
     * Get the user who fulfilled this order.
     */
    public function fulfiller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'fulfilled_by');
    }

    /**
     * Get the stock movements for this uniform order.
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(UniformStockMovement::class);
    }

    // Computed Properties

    /**
     * Get formatted amount in AED.
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'AED ' . number_format($this->amount, 2);
    }

    /**
     * Get total amount (quantity * amount).
     */
    public function getTotalAmountAttribute(): float
    {
        return ($this->quantity ?? 1) * $this->amount;
    }

    /**
     * Get formatted total amount in AED.
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return 'AED ' . number_format($this->total_amount, 2);
    }

    /**
     * Get human-readable status text.
     */
    public function getStatusTextAttribute(): string
    {
        // Handle both new 'status' column and legacy branch_status/office_status
        $status = $this->status ?? $this->getEffectiveStatus();

        return match ($status) {
            'ordered' => 'Ordered',
            'processing' => 'Processing',
            'ready' => 'Ready for Pickup',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled',
            'pending' => 'Pending',
            'received' => 'Received',
            default => 'Ordered'
        };
    }

    /**
     * Get status badge CSS class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        // Handle both new 'status' column and legacy branch_status/office_status
        $status = $this->status ?? $this->getEffectiveStatus();

        return match ($status) {
            'ordered' => 'badge-info',
            'processing' => 'badge-warning',
            'ready' => 'badge-primary',
            'delivered' => 'badge-success',
            'cancelled' => 'badge-danger',
            'pending' => 'badge-warning',
            'received' => 'badge-info',
            default => 'badge-info'
        };
    }

    /**
     * Get effective status from legacy columns if new status column doesn't exist.
     */
    private function getEffectiveStatus(): string
    {
        // If both branch and office have delivered, consider it delivered
        if ($this->branch_status === 'delivered' && $this->office_status === 'delivered') {
            return 'delivered';
        }

        // If either is received, consider it processing
        if ($this->branch_status === 'received' || $this->office_status === 'received') {
            return 'processing';
        }

        // Default to pending/ordered
        return 'ordered';
    }

    /**
     * Get formatted order date.
     */
    public function getFormattedOrderDateAttribute(): string
    {
        return $this->order_date ? $this->order_date->format('M d, Y') : '';
    }

    /**
     * Get formatted delivery date.
     */
    public function getFormattedDeliveryDateAttribute(): string
    {
        return $this->delivery_date ? $this->delivery_date->format('M d, Y') : '';
    }

    /**
     * Get branch status text.
     */
    public function getBranchStatusTextAttribute(): string
    {
        return match ($this->branch_status) {
            'pending' => 'Pending',
            'received' => 'Received',
            'delivered' => 'Delivered',
            default => 'Pending'
        };
    }

    /**
     * Get office status text.
     */
    public function getOfficeStatusTextAttribute(): string
    {
        return match ($this->office_status) {
            'pending' => 'Pending',
            'received' => 'Received',
            'delivered' => 'Delivered',
            default => 'Pending'
        };
    }

    /**
     * Get size display text.
     */
    public function getSizeDisplayAttribute(): string
    {
        if (empty($this->size)) {
            return 'Unknown';
        }

        $sizes = self::getAvailableSizes();

        // Check if the exact size exists in our array
        if (isset($sizes[$this->size])) {
            return $sizes[$this->size];
        }

        // Try case-insensitive lookup for legacy sizes
        $sizeLower = strtolower($this->size);
        foreach ($sizes as $key => $value) {
            if (strtolower($key) === $sizeLower) {
                return $value;
            }
        }

        // If not found, return the original size value
        return $this->size;
    }

    /**
     * Get payment method text.
     */
    public function getMethodTextAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'Cash',
            'card' => 'Credit/Debit Card',
            'bank_transfer' => 'Bank Transfer',
            default => 'Cash'
        };
    }

    /**
     * Get fulfillment status text.
     */
    public function getFulfillmentStatusTextAttribute(): string
    {
        return match ($this->fulfillment_status) {
            'pending' => 'Pending',
            'reserved' => 'Stock Reserved',
            'picked' => 'Picked',
            'packed' => 'Packed',
            'shipped' => 'Shipped',
            'delivered' => 'Delivered',
            default => 'Pending'
        };
    }

    /**
     * Get fulfillment status badge class for UI.
     */
    public function getFulfillmentStatusBadgeClassAttribute(): string
    {
        return match ($this->fulfillment_status) {
            'pending' => 'bg-gray-100 text-gray-800',
            'reserved' => 'bg-yellow-100 text-yellow-800',
            'picked' => 'bg-blue-100 text-blue-800',
            'packed' => 'bg-purple-100 text-purple-800',
            'shipped' => 'bg-indigo-100 text-indigo-800',
            'delivered' => 'bg-green-100 text-green-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Check if stock can be reserved for this order.
     */
    public function getCanReserveStockAttribute(): bool
    {
        return !$this->stock_reserved &&
            $this->fulfillment_status === 'pending' &&
            $this->inventoryItem &&
            $this->inventoryItem->available_stock >= $this->quantity;
    }

    /**
     * Check if order can be fulfilled.
     */
    public function getCanFulfillAttribute(): bool
    {
        return $this->stock_reserved &&
            in_array($this->fulfillment_status, ['reserved', 'picked', 'packed']);
    }

    /**
     * Check if inventory is available for this order.
     */
    public function getInventoryAvailableAttribute(): bool
    {
        if (!$this->inventoryItem) {
            return false;
        }

        return $this->inventoryItem->available_stock >= $this->quantity;
    }

    /**
     * Get stock status text for this order.
     */
    public function getStockStatusTextAttribute(): string
    {
        if (!$this->inventoryItem) {
            return 'No inventory item linked';
        }

        if ($this->stock_reserved) {
            return 'Stock reserved';
        }

        if ($this->inventory_available) {
            return 'Stock available';
        }

        return 'Insufficient stock';
    }

    // Query Scopes

    /**
     * Scope a query to only include ordered uniforms.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        // Check if status column exists, otherwise use branch_status
        if (Schema::hasColumn('uniforms', 'status')) {
            return $query->where('status', 'ordered');
        }
        return $query->where('branch_status', 'pending');
    }

    /**
     * Scope a query to only include delivered uniforms.
     */
    public function scopeDelivered(Builder $query): Builder
    {
        // Check if status column exists, otherwise use branch_status and office_status
        if (Schema::hasColumn('uniforms', 'status')) {
            return $query->where('status', 'delivered');
        }
        return $query->where('branch_status', 'delivered')
            ->where('office_status', 'delivered');
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope a query to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope a query to filter by branch status.
     */
    public function scopeByBranchStatus(Builder $query, string $status): Builder
    {
        return $query->where('branch_status', $status);
    }

    /**
     * Scope a query to filter by office status.
     */
    public function scopeByOfficeStatus(Builder $query, string $status): Builder
    {
        return $query->where('office_status', $status);
    }

    // Utility Methods

    /**
     * Check if uniform is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * Mark uniform as delivered.
     */
    public function markAsDelivered(): bool
    {
        $this->status = 'delivered';
        $this->delivery_date = now();
        return $this->save();
    }

    /**
     * Get available uniform sizes.
     */
    public static function getAvailableSizes(): array
    {
        return [
            // Kids Sizes
            '6xs-24-4' => '6XS (24-4)',
            '5xs-26-5' => '5XS (26-5)',
            '4xs-28-6' => '4XS (28-6)',
            '3xs-30-7' => '3XS (30-7)',
            '2xs-32-8' => '2XS (32-8)',

            // Standard Sizes
            'xs-34-9' => 'XS (34-9)',
            's-36-10' => 'S (36-10)',
            'm-38-11' => 'M (38-11)',
            'l-40-12' => 'L (40-12)',
            'xl-42-13' => 'XL (42-13)',
            'xxl-44-14' => 'XXL (44-14)',

            // Large Sizes
            '3xl-46-15' => '3XL (46-15)',
            '4xl-48-16' => '4XL (48-16)',

            // Legacy sizes for backward compatibility
            'XS' => 'Extra Small',
            'S' => 'Small',
            'M' => 'Medium',
            'L' => 'Large',
            'XL' => 'Extra Large',
            'XXL' => 'Double Extra Large',
            '3XL' => 'Triple Extra Large',
            '4XL' => 'Quadruple Extra Large',

            // Lowercase variants for compatibility
            'xs' => 'Extra Small',
            's' => 'Small',
            'm' => 'Medium',
            'l' => 'Large',
            'xl' => 'Extra Large',
            'xxl' => 'Double Extra Large',
            '3xl' => 'Triple Extra Large',
            '4xl' => 'Quadruple Extra Large',
        ];
    }

    // Inventory Management Methods

    /**
     * Reserve stock for this uniform order.
     */
    public function reserveStock(): bool
    {
        if (!$this->can_reserve_stock) {
            return false;
        }

        $inventoryItem = $this->inventoryItem;
        if (!$inventoryItem) {
            return false;
        }

        // Reserve the stock
        $inventoryItem->increment('reserved_stock', $this->quantity);
        $inventoryItem->decrement('available_stock', $this->quantity);

        // Update order status
        $this->update([
            'stock_reserved' => true,
            'stock_reserved_at' => now(),
            'fulfillment_status' => 'reserved',
        ]);

        // Create stock movement record
        UniformStockMovement::create([
            'uniform_inventory_id' => $this->uniform_inventory_id,
            'branch_id' => $this->branch_id,
            'academy_id' => $this->academy_id,
            'user_id' => 1, // System user
            'reference_number' => UniformStockMovement::generateReferenceNumber(),
            'movement_type' => 'reservation',
            'quantity' => -$this->quantity, // Negative because it's reserved
            'stock_before' => $inventoryItem->available_stock + $this->quantity,
            'stock_after' => $inventoryItem->available_stock,
            'uniform_id' => $this->id,
            'movement_date' => now()->toDateString(),
            'reason' => "Stock reserved for uniform order #{$this->id}",
        ]);

        return true;
    }

    /**
     * Release reserved stock for this uniform order.
     */
    public function releaseStock(): bool
    {
        if (!$this->stock_reserved) {
            return false;
        }

        $inventoryItem = $this->inventoryItem;
        if (!$inventoryItem) {
            return false;
        }

        // Release the stock
        $inventoryItem->decrement('reserved_stock', $this->quantity);
        $inventoryItem->increment('available_stock', $this->quantity);

        // Update order status
        $this->update([
            'stock_reserved' => false,
            'stock_released_at' => now(),
            'fulfillment_status' => 'pending',
        ]);

        // Create stock movement record
        UniformStockMovement::create([
            'uniform_inventory_id' => $this->uniform_inventory_id,
            'branch_id' => $this->branch_id,
            'academy_id' => $this->academy_id,
            'user_id' => 1, // System user
            'reference_number' => UniformStockMovement::generateReferenceNumber(),
            'movement_type' => 'release',
            'quantity' => $this->quantity, // Positive because it's released back
            'stock_before' => $inventoryItem->available_stock - $this->quantity,
            'stock_after' => $inventoryItem->available_stock,
            'uniform_id' => $this->id,
            'movement_date' => now()->toDateString(),
            'reason' => "Stock released from uniform order #{$this->id}",
        ]);

        return true;
    }

    /**
     * Fulfill this uniform order (complete the sale).
     */
    public function fulfill(?User $user = null): bool
    {
        if (!$this->can_fulfill) {
            return false;
        }

        $inventoryItem = $this->inventoryItem;
        if (!$inventoryItem) {
            return false;
        }

        // Complete the sale - remove from current stock and reserved stock
        $inventoryItem->decrement('current_stock', $this->quantity);
        $inventoryItem->decrement('reserved_stock', $this->quantity);
        $inventoryItem->update(['last_sold_at' => now()->toDateString()]);

        // Update order status
        $this->update([
            'fulfillment_status' => 'delivered',
            'status' => 'delivered',
            'fulfilled_by' => $user?->id ?? 1,
            'fulfilled_at' => now(),
            'delivery_date' => now()->toDateString(),
        ]);

        // Create stock movement record
        UniformStockMovement::create([
            'uniform_inventory_id' => $this->uniform_inventory_id,
            'branch_id' => $this->branch_id,
            'academy_id' => $this->academy_id,
            'user_id' => $user?->id ?? 1,
            'reference_number' => UniformStockMovement::generateReferenceNumber(),
            'movement_type' => 'sale',
            'quantity' => -$this->quantity, // Negative because it's sold
            'stock_before' => $inventoryItem->current_stock + $this->quantity,
            'stock_after' => $inventoryItem->current_stock,
            'unit_cost' => $inventoryItem->cost_price,
            'total_cost' => $this->quantity * $inventoryItem->cost_price,
            'uniform_id' => $this->id,
            'movement_date' => now()->toDateString(),
            'reason' => "Uniform sold to student: {$this->student->full_name}",
        ]);

        return true;
    }

    /**
     * Link this uniform order to an inventory item.
     */
    public function linkToInventory(UniformInventory $inventoryItem): bool
    {
        // Validate that the inventory item matches the order
        if (
            $inventoryItem->branch_id !== $this->branch_id ||
            $inventoryItem->academy_id !== $this->academy_id ||
            $inventoryItem->size !== $this->size
        ) {
            return false;
        }

        $this->update([
            'uniform_inventory_id' => $inventoryItem->id,
            'sku' => $inventoryItem->sku,
            'color' => $inventoryItem->color,
        ]);

        return true;
    }

    /**
     * Auto-link to inventory based on item, size, and color.
     */
    public function autoLinkToInventory(): bool
    {
        $inventoryItem = UniformInventory::where('branch_id', $this->branch_id)
            ->where('academy_id', $this->academy_id)
            ->where('size', $this->size)
            ->where('status', 'active')
            ->whereHas('category', function ($query) {
                $query->where('name', 'like', "%{$this->item}%");
            })
            ->first();

        if ($inventoryItem) {
            return $this->linkToInventory($inventoryItem);
        }

        return false;
    }
}
