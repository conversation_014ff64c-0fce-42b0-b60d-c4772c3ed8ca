<?php

/**
 * UAE English Sports Academy - Backup Verification Script
 * Verifies the integrity and completeness of the backup
 */

echo "🔍 UAE English Sports Academy - Backup Verification\n";
echo "=" . str_repeat("=", 60) . "\n\n";

$backupPath = $argv[1] ?? null;

if (!$backupPath) {
    echo "❌ Usage: php verify_backup.php [backup_path]\n";
    echo "   Example: php verify_backup.php ./backups/uae_academy_backup_2025-06-24_16-18-04\n";
    exit(1);
}

if (!is_dir($backupPath)) {
    echo "❌ Backup path does not exist: {$backupPath}\n";
    exit(1);
}

echo "📁 Verifying backup: {$backupPath}\n\n";

$errors = [];
$warnings = [];
$success = [];

// 1. Verify Backup Structure
echo "1️⃣ Verifying Backup Structure...\n";
$requiredDirs = [
    'database' => 'Database backup directory',
    'application' => 'Application files directory',
    'configuration' => 'Configuration files directory',
    'documentation' => 'Documentation directory'
];

foreach ($requiredDirs as $dir => $description) {
    $dirPath = $backupPath . '/' . $dir;
    if (is_dir($dirPath)) {
        echo "   ✅ {$dir}/ - {$description}\n";
        $success[] = "Directory {$dir} exists";
    } else {
        echo "   ❌ {$dir}/ - Missing {$description}\n";
        $errors[] = "Missing directory: {$dir}";
    }
}

// 2. Verify Database Backup
echo "\n2️⃣ Verifying Database Backup...\n";
$dbBackupFile = $backupPath . '/database/database_backup.sql';
if (file_exists($dbBackupFile)) {
    $fileSize = filesize($dbBackupFile);
    echo "   ✅ Database backup file exists\n";
    echo "   📊 File size: " . formatBytes($fileSize) . "\n";

    // Check if file contains data
    $content = file_get_contents($dbBackupFile, false, null, 0, 5000);
    if (strpos($content, 'CREATE TABLE') !== false || strpos($content, 'Table structure for table') !== false) {
        // Count tables in backup
        $tableCount = exec("grep -c 'CREATE TABLE' " . escapeshellarg($dbBackupFile));
        echo "   ✅ Database backup contains table structures ({$tableCount} tables)\n";
        $success[] = "Database backup is valid with {$tableCount} tables";
    } else {
        echo "   ❌ Database backup appears to be empty or corrupted\n";
        $errors[] = "Database backup is invalid";
    }
} else {
    echo "   ❌ Database backup file not found\n";
    $errors[] = "Database backup file missing";
}

// 3. Verify Application Files
echo "\n3️⃣ Verifying Application Files...\n";
$appPath = $backupPath . '/application';
$requiredAppDirs = [
    'app' => 'Application logic',
    'config' => 'Configuration files',
    'database' => 'Migrations and seeders',
    'resources' => 'Views and assets',
    'routes' => 'Route definitions',
    'public' => 'Public assets',
    'bootstrap' => 'Bootstrap files'
];

foreach ($requiredAppDirs as $dir => $description) {
    $dirPath = $appPath . '/' . $dir;
    if (is_dir($dirPath)) {
        $fileCount = countFiles($dirPath);
        echo "   ✅ {$dir}/ - {$description} ({$fileCount} files)\n";
        $success[] = "Application directory {$dir} exists with {$fileCount} files";
    } else {
        echo "   ❌ {$dir}/ - Missing {$description}\n";
        $errors[] = "Missing application directory: {$dir}";
    }
}

// 4. Verify Configuration Files
echo "\n4️⃣ Verifying Configuration Files...\n";
$configPath = $backupPath . '/configuration';
$requiredConfigFiles = [
    '.env' => 'Environment configuration',
    'composer.json' => 'PHP dependencies',
    'package.json' => 'Node.js dependencies'
];

foreach ($requiredConfigFiles as $file => $description) {
    $filePath = $configPath . '/' . $file;
    if (file_exists($filePath)) {
        $fileSize = filesize($filePath);
        echo "   ✅ {$file} - {$description} (" . formatBytes($fileSize) . ")\n";
        $success[] = "Configuration file {$file} exists";
    } else {
        echo "   ❌ {$file} - Missing {$description}\n";
        $errors[] = "Missing configuration file: {$file}";
    }
}

// 5. Verify Documentation
echo "\n5️⃣ Verifying Documentation...\n";
$docsPath = $backupPath . '/documentation';
if (is_dir($docsPath)) {
    $mdFiles = glob($docsPath . '/*.md');
    $mdCount = count($mdFiles);
    echo "   ✅ Documentation directory exists\n";
    echo "   📄 Markdown files: {$mdCount}\n";

    if ($mdCount > 0) {
        echo "   📋 Documentation files:\n";
        foreach (array_slice($mdFiles, 0, 5) as $mdFile) {
            $fileName = basename($mdFile);
            echo "      - {$fileName}\n";
        }
        if ($mdCount > 5) {
            echo "      ... and " . ($mdCount - 5) . " more files\n";
        }
        $success[] = "Documentation includes {$mdCount} files";
    } else {
        echo "   ⚠️  No documentation files found\n";
        $warnings[] = "No documentation files found";
    }
} else {
    echo "   ❌ Documentation directory not found\n";
    $errors[] = "Documentation directory missing";
}

// 6. Verify Backup Manifest
echo "\n6️⃣ Verifying Backup Manifest...\n";
$manifestFile = $backupPath . '/BACKUP_MANIFEST.json';
if (file_exists($manifestFile)) {
    $manifest = json_decode(file_get_contents($manifestFile), true);
    if ($manifest) {
        echo "   ✅ Backup manifest exists and is valid JSON\n";
        echo "   📅 Created: " . ($manifest['backup_info']['created_at'] ?? 'Unknown') . "\n";
        echo "   🏷️  Name: " . ($manifest['backup_info']['name'] ?? 'Unknown') . "\n";
        $success[] = "Backup manifest is valid";
    } else {
        echo "   ❌ Backup manifest is corrupted\n";
        $errors[] = "Backup manifest is corrupted";
    }
} else {
    echo "   ⚠️  Backup manifest not found\n";
    $warnings[] = "Backup manifest missing";
}

// 7. Calculate Total Backup Size
echo "\n7️⃣ Calculating Backup Size...\n";
$totalSize = getDirSize($backupPath);
echo "   📊 Total backup size: " . formatBytes($totalSize) . "\n";

// 8. Verification Summary
echo "\n" . str_repeat("=", 60) . "\n";
echo "📋 VERIFICATION SUMMARY\n";
echo str_repeat("=", 60) . "\n";

echo "\n✅ SUCCESSES (" . count($success) . "):\n";
foreach ($success as $item) {
    echo "   • {$item}\n";
}

if (!empty($warnings)) {
    echo "\n⚠️  WARNINGS (" . count($warnings) . "):\n";
    foreach ($warnings as $item) {
        echo "   • {$item}\n";
    }
}

if (!empty($errors)) {
    echo "\n❌ ERRORS (" . count($errors) . "):\n";
    foreach ($errors as $item) {
        echo "   • {$item}\n";
    }
}

echo "\n🎯 OVERALL STATUS: ";
if (empty($errors)) {
    if (empty($warnings)) {
        echo "✅ PERFECT - Backup is complete and ready for restoration\n";
    } else {
        echo "⚠️  GOOD - Backup is usable with minor issues\n";
    }
} else {
    echo "❌ ISSUES FOUND - Backup may not restore properly\n";
}

echo "\n📊 BACKUP STATISTICS:\n";
echo "   • Total Size: " . formatBytes($totalSize) . "\n";
echo "   • Success Items: " . count($success) . "\n";
echo "   • Warnings: " . count($warnings) . "\n";
echo "   • Errors: " . count($errors) . "\n";

echo "\n✨ Verification completed at " . date('Y-m-d H:i:s') . "\n";

// Helper functions
function getDirSize($dir)
{
    $size = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }
    return $size;
}

function countFiles($dir)
{
    $count = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $count++;
        }
    }
    return $count;
}

function formatBytes($size, $precision = 2)
{
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}
