<!-- Create Uniform Modal -->
<x-modal name="create-uniform" maxWidth="4xl">
    <div class="p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-charcoal-black">Create New Uniform Order</h2>
            <button @click="$dispatch('close-modal', 'create-uniform')" class="text-medium-gray hover:text-charcoal-black">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="create-uniform-form" method="POST" action="{{ route('uniforms.store') }}" class="space-y-6">
            @csrf
            
            <!-- Student Selection -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="form-label-bank">Student <span class="text-error-red">*</span></label>
                    <select name="student_id" required class="form-select-bank" id="student-select">
                        <option value="">Select Student</option>
                        @foreach($students as $student)
                            <option value="{{ $student->id }}" 
                                    data-branch="{{ $student->branch_id }}" 
                                    data-academy="{{ $student->academy_id }}">
                                {{ $student->full_name }} - {{ $student->email }}
                            </option>
                        @endforeach
                    </select>
                    <div class="form-error-bank" id="student_id-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Branch <span class="text-error-red">*</span></label>
                    <select name="branch_id" required class="form-select-bank" id="branch-select">
                        <option value="">Select Branch</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                        @endforeach
                    </select>
                    <div class="form-error-bank" id="branch_id-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Academy <span class="text-error-red">*</span></label>
                    <select name="academy_id" required class="form-select-bank" id="academy-select">
                        <option value="">Select Academy</option>
                        @foreach($academies as $academy)
                            <option value="{{ $academy->id }}">{{ $academy->name }}</option>
                        @endforeach
                    </select>
                    <div class="form-error-bank" id="academy_id-error" style="display: none;"></div>
                </div>
            </div>

            <!-- Item Details -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                    <label class="form-label-bank">Item Type <span class="text-error-red">*</span></label>
                    <select name="item" required class="form-select-bank">
                        <option value="">Select Item</option>
                        <option value="jersey">Jersey</option>
                        <option value="shorts">Shorts</option>
                        <option value="socks">Socks</option>
                        <option value="tracksuit">Tracksuit</option>
                        <option value="jacket">Jacket</option>
                        <option value="cap">Cap</option>
                        <option value="bag">Bag</option>
                        <option value="complete_set">Complete Set</option>
                    </select>
                    <div class="form-error-bank" id="item-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Size <span class="text-error-red">*</span></label>
                    <select name="size" required class="form-select-bank">
                        <option value="">Select Size</option>
                        <optgroup label="Kids Sizes">
                            <option value="6xs-24-4">6XS (24-4)</option>
                            <option value="5xs-26-5">5XS (26-5)</option>
                            <option value="4xs-28-6">4XS (28-6)</option>
                            <option value="3xs-30-7">3XS (30-7)</option>
                            <option value="2xs-32-8">2XS (32-8)</option>
                        </optgroup>
                        <optgroup label="Standard Sizes">
                            <option value="xs-34-9">XS (34-9)</option>
                            <option value="s-36-10">S (36-10)</option>
                            <option value="m-38-11">M (38-11)</option>
                            <option value="l-40-12">L (40-12)</option>
                            <option value="xl-42-13">XL (42-13)</option>
                            <option value="xxl-44-14">XXL (44-14)</option>
                        </optgroup>
                        <optgroup label="Large Sizes">
                            <option value="3xl-46-15">3XL (46-15)</option>
                            <option value="4xl-48-16">4XL (48-16)</option>
                        </optgroup>
                    </select>
                    <div class="form-error-bank" id="size-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Quantity <span class="text-error-red">*</span></label>
                    <input type="number" name="quantity" min="1" max="50" value="1" required class="form-input-bank" id="quantity-input">
                    <div class="form-error-bank" id="quantity-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Unit Price (AED) <span class="text-error-red">*</span></label>
                    <input type="number" name="amount" step="0.01" min="0" max="999999.99" required class="form-input-bank" id="amount-input">
                    <div class="form-error-bank" id="amount-error" style="display: none;"></div>
                </div>
            </div>

            <!-- Total Amount Display -->
            <div class="bg-light-gray p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <span class="text-lg font-medium text-charcoal-black">Total Amount:</span>
                    <span class="text-2xl font-bold text-success-green" id="total-amount">AED 0.00</span>
                </div>
            </div>

            <!-- Payment & Status -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="form-label-bank">Payment Method <span class="text-error-red">*</span></label>
                    <select name="payment_method" required class="form-select-bank">
                        <option value="">Select Method</option>
                        <option value="cash">Cash</option>
                        <option value="card">Credit/Debit Card</option>
                        <option value="bank_transfer">Bank Transfer</option>
                    </select>
                    <div class="form-error-bank" id="payment_method-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Status <span class="text-error-red">*</span></label>
                    <select name="status" required class="form-select-bank">
                        <option value="ordered">Ordered</option>
                        <option value="processing">Processing</option>
                        <option value="ready">Ready for Pickup</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <div class="form-error-bank" id="status-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Order Date <span class="text-error-red">*</span></label>
                    <input type="date" name="order_date" value="{{ date('Y-m-d') }}" required class="form-input-bank">
                    <div class="form-error-bank" id="order_date-error" style="display: none;"></div>
                </div>
            </div>

            <!-- Tracking Status -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="form-label-bank">Branch Status <span class="text-error-red">*</span></label>
                    <select name="branch_status" required class="form-select-bank">
                        <option value="pending">Pending</option>
                        <option value="received">Received</option>
                        <option value="delivered">Delivered</option>
                    </select>
                    <div class="form-error-bank" id="branch_status-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Office Status <span class="text-error-red">*</span></label>
                    <select name="office_status" required class="form-select-bank">
                        <option value="pending">Pending</option>
                        <option value="received">Received</option>
                        <option value="delivered">Delivered</option>
                    </select>
                    <div class="form-error-bank" id="office_status-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Delivery Date</label>
                    <input type="date" name="delivery_date" class="form-input-bank">
                    <div class="form-error-bank" id="delivery_date-error" style="display: none;"></div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="form-label-bank">Reference Number</label>
                    <input type="text" name="reference_number" maxlength="50" class="form-input-bank" 
                           placeholder="Auto-generated if empty">
                    <div class="form-error-bank" id="reference_number-error" style="display: none;"></div>
                </div>

                <div>
                    <label class="form-label-bank">Description</label>
                    <input type="text" name="description" maxlength="1000" class="form-input-bank" 
                           placeholder="Brief description of the order">
                    <div class="form-error-bank" id="description-error" style="display: none;"></div>
                </div>
            </div>

            <!-- Notes -->
            <div>
                <label class="form-label-bank">Notes</label>
                <textarea name="note" rows="3" maxlength="1000" class="form-textarea-bank" 
                          placeholder="Additional notes or special instructions..."></textarea>
                <div class="form-error-bank" id="note-error" style="display: none;"></div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-light-gray">
                <button type="button" @click="$dispatch('close-modal', 'create-uniform')" class="btn-bank-outline">
                    Cancel
                </button>
                <button type="submit" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                        </path>
                    </svg>
                    Create Order
                </button>
            </div>
        </form>
    </div>
</x-modal>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate branch and academy when student is selected
    const studentSelect = document.getElementById('student-select');
    const branchSelect = document.getElementById('branch-select');
    const academySelect = document.getElementById('academy-select');
    const quantityInput = document.getElementById('quantity-input');
    const amountInput = document.getElementById('amount-input');
    const totalAmountDisplay = document.getElementById('total-amount');

    if (studentSelect) {
        studentSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                const branchId = selectedOption.dataset.branch;
                const academyId = selectedOption.dataset.academy;
                
                if (branchId) {
                    branchSelect.value = branchId;
                }
                if (academyId) {
                    academySelect.value = academyId;
                }
            }
        });
    }

    // Calculate total amount
    function updateTotalAmount() {
        const quantity = parseInt(quantityInput.value) || 0;
        const amount = parseFloat(amountInput.value) || 0;
        const total = quantity * amount;
        totalAmountDisplay.textContent = 'AED ' + total.toFixed(2);
    }

    if (quantityInput && amountInput) {
        quantityInput.addEventListener('input', updateTotalAmount);
        amountInput.addEventListener('input', updateTotalAmount);
    }

    // Form submission
    const createForm = document.getElementById('create-uniform-form');
    if (createForm) {
        createForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // Clear previous errors
            document.querySelectorAll('.form-error-bank').forEach(error => {
                error.style.display = 'none';
                error.textContent = '';
            });

            const formData = new FormData(this);
            
            try {
                const response = await fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    window.location.reload();
                } else {
                    if (result.errors) {
                        Object.keys(result.errors).forEach(field => {
                            const errorElement = document.getElementById(field + '-error');
                            if (errorElement) {
                                errorElement.textContent = result.errors[field][0];
                                errorElement.style.display = 'block';
                            }
                        });
                    } else {
                        alert('Error: ' + result.message);
                    }
                }
            } catch (error) {
                alert('An error occurred while creating the uniform order');
            }
        });
    }
});
</script>
