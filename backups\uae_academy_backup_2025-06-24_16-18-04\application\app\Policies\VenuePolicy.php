<?php

namespace App\Policies;

use App\Models\Venue;
use App\Models\User;

class VenuePolicy
{
    /**
     * Determine whether the user can view any venues.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the venue.
     */
    public function view(User $user, Venue $venue): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can create venues.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can update the venue.
     */
    public function update(User $user, Venue $venue): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can delete the venue.
     */
    public function delete(User $user, Venue $venue): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can restore the venue.
     */
    public function restore(User $user, Venue $venue): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can permanently delete the venue.
     */
    public function forceDelete(User $user, Venue $venue): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can export venue data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view venue statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can manage venue fields.
     */
    public function manageFields(User $user, Venue $venue): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can view venue reservations.
     */
    public function viewReservations(User $user, Venue $venue): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can manage venue reservations.
     */
    public function manageReservations(User $user, Venue $venue): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }
}
