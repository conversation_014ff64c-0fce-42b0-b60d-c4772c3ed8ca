/* Branch Management Enhanced Styles */

/* Premium Card Enhancements */
.branch-card-premium {
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.branch-card-premium:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border-color: #d1d5db;
}

/* Responsive Icon Sizing */
.search-icon-responsive {
    width: 1rem;
    height: 1rem;
}

@media (min-width: 640px) {
    .search-icon-responsive {
        width: 1.25rem;
        height: 1.25rem;
    }
}

/* Statistics Card Enhancements */
.stats-mini-card {
    background: linear-gradient(135deg, var(--bg-from) 0%, var(--bg-to) 100%);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.75rem;
    text-align: center;
    transition: all 0.2s ease-in-out;
}

.stats-mini-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Premium Search Bar Enhancements */
.premium-search-container {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.premium-search-input {
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.premium-search-input:focus {
    border-color: var(--leaders-red);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    background: #ffffff;
}

.premium-search-input:hover {
    border-color: #d1d5db;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Filter Group Enhancements */
.filter-group {
    position: relative;
}

.filter-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.filter-group select,
.filter-group input {
    transition: all 0.2s ease-in-out;
    font-weight: 500;
}

.filter-group select:focus,
.filter-group input:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Premium Button Animations */
.premium-apply-btn {
    background: linear-gradient(135deg, var(--leaders-red) 0%, var(--leaders-deep-red) 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.premium-apply-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.premium-apply-btn:hover::before {
    left: 100%;
}

.premium-apply-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Mobile-First Responsive Design */
@media (max-width: 640px) {
    /* Statistics Cards - Stack vertically on mobile */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .bank-card {
        padding: 1rem;
    }

    /* Search Form - Stack elements */
    .search-form {
        flex-direction: column;
        gap: 1rem;
    }

    .search-form .flex-1 {
        width: 100%;
    }

    /* Advanced Filters - Single column */
    .advanced-filters {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Table - Horizontal scroll */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .table-container table {
        min-width: 800px;
    }

    /* Bulk Actions - Stack vertically */
    .bulk-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    /* Premium Cards on Mobile */
    .branch-card-premium {
        margin: 0.5rem;
        border-radius: 0.75rem;
    }

    .branch-card-premium .card-header {
        padding: 1.25rem 1.25rem 1rem;
    }

    .branch-card-premium .card-footer {
        padding: 1rem 1.25rem;
    }

    /* Export buttons - Full width */
    .export-buttons {
        flex-direction: column;
        width: 100%;
    }

    .export-buttons a {
        width: 100%;
        text-align: center;
    }

    /* Form - Single column */
    .form-grid {
        grid-template-columns: 1fr;
    }

    /* Header actions - Stack */
    .header-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    /* Branch details - Compact view */
    .branch-details {
        padding: 0.75rem;
    }

    .branch-avatar {
        width: 2rem;
        height: 2rem;
        font-size: 0.75rem;
    }

    /* Hide less important columns on mobile */
    .table th:nth-child(n+4),
    .table td:nth-child(n+4) {
        display: none;
    }

    /* Show essential info only */
    .mobile-essential {
        display: block;
    }

    .mobile-hidden {
        display: none;
    }
}

/* Tablet Responsive */
@media (min-width: 641px) and (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .advanced-filters {
        grid-template-columns: repeat(2, 1fr);
    }

    .form-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop Enhancements */
@media (min-width: 1025px) {
    .stats-grid {
        grid-template-columns: repeat(5, 1fr);
    }

    .advanced-filters {
        grid-template-columns: repeat(4, 1fr);
    }

    /* Hover effects for desktop */
    .bank-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        transition: all 0.3s ease;
    }

    .table tbody tr:hover {
        background-color: #f8fafc;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }
}

/* Arabic RTL Support */
[dir="rtl"] {
    .search-form {
        direction: rtl;
    }

    .table {
        direction: rtl;
    }

    .table th,
    .table td {
        text-align: right;
    }

    .branch-avatar {
        margin-left: 1rem;
        margin-right: 0;
    }

    .stats-grid {
        direction: rtl;
    }

    .form-grid {
        direction: rtl;
    }

    /* Flip icons for RTL */
    .rtl-flip {
        transform: scaleX(-1);
    }
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.4s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Status Indicators */
.status-indicator {
    position: relative;
    display: inline-flex;
    align-items: center;
}

.status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.status-active::before {
    background-color: #10b981;
}

.status-inactive::before {
    background-color: #ef4444;
}

/* Enhanced Form Validation */
.form-field {
    position: relative;
}

.form-field.has-error .input-bank {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-field.has-success .input-bank {
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.error-message {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
}

.error-message::before {
    content: '⚠️';
    margin-right: 0.25rem;
}

/* Enhanced Notifications */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
    max-width: 400px;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background-color: #10b981;
    color: white;
}

.notification-error {
    background-color: #ef4444;
    color: white;
}

.notification-warning {
    background-color: #f59e0b;
    color: white;
}

.notification-info {
    background-color: #3b82f6;
    color: white;
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .bank-card {
        border: 1px solid #e5e7eb;
        box-shadow: none;
        page-break-inside: avoid;
    }

    .table {
        font-size: 12px;
    }

    .table th,
    .table td {
        padding: 0.5rem;
    }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
    .bank-card {
        background-color: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }

    .input-bank {
        background-color: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }

    .table th {
        background-color: #374151;
        color: #f9fafb;
    }

    .table td {
        border-color: #4b5563;
        color: #e5e7eb;
    }
}

/* Accessibility Enhancements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-visible:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .bank-card {
        border: 2px solid #000;
    }

    .btn-bank-primary {
        background-color: #000;
        color: #fff;
        border: 2px solid #000;
    }

    .btn-bank-outline {
        border: 2px solid #000;
        color: #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
