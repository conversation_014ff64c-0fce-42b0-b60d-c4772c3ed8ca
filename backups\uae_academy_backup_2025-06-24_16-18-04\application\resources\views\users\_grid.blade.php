<!-- Grid View -->
<div class="p-6">
    @if($users->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            @foreach($users as $user)
                <div class="bank-card bank-card-hover">
                    <div class="bank-card-body">
                        <!-- User Header -->
                        <div class="flex items-center justify-between mb-4">
                            @can('bulkAction', App\Models\User::class)
                                <input type="checkbox" name="user_ids[]" value="{{ $user->id }}"
                                    @change="toggleUserSelection({{ $user->id }})"
                                    :checked="selectedUsers.includes({{ $user->id }})"
                                    class="form-checkbox-bank">
                            @endcan
                            <span class="badge-bank {{ $user->status_badge_class }}">
                                {{ $user->status_text }}
                            </span>
                        </div>

                        <!-- User Avatar & Info -->
                        <div class="text-center mb-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center mx-auto mb-3">
                                <span class="text-white font-bold text-lg">
                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                </span>
                            </div>
                            <h3 class="font-semibold text-lg text-charcoal-black">{{ $user->name }}</h3>
                            <p class="text-sm text-dark-gray">{{ $user->email }}</p>
                        </div>

                        <!-- Role Badge -->
                        <div class="text-center mb-4">
                            <span class="badge-bank {{ $user->role_badge_class }}">
                                {{ $user->role_text }}
                            </span>
                        </div>

                        <!-- Branch & Academy Info -->
                        <div class="space-y-2 mb-4">
                            @if($user->branch)
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                        </path>
                                    </svg>
                                    <span class="text-sm text-charcoal-black">{{ $user->branch->name }}</span>
                                </div>
                            @endif
                            @if($user->academy)
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                        </path>
                                    </svg>
                                    <span class="text-sm text-charcoal-black">{{ $user->academy->name }}</span>
                                </div>
                            @endif
                            @if(!$user->branch && !$user->academy)
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4 text-medium-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4">
                                        </path>
                                    </svg>
                                    <span class="text-sm text-medium-gray">No assignment</span>
                                </div>
                            @endif
                        </div>

                        <!-- Last Login -->
                        <div class="flex items-center space-x-2 mb-4">
                            <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm text-dark-gray">{{ $user->last_login_text }}</span>
                        </div>

                        <!-- Created Date -->
                        <div class="flex items-center space-x-2 mb-4">
                            <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-sm text-dark-gray">{{ $user->formatted_created_at }}</span>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex items-center justify-center space-x-2">
                            @can('view', $user)
                                <a href="{{ route('users.show', $user) }}" class="btn-action btn-action-view"
                                    title="View User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @can('update', $user)
                                <a href="{{ route('users.edit', $user) }}" class="btn-action btn-action-edit"
                                    title="Edit User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @can('toggleStatus', $user)
                                <button onclick="toggleUserStatus({{ $user->id }})" 
                                    class="btn-action {{ $user->status ? 'btn-action-warning' : 'btn-action-success' }}"
                                    title="{{ $user->status ? 'Deactivate' : 'Activate' }} User">
                                    @if($user->status)
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728">
                                            </path>
                                        </svg>
                                    @else
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    @endif
                                </button>
                            @endcan

                            @can('delete', $user)
                                <button onclick="deleteUser({{ $user->id }})" class="btn-action btn-action-delete"
                                    title="Delete User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <!-- Empty State -->
        <div class="text-center py-12">
            <div class="flex flex-col items-center space-y-4">
                <svg class="w-24 h-24 text-medium-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                    </path>
                </svg>
                <div class="text-center">
                    <h3 class="text-xl font-semibold text-charcoal-black">No users found</h3>
                    <p class="text-dark-gray">Try adjusting your search criteria or create a new user.</p>
                </div>
                @can('create', App\Models\User::class)
                    <a href="{{ route('users.create') }}" class="btn-bank">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                            </path>
                        </svg>
                        Add New User
                    </a>
                @endcan
            </div>
        </div>
    @endif
</div>
