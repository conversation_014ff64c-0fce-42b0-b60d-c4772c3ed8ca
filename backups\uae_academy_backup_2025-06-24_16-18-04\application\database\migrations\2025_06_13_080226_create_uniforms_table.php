<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('uniforms', function (Blueprint $table) {
            $table->id();
            $table->foreignId('student_id')->constrained()->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('academy_id')->constrained()->onDelete('cascade');
            $table->date('order_date');
            $table->string('size'); // 6xs-24-4, XS, S, M, L, XL, XXL, etc.
            $table->decimal('amount', 10, 2); // AED with 2 decimal places
            $table->string('currency', 3)->default('AED');
            $table->enum('branch_status', ['pending', 'received', 'delivered'])->default('pending');
            $table->enum('office_status', ['pending', 'received', 'delivered'])->default('pending');
            $table->enum('payment_method', ['card', 'cash', 'bank_transfer'])->default('cash');
            $table->text('note')->charset('utf8mb4')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['student_id', 'branch_status', 'office_status']);
            $table->index(['branch_id', 'academy_id']);
            $table->index('order_date');
            $table->index('size');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('uniforms');
    }
};
