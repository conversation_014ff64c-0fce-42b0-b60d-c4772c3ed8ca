<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Models\Student;
use App\Models\Payment;
use App\Models\Uniform;
use Database\Seeders\StudentSeeder;
use Database\Seeders\PaymentSeeder;
use Database\Seeders\UniformSeeder;

class SeedRealisticStudents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seed:students
                            {--fresh : Clear existing students before seeding}
                            {--with-payments : Also seed payments for students}
                            {--with-uniforms : Also seed uniforms for students}
                            {--all : Seed students, payments, and uniforms}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Seed realistic students for all programs with optional payments and uniforms';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $fresh = $this->option('fresh');
        $withPayments = $this->option('with-payments') || $this->option('all');
        $withUniforms = $this->option('with-uniforms') || $this->option('all');

        if ($fresh) {
            $this->info('Clearing existing data...');

            // Disable foreign key checks temporarily
            DB::statement('SET FOREIGN_KEY_CHECKS=0;');

            if ($withUniforms) {
                Uniform::truncate();
                $this->info('Cleared uniforms');
            }

            if ($withPayments) {
                Payment::truncate();
                $this->info('Cleared payments');
            }

            // Clear related tables that reference students
            DB::table('attendances')->truncate();
            $this->info('Cleared attendances');

            Student::truncate();
            $this->info('Cleared students');

            // Re-enable foreign key checks
            DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        }

        $this->info('Seeding realistic students...');
        $this->call('db:seed', ['--class' => StudentSeeder::class]);

        if ($withPayments) {
            $this->info('Seeding payments...');
            $this->call('db:seed', ['--class' => PaymentSeeder::class]);
        }

        if ($withUniforms) {
            $this->info('Seeding uniforms...');
            $this->call('db:seed', ['--class' => UniformSeeder::class]);
        }

        // Display summary
        $this->newLine();
        $this->info('✅ Seeding completed successfully!');
        $this->newLine();

        $this->table(
            ['Entity', 'Count'],
            [
                ['Students', Student::count()],
                ['Payments', Payment::count()],
                ['Uniforms', Uniform::count()],
            ]
        );

        $this->newLine();
        $this->info('Sample data distribution:');

        // Show program enrollment stats
        $programs = \App\Models\Program::withCount('students')->get();
        $programStats = $programs->map(function ($program) {
            $percentage = $program->max_students > 0
                ? round(($program->students_count / $program->max_students) * 100, 1)
                : 0;
            return [
                'Program' => Str::limit($program->name, 30),
                'Students' => $program->students_count,
                'Max' => $program->max_students ?? 'N/A',
                'Fill %' => $percentage . '%'
            ];
        })->take(5)->toArray();

        $this->table(
            ['Program', 'Students', 'Max', 'Fill %'],
            $programStats
        );

        $this->newLine();
        $this->info('🎉 All programs now have realistic student enrollment for optimal UX!');
    }
}
