@extends('layouts.dashboard')

@section('title', 'Create User - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-success-green to-green-600 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Create New User</h1>
                <p class="text-lg text-dark-gray">Add a new user to the system</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('users.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Users
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="userForm()" x-init="init()">
        <form method="POST" action="{{ route('users.store') }}" class="space-y-6">
            @csrf

            <!-- Basic Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                    <p class="bank-card-subtitle">Enter the user's basic details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" value="{{ old('name') }}" required
                                class="form-control-bank @error('name') border-error-red @enderror"
                                placeholder="Enter full name">
                            @error('name')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" value="{{ old('email') }}" required
                                class="form-control-bank @error('email') border-error-red @enderror"
                                placeholder="Enter email address">
                            @error('email')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Password -->
                        <div>
                            <x-password-input id="password" name="password" placeholder="Enter password" label="Password"
                                required autocomplete="new-password" class="form-control-bank" />
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <x-password-input id="password_confirmation" name="password_confirmation"
                                placeholder="Confirm password" label="Confirm Password" required autocomplete="new-password"
                                class="form-control-bank" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role & Access Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Role & Access</h3>
                    <p class="bank-card-subtitle">Configure user role and permissions</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Role -->
                        <div>
                            <label for="role" class="form-label">User Role *</label>
                            <select id="role" name="role" x-model="selectedRole" @change="handleRoleChange()"
                                required class="form-select-bank @error('role') border-error-red @enderror">
                                <option value="">Select Role</option>
                                <option value="admin" {{ old('role') === 'admin' ? 'selected' : '' }}>
                                    System Administrator
                                </option>
                                <option value="branch_manager" {{ old('role') === 'branch_manager' ? 'selected' : '' }}>
                                    Branch Manager
                                </option>
                                <option value="academy_manager" {{ old('role') === 'academy_manager' ? 'selected' : '' }}>
                                    Academy Manager
                                </option>
                            </select>
                            @error('role')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Branch -->
                        <div x-show="selectedRole === 'branch_manager' || selectedRole === 'academy_manager'">
                            <label for="branch_id" class="form-label">Branch *</label>
                            <select id="branch_id" name="branch_id" x-model="selectedBranch" @change="loadAcademies()"
                                class="form-select-bank @error('branch_id') border-error-red @enderror">
                                <option value="">Select Branch</option>
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('branch_id')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Academy -->
                        <div x-show="selectedRole === 'academy_manager'">
                            <label for="academy_id" class="form-label">Academy *</label>
                            <select id="academy_id" name="academy_id" x-model="selectedAcademy"
                                class="form-select-bank @error('academy_id') border-error-red @enderror">
                                <option value="">Select Academy</option>
                                <template x-for="academy in academies" :key="academy.id">
                                    <option :value="academy.id" x-text="academy.name"></option>
                                </template>
                            </select>
                            @error('academy_id')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Role Description -->
                    <div class="mt-6 p-4 bg-off-white rounded-lg">
                        <div x-show="selectedRole === 'admin'">
                            <h4 class="font-semibold text-charcoal-black">System Administrator</h4>
                            <p class="text-sm text-dark-gray">Full access to all system features including user management,
                                all branches, academies, and system settings.</p>
                        </div>
                        <div x-show="selectedRole === 'branch_manager'">
                            <h4 class="font-semibold text-charcoal-black">Branch Manager</h4>
                            <p class="text-sm text-dark-gray">Manage all academies, programs, students, and operations
                                within assigned branches.</p>
                        </div>
                        <div x-show="selectedRole === 'academy_manager'">
                            <h4 class="font-semibold text-charcoal-black">Academy Manager</h4>
                            <p class="text-sm text-dark-gray">Manage programs, students, payments, and operations within
                                assigned academy only.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Account Status</h3>
                    <p class="bank-card-subtitle">Set the initial account status</p>
                </div>
                <div class="bank-card-body">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" id="status" name="status" value="1"
                            {{ old('status', true) ? 'checked' : '' }} class="form-checkbox-bank">
                        <label for="status" class="form-label mb-0">Active Account</label>
                    </div>
                    <p class="text-sm text-dark-gray mt-2">When checked, the user will be able to log in immediately after
                        creation.</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('users.index') }}" class="btn-bank btn-bank-outline">
                    Cancel
                </a>
                <button type="submit" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z">
                        </path>
                    </svg>
                    Create User
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function userForm() {
            return {
                selectedRole: '{{ old('role') }}',
                selectedBranch: '{{ old('branch_id') }}',
                selectedAcademy: '{{ old('academy_id') }}',
                allAcademies: @json($academies),
                academies: [],

                init() {
                    this.loadAcademies();
                },

                handleRoleChange() {
                    if (this.selectedRole === 'admin') {
                        this.selectedBranch = '';
                        this.selectedAcademy = '';
                        this.academies = [];
                    } else if (this.selectedRole === 'academy_manager') {
                        // Load academies when switching to academy_manager
                        this.loadAcademies();
                    }
                },

                loadAcademies() {
                    if (!this.selectedBranch) {
                        this.academies = [];
                        this.selectedAcademy = '';
                        return;
                    }

                    // Filter academies by selected branch
                    this.academies = this.allAcademies.filter(academy =>
                        academy.branch_id == this.selectedBranch
                    );

                    // Reset academy selection if current selection is not in new list
                    if (this.selectedAcademy && !this.academies.find(a => a.id == this.selectedAcademy)) {
                        this.selectedAcademy = '';
                    }
                }
            }
        }
    </script>
@endpush
