<?php

/**
 * UAE English Sports Academy - Backup Manager
 * Quick access tool for backup operations
 */

echo "🏆 UAE English Sports Academy - Backup Manager\n";
echo "=" . str_repeat("=", 50) . "\n\n";

$backupDir = '/Users/<USER>/Sites/uae_english_sports_academy/backups';

if (!is_dir($backupDir)) {
    echo "❌ Backup directory not found: {$backupDir}\n";
    exit(1);
}

// List available backups
$backups = glob($backupDir . '/uae_academy_backup_*');
usort($backups, function($a, $b) {
    return filemtime($b) - filemtime($a);
});

echo "📁 Available Backups:\n";
echo str_repeat("-", 50) . "\n";

if (empty($backups)) {
    echo "No backups found.\n";
    exit(0);
}

foreach ($backups as $index => $backup) {
    $backupName = basename($backup);
    $backupDate = date('Y-m-d H:i:s', filemtime($backup));
    $backupSize = formatBytes(getDirSize($backup));
    
    echo sprintf("%d. %s\n", $index + 1, $backupName);
    echo sprintf("   📅 Created: %s\n", $backupDate);
    echo sprintf("   📊 Size: %s\n", $backupSize);
    echo sprintf("   📁 Path: %s\n", $backup);
    echo "\n";
}

echo "🔧 Available Operations:\n";
echo str_repeat("-", 50) . "\n";
echo "1. Verify backup integrity\n";
echo "2. Create new backup\n";
echo "3. Restore from backup\n";
echo "4. View backup details\n";
echo "5. Exit\n\n";

echo "Enter your choice (1-5): ";
$choice = trim(fgets(STDIN));

switch ($choice) {
    case '1':
        echo "\nSelect backup to verify (1-" . count($backups) . "): ";
        $backupIndex = trim(fgets(STDIN)) - 1;
        
        if (isset($backups[$backupIndex])) {
            $backupPath = $backups[$backupIndex];
            echo "\n🔍 Verifying backup: " . basename($backupPath) . "\n";
            system("php verify_backup.php " . escapeshellarg($backupPath));
        } else {
            echo "❌ Invalid backup selection.\n";
        }
        break;
        
    case '2':
        echo "\n🔄 Creating new backup...\n";
        system("php create_backup.php");
        break;
        
    case '3':
        echo "\nSelect backup to restore (1-" . count($backups) . "): ";
        $backupIndex = trim(fgets(STDIN)) - 1;
        
        if (isset($backups[$backupIndex])) {
            echo "Enter target path for restoration: ";
            $targetPath = trim(fgets(STDIN));
            
            if ($targetPath) {
                $backupPath = $backups[$backupIndex];
                echo "\n🔄 Restoring backup...\n";
                system("php restore_backup.php " . escapeshellarg($backupPath) . " " . escapeshellarg($targetPath));
            } else {
                echo "❌ Target path is required.\n";
            }
        } else {
            echo "❌ Invalid backup selection.\n";
        }
        break;
        
    case '4':
        echo "\nSelect backup to view details (1-" . count($backups) . "): ";
        $backupIndex = trim(fgets(STDIN)) - 1;
        
        if (isset($backups[$backupIndex])) {
            $backupPath = $backups[$backupIndex];
            $manifestFile = $backupPath . '/BACKUP_MANIFEST.json';
            
            echo "\n📋 Backup Details:\n";
            echo str_repeat("-", 50) . "\n";
            
            if (file_exists($manifestFile)) {
                $manifest = json_decode(file_get_contents($manifestFile), true);
                
                echo "Name: " . ($manifest['backup_info']['name'] ?? 'Unknown') . "\n";
                echo "Created: " . ($manifest['backup_info']['created_at'] ?? 'Unknown') . "\n";
                echo "Version: " . ($manifest['backup_info']['version'] ?? 'Unknown') . "\n";
                echo "Type: " . ($manifest['backup_info']['backup_type'] ?? 'Unknown') . "\n";
                echo "Database: " . ($manifest['database']['name'] ?? 'Unknown') . "\n";
                echo "Size: " . formatBytes(getDirSize($backupPath)) . "\n";
                
                echo "\nApplication Directories:\n";
                foreach ($manifest['application']['directories'] ?? [] as $dir) {
                    echo "  - {$dir}/\n";
                }
                
                echo "\nConfiguration Files:\n";
                foreach ($manifest['configuration']['files'] ?? [] as $file) {
                    echo "  - {$file}\n";
                }
            } else {
                echo "❌ Manifest file not found.\n";
            }
        } else {
            echo "❌ Invalid backup selection.\n";
        }
        break;
        
    case '5':
        echo "\n👋 Goodbye!\n";
        break;
        
    default:
        echo "❌ Invalid choice. Please select 1-5.\n";
        break;
}

// Helper functions
function getDirSize($dir) {
    $size = 0;
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    foreach ($iterator as $file) {
        if ($file->isFile()) {
            $size += $file->getSize();
        }
    }
    return $size;
}

function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    return round($size, $precision) . ' ' . $units[$i];
}
