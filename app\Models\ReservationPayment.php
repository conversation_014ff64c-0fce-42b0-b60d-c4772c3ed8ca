<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class ReservationPayment extends Model
{
    protected $fillable = [
        'reservation_id',
        'customer_id',
        'venue_id',
        'payment_number',
        'reference_number',
        'payment_type',
        'amount',
        'subtotal',
        'vat_rate',
        'vat_amount',
        'total_amount',
        'vat_inclusive',
        'currency',
        'payment_method',
        'card_type',
        'card_last_four',
        'transaction_id',
        'authorization_code',
        'payment_date',
        'payment_datetime',
        'status',
        'processed_at',
        'failed_at',
        'failure_reason',
        'refunded_at',
        'refund_amount',
        'refund_reason',
        'receipt_number',
        'receipt_printed',
        'receipt_emailed',
        'payment_gateway_response',
        'notes',
        'notes_ar',
        'processed_by',
        'refunded_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'vat_rate' => 'decimal:2',
        'vat_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'refund_amount' => 'decimal:2',
        'vat_inclusive' => 'boolean',
        'receipt_printed' => 'boolean',
        'receipt_emailed' => 'boolean',
        'payment_date' => 'date',
        'payment_datetime' => 'datetime',
        'processed_at' => 'datetime',
        'failed_at' => 'datetime',
        'refunded_at' => 'datetime',
        'payment_gateway_response' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_amount',
        'formatted_total_amount',
        'formatted_refund_amount',
        'status_text',
        'status_badge_class',
        'payment_type_text',
        'payment_method_text',
        'localized_notes',
        'is_refundable',
        'net_amount',
    ];

    /**
     * Get the reservation for this payment.
     */
    public function reservation(): BelongsTo
    {
        return $this->belongsTo(Reservation::class);
    }

    /**
     * Get the customer for this payment.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the venue for this payment.
     */
    public function venue(): BelongsTo
    {
        return $this->belongsTo(Venue::class);
    }

    /**
     * Get the user who processed this payment.
     */
    public function processedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'processed_by');
    }

    /**
     * Get the user who processed the refund.
     */
    public function refundedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'refunded_by');
    }

    // Computed Properties

    /**
     * Get formatted amount.
     */
    public function getFormattedAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->amount, 2);
    }

    /**
     * Get formatted total amount.
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->total_amount, 2);
    }

    /**
     * Get formatted refund amount.
     */
    public function getFormattedRefundAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->refund_amount, 2);
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'Pending',
            'completed' => 'Completed',
            'failed' => 'Failed',
            'cancelled' => 'Cancelled',
            'refunded' => 'Refunded',
            default => 'Unknown',
        };
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'badge-warning',
            'completed' => 'badge-success',
            'failed' => 'badge-danger',
            'cancelled' => 'badge-secondary',
            'refunded' => 'badge-info',
            default => 'badge-secondary',
        };
    }

    /**
     * Get payment type text.
     */
    public function getPaymentTypeTextAttribute(): string
    {
        return match ($this->payment_type) {
            'deposit' => 'Deposit',
            'full_payment' => 'Full Payment',
            'partial_payment' => 'Partial Payment',
            'balance' => 'Balance',
            'refund' => 'Refund',
            default => 'Payment',
        };
    }

    /**
     * Get payment method text.
     */
    public function getPaymentMethodTextAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'Cash',
            'card' => 'Card',
            'bank_transfer' => 'Bank Transfer',
            'online' => 'Online',
            'cheque' => 'Cheque',
            'credit' => 'Credit',
            default => 'Unknown',
        };
    }

    /**
     * Get localized notes.
     */
    public function getLocalizedNotesAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->notes_ar ? $this->notes_ar : $this->notes;
    }

    /**
     * Check if payment is refundable.
     */
    public function getIsRefundableAttribute(): bool
    {
        return $this->status === 'completed' && 
               $this->payment_type !== 'refund' && 
               $this->refund_amount < $this->total_amount;
    }

    /**
     * Get net amount (total - refunded).
     */
    public function getNetAmountAttribute(): float
    {
        return $this->total_amount - $this->refund_amount;
    }

    // Query Scopes

    /**
     * Scope to get completed payments.
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope to get pending payments.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to filter by payment method.
     */
    public function scopeByPaymentMethod(Builder $query, string $method): Builder
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope to filter by payment type.
     */
    public function scopeByPaymentType(Builder $query, string $type): Builder
    {
        return $query->where('payment_type', $type);
    }

    /**
     * Scope to filter by venue.
     */
    public function scopeByVenue(Builder $query, int $venueId): Builder
    {
        return $query->where('venue_id', $venueId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeByDateRange(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('payment_date', [$startDate, $endDate]);
    }

    // Utility Methods

    /**
     * Generate unique payment number.
     */
    public static function generatePaymentNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $counter = self::whereYear('created_at', $year)
                      ->whereMonth('created_at', $month)
                      ->count() + 1;
        
        return 'PAY' . $year . $month . str_pad($counter, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Generate receipt number.
     */
    public function generateReceiptNumber(): string
    {
        $year = now()->year;
        $counter = self::whereYear('created_at', $year)->count();
        
        return 'RCP' . $year . str_pad($counter, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate VAT amount.
     */
    public function calculateVAT(): void
    {
        if ($this->vat_inclusive) {
            // VAT is included in the amount
            $this->vat_amount = ($this->amount * $this->vat_rate) / (100 + $this->vat_rate);
            $this->subtotal = $this->amount - $this->vat_amount;
            $this->total_amount = $this->amount;
        } else {
            // VAT is added to the amount
            $this->subtotal = $this->amount;
            $this->vat_amount = ($this->amount * $this->vat_rate) / 100;
            $this->total_amount = $this->amount + $this->vat_amount;
        }
    }

    /**
     * Process the payment.
     */
    public function process(?int $processedBy = null): bool
    {
        $this->status = 'completed';
        $this->processed_at = now();
        $this->processed_by = $processedBy ?? auth()->id();
        
        if (!$this->receipt_number) {
            $this->receipt_number = $this->generateReceiptNumber();
        }
        
        return $this->save();
    }

    /**
     * Fail the payment.
     */
    public function fail(string $reason): bool
    {
        $this->status = 'failed';
        $this->failed_at = now();
        $this->failure_reason = $reason;
        
        return $this->save();
    }

    /**
     * Refund the payment.
     */
    public function refund(float $amount, string $reason, ?int $refundedBy = null): bool
    {
        if ($amount > ($this->total_amount - $this->refund_amount)) {
            return false; // Cannot refund more than available
        }
        
        $this->refund_amount += $amount;
        $this->refund_reason = $reason;
        $this->refunded_at = now();
        $this->refunded_by = $refundedBy ?? auth()->id();
        
        // If fully refunded, update status
        if ($this->refund_amount >= $this->total_amount) {
            $this->status = 'refunded';
        }
        
        return $this->save();
    }

    /**
     * Get payment statistics for a venue.
     */
    public static function getVenueStatistics(int $venueId, ?string $startDate = null, ?string $endDate = null): array
    {
        $query = self::where('venue_id', $venueId)->where('status', 'completed');
        
        if ($startDate && $endDate) {
            $query->whereBetween('payment_date', [$startDate, $endDate]);
        }
        
        $payments = $query->get();
        
        return [
            'total_payments' => $payments->count(),
            'total_amount' => $payments->sum('total_amount'),
            'total_vat' => $payments->sum('vat_amount'),
            'total_refunds' => $payments->sum('refund_amount'),
            'net_revenue' => $payments->sum('total_amount') - $payments->sum('refund_amount'),
            'payment_methods' => $payments->groupBy('payment_method')->map->count(),
            'payment_types' => $payments->groupBy('payment_type')->map->count(),
        ];
    }
}
