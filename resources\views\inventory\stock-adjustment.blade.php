@extends('layouts.dashboard')

@section('title', __('Stock Adjustment'))

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ __('Stock Adjustment') }}</h1>
                <p class="text-lg text-dark-gray">{{ __('Adjust inventory stock levels') }}</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('inventory.show', $inventory) }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                {{ __('View Item') }}
            </a>
            <a href="{{ route('inventory.index') }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                {{ __('Back to Inventory') }}
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="stockAdjustmentForm()">
        <!-- Item Information Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Item Information') }}</h3>
            </div>
            <div class="bank-card-body">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div>
                        <label class="form-label">{{ __('Item Name') }}</label>
                        <p class="font-medium">{{ $inventory->name }}</p>
                        <p class="text-sm text-gray-500">{{ $inventory->name_ar }}</p>
                    </div>
                    <div>
                        <label class="form-label">{{ __('SKU') }}</label>
                        <p class="font-medium">{{ $inventory->sku }}</p>
                    </div>
                    <div>
                        <label class="form-label">{{ __('Current Stock') }}</label>
                        <p class="font-medium text-2xl 
                            @if($inventory->current_stock <= 0) text-red-600
                            @elseif($inventory->current_stock <= $inventory->minimum_stock) text-yellow-600
                            @else text-green-600 @endif">
                            {{ $inventory->current_stock }}
                        </p>
                    </div>
                    <div>
                        <label class="form-label">{{ __('Available Stock') }}</label>
                        <p class="font-medium">{{ $inventory->available_stock }}</p>
                        <p class="text-sm text-gray-500">Reserved: {{ $inventory->reserved_stock ?? 0 }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Adjustment Form -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Adjust Stock') }}</h3>
                </div>
                <div class="bank-card-body">
                    <form method="POST" action="{{ route('inventory.adjust-stock', $inventory) }}" @submit="handleSubmit">
                        @csrf

                        <div class="space-y-6">
                            <!-- Adjustment Type -->
                            <div>
                                <label class="form-label required">{{ __('Adjustment Type') }}</label>
                                <div class="grid grid-cols-3 gap-3">
                                    <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                        :class="adjustmentType === 'increase' ? 'border-green-500 bg-green-50' : 'border-gray-300'">
                                        <input type="radio" name="adjustment_type" value="increase" 
                                            x-model="adjustmentType" class="form-radio text-green-600">
                                        <div class="ml-3">
                                            <div class="font-medium text-green-800">{{ __('Increase') }}</div>
                                            <div class="text-sm text-green-600">{{ __('Add stock') }}</div>
                                        </div>
                                    </label>
                                    
                                    <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                        :class="adjustmentType === 'decrease' ? 'border-red-500 bg-red-50' : 'border-gray-300'">
                                        <input type="radio" name="adjustment_type" value="decrease" 
                                            x-model="adjustmentType" class="form-radio text-red-600">
                                        <div class="ml-3">
                                            <div class="font-medium text-red-800">{{ __('Decrease') }}</div>
                                            <div class="text-sm text-red-600">{{ __('Remove stock') }}</div>
                                        </div>
                                    </label>
                                    
                                    <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                                        :class="adjustmentType === 'set' ? 'border-blue-500 bg-blue-50' : 'border-gray-300'">
                                        <input type="radio" name="adjustment_type" value="set" 
                                            x-model="adjustmentType" class="form-radio text-blue-600">
                                        <div class="ml-3">
                                            <div class="font-medium text-blue-800">{{ __('Set To') }}</div>
                                            <div class="text-sm text-blue-600">{{ __('Set exact amount') }}</div>
                                        </div>
                                    </label>
                                </div>
                                @error('adjustment_type')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Quantity -->
                            <div>
                                <label for="quantity" class="form-label required">
                                    <span x-show="adjustmentType === 'increase'">{{ __('Quantity to Add') }}</span>
                                    <span x-show="adjustmentType === 'decrease'">{{ __('Quantity to Remove') }}</span>
                                    <span x-show="adjustmentType === 'set'">{{ __('New Stock Level') }}</span>
                                </label>
                                <input type="number" id="quantity" name="quantity" x-model="quantity"
                                    class="form-input-bank" min="1" required>
                                @error('quantity')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                                
                                <!-- Preview -->
                                <div x-show="adjustmentType && quantity" class="mt-2 p-3 bg-gray-50 rounded-lg">
                                    <div class="text-sm">
                                        <span class="font-medium">{{ __('Preview:') }}</span>
                                        <span x-text="getPreviewText()"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- Reason -->
                            <div>
                                <label for="reason" class="form-label required">{{ __('Reason') }}</label>
                                <select id="reason" name="reason" class="form-select-bank" required>
                                    <option value="">{{ __('Select Reason') }}</option>
                                    <option value="damaged">{{ __('Damaged Items') }}</option>
                                    <option value="lost">{{ __('Lost Items') }}</option>
                                    <option value="found">{{ __('Found Items') }}</option>
                                    <option value="recount">{{ __('Physical Recount') }}</option>
                                    <option value="return">{{ __('Customer Return') }}</option>
                                    <option value="supplier_return">{{ __('Return to Supplier') }}</option>
                                    <option value="transfer_in">{{ __('Transfer In') }}</option>
                                    <option value="transfer_out">{{ __('Transfer Out') }}</option>
                                    <option value="correction">{{ __('Data Correction') }}</option>
                                    <option value="other">{{ __('Other') }}</option>
                                </select>
                                @error('reason')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Reference Number -->
                            <div>
                                <label for="reference_number" class="form-label">{{ __('Reference Number') }}</label>
                                <input type="text" id="reference_number" name="reference_number"
                                    class="form-input-bank" placeholder="e.g., ADJ-2024-001">
                                @error('reference_number')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Notes -->
                            <div>
                                <label for="notes" class="form-label">{{ __('Notes') }}</label>
                                <textarea id="notes" name="notes" rows="3" class="form-textarea-bank"
                                    placeholder="{{ __('Additional notes about this adjustment...') }}"></textarea>
                                @error('notes')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Submit Button -->
                            <div class="flex items-center space-x-4 pt-6 border-t border-light-gray">
                                <button type="submit" class="btn-bank" :disabled="!adjustmentType || !quantity">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12">
                                        </path>
                                    </svg>
                                    {{ __('Apply Adjustment') }}
                                </button>
                                <a href="{{ route('inventory.show', $inventory) }}" class="btn-bank-outline">
                                    {{ __('Cancel') }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Recent Stock Movements -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Recent Stock Movements') }}</h3>
                </div>
                <div class="bank-card-body">
                    @if($recentMovements->count() > 0)
                        <div class="space-y-3">
                            @foreach($recentMovements as $movement)
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <div class="font-medium text-sm">
                                            {{ ucfirst(str_replace('_', ' ', $movement->movement_type)) }}
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            {{ $movement->movement_date->format('M j, Y H:i') }}
                                        </div>
                                        <div class="text-xs text-gray-600">{{ $movement->reason }}</div>
                                    </div>
                                    <div class="text-right">
                                        <div class="font-medium 
                                            @if(str_contains($movement->movement_type, 'in') || str_contains($movement->movement_type, 'increase')) text-green-600
                                            @else text-red-600 @endif">
                                            @if(str_contains($movement->movement_type, 'in') || str_contains($movement->movement_type, 'increase'))
                                                +{{ $movement->quantity }}
                                            @else
                                                -{{ $movement->quantity }}
                                            @endif
                                        </div>
                                        <div class="text-xs text-gray-500">
                                            {{ $movement->quantity_before }} → {{ $movement->quantity_after }}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8 text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                            <p>{{ __('No stock movements recorded yet') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            function stockAdjustmentForm() {
                return {
                    adjustmentType: '',
                    quantity: '',
                    currentStock: {{ $inventory->current_stock }},

                    getPreviewText() {
                        if (!this.adjustmentType || !this.quantity) return '';
                        
                        const qty = parseInt(this.quantity);
                        let newStock = this.currentStock;
                        
                        switch (this.adjustmentType) {
                            case 'increase':
                                newStock = this.currentStock + qty;
                                return `Current: ${this.currentStock} → New: ${newStock} (+${qty})`;
                            case 'decrease':
                                newStock = Math.max(0, this.currentStock - qty);
                                return `Current: ${this.currentStock} → New: ${newStock} (-${qty})`;
                            case 'set':
                                const diff = qty - this.currentStock;
                                const sign = diff >= 0 ? '+' : '';
                                return `Current: ${this.currentStock} → New: ${qty} (${sign}${diff})`;
                        }
                        return '';
                    },

                    handleSubmit(event) {
                        if (!this.adjustmentType || !this.quantity) {
                            event.preventDefault();
                            alert('Please select adjustment type and enter quantity');
                            return;
                        }

                        if (this.adjustmentType === 'decrease' && parseInt(this.quantity) > this.currentStock) {
                            if (!confirm('This will reduce stock below zero. Are you sure you want to continue?')) {
                                event.preventDefault();
                                return;
                            }
                        }

                        if (!confirm('Are you sure you want to apply this stock adjustment?')) {
                            event.preventDefault();
                        }
                    }
                }
            }
        </script>
    @endpush
@endsection
