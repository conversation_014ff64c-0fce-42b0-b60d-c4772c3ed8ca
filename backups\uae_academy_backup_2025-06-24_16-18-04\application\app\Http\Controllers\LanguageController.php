<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Auth;

class LanguageController extends Controller
{
    /**
     * Switch the application language.
     */
    public function switch(Request $request, string $locale): RedirectResponse
    {
        // Validate the locale
        if (!in_array($locale, ['en', 'ar'])) {
            return back()->with('error', 'Invalid language selection');
        }

        // Set the application locale
        App::setLocale($locale);

        // Store the locale in session
        Session::put('locale', $locale);

        // Store the locale in cookie for persistence
        Cookie::queue('locale', $locale, 60 * 24 * 365); // 1 year

        // Update user preference if authenticated
        if (Auth::check()) {
            Auth::user()->update(['preferred_language' => $locale]);
        }

        return back()->with('success', 'Language changed successfully');
    }

    /**
     * Get the current locale.
     */
    public function current(): string
    {
        return App::getLocale();
    }

    /**
     * Get available locales.
     */
    public function available(): array
    {
        return [
            'en' => [
                'code' => 'en',
                'name' => 'English',
                'native' => 'English',
                'flag' => '🇺🇸',
                'direction' => 'ltr'
            ],
            'ar' => [
                'code' => 'ar',
                'name' => 'Arabic',
                'native' => 'العربية',
                'flag' => '🇦🇪',
                'direction' => 'rtl'
            ]
        ];
    }

    /**
     * Get language direction (LTR or RTL).
     */
    public function direction(?string $locale = null): string
    {
        $locale = $locale ?? App::getLocale();

        return match ($locale) {
            'ar' => 'rtl',
            default => 'ltr',
        };
    }

    /**
     * Check if current locale is RTL.
     */
    public function isRtl(?string $locale = null): bool
    {
        return $this->direction($locale) === 'rtl';
    }

    /**
     * Get localized text based on current locale.
     */
    public function getLocalizedText(?string $english, ?string $arabic): ?string
    {
        if (App::getLocale() === 'ar' && !empty($arabic)) {
            return $arabic;
        }

        return $english;
    }

    /**
     * Format number based on locale.
     */
    public function formatNumber(float $number, int $decimals = 2, ?string $locale = null): string
    {
        $locale = $locale ?? App::getLocale();

        if ($locale === 'ar') {
            // Arabic number formatting
            return number_format($number, $decimals, '.', ',');
        }

        // English number formatting
        return number_format($number, $decimals, '.', ',');
    }

    /**
     * Format currency based on locale.
     */
    public function formatCurrency(float $amount, string $currency = 'AED', ?string $locale = null): string
    {
        $locale = $locale ?? App::getLocale();
        $formattedNumber = $this->formatNumber($amount, 2, $locale);

        if ($locale === 'ar') {
            return $formattedNumber . ' ' . $currency;
        }

        return $currency . ' ' . $formattedNumber;
    }

    /**
     * Format date based on locale.
     */
    public function formatDate(\DateTime $date, ?string $format = null, ?string $locale = null): string
    {
        $locale = $locale ?? App::getLocale();

        if ($locale === 'ar') {
            $format = $format ?? 'd/m/Y';
        } else {
            $format = $format ?? 'm/d/Y';
        }

        return $date->format($format);
    }

    /**
     * Get month names in current locale.
     */
    public function getMonthNames(?string $locale = null): array
    {
        $locale = $locale ?? App::getLocale();

        if ($locale === 'ar') {
            return [
                1 => 'يناير',
                2 => 'فبراير',
                3 => 'مارس',
                4 => 'أبريل',
                5 => 'مايو',
                6 => 'يونيو',
                7 => 'يوليو',
                8 => 'أغسطس',
                9 => 'سبتمبر',
                10 => 'أكتوبر',
                11 => 'نوفمبر',
                12 => 'ديسمبر'
            ];
        }

        return [
            1 => 'January',
            2 => 'February',
            3 => 'March',
            4 => 'April',
            5 => 'May',
            6 => 'June',
            7 => 'July',
            8 => 'August',
            9 => 'September',
            10 => 'October',
            11 => 'November',
            12 => 'December'
        ];
    }

    /**
     * Get day names in current locale.
     */
    public function getDayNames(?string $locale = null): array
    {
        $locale = $locale ?? App::getLocale();

        if ($locale === 'ar') {
            return [
                'sunday' => 'الأحد',
                'monday' => 'الإثنين',
                'tuesday' => 'الثلاثاء',
                'wednesday' => 'الأربعاء',
                'thursday' => 'الخميس',
                'friday' => 'الجمعة',
                'saturday' => 'السبت'
            ];
        }

        return [
            'sunday' => 'Sunday',
            'monday' => 'Monday',
            'tuesday' => 'Tuesday',
            'wednesday' => 'Wednesday',
            'thursday' => 'Thursday',
            'friday' => 'Friday',
            'saturday' => 'Saturday'
        ];
    }

    /**
     * Convert English numbers to Arabic numbers.
     */
    public function toArabicNumbers(string $text): string
    {
        $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

        return str_replace($englishNumbers, $arabicNumbers, $text);
    }

    /**
     * Convert Arabic numbers to English numbers.
     */
    public function toEnglishNumbers(string $text): string
    {
        $arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        $englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

        return str_replace($arabicNumbers, $englishNumbers, $text);
    }

    /**
     * Get CSS class for text alignment based on locale.
     */
    public function getTextAlignClass(?string $locale = null): string
    {
        return $this->isRtl($locale) ? 'text-right' : 'text-left';
    }

    /**
     * Get CSS class for float direction based on locale.
     */
    public function getFloatClass(string $direction = 'left', ?string $locale = null): string
    {
        if ($this->isRtl($locale)) {
            return $direction === 'left' ? 'float-right' : 'float-left';
        }

        return 'float-' . $direction;
    }

    /**
     * Get margin/padding class for locale-aware spacing.
     */
    public function getSpacingClass(string $property, string $side, string $size, ?string $locale = null): string
    {
        if ($this->isRtl($locale)) {
            $side = match ($side) {
                'left' => 'right',
                'right' => 'left',
                default => $side
            };
        }

        return $property . $side . '-' . $size;
    }
}
