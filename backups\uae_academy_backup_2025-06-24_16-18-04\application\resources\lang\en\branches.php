<?php

// UAE English Sports Academy - Branch Management Translations (English)
// Complete translation file for branch management module

return [
    // Page Titles
    'title' => 'Branch Management',
    'create_title' => 'Create New Branch',
    'edit_title' => 'Edit Branch',
    'show_title' => 'Branch Details',

    // Navigation & Actions
    'add_new_branch' => 'Add New Branch',
    'back_to_branches' => 'Back to Branches',
    'view_branch' => 'View Branch',
    'edit_branch' => 'Edit Branch',
    'delete_branch' => 'Delete Branch',
    'create_branch' => 'Create Branch',
    'update_branch' => 'Update Branch',
    'save_changes' => 'Save Changes',
    'cancel' => 'Cancel',

    // Form Fields
    'branch_name' => 'Branch Name',
    'branch_name_placeholder' => 'e.g., Dubai Marina Branch',
    'location' => 'Location',
    'location_placeholder' => 'e.g., Dubai Marina, Dubai',
    'location_help' => 'Enter the general location or area of the branch',
    'full_address' => 'Full Address',
    'address_placeholder' => 'Enter the complete address including building name, street, area, and emirate',
    'address_help' => 'Optional: Provide detailed address for better location identification',
    'phone_number' => 'Phone Number',
    'phone_placeholder' => '50 123 4567',
    'phone_help' => 'Enter UAE phone number without country code',
    'email_address' => 'Email Address',
    'email_placeholder' => '<EMAIL>',
    'email_help' => 'Optional: Branch-specific email address',
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',

    // Statistics
    'total_branches' => 'Total Branches',
    'active_branches' => 'Active Branches',
    'inactive_branches' => 'Inactive Branches',
    'total_academies' => 'Total Academies',
    'total_students' => 'Total Students',
    'total_programs' => 'Total Programs',
    'total_revenue' => 'Total Revenue',
    'pending_payments' => 'Pending Payments',

    // Search & Filters
    'search' => 'Search',
    'search_placeholder' => 'Search by name, location, address, phone, or email...',
    'advanced_filters' => 'Advanced',
    'hide_filters' => 'Hide Filters',
    'all_status' => 'All Status',
    'active_only' => 'Active Only',
    'inactive_only' => 'Inactive Only',
    'date_from' => 'Date From',
    'date_to' => 'Date To',
    'min_academies' => 'Min Academies',
    'max_academies' => 'Max Academies',
    'min_students' => 'Min Students',
    'max_students' => 'Max Students',
    'all_locations' => 'All Locations',
    'sort_by' => 'Sort By',
    'sort_by_name' => 'Name',
    'sort_by_location' => 'Location',
    'sort_by_date_created' => 'Date Created',
    'sort_by_last_updated' => 'Last Updated',
    'sort_by_academy_count' => 'Academy Count',
    'sort_by_student_count' => 'Student Count',
    'ascending' => 'Ascending',
    'descending' => 'Descending',
    'clear_all_filters' => 'Clear All Filters',
    'per_page' => 'per page',

    // Bulk Actions
    'select_all' => 'Select All',
    'bulk_actions' => 'Bulk Actions',
    'choose_action' => 'Choose Action',
    'activate_selected' => 'Activate Selected',
    'deactivate_selected' => 'Deactivate Selected',
    'delete_selected' => 'Delete Selected',
    'execute' => 'Execute',

    // Export
    'export' => 'Export',
    'export_excel' => 'Excel',
    'export_pdf' => 'PDF',

    // Table Headers
    'branch_details' => 'Branch Details',
    'location_contact' => 'Location & Contact',
    'statistics' => 'Statistics',
    'actions' => 'Actions',

    // Messages
    'no_branches_found' => 'No branches found',
    'no_branches_match_filters' => 'No branches match your current filters. Try adjusting your search criteria.',
    'get_started_message' => 'Get started by creating your first branch.',
    'add_first_branch' => 'Add First Branch',
    'clear_filters' => 'Clear Filters',

    // Status Messages
    'branch_active_message' => 'This branch is currently active and operational',
    'branch_inactive_message' => 'This branch is currently inactive',

    // Quick Actions
    'quick_actions' => 'Quick Actions',
    'activate_branch' => 'Activate Branch',
    'deactivate_branch' => 'Deactivate Branch',
    'export_branch_report' => 'Export Branch Report',
    'view_academies' => 'View Academies',
    'view_students' => 'View Students',
    'view_payments' => 'View Payments',

    // Performance Metrics
    'performance_metrics' => 'Performance Metrics',
    'academy_utilization' => 'Academy Utilization',
    'revenue_per_student' => 'Revenue per Student',
    'programs_per_academy' => 'Programs per Academy',

    // Recent Activities
    'recent_activities' => 'Recent Activities',
    'recent_student_enrollments' => 'Recent Student Enrollments',
    'recent_payments' => 'Recent Payments',
    'no_phone' => 'No phone',
    'unknown_student' => 'Unknown Student',
    'payment_id' => 'Payment ID',

    // Form Sections
    'basic_information' => 'Basic Information',
    'basic_info_description' => 'Enter the basic details for the new branch',
    'location_information' => 'Location Information',
    'location_info_description' => 'Specify the branch location and address details',
    'contact_information' => 'Contact Information',
    'contact_info_description' => 'Add contact details for the branch',

    // Validation Messages
    'name_required' => 'Branch name is required.',
    'name_unique' => 'A branch with this name already exists.',
    'location_required' => 'Branch location is required.',
    'phone_invalid' => 'Please enter a valid UAE phone number.',
    'email_unique' => 'A branch with this email already exists.',

    // Success Messages
    'created_successfully' => 'Branch created successfully.',
    'updated_successfully' => 'Branch updated successfully.',
    'deleted_successfully' => 'Branch deleted successfully.',
    'status_updated_successfully' => 'Branch status updated successfully!',

    // Error Messages
    'creation_failed' => 'Failed to create branch. Please try again.',
    'update_failed' => 'Failed to update branch. Please try again.',
    'delete_failed' => 'Failed to delete branch. Please try again.',
    'status_update_failed' => 'Failed to update branch status.',

    // Confirmation Messages
    'confirm_status_change' => 'Are you sure you want to change the status of this branch?',
    'confirm_delete' => 'Are you sure you want to delete this branch? This action may deactivate the branch if it has associated data.',
    'confirm_bulk_action' => 'Are you sure you want to :action :count selected branches?',

    // Time & Date
    'created' => 'Created',
    'last_updated' => 'Last Updated',
    'created_at' => 'Created at',
    'updated_at' => 'Updated at',

    // Currency
    'aed' => 'AED',
    'currency_symbol' => 'AED',

    // Report
    'report_title' => 'Branch Management Report',
    'report_subtitle' => 'Comprehensive report of all academy branches',
    'generated_on' => 'Generated on',
    'report_summary' => 'Report Summary',
    'branch_distribution' => 'Branch Distribution',
    'confidential_notice' => 'This report contains confidential information. Please handle with care.',
    'auto_generated' => 'Report generated automatically on',
];
