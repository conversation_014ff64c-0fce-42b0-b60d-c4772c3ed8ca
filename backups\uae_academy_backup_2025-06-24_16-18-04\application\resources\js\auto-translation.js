// UAE English Sports Academy - Automatic Translation System
// Automatically applies translations when language switches without page reload

class AutoTranslation {
    constructor() {
        this.currentLocale = document.documentElement.lang || 'en';
        this.translations = {};
        this.translationElements = new Map();
        this.init();
    }

    init() {
        // Load initial translations from server-side data
        this.loadTranslations();

        // Scan and register all translatable elements
        this.scanTranslatableElements();

        // Listen for language change events
        this.setupEventListeners();

        // Apply initial translations
        this.applyTranslations();
    }

    loadTranslations() {
        // Get translations from server-side shared data
        if (window.commonTranslations) {
            this.translations.common = window.commonTranslations;
        }

        if (window.dashboardTranslations) {
            this.translations.dashboard = window.dashboardTranslations;
        }
    }

    scanTranslatableElements() {
        // Find all elements with translation keys
        const elements = document.querySelectorAll('[data-trans-key]');

        elements.forEach(element => {
            const key = element.getAttribute('data-trans-key');
            const context = element.getAttribute('data-context') || 'common';

            if (key) {
                this.translationElements.set(element, {
                    key: key,
                    context: context,
                    originalText: element.textContent.trim()
                });
            }
        });

        // Also scan for hardcoded text that should be translated
        this.scanHardcodedText();
    }

    scanHardcodedText() {
        // Scan specific elements first
        this.scanTableHeaders();
        this.scanButtons();
        this.scanFormLabels();
        this.scanStatusBadges();

        // Comprehensive hardcoded texts that should be translated
        const hardcodedMappings = {
            // Dashboard
            'Total Branches': 'dashboard.total_branches',
            'Total Academies': 'dashboard.total_academies',
            'Total Students': 'dashboard.total_students',
            'Total Payments': 'dashboard.total_payments',
            'Monthly Revenue (AED)': 'dashboard.monthly_revenue',
            'Welcome Back!': 'dashboard.welcome_back',
            'Recent Students': 'dashboard.recent_students',
            'Recent Payments': 'dashboard.recent_payments',
            'Quick Actions': 'dashboard.Quick Actions',
            'View All': 'dashboard.view_all',

            // Navigation
            'Dashboard': 'dashboard.Dashboard',
            'Branch Management': 'dashboard.Branch Management',
            'Academy Management': 'dashboard.Academy Management',
            'Student Management': 'dashboard.Student Management',
            'Payment Management': 'dashboard.Payment Management',
            'Uniform Management': 'dashboard.Uniform Management',
            'User Management': 'dashboard.User Management',
            'Settings': 'dashboard.Settings',
            'Reports': 'dashboard.Reports',
            'Administration': 'dashboard.Administration',

            // Actions
            'Add Student': 'dashboard.Add Student',
            'Add Payment': 'dashboard.Add Payment',
            'Order Uniform': 'dashboard.Order Uniform',
            'View Details': 'dashboard.view_details',
            'View Reports': 'dashboard.view_details',
            'Export Report': 'dashboard.export_report',
            'Add New Student': 'dashboard.Add Student',
            'Create Order': 'common.create_order',

            // Common Actions
            'Search': 'common.search',
            'Filter': 'common.filter',
            'Save': 'common.save',
            'Cancel': 'common.cancel',
            'Delete': 'common.delete',
            'Edit': 'common.edit',
            'Add': 'common.add',
            'Create': 'common.create',
            'Update': 'common.update',
            'Back': 'common.back',
            'Next': 'common.next',
            'Previous': 'common.previous',
            'Loading...': 'common.loading',
            'Please wait...': 'common.please_wait',
            'Submit': 'common.submit',
            'Reset': 'common.reset',
            'Close': 'common.close',
            'Confirm': 'common.confirm',
            'Yes': 'common.yes',
            'No': 'common.no',

            // Table Headers
            'ID': 'common.id',
            'Name': 'common.name',
            'Student': 'common.student',
            'Academy': 'common.academy',
            'Branch': 'common.branch',
            'Status': 'common.status',
            'Actions': 'common.actions',
            'Date': 'common.date',
            'Amount': 'common.amount',
            'Amount (AED)': 'common.amount_aed',
            'Student Info': 'common.student_info',
            'Contact & Location': 'common.contact_location',
            'Academy Name': 'common.academy_name',
            'Branch Name': 'common.branch_name',
            'Coach': 'common.coach',
            'Programs': 'common.programs',
            'Students': 'common.students',
            'Revenue (AED)': 'common.revenue_aed',
            'Created': 'common.created',
            'Join Date': 'common.join_date',
            'Order Date': 'common.order_date',
            'Delivery Date': 'common.delivery_date',
            'Item': 'common.item',
            'Size': 'common.size',
            'Quantity': 'common.quantity',

            // Status Values
            'Active': 'common.active',
            'Inactive': 'common.inactive',
            'Pending': 'common.pending',
            'Expired': 'common.expired',
            'Completed': 'common.completed',
            'Processing': 'common.processing',
            'Cancelled': 'common.cancelled',
            'Approved': 'common.approved',
            'Rejected': 'common.rejected',

            // Messages
            'No students found.': 'common.no_students_found',
            'No payments found.': 'common.no_payments_found',
            'No data available': 'common.no_data_available',
            'No results found': 'common.no_results_found',
            'Data updated successfully': 'common.data_updated_successfully',
            'Operation completed successfully': 'common.operation_completed_successfully',
            'Error occurred': 'common.error_occurred',
            'Please try again': 'common.please_try_again',

            // Form Labels
            'Full Name': 'common.full_name',
            'Email': 'common.email',
            'Phone': 'common.phone',
            'Address': 'common.address',
            'Nationality': 'common.nationality',
            'Birth Date': 'common.birth_date',
            'Password': 'common.password',
            'Confirm Password': 'common.confirm_password',
            'Role': 'common.role',
            'Description': 'common.description',
            'Notes': 'common.notes',
            'Location': 'common.location',
            'Manager': 'common.manager',
            'Contact Person': 'common.contact_person',
            'Price': 'common.price',
            'Days': 'common.days',
            'Classes': 'common.classes',
            'Start Date': 'common.start_date',
            'End Date': 'common.end_date',
            'Payment Method': 'common.payment_method',
            'Discount': 'common.discount',
            'Class Time': 'common.class_time',
            'From': 'common.from',
            'To': 'common.to'
        };

        // Find elements with hardcoded text
        Object.keys(hardcodedMappings).forEach(hardcodedText => {
            const elements = this.findElementsWithText(hardcodedText);
            elements.forEach(element => {
                const translationKey = hardcodedMappings[hardcodedText];
                const [context, key] = translationKey.split('.');

                // Add translation attributes
                element.setAttribute('data-trans-key', key);
                element.setAttribute('data-context', context);

                this.translationElements.set(element, {
                    key: key,
                    context: context,
                    originalText: hardcodedText
                });
            });
        });
    }

    findElementsWithText(text) {
        const elements = [];
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.trim() === text) {
                const element = node.parentElement;
                if (element && !element.hasAttribute('data-trans-key')) {
                    elements.push(element);
                }
            }
        }

        return elements;
    }

    scanTableHeaders() {
        // Scan table headers specifically
        const headers = document.querySelectorAll('th');
        headers.forEach(header => {
            const text = header.textContent.trim();
            if (text && !header.hasAttribute('data-trans-key')) {
                const mapping = this.getTranslationMapping(text);
                if (mapping) {
                    const [context, key] = mapping.split('.');
                    header.setAttribute('data-trans-key', key);
                    header.setAttribute('data-context', context);

                    this.translationElements.set(header, {
                        key: key,
                        context: context,
                        originalText: text
                    });
                }
            }
        });
    }

    scanButtons() {
        // Scan buttons and links
        const buttons = document.querySelectorAll('button, a.btn, .btn-bank, .btn-bank-outline, .btn-bank-secondary');
        buttons.forEach(button => {
            const text = button.textContent.trim();
            if (text && !button.hasAttribute('data-trans-key')) {
                const mapping = this.getTranslationMapping(text);
                if (mapping) {
                    const [context, key] = mapping.split('.');
                    button.setAttribute('data-trans-key', key);
                    button.setAttribute('data-context', context);

                    this.translationElements.set(button, {
                        key: key,
                        context: context,
                        originalText: text
                    });
                }
            }
        });
    }

    scanFormLabels() {
        // Scan form labels
        const labels = document.querySelectorAll('label, .form-label, .input-label');
        labels.forEach(label => {
            const text = label.textContent.trim();
            if (text && !label.hasAttribute('data-trans-key')) {
                const mapping = this.getTranslationMapping(text);
                if (mapping) {
                    const [context, key] = mapping.split('.');
                    label.setAttribute('data-trans-key', key);
                    label.setAttribute('data-context', context);

                    this.translationElements.set(label, {
                        key: key,
                        context: context,
                        originalText: text
                    });
                }
            }
        });
    }

    scanStatusBadges() {
        // Scan status badges and spans
        const badges = document.querySelectorAll('.badge, .badge-bank, .status, .badge-success, .badge-error, .badge-warning, .badge-neutral');
        badges.forEach(badge => {
            const text = badge.textContent.trim();
            if (text && !badge.hasAttribute('data-trans-key')) {
                const mapping = this.getTranslationMapping(text);
                if (mapping) {
                    const [context, key] = mapping.split('.');
                    badge.setAttribute('data-trans-key', key);
                    badge.setAttribute('data-context', context);

                    this.translationElements.set(badge, {
                        key: key,
                        context: context,
                        originalText: text
                    });
                }
            }
        });
    }

    getTranslationMapping(text) {
        // Get the hardcoded mappings
        const hardcodedMappings = {
            // Dashboard
            'Total Branches': 'dashboard.total_branches',
            'Total Academies': 'dashboard.total_academies',
            'Total Students': 'dashboard.total_students',
            'Total Payments': 'dashboard.total_payments',
            'Monthly Revenue (AED)': 'dashboard.monthly_revenue',
            'Welcome Back!': 'dashboard.welcome_back',
            'Recent Students': 'dashboard.recent_students',
            'Recent Payments': 'dashboard.recent_payments',
            'Quick Actions': 'dashboard.Quick Actions',
            'View All': 'dashboard.view_all',

            // Navigation
            'Dashboard': 'dashboard.Dashboard',
            'Branch Management': 'dashboard.Branch Management',
            'Academy Management': 'dashboard.Academy Management',
            'Student Management': 'dashboard.Student Management',
            'Payment Management': 'dashboard.Payment Management',
            'Uniform Management': 'dashboard.Uniform Management',
            'User Management': 'dashboard.User Management',
            'Settings': 'dashboard.Settings',
            'Reports': 'dashboard.Reports',
            'Administration': 'dashboard.Administration',

            // Actions
            'Add Student': 'dashboard.Add Student',
            'Add Payment': 'dashboard.Add Payment',
            'Order Uniform': 'dashboard.Order Uniform',
            'View Details': 'dashboard.view_details',
            'View Reports': 'dashboard.view_details',
            'Export Report': 'dashboard.export_report',
            'Add New Student': 'dashboard.Add Student',
            'Create Order': 'common.create_order',

            // Common Actions
            'Search': 'common.search',
            'Filter': 'common.filter',
            'Save': 'common.save',
            'Cancel': 'common.cancel',
            'Delete': 'common.delete',
            'Edit': 'common.edit',
            'Add': 'common.add',
            'Create': 'common.create',
            'Update': 'common.update',
            'Back': 'common.back',
            'Next': 'common.next',
            'Previous': 'common.previous',
            'Loading...': 'common.loading',
            'Please wait...': 'common.please_wait',
            'Submit': 'common.submit',
            'Reset': 'common.reset',
            'Close': 'common.close',
            'Confirm': 'common.confirm',
            'Yes': 'common.yes',
            'No': 'common.no',

            // Table Headers
            'ID': 'common.id',
            'Name': 'common.name',
            'Student': 'common.student',
            'Academy': 'common.academy',
            'Branch': 'common.branch',
            'Status': 'common.status',
            'Actions': 'common.actions',
            'Date': 'common.date',
            'Amount': 'common.amount',
            'Amount (AED)': 'common.amount_aed',
            'Student Info': 'common.student_info',
            'Contact & Location': 'common.contact_location',
            'Academy Name': 'common.academy_name',
            'Branch Name': 'common.branch_name',
            'Coach': 'common.coach',
            'Programs': 'common.programs',
            'Students': 'common.students',
            'Revenue (AED)': 'common.revenue_aed',
            'Created': 'common.created',
            'Join Date': 'common.join_date',
            'Order Date': 'common.order_date',
            'Delivery Date': 'common.delivery_date',
            'Item': 'common.item',
            'Size': 'common.size',
            'Quantity': 'common.quantity',

            // Status Values
            'Active': 'common.active',
            'Inactive': 'common.inactive',
            'Pending': 'common.pending',
            'Expired': 'common.expired',
            'Completed': 'common.completed',
            'Processing': 'common.processing',
            'Cancelled': 'common.cancelled',
            'Approved': 'common.approved',
            'Rejected': 'common.rejected',

            // Messages
            'No students found.': 'common.no_students_found',
            'No payments found.': 'common.no_payments_found',
            'No data available': 'common.no_data_available',
            'No results found': 'common.no_results_found',
            'Data updated successfully': 'common.data_updated_successfully',
            'Operation completed successfully': 'common.operation_completed_successfully',
            'Error occurred': 'common.error_occurred',
            'Please try again': 'common.please_try_again',

            // Form Labels
            'Full Name': 'common.full_name',
            'Email': 'common.email',
            'Phone': 'common.phone',
            'Address': 'common.address',
            'Nationality': 'common.nationality',
            'Birth Date': 'common.birth_date',
            'Password': 'common.password',
            'Confirm Password': 'common.confirm_password',
            'Role': 'common.role',
            'Description': 'common.description',
            'Notes': 'common.notes',
            'Location': 'common.location',
            'Manager': 'common.manager',
            'Contact Person': 'common.contact_person',
            'Price': 'common.price',
            'Days': 'common.days',
            'Classes': 'common.classes',
            'Start Date': 'common.start_date',
            'End Date': 'common.end_date',
            'Payment Method': 'common.payment_method',
            'Discount': 'common.discount',
            'Class Time': 'common.class_time',
            'From': 'common.from',
            'To': 'common.to',

            // Additional Report Navigation
            'Reports Dashboard': 'dashboard.Reports Dashboard',
            'Financial Reports': 'dashboard.Financial Reports',
            'Uniform Reports': 'dashboard.Uniform Reports',
            'Program Reports': 'dashboard.Program Reports',
            'Status Reports': 'dashboard.Status Reports',
            'Daily Reports': 'dashboard.Daily Reports',

            // Additional Status Indicators
            'Active & Growing': 'dashboard.active_growing',
            'Excellence in Sports': 'dashboard.excellence_sports',
            'Future Champions': 'dashboard.future_champions',

            // Currency and Numbers
            'AED': 'common.aed',
            'N/A': 'common.not_available',

            // Additional Common Terms
            'Administration': 'dashboard.Administration',
            'Quick Actions': 'dashboard.Quick Actions',
            'Latest student registrations': 'dashboard.new_registrations',
            'Latest payment transactions': 'dashboard.recent_payments',
            'Frequently used operations': 'dashboard.quick_stats'
        };

        return hardcodedMappings[text] || null;
    }

    setupEventListeners() {
        // Listen for language change events
        window.addEventListener('languageChanged', (event) => {
            this.currentLocale = event.detail.locale;
            this.loadNewTranslations(event.detail.locale);
        });

        // Listen for DOM changes to catch dynamically added content
        const observer = new MutationObserver((mutations) => {
            let shouldRescan = false;
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.scanNewElement(node);
                            shouldRescan = true;
                        }
                    });
                }
            });

            // Re-scan if new content was added
            if (shouldRescan) {
                setTimeout(() => {
                    this.scanTranslatableElements();
                    this.applyTranslations();
                }, 100);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Also listen for AJAX completion events
        document.addEventListener('ajaxComplete', () => {
            setTimeout(() => {
                this.scanTranslatableElements();
                this.applyTranslations();
            }, 200);
        });

        // Listen for page visibility changes to re-apply translations
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.applyTranslations();
            }
        });
    }

    scanNewElement(element) {
        // Scan newly added elements for translation keys
        const translatableElements = element.querySelectorAll('[data-trans-key]');
        translatableElements.forEach(el => {
            const key = el.getAttribute('data-trans-key');
            const context = el.getAttribute('data-context') || 'common';

            if (key && !this.translationElements.has(el)) {
                this.translationElements.set(el, {
                    key: key,
                    context: context,
                    originalText: el.textContent.trim()
                });

                // Apply translation immediately
                this.translateElement(el);
            }
        });
    }

    async loadNewTranslations(locale) {
        try {
            // Fetch new translations from server
            const response = await fetch(`/api/translations/${locale}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.translations = data;
                this.applyTranslations();
            }
        } catch (error) {
            console.warn('Failed to load translations:', error);
            // Fallback to existing translations
            this.applyTranslations();
        }
    }

    applyTranslations() {
        this.translationElements.forEach((data, element) => {
            this.translateElement(element);
        });
    }

    translateElement(element) {
        const data = this.translationElements.get(element);
        if (!data) return;

        const { key, context } = data;
        const translation = this.getTranslation(key, context);

        if (translation && translation !== key) {
            element.textContent = translation;
        }
    }

    getTranslation(key, context = 'common') {
        if (!this.translations[context]) {
            return key;
        }

        return this.translations[context][key] || key;
    }

    // Public method to manually translate text
    translate(key, context = 'common', fallback = null) {
        const translation = this.getTranslation(key, context);
        return translation !== key ? translation : (fallback || key);
    }

    // Public method to add new translation mapping
    addTranslation(element, key, context = 'common') {
        element.setAttribute('data-trans-key', key);
        element.setAttribute('data-context', context);

        this.translationElements.set(element, {
            key: key,
            context: context,
            originalText: element.textContent.trim()
        });

        this.translateElement(element);
    }
}

// Initialize auto-translation system
document.addEventListener('DOMContentLoaded', () => {
    window.autoTranslation = new AutoTranslation();
});

// Export for use in other modules
export default AutoTranslation;
