<?php

namespace App\Policies;

use App\Models\Student;
use App\Models\User;

class StudentPolicy
{
    /**
     * Determine whether the user can view any students.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the student.
     */
    public function view(User $user, Student $student): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can view students in their branch
                return $user->branch_id === $student->branch_id;
            case 'academy_manager':
                // Academy managers can view students in their academy
                return $user->academy_id === $student->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can create students.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can update the student.
     */
    public function update(User $user, Student $student): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can update students in their branch
                return $user->branch_id === $student->branch_id;
            case 'academy_manager':
                // Academy managers can update students in their academy
                return $user->academy_id === $student->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can delete the student.
     */
    public function delete(User $user, Student $student): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can delete students in their branch
                return $user->branch_id === $student->branch_id;
            case 'academy_manager':
                // Academy managers can delete students in their academy
                return $user->academy_id === $student->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can restore the student.
     */
    public function restore(User $user, Student $student): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can permanently delete the student.
     */
    public function forceDelete(User $user, Student $student): bool
    {
        // Only admin can permanently delete
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can export student data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }
}
