<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Payment;
use App\Models\Student;
use App\Models\Branch;
use App\Models\Academy;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $amount = fake()->randomFloat(2, 200, 800);
        $discount = fake()->randomFloat(2, 0, 50);
        $paymentDate = fake()->dateTimeBetween('-6 months', 'now');
        
        return [
            'student_id' => Student::factory(),
            'branch_id' => Branch::factory(),
            'academy_id' => Academy::factory(),
            'amount' => $amount,
            'subtotal' => $amount,
            'vat_rate' => 5.00,
            'vat_amount' => $amount * 0.05,
            'total_amount' => $amount * 1.05,
            'vat_inclusive' => false,
            'discount' => $discount,
            'currency' => 'AED',
            'payment_method' => fake()->randomElement(['cash', 'card', 'bank_transfer']),
            'payment_date' => $paymentDate->format('Y-m-d'),
            'start_date' => $paymentDate->format('Y-m-d'),
            'end_date' => $paymentDate->modify('+30 days')->format('Y-m-d'),
            'status' => fake()->randomElement(['active', 'expired']),
            'reset_num' => 'RST' . fake()->unique()->numberBetween(1000, 9999),
            'class_time_from' => '16:00:00',
            'class_time_to' => '17:00:00',
            'renewal' => fake()->boolean(30), // 30% chance of being a renewal
            'payment_type' => fake()->randomElement(['fresh', 'renewal']),
            'reference_number' => 'PAY' . fake()->unique()->numberBetween(100000, 999999),
            'description' => fake()->sentence(),
            'note' => fake()->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the payment is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
            'end_date' => fake()->dateTimeBetween('-30 days', '-1 day')->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the payment is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'end_date' => fake()->dateTimeBetween('now', '+30 days')->format('Y-m-d'),
        ]);
    }

    /**
     * Indicate that the payment is a renewal.
     */
    public function renewal(): static
    {
        return $this->state(fn (array $attributes) => [
            'renewal' => true,
            'payment_type' => 'renewal',
        ]);
    }
}
