<?php $__env->startSection('title', 'Academy Management - LEADERS SPORTS SERVICES'); ?>

<?php $__env->startSection('header'); ?>
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                    </path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Academy Management</h1>
                <p class="text-lg text-dark-gray">Manage sports academies and programs</p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?php echo e($academies->total()); ?> Total Academies
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Academy::class)): ?>
                <a href="<?php echo e(route('academies.create')); ?>" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                        </path>
                    </svg>
                    Add New Academy
                </a>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export', App\Models\Academy::class)): ?>
                <div class="flex items-center space-x-2">
                    <button onclick="exportData('excel')" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Export Excel
                    </button>
                    <button onclick="exportData('pdf')" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                            </path>
                        </svg>
                        Export PDF
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="space-y-6" x-data="academyManagement()" x-init="init()">
        <!-- Advanced Search & Filters -->
        <?php echo $__env->make('academies._filters', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Statistics Cards -->
        <?php echo $__env->make('academies._stats', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Main Content Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">All Academies</h3>
                    <p class="bank-card-subtitle">
                        Showing <?php echo e($academies->firstItem() ?? 0); ?> to <?php echo e($academies->lastItem() ?? 0); ?>

                        of <?php echo e($academies->total()); ?> academies
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Bulk Actions -->
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bulkAction', App\Models\Academy::class)): ?>
                        <div x-show="selectedAcademies.length > 0" x-transition class="flex items-center space-x-2">
                            <span class="text-sm text-dark-gray" x-text="`${selectedAcademies.length} selected`"></span>
                            <select x-model="bulkAction" class="form-select-bank text-sm">
                                <option value="">Bulk Actions</option>
                                <option value="activate">Activate Selected</option>
                                <option value="deactivate">Deactivate Selected</option>
                                <option value="delete">Delete Selected</option>
                            </select>
                            <button @click="executeBulkAction()" :disabled="!bulkAction" class="btn-bank btn-bank-sm">
                                Apply
                            </button>
                        </div>
                    <?php endif; ?>

                    <!-- View Toggle -->
                    <div class="flex items-center bg-off-white rounded-lg p-1">
                        <button @click="viewMode = 'table'" :class="viewMode === 'table' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M3 6h18m-9 10h9"></path>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bank-card-body p-0">
                <!-- Table View -->
                <div x-show="viewMode === 'table'" x-transition>
                    <?php echo $__env->make('academies._table', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Grid View -->
                <div x-show="viewMode === 'grid'" x-transition>
                    <?php echo $__env->make('academies._grid', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Pagination -->
                <?php if($academies->hasPages()): ?>
                    <div class="px-6 py-4 border-t border-light-gray">
                        <?php echo e($academies->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        function academyManagement() {
            return {
                selectedAcademies: [],
                bulkAction: '',
                viewMode: localStorage.getItem('academyViewMode') || 'table',

                init() {
                    this.$watch('viewMode', (value) => {
                        localStorage.setItem('academyViewMode', value);
                    });
                },

                toggleAcademySelection(academyId) {
                    const index = this.selectedAcademies.indexOf(academyId);
                    if (index > -1) {
                        this.selectedAcademies.splice(index, 1);
                    } else {
                        this.selectedAcademies.push(academyId);
                    }
                },

                selectAllAcademies() {
                    const checkboxes = document.querySelectorAll('input[name="academy_ids[]"]');
                    const allSelected = this.selectedAcademies.length === checkboxes.length;

                    if (allSelected) {
                        this.selectedAcademies = [];
                    } else {
                        this.selectedAcademies = Array.from(checkboxes).map(cb => parseInt(cb.value));
                    }
                },

                async executeBulkAction() {
                    if (!this.bulkAction || this.selectedAcademies.length === 0) return;

                    const confirmed = await this.confirmBulkAction();
                    if (!confirmed) return;

                    try {
                        const response = await fetch('<?php echo e(route('academies.bulk-action')); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                action: this.bulkAction,
                                academy_ids: this.selectedAcademies
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while processing the request.');
                    }
                },

                confirmBulkAction() {
                    const actionText = this.bulkAction.charAt(0).toUpperCase() + this.bulkAction.slice(1);
                    return confirm(
                        `Are you sure you want to ${actionText.toLowerCase()} ${this.selectedAcademies.length} selected academy(ies)?`
                    );
                }
            }
        }

        function exportData(format) {
            const url = format === 'excel' ? '<?php echo e(route('academies.export.excel')); ?>' :
                '<?php echo e(route('academies.export.pdf')); ?>';
            const params = new URLSearchParams(window.location.search);
            window.open(`${url}?${params.toString()}`, '_blank');
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            alert(message);
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xampp\htdocs\erp-login2\resources\views/academies/index.blade.php ENDPATH**/ ?>