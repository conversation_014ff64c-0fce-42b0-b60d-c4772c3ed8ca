@props([
    'id' => 'password',
    'name' => 'password',
    'placeholder' => 'Enter password',
    'required' => false,
    'value' => '',
    'autocomplete' => 'current-password',
    'class' => '',
    'showToggle' => true,
    'label' => 'Password',
    'showLabel' => true,
    'error' => null,
    'helpText' => null,
])

@php
    $uniqueId = $id . '_' . uniqid();
    $toggleId = 'toggle_' . $uniqueId;
    $iconId = 'icon_' . $uniqueId;
@endphp

<div>
    @if($showLabel)
        <label for="{{ $id }}" class="form-label fw-medium" style="color: var(--charcoal-black);">
            {{ $label }}
            @if($required)
                <span class="text-danger">*</span>
            @endif
        </label>
    @endif
    
    <div class="position-relative">
        <input 
            id="{{ $id }}" 
            name="{{ $name }}" 
            type="password"
            value="{{ old($name, $value) }}"
            placeholder="{{ $placeholder }}"
            autocomplete="{{ $autocomplete }}"
            class="form-control {{ $class }} @error($name) is-invalid @enderror"
            style="{{ $showToggle ? 'padding-right: 45px;' : '' }}"
            {{ $required ? 'required' : '' }}
            {{ $attributes }}
        >
        
        @if($showToggle)
            <button 
                type="button" 
                class="btn position-absolute top-50 end-0 translate-middle-y me-2 p-0 border-0 bg-transparent password-toggle-btn"
                id="{{ $toggleId }}" 
                style="z-index: 10;"
                aria-label="Show password"
                data-target="{{ $id }}"
                data-icon="{{ $iconId }}"
            >
                <i class="fas fa-eye text-muted" id="{{ $iconId }}" style="font-size: 1rem;"></i>
            </button>
        @endif
    </div>
    
    @if($error || $errors->has($name))
        <div class="invalid-feedback d-block">
            {{ $error ?? $errors->first($name) }}
        </div>
    @endif
    
    @if($helpText)
        <div class="form-text text-muted mt-1">
            {{ $helpText }}
        </div>
    @endif
</div>

@once
    @push('styles')
        <style>
            /* Password Toggle Button Styles */
            .password-toggle-btn {
                cursor: pointer;
                transition: color 0.2s ease;
            }

            .password-toggle-btn:hover .fas {
                color: var(--leaders-red) !important;
            }

            .password-toggle-btn:focus {
                outline: none;
                box-shadow: none;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Handle all password toggle buttons
                document.querySelectorAll('.password-toggle-btn').forEach(function(toggleBtn) {
                    const targetId = toggleBtn.getAttribute('data-target');
                    const iconId = toggleBtn.getAttribute('data-icon');
                    const passwordInput = document.getElementById(targetId);
                    const passwordIcon = document.getElementById(iconId);

                    if (passwordInput && passwordIcon) {
                        toggleBtn.addEventListener('click', function() {
                            // Toggle password visibility
                            const isPassword = passwordInput.type === 'password';
                            
                            if (isPassword) {
                                passwordInput.type = 'text';
                                passwordIcon.classList.remove('fa-eye');
                                passwordIcon.classList.add('fa-eye-slash');
                                toggleBtn.setAttribute('aria-label', 'Hide password');
                            } else {
                                passwordInput.type = 'password';
                                passwordIcon.classList.remove('fa-eye-slash');
                                passwordIcon.classList.add('fa-eye');
                                toggleBtn.setAttribute('aria-label', 'Show password');
                            }
                            
                            // Keep focus on password input
                            passwordInput.focus();
                        });

                        // Add keyboard support (Enter and Space)
                        toggleBtn.addEventListener('keydown', function(e) {
                            if (e.key === 'Enter' || e.key === ' ') {
                                e.preventDefault();
                                toggleBtn.click();
                            }
                        });
                    }
                });
            });
        </script>
    @endpush
@endonce
