/* UAE English Sports Academy - Unified Table Header Styling Fix */

/*
 * This file ensures all table headers across the system have consistent white text
 * on the dark gradient background, regardless of other CSS conflicts.
 */

/* Primary table header styling - highest specificity */
.table-bank thead th,
.table-bank thead th *,
table.table-bank thead th,
table.table-bank thead th *,
.table thead th,
.table thead th *,
thead th,
thead th *,
.table-bank thead th[class*="text-"],
.table thead th[class*="text-"],
thead th[class*="text-"],
.min-w-full thead th,
.min-w-full thead th *,
table.min-w-full thead th,
table.min-w-full thead th *,
.min-w-full thead th[class*="text-"] {
    color: white !important;
    background: transparent !important;
}

/* Table head background */
.table-bank thead,
.table thead,
thead,
table.table-bank thead,
table thead,
.min-w-full thead,
table.min-w-full thead,
.bg-off-white {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
}

/* Comprehensive table header styling */
.table-bank thead th {
    color: white !important;
    font-weight: 600 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.1em !important;
    font-size: 0.75rem !important;
    padding: 1rem !important;
    border-bottom: 2px solid #e53e3e !important;
    background: transparent !important;
    text-align: left !important;
}

/* RTL Support */
[dir="rtl"] .table-bank thead th,
[dir="rtl"] .table thead th,
[dir="rtl"] thead th {
    text-align: right !important;
}

/* Rounded corners for first and last headers */
.table-bank thead th:first-child {
    border-top-left-radius: 0.5rem !important;
}

.table-bank thead th:last-child {
    border-top-right-radius: 0.5rem !important;
}

/* RTL rounded corners */
[dir="rtl"] .table-bank thead th:first-child {
    border-top-left-radius: 0 !important;
    border-top-right-radius: 0.5rem !important;
}

[dir="rtl"] .table-bank thead th:last-child {
    border-top-left-radius: 0.5rem !important;
    border-top-right-radius: 0 !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .table-bank thead th,
    .table thead th,
    thead th {
        color: white !important;
        padding: 0.5rem !important;
        font-size: 0.625rem !important;
    }
}

/* Specific overrides for common conflicting classes */
.table-bank thead th.text-dark,
.table-bank thead th.text-black,
.table-bank thead th.text-gray-800,
.table-bank thead th.text-gray-900,
.table-bank thead th.text-dark-gray,
.table thead th.text-dark,
.table thead th.text-black,
.table thead th.text-gray-800,
.table thead th.text-gray-900,
.table thead th.text-dark-gray,
thead th.text-dark,
thead th.text-black,
thead th.text-gray-800,
thead th.text-gray-900,
thead th.text-dark-gray,
.min-w-full thead th.text-dark,
.min-w-full thead th.text-black,
.min-w-full thead th.text-gray-800,
.min-w-full thead th.text-gray-900,
.min-w-full thead th.text-dark-gray {
    color: white !important;
}

/* Ensure any nested elements also have white text */
.table-bank thead th span,
.table-bank thead th div,
.table-bank thead th a,
.table-bank thead th button,
.table thead th span,
.table thead th div,
.table thead th a,
.table thead th button,
thead th span,
thead th div,
thead th a,
thead th button {
    color: white !important;
    background: transparent !important;
}

/* Additional specificity for stubborn elements */
.table-bank thead th *[class*="text-"],
.table thead th *[class*="text-"],
thead th *[class*="text-"] {
    color: white !important;
}

/* Hover effects for table headers */
.table-bank thead th:hover,
.table thead th:hover,
thead th:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: white !important;
}

/* Focus states for accessibility */
.table-bank thead th:focus,
.table thead th:focus,
thead th:focus,
.table-bank thead th:focus-within,
.table thead th:focus-within,
thead th:focus-within {
    outline: 2px solid #e53e3e !important;
    outline-offset: -2px !important;
    color: white !important;
}

/* Print styles */
@media print {
    .table-bank thead th,
    .table thead th,
    thead th {
        color: black !important;
        background: #f3f4f6 !important;
    }
}

/* Ultra-aggressive override for any remaining cases */
table thead th,
table thead th *,
table thead th span,
table thead th div,
table thead th a,
table thead th button,
table thead th[class],
table thead th *[class] {
    color: white !important;
}

/* Force background on all table heads */
table thead,
.table thead,
.min-w-full thead {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
}

/* Nuclear option - catch everything */
*[class*="table"] thead th,
*[class*="table"] thead th *,
div[class*="table"] thead th,
div[class*="table"] thead th * {
    color: white !important;
}
