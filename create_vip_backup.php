<?php

/**
 * VIP Backup Script for UAE English Sports Academy
 * Date: 27 June 2025
 * 
 * This script creates a complete backup of the entire project
 */

function copyDirectory($source, $destination) {
    if (!is_dir($source)) {
        return false;
    }

    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }

    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );

    foreach ($iterator as $item) {
        $target = $destination . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
        
        if ($item->isDir()) {
            if (!is_dir($target)) {
                mkdir($target, 0755, true);
            }
        } else {
            copy($item, $target);
        }
    }
    
    return true;
}

function copyFile($source, $destination) {
    $destinationDir = dirname($destination);
    if (!is_dir($destinationDir)) {
        mkdir($destinationDir, 0755, true);
    }
    return copy($source, $destination);
}

// Get current date and time
$backupDate = date('Y-m-d_H-i-s');
$backupFolder = "VIP BACKUP 27 june 2025";
$projectRoot = __DIR__;

echo "Starting VIP Backup for UAE English Sports Academy...\n";
echo "Backup Date: " . date('Y-m-d H:i:s') . "\n";
echo "Backup Folder: $backupFolder\n\n";

// Create main backup directory
if (!is_dir($backupFolder)) {
    mkdir($backupFolder, 0755, true);
}

// List of directories to backup
$directoriesToBackup = [
    'app',
    'bootstrap',
    'config',
    'database',
    'docs',
    'public',
    'resources',
    'routes',
    'storage',
    'tests',
    'updates',
    'vendor'
];

// List of files to backup
$filesToBackup = [
    'artisan',
    'composer.json',
    'composer.lock',
    'package.json',
    'package-lock.json',
    'phpunit.xml',
    'postcss.config.js',
    'tailwind.config.js',
    'vite.config.js',
    'README.md',
    'logo.jpg',
    '.env.example'
];

// List of documentation files to backup
$documentationFiles = [
    'AR_LAYOUT_COMMERCIAL_TRANSLATION_PLAN.md',
    'AUTOMATIC_TRANSLATION_SYSTEM.md',
    'BACKUP_COMPLETION_SUMMARY.md',
    'BACKUP_DOCUMENTATION.md',
    'COMPLETE_SYSTEM_BACKUP_DOCUMENTATION.md',
    'DASHBOARD_LINKING_COMPLETE_ANALYSIS.md',
    'ENHANCED_LAYOUT_STRUCTURE_SUMMARY.md',
    'IMPLEMENTATION_COMPLETE.md',
    'IMPLEMENTATION_GUIDE.md',
    'LARAVEL_DEV_SERVER_CONFIGURATION.md',
    'MODULES_LINKING_AUDIT_COMPLETE.md',
    'NAMES_DATES_TRANSLATION_SYSTEM.md',
    'PERFECT_BANK_DASHBOARD_DESIGN.md',
    'PHASE_3_COMPLETION_SUMMARY.md',
    'QUICK_ACTIONS_REPOSITIONING_SUMMARY.md',
    'RTL_NAVIGATION_FIX_SUMMARY.md',
    'RTL_SIDEBAR_ICON_POSITIONING_FIX.md',
    'TRANSLATION_KEYS_FIX_SUMMARY.md',
    'TRANSLATION_PANEL_IMPLEMENTATION_SUMMARY.md',
    'TRANSLATION_PANEL_UI_ENHANCEMENT_SUMMARY.md',
    'TRANSLATION_TEST_GUIDE.md',
    'UNIFORM_INVENTORY_SYSTEM_DOCUMENTATION.md',
    'UNIFORM_MANAGEMENT_IMPLEMENTATION_SUMMARY.md',
    'technical-stack.md',
    'uniform-management-documentation.md'
];

// Backup directories
echo "Backing up directories...\n";
foreach ($directoriesToBackup as $dir) {
    if (is_dir($dir)) {
        echo "Copying directory: $dir\n";
        copyDirectory($dir, "$backupFolder/$dir");
    }
}

// Backup individual files
echo "\nBacking up individual files...\n";
foreach ($filesToBackup as $file) {
    if (file_exists($file)) {
        echo "Copying file: $file\n";
        copyFile($file, "$backupFolder/$file");
    }
}

// Backup documentation files
echo "\nBacking up documentation files...\n";
foreach ($documentationFiles as $file) {
    if (file_exists($file)) {
        echo "Copying documentation: $file\n";
        copyFile($file, "$backupFolder/$file");
    }
}

// Create backup info file
$backupInfo = [
    'backup_date' => date('Y-m-d H:i:s'),
    'backup_type' => 'VIP Complete Backup',
    'project_name' => 'UAE English Sports Academy',
    'backup_location' => $backupFolder,
    'directories_backed_up' => $directoriesToBackup,
    'files_backed_up' => array_merge($filesToBackup, $documentationFiles),
    'backup_size' => 'Calculating...',
    'notes' => 'Complete project backup including all source code, documentation, and configuration files'
];

file_put_contents("$backupFolder/BACKUP_INFO.json", json_encode($backupInfo, JSON_PRETTY_PRINT));

echo "\n" . str_repeat("=", 60) . "\n";
echo "VIP BACKUP COMPLETED SUCCESSFULLY!\n";
echo "Backup Location: $backupFolder\n";
echo "Backup Date: " . date('Y-m-d H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";

?>
