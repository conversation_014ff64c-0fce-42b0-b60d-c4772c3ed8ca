<?php

namespace App\Http\Controllers;

use App\Models\Student;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Program;
use App\Models\Payment;
use App\Models\Uniform;
use App\Models\Attendance;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use App\Services\NameTranslationService;
use App\Services\DateLocalizationService;
use App\Services\FileUploadService;

class StudentController extends Controller
{
    /**
     * Display a listing of students with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Student::class);

        $query = Student::with(['branch', 'academy', 'payments', 'uniforms', 'attendances'])
            ->withCount(['payments', 'uniforms', 'attendances']);

        // Apply branch-level filtering for non-admin users
        $user = Auth::user();
        if ($user->role === 'branch_manager') {
            $query->where('branch_id', $user->branch_id);
        } elseif ($user->role === 'academy_manager') {
            $query->where('academy_id', $user->academy_id);
        }

        // Phone number search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->search($search);
        }

        // Name search filter
        if ($request->filled('name_search')) {
            $nameSearch = $request->get('name_search');
            $query->searchByName($nameSearch);
        }

        // Branch filter
        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }

        // Academy filter
        if ($request->filled('academy_id')) {
            $query->byAcademy($request->get('academy_id'));
        }

        // Program filter
        if ($request->filled('program_id')) {
            $query->byProgram($request->get('program_id'));
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->active();
            } elseif ($status === 'inactive') {
                $query->inactive();
            } elseif ($status === 'suspended') {
                $query->suspended();
            }
        }

        // Age range filter
        if ($request->filled('min_age') && $request->filled('max_age')) {
            $query->byAgeRange($request->get('min_age'), $request->get('max_age'));
        }

        // Join date range filter
        if ($request->filled('join_start_date') && $request->filled('join_end_date')) {
            $query->byJoinDateRange($request->get('join_start_date'), $request->get('join_end_date'));
        }

        // Nationality filter
        if ($request->filled('nationality')) {
            $query->where('nationality', 'like', '%' . $request->get('nationality') . '%');
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $students = $query->paginate($perPage)->withQueryString();

        // Get branches and academies for filter dropdowns
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();
        $programs = Program::active()->orderBy('name')->get();

        // Calculate statistics
        $stats = [
            'total_students' => Student::count(),
            'active_students' => Student::active()->count(),
            'inactive_students' => Student::inactive()->count(),
            'suspended_students' => Student::suspended()->count(),
            'total_payments' => Payment::completed()->sum('amount'),
            'pending_payments' => Payment::pending()->sum('amount'),
            'total_uniforms' => Uniform::count(),
            'delivered_uniforms' => Uniform::delivered()->count(),
        ];

        if ($request->ajax()) {
            return response()->json([
                'students' => $students,
                'stats' => $stats
            ]);
        }

        return view('students.index', compact('students', 'branches', 'academies', 'programs', 'stats'));
    }

    /**
     * Show the form for creating a new student.
     */
    public function create(Request $request): View
    {
        Gate::authorize('create', Student::class);

        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();
        $programs = Program::active()->orderBy('name')->get();

        // Pre-fill program data if program_id is provided
        $prefilledProgram = null;
        $programStudentContext = null;

        if ($request->has('program_id')) {
            $prefilledProgram = Program::with(['academy.branch'])->find($request->get('program_id'));

            if ($prefilledProgram) {
                // Get program context for student creation
                $programStudentContext = [
                    'program_name' => $prefilledProgram->name,
                    'program_price' => $prefilledProgram->price,
                    'program_classes' => $prefilledProgram->classes,
                    'program_duration' => $prefilledProgram->duration_hours,
                    'academy_name' => $prefilledProgram->academy->name,
                    'branch_name' => $prefilledProgram->academy->branch->name,
                    'suggested_payment_amount' => $prefilledProgram->price,
                    'suggested_join_date' => now()->format('Y-m-d'),
                    'class_time_from' => $prefilledProgram->start_time ? $prefilledProgram->start_time->format('H:i') : null,
                    'class_time_to' => $prefilledProgram->end_time ? $prefilledProgram->end_time->format('H:i') : null,
                ];

                // Filter data to include only relevant branch, academy, and program
                $branches = Branch::where('id', $prefilledProgram->academy->branch_id)->get();
                $academies = Academy::where('id', $prefilledProgram->academy_id)->with('branch')->get();
                $programs = Program::where('id', $prefilledProgram->id)->get();
            }
        }

        return view('students.create', compact(
            'branches',
            'academies',
            'programs',
            'prefilledProgram',
            'programStudentContext'
        ));
    }

    /**
     * Store a newly created student in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Student::class);

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'program_id' => 'nullable|exists:programs,id',
            'full_name' => 'required|string|max:255',
            'full_name_ar' => 'nullable|string|max:255',
            'first_name' => 'nullable|string|max:100',
            'first_name_ar' => 'nullable|string|max:100',
            'last_name' => 'nullable|string|max:100',
            'last_name_ar' => 'nullable|string|max:100',
            'email' => 'nullable|email|unique:students,email',
            'phone' => [
                'required',
                'string',
                'regex:/^\+971[0-9]{9}$/',
                'unique:students,phone'
            ],
            'nationality' => 'nullable|string|max:100',
            'nationality_ar' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'address_ar' => 'nullable|string|max:500',
            'birth_date' => 'nullable|date|before:today',
            'join_date' => 'required|date',
            'status' => 'required|in:active,inactive,suspended',
            'notes' => 'nullable|string|max:1000',
            'notes_ar' => 'nullable|string|max:1000',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',

            // Payment fields
            'create_payment' => 'boolean',
            'payment_amount' => 'required_if:create_payment,1|nullable|numeric|min:0|max:999999.99',
            'vat_inclusive' => 'boolean',
            'vat_rate' => 'nullable|numeric|min:0|max:100',
            'payment_method' => 'required_if:create_payment,1|nullable|in:cash,card,bank_transfer,online,cheque',
            'payment_date' => 'required_if:create_payment,1|nullable|date',
            'start_date' => 'required_if:create_payment,1|nullable|date',
            'end_date' => 'required_if:create_payment,1|nullable|date|after:start_date',
            'payment_type' => 'nullable|in:new_entry,renewal,regular',
            'payment_note' => 'nullable|string|max:1000',

            // Uniform fields
            'create_uniform' => 'boolean',
            'uniform_item' => 'required_if:create_uniform,1|nullable|string|max:100',
            'uniform_size' => 'required_if:create_uniform,1|nullable|in:XS,S,M,L,XL,XXL,XXXL',
            'uniform_quantity' => 'required_if:create_uniform,1|nullable|integer|min:1|max:10',
            'uniform_amount' => 'required_if:create_uniform,1|nullable|numeric|min:0|max:999999.99',
            'uniform_payment_method' => 'required_if:create_uniform,1|nullable|in:cash,card,bank_transfer,online,cheque',
            'uniform_delivery_date' => 'nullable|date|after:today',
            'uniform_note' => 'nullable|string|max:1000',
        ], [
            'phone.regex' => 'Phone number must be in UAE format: +971XXXXXXXXX',
            'profile_image.image' => 'Profile image must be a valid image file.',
            'profile_image.mimes' => 'Profile image must be a JPEG, PNG, JPG, or GIF file.',
            'profile_image.max' => 'Profile image must not be larger than 2MB.',
            'end_date.after' => 'End date must be after start date.',
            'uniform_delivery_date.after' => 'Delivery date must be in the future.',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $validatedData = $validator->validated();

            // Handle profile image upload
            try {
                $imagePath = $this->handleProfileImageUpload($request);
                if ($imagePath) {
                    $validatedData['profile_image'] = $imagePath;
                }
            } catch (\Exception $e) {
                Log::error('Profile image upload failed during student creation', [
                    'error' => $e->getMessage(),
                    'file_name' => $request->hasFile('profile_image') ? $request->file('profile_image')->getClientOriginalName() : 'unknown',
                    'file_size' => $request->hasFile('profile_image') ? $request->file('profile_image')->getSize() : 0
                ]);

                return redirect()
                    ->back()
                    ->withInput()
                    ->withErrors(['profile_image' => 'Failed to upload profile image: ' . $e->getMessage()]);
            }

            // Auto-translate names if Arabic versions are not provided
            if (empty($validatedData['full_name_ar']) && !empty($validatedData['full_name'])) {
                $arabicName = NameTranslationService::translateName($validatedData['full_name']);
                if ($arabicName) {
                    $validatedData['full_name_ar'] = $arabicName;
                }
            }

            if (empty($validatedData['first_name_ar']) && !empty($validatedData['first_name'])) {
                $arabicFirstName = NameTranslationService::translateName($validatedData['first_name']);
                if ($arabicFirstName) {
                    $validatedData['first_name_ar'] = $arabicFirstName;
                }
            }

            if (empty($validatedData['last_name_ar']) && !empty($validatedData['last_name'])) {
                $arabicLastName = NameTranslationService::translateName($validatedData['last_name']);
                if ($arabicLastName) {
                    $validatedData['last_name_ar'] = $arabicLastName;
                }
            }

            if (empty($validatedData['nationality_ar']) && !empty($validatedData['nationality'])) {
                $arabicNationality = NameTranslationService::translateName($validatedData['nationality'], 'nationality');
                if ($arabicNationality) {
                    $validatedData['nationality_ar'] = $arabicNationality;
                }
            }

            // Start database transaction for integrated creation
            \DB::beginTransaction();

            // Extract student data only
            $studentData = collect($validatedData)->except([
                'create_payment',
                'payment_amount',
                'vat_inclusive',
                'vat_rate',
                'payment_method',
                'payment_date',
                'start_date',
                'end_date',
                'payment_type',
                'payment_note',
                'create_uniform',
                'uniform_item',
                'uniform_size',
                'uniform_quantity',
                'uniform_amount',
                'uniform_payment_method',
                'uniform_delivery_date',
                'uniform_note'
            ])->toArray();

            $student = Student::create($studentData);
            $createdRecords = ['student' => $student];

            // Create payment if requested
            if ($request->filled('create_payment') && $request->boolean('create_payment')) {
                $payment = $this->createStudentPayment($student, $request);
                $createdRecords['payment'] = $payment;
            }

            // Create uniform order if requested
            if ($request->filled('create_uniform') && $request->boolean('create_uniform')) {
                $uniform = $this->createStudentUniform($student, $request);
                $createdRecords['uniform'] = $uniform;
            }

            \DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Student created successfully with integrated records',
                    'student' => $student->load(['branch', 'academy']),
                    'created_records' => $createdRecords
                ]);
            }

            // Redirect with success message and print options
            return redirect()->route('students.show', $student)
                ->with('success', 'Student created successfully with integrated records')
                ->with('created_records', $createdRecords);
        } catch (\Exception $e) {
            \DB::rollBack();

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create student: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to create student: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified student with detailed information.
     */
    public function show(Student $student): View|JsonResponse
    {
        Gate::authorize('view', $student);

        $student->load([
            'branch',
            'academy',
            'program',
            'payments.academy',
            'uniforms',
            'attendances.program'
        ]);

        // Get student statistics
        $stats = $student->getStatistics();

        // Get payment history
        $paymentHistory = $student->getPaymentHistory();

        // Get attendance summary
        $attendanceSummary = $student->getAttendanceSummary();

        // Get uniform orders
        $uniformOrders = $student->getUniformOrders();

        // Get actual uniform models for authorization checks
        $uniformModels = $student->uniforms()->orderBy('created_at', 'desc')->get();

        if (request()->ajax()) {
            return response()->json([
                'student' => $student,
                'stats' => $stats,
                'paymentHistory' => $paymentHistory,
                'attendanceSummary' => $attendanceSummary,
                'uniformOrders' => $uniformOrders
            ]);
        }

        return view('students.show', compact(
            'student',
            'stats',
            'paymentHistory',
            'attendanceSummary',
            'uniformOrders',
            'uniformModels'
        ));
    }

    /**
     * Show the form for editing the specified student.
     */
    public function edit(Student $student): View
    {
        Gate::authorize('update', $student);

        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        return view('students.edit', compact('student', 'branches', 'academies'));
    }

    /**
     * Update the specified student in storage.
     */
    public function update(Request $request, Student $student): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $student);

        $validator = Validator::make($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'full_name' => 'required|string|max:255',
            'full_name_ar' => 'nullable|string|max:255',
            'first_name' => 'nullable|string|max:100',
            'first_name_ar' => 'nullable|string|max:100',
            'last_name' => 'nullable|string|max:100',
            'last_name_ar' => 'nullable|string|max:100',
            'email' => [
                'nullable',
                'email',
                Rule::unique('students', 'email')->ignore($student->id)
            ],
            'phone' => [
                'required',
                'string',
                'regex:/^\+971[0-9]{9}$/',
                Rule::unique('students', 'phone')->ignore($student->id)
            ],
            'nationality' => 'nullable|string|max:100',
            'nationality_ar' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'address_ar' => 'nullable|string|max:500',
            'birth_date' => 'nullable|date|before:today',
            'join_date' => 'required|date',
            'status' => 'required|in:active,inactive,suspended',
            'notes' => 'nullable|string|max:1000',
            'notes_ar' => 'nullable|string|max:1000',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'phone.regex' => 'Phone number must be in UAE format: +971XXXXXXXXX',
            'profile_image.image' => 'Profile image must be a valid image file.',
            'profile_image.mimes' => 'Profile image must be a JPEG, PNG, JPG, or GIF file.',
            'profile_image.max' => 'Profile image must not be larger than 2MB.',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $validatedData = $validator->validated();

            // Handle profile image upload
            try {
                $imagePath = $this->handleProfileImageUpload($request, $student);
                if ($imagePath) {
                    $validatedData['profile_image'] = $imagePath;
                }
            } catch (\Exception $e) {
                Log::error('Profile image upload failed during student update', [
                    'student_id' => $student->id,
                    'error' => $e->getMessage(),
                    'file_name' => $request->hasFile('profile_image') ? $request->file('profile_image')->getClientOriginalName() : 'unknown',
                    'file_size' => $request->hasFile('profile_image') ? $request->file('profile_image')->getSize() : 0
                ]);

                return redirect()
                    ->back()
                    ->withInput()
                    ->withErrors(['profile_image' => 'Failed to upload profile image: ' . $e->getMessage()]);
            }

            $student->update($validatedData);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Student updated successfully',
                    'student' => $student->load(['branch', 'academy'])
                ]);
            }

            return redirect("/students/{$student->id}?updated=1")
                ->with('success', 'Student updated successfully');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update student: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to update student: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified student from storage.
     */
    public function destroy(Student $student): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $student);

        try {
            // Check if student has any related records
            $hasPayments = $student->payments()->exists();
            $hasUniforms = $student->uniforms()->exists();
            $hasAttendances = $student->attendances()->exists();

            if ($hasPayments || $hasUniforms || $hasAttendances) {
                // Soft delete by setting status to inactive instead of hard delete
                $student->update(['status' => 'inactive']);
                $message = 'Student deactivated successfully (has related records)';
            } else {
                $student->delete();
                $message = 'Student deleted successfully';
            }

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message
                ]);
            }

            return redirect()->route('students.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete student: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to delete student: ' . $e->getMessage());
        }
    }

    /**
     * Toggle student status (AJAX).
     */
    public function toggleStatus(Student $student): JsonResponse
    {
        Gate::authorize('update', $student);

        try {
            $student->toggleStatus();

            return response()->json([
                'success' => true,
                'message' => 'Student status updated successfully',
                'status' => $student->status,
                'status_text' => $student->status_text
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload profile image for a student.
     */
    public function uploadProfileImage(Request $request, Student $student): JsonResponse
    {
        Gate::authorize('update', $student);

        // Validate only the profile image
        $validator = Validator::make($request->all(), [
            'profile_image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048',
        ], [
            'profile_image.required' => 'Please select an image file.',
            'profile_image.image' => 'Profile image must be a valid image file.',
            'profile_image.mimes' => 'Profile image must be a JPEG, PNG, JPG, or GIF file.',
            'profile_image.max' => 'Profile image must not be larger than 2MB.',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Handle profile image upload
            $imagePath = $this->handleProfileImageUpload($request, $student);

            if ($imagePath) {
                $student->update(['profile_image' => $imagePath]);

                return response()->json([
                    'success' => true,
                    'message' => 'Profile image uploaded successfully',
                    'image_url' => $student->profile_image_url
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to upload image'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Profile image upload failed', [
                'student_id' => $student->id,
                'error' => $e->getMessage(),
                'file_name' => $request->hasFile('profile_image') ? $request->file('profile_image')->getClientOriginalName() : 'unknown',
                'file_size' => $request->hasFile('profile_image') ? $request->file('profile_image')->getSize() : 0
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to upload profile image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete student profile image (AJAX).
     */
    public function deleteProfileImage(Student $student): JsonResponse
    {
        Gate::authorize('update', $student);

        try {
            $student->deleteProfileImage();

            return response()->json([
                'success' => true,
                'message' => 'Profile image deleted successfully',
                'default_avatar_url' => $student->getDefaultAvatarUrl()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to delete profile image', [
                'student_id' => $student->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete profile image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle profile image upload with comprehensive error handling.
     */
    private function handleProfileImageUpload($request, $student = null): ?string
    {
        if (!$request->hasFile('profile_image')) {
            return null;
        }

        $image = $request->file('profile_image');

        // Prepare upload options
        $options = [
            'max_size' => 2048, // 2MB in KB
            'allowed_types' => ['jpeg', 'jpg', 'png', 'gif'],
            'disk' => 'public',
            'generate_unique_name' => true,
            'old_file_path' => $student && $student->profile_image ? $student->profile_image : null
        ];

        // Use FileUploadService for upload
        $result = FileUploadService::uploadImage($image, 'students/profile_images', $options);

        if (!$result['success']) {
            // Log the detailed error
            Log::error('Profile image upload failed', [
                'student_id' => $student ? $student->id : 'new',
                'error_code' => $result['error_code'] ?? 'UNKNOWN',
                'error_message' => $result['error'],
                'file_name' => $image->getClientOriginalName(),
                'file_size' => $image->getSize()
            ]);

            // Throw exception with user-friendly message
            $userMessage = FileUploadService::getErrorMessage($result['error_code'] ?? 'UNKNOWN');
            throw new \Exception($userMessage);
        }

        Log::info('Profile image processed successfully', [
            'student_id' => $student ? $student->id : 'new',
            'file_name' => $result['filename'],
            'file_path' => $result['file_path'],
            'file_size' => $result['size']
        ]);

        return $result['file_path'];
    }

    /**
     * Handle bulk actions on students.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,suspend,delete',
            'student_ids' => 'required|array|min:1',
            'student_ids.*' => 'exists:students,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $studentIds = $request->get('student_ids');
            $action = $request->get('action');
            $count = 0;

            foreach ($studentIds as $studentId) {
                $student = Student::find($studentId);
                if (!$student) continue;

                Gate::authorize('update', $student);

                switch ($action) {
                    case 'activate':
                        $student->update(['status' => 'active']);
                        $count++;
                        break;
                    case 'deactivate':
                        $student->update(['status' => 'inactive']);
                        $count++;
                        break;
                    case 'suspend':
                        $student->update(['status' => 'suspended']);
                        $count++;
                        break;
                    case 'delete':
                        // Check if student has related records
                        if (
                            $student->payments()->exists() ||
                            $student->uniforms()->exists() ||
                            $student->attendances()->exists()
                        ) {
                            $student->update(['status' => 'inactive']);
                        } else {
                            $student->delete();
                        }
                        $count++;
                        break;
                }
            }

            $actionText = match ($action) {
                'activate' => 'activated',
                'deactivate' => 'deactivated',
                'suspend' => 'suspended',
                'delete' => 'deleted/deactivated',
                default => 'processed'
            };

            return response()->json([
                'success' => true,
                'message' => "{$count} students {$actionText} successfully"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export students to Excel.
     */
    public function exportExcel(Request $request)
    {
        Gate::authorize('export', Student::class);

        try {
            $query = Student::with(['branch', 'academy']);

            // Apply same filters as index method
            if ($request->filled('search')) {
                $query->search($request->get('search'));
            }
            if ($request->filled('name_search')) {
                $query->searchByName($request->get('name_search'));
            }
            if ($request->filled('branch_id')) {
                $query->byBranch($request->get('branch_id'));
            }
            if ($request->filled('academy_id')) {
                $query->byAcademy($request->get('academy_id'));
            }
            if ($request->filled('program_id')) {
                $query->byProgram($request->get('program_id'));
            }
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'active') $query->active();
                elseif ($status === 'inactive') $query->inactive();
                elseif ($status === 'suspended') $query->suspended();
            }

            $students = $query->get();

            $filename = 'students_' . date('Y-m-d_H-i-s') . '.csv';

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function () use ($students) {
                $file = fopen('php://output', 'w');

                // CSV Headers
                fputcsv($file, [
                    'ID',
                    'Full Name',
                    'Email',
                    'Phone',
                    'Nationality',
                    'Branch',
                    'Academy',
                    'Birth Date',
                    'Age',
                    'Join Date',
                    'Status',
                    'Total Payments (AED)',
                    'Pending Payments (AED)',
                    'Attendance Rate (%)',
                    'Uniform Orders',
                    'Address',
                    'Notes'
                ]);

                // CSV Data
                foreach ($students as $student) {
                    fputcsv($file, [
                        $student->id,
                        $student->full_name,
                        $student->email,
                        $student->formatted_phone,
                        $student->nationality,
                        $student->branch->name ?? '',
                        $student->academy->name ?? '',
                        $student->formatted_birth_date,
                        $student->age,
                        $student->formatted_join_date,
                        $student->status_text,
                        number_format($student->total_payments, 2),
                        number_format($student->pending_payments, 2),
                        $student->attendance_rate,
                        $student->uniform_orders_count,
                        $student->address,
                        $student->notes
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Export failed: ' . $e->getMessage());
        }
    }

    /**
     * Export students to PDF.
     */
    public function exportPdf(Request $request)
    {
        Gate::authorize('export', Student::class);

        try {
            $query = Student::with(['branch', 'academy']);

            // Apply same filters as index method
            if ($request->filled('search')) {
                $query->search($request->get('search'));
            }
            if ($request->filled('branch_id')) {
                $query->byBranch($request->get('branch_id'));
            }
            if ($request->filled('academy_id')) {
                $query->byAcademy($request->get('academy_id'));
            }
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'active') $query->active();
                elseif ($status === 'inactive') $query->inactive();
                elseif ($status === 'suspended') $query->suspended();
            }

            $students = $query->get();
            $filters = $request->only(['search', 'branch_id', 'academy_id', 'status']);

            return view('students.export-pdf', compact('students', 'filters'));
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'PDF export failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate and display student profile for printing.
     */
    public function printProfile(Student $student): View
    {
        Gate::authorize('view', $student);

        $student->load([
            'branch',
            'academy',
            'program',
            'payments.academy',
            'uniforms',
            'attendances.program'
        ]);

        // Get student statistics
        $stats = $student->getStatistics();

        // Get payment history
        $paymentHistory = $student->getPaymentHistory();

        // Get attendance summary
        $attendanceSummary = $student->getAttendanceSummary();

        // Get uniform orders
        $uniformOrders = $student->getUniformOrders();

        return view('students.print-profile', compact(
            'student',
            'stats',
            'paymentHistory',
            'attendanceSummary',
            'uniformOrders'
        ));
    }

    /**
     * API endpoint for AJAX requests.
     */
    public function apiIndex(Request $request): JsonResponse
    {
        try {
            $query = Student::with(['branch', 'academy'])
                ->withCount(['payments', 'uniforms', 'attendances']);

            // Apply branch-level filtering for non-admin users
            $user = Auth::user();
            if ($user->role === 'branch_manager') {
                $query->where('branch_id', $user->branch_id);
            } elseif ($user->role === 'academy_manager') {
                $query->where('academy_id', $user->academy_id);
            }

            // Apply filters
            if ($request->filled('search')) {
                $query->search($request->get('search'));
            }
            if ($request->filled('name_search')) {
                $query->searchByName($request->get('name_search'));
            }
            if ($request->filled('branch_id')) {
                $query->byBranch($request->get('branch_id'));
            }
            if ($request->filled('academy_id')) {
                $query->byAcademy($request->get('academy_id'));
            }
            if ($request->filled('program_id')) {
                $query->byProgram($request->get('program_id'));
            }
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'active') $query->active();
                elseif ($status === 'inactive') $query->inactive();
                elseif ($status === 'suspended') $query->suspended();
            }

            $perPage = $request->get('per_page', 15);
            $students = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'students' => $students
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch students: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student statistics for dashboard.
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $stats = [
                'total_students' => Student::count(),
                'active_students' => Student::active()->count(),
                'inactive_students' => Student::inactive()->count(),
                'suspended_students' => Student::suspended()->count(),
                'new_students_this_month' => Student::whereMonth('join_date', now()->month)
                    ->whereYear('join_date', now()->year)->count(),
                'total_payments' => Payment::completed()->sum('amount'),
                'pending_payments' => Payment::pending()->sum('amount'),
                'average_age' => Student::whereNotNull('birth_date')
                    ->get()
                    ->avg(function ($student) {
                        return $student->age;
                    }),
                'attendance_rate' => Student::active()
                    ->get()
                    ->avg(function ($student) {
                        return $student->attendance_rate;
                    }),
                'uniform_orders' => Uniform::count(),
                'delivered_uniforms' => Uniform::delivered()->count(),
            ];

            // Monthly registration data for charts
            $monthlyRegistrations = [];
            for ($i = 11; $i >= 0; $i--) {
                $date = now()->subMonths($i);
                $monthlyRegistrations[] = [
                    'month' => $date->format('M Y'),
                    'count' => Student::whereMonth('join_date', $date->month)
                        ->whereYear('join_date', $date->year)
                        ->count()
                ];
            }

            return response()->json([
                'success' => true,
                'stats' => $stats,
                'monthlyRegistrations' => $monthlyRegistrations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get academies by branch (AJAX helper).
     */
    public function getAcademiesByBranch(Request $request): JsonResponse
    {
        try {
            $branchId = $request->get('branch_id');
            $academies = Academy::where('branch_id', $branchId)
                ->active()
                ->orderBy('name')
                ->get(['id', 'name']);

            return response()->json([
                'success' => true,
                'academies' => $academies
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch academies: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Translate student name to Arabic (API endpoint).
     */
    public function translateName(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'type' => 'nullable|string|in:name,nationality'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $name = $request->get('name');
            $type = $request->get('type', 'name');

            $arabicName = NameTranslationService::translateName($name, $type);
            $suggestions = NameTranslationService::getSuggestedArabicName($name);

            return response()->json([
                'success' => true,
                'original' => $name,
                'translation' => $arabicName,
                'suggestions' => $suggestions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Translation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk translate student names (API endpoint).
     */
    public function bulkTranslateNames(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'student_ids' => 'required|array|min:1',
            'student_ids.*' => 'exists:students,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $studentIds = $request->get('student_ids');
            $count = NameTranslationService::bulkTranslateStudents($studentIds);

            return response()->json([
                'success' => true,
                'message' => "{$count} students translated successfully",
                'translated_count' => $count
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk translation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create payment for student during integrated creation.
     */
    private function createStudentPayment(Student $student, Request $request): Payment
    {
        $paymentData = [
            'student_id' => $student->id,
            'branch_id' => $student->branch_id,
            'academy_id' => $student->academy_id,
            'amount' => $request->get('payment_amount'),
            'vat_inclusive' => $request->boolean('vat_inclusive'),
            'vat_rate' => $request->get('vat_rate', 5),
            'payment_method' => $request->get('payment_method'),
            'payment_date' => $request->get('payment_date'),
            'start_date' => $request->get('start_date'),
            'end_date' => $request->get('end_date'),
            'payment_type' => $request->get('payment_type', 'new_entry'), // Default to new_entry for new students
            'status' => 'completed',
            'currency' => 'AED',
            'note' => $request->get('payment_note'),
            'description' => 'Initial payment for new student enrollment',
        ];

        // Calculate VAT amounts
        $amount = (float) $paymentData['amount'];
        $vatRate = (float) $paymentData['vat_rate'];

        if ($paymentData['vat_inclusive']) {
            $paymentData['total_amount'] = $amount;
            $paymentData['vat_amount'] = ($amount * $vatRate) / (100 + $vatRate);
            $paymentData['subtotal'] = $amount - $paymentData['vat_amount'];
        } else {
            $paymentData['subtotal'] = $amount;
            $paymentData['vat_amount'] = ($amount * $vatRate) / 100;
            $paymentData['total_amount'] = $amount + $paymentData['vat_amount'];
        }

        return Payment::create($paymentData);
    }

    /**
     * Create uniform order for student during integrated creation.
     */
    private function createStudentUniform(Student $student, Request $request): Uniform
    {
        $uniformData = [
            'student_id' => $student->id,
            'branch_id' => $student->branch_id,
            'academy_id' => $student->academy_id,
            'item' => $request->get('uniform_item'),
            'size' => $request->get('uniform_size'),
            'quantity' => $request->get('uniform_quantity', 1),
            'amount' => $request->get('uniform_amount'),
            'payment_method' => $request->get('uniform_payment_method'),
            'delivery_date' => $request->get('uniform_delivery_date'),
            'order_date' => now()->toDateString(),
            'status' => 'pending',
            'branch_status' => 'pending',
            'office_status' => 'pending',
            'currency' => 'AED',
            'note' => $request->get('uniform_note'),
        ];

        return Uniform::create($uniformData);
    }
}
