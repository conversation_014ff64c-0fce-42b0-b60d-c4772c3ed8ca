<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\App;

class DateLocalizationService
{
    /**
     * Arabic month names
     */
    private static array $arabicMonths = [
        1 => 'يناير',
        2 => 'فبراير', 
        3 => 'مارس',
        4 => 'أبريل',
        5 => 'مايو',
        6 => 'يونيو',
        7 => 'يوليو',
        8 => 'أغسطس',
        9 => 'سبتمبر',
        10 => 'أكتوبر',
        11 => 'نوفمبر',
        12 => 'ديسمبر'
    ];

    /**
     * Arabic day names
     */
    private static array $arabicDays = [
        'Sunday' => 'الأحد',
        'Monday' => 'الاثنين',
        'Tuesday' => 'الثلاثاء',
        'Wednesday' => 'الأربعاء',
        'Thursday' => 'الخميس',
        'Friday' => 'الجمعة',
        'Saturday' => 'السبت'
    ];

    /**
     * Arabic numerals mapping
     */
    private static array $arabicNumerals = [
        '0' => '٠',
        '1' => '١',
        '2' => '٢',
        '3' => '٣',
        '4' => '٤',
        '5' => '٥',
        '6' => '٦',
        '7' => '٧',
        '8' => '٨',
        '9' => '٩'
    ];

    /**
     * Format date based on current locale
     */
    public static function formatDate(?Carbon $date, string $format = null): string
    {
        if (!$date) return '';

        $locale = App::getLocale();
        
        if ($locale === 'ar') {
            return self::formatArabicDate($date, $format);
        }

        return self::formatEnglishDate($date, $format);
    }

    /**
     * Format date in Arabic
     */
    public static function formatArabicDate(?Carbon $date, string $format = null): string
    {
        if (!$date) return '';

        $format = $format ?? 'd F Y';
        
        $day = $date->day;
        $month = self::$arabicMonths[$date->month];
        $year = $date->year;
        
        // Convert to Arabic numerals if needed
        $dayArabic = self::convertToArabicNumerals($day);
        $yearArabic = self::convertToArabicNumerals($year);

        return match($format) {
            'd/m/Y' => "{$day}/{$date->month}/{$year}",
            'd/m/Y-ar' => "{$dayArabic}/{$date->month}/{$yearArabic}",
            'd F Y' => "{$day} {$month} {$year}",
            'd F Y-ar' => "{$dayArabic} {$month} {$yearArabic}",
            'F d, Y' => "{$month} {$day}، {$year}",
            'F d, Y-ar' => "{$month} {$dayArabic}، {$yearArabic}",
            'Y-m-d' => $date->format('Y-m-d'),
            'short' => "{$day}/{$date->month}/{$year}",
            'medium' => "{$day} {$month} {$year}",
            'long' => self::$arabicDays[$date->format('l')] . "، {$day} {$month} {$year}",
            default => "{$day} {$month} {$year}"
        };
    }

    /**
     * Format date in English
     */
    public static function formatEnglishDate(?Carbon $date, string $format = null): string
    {
        if (!$date) return '';

        $format = $format ?? 'M d, Y';
        
        return match($format) {
            'd/m/Y' => $date->format('d/m/Y'),
            'M d, Y' => $date->format('M d, Y'),
            'F d, Y' => $date->format('F d, Y'),
            'Y-m-d' => $date->format('Y-m-d'),
            'short' => $date->format('m/d/Y'),
            'medium' => $date->format('M d, Y'),
            'long' => $date->format('l, F d, Y'),
            default => $date->format($format)
        };
    }

    /**
     * Convert Western numerals to Arabic numerals
     */
    public static function convertToArabicNumerals($number): string
    {
        $number = (string) $number;
        return strtr($number, self::$arabicNumerals);
    }

    /**
     * Convert Arabic numerals to Western numerals
     */
    public static function convertToWesternNumerals(string $arabicNumber): string
    {
        $westernNumerals = array_flip(self::$arabicNumerals);
        return strtr($arabicNumber, $westernNumerals);
    }

    /**
     * Get relative time in Arabic
     */
    public static function getRelativeTime(?Carbon $date): string
    {
        if (!$date) return '';

        $locale = App::getLocale();
        
        if ($locale === 'ar') {
            return self::getArabicRelativeTime($date);
        }

        return $date->diffForHumans();
    }

    /**
     * Get relative time in Arabic
     */
    private static function getArabicRelativeTime(Carbon $date): string
    {
        $now = now();
        $diff = $date->diffInSeconds($now);
        
        if ($diff < 60) {
            return 'منذ لحظات';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return "منذ {$minutes} دقيقة";
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return "منذ {$hours} ساعة";
        } elseif ($diff < 2592000) {
            $days = floor($diff / 86400);
            return "منذ {$days} يوم";
        } elseif ($diff < 31536000) {
            $months = floor($diff / 2592000);
            return "منذ {$months} شهر";
        } else {
            $years = floor($diff / 31536000);
            return "منذ {$years} سنة";
        }
    }

    /**
     * Format age in Arabic
     */
    public static function formatAge(?int $age): string
    {
        if (!$age) return '';

        $locale = App::getLocale();
        
        if ($locale === 'ar') {
            $ageArabic = self::convertToArabicNumerals($age);
            return "{$ageArabic} سنة";
        }

        return "{$age} years old";
    }

    /**
     * Get month name in current locale
     */
    public static function getMonthName(int $month): string
    {
        $locale = App::getLocale();
        
        if ($locale === 'ar') {
            return self::$arabicMonths[$month] ?? '';
        }

        return Carbon::create()->month($month)->format('F');
    }

    /**
     * Get day name in current locale
     */
    public static function getDayName(string $englishDay): string
    {
        $locale = App::getLocale();
        
        if ($locale === 'ar') {
            return self::$arabicDays[$englishDay] ?? $englishDay;
        }

        return $englishDay;
    }

    /**
     * Parse Arabic date input
     */
    public static function parseArabicDate(string $arabicDate): ?Carbon
    {
        try {
            // Convert Arabic numerals to Western
            $westernDate = self::convertToWesternNumerals($arabicDate);
            
            // Try different date formats
            $formats = ['d/m/Y', 'Y-m-d', 'd-m-Y', 'm/d/Y'];
            
            foreach ($formats as $format) {
                try {
                    return Carbon::createFromFormat($format, $westernDate);
                } catch (\Exception $e) {
                    continue;
                }
            }
            
            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get localized date format pattern
     */
    public static function getDateFormatPattern(): string
    {
        $locale = App::getLocale();
        
        return $locale === 'ar' ? 'd/m/Y' : 'M d, Y';
    }

    /**
     * Get localized date input format for HTML
     */
    public static function getHtmlDateFormat(): string
    {
        return 'Y-m-d'; // HTML date inputs always use this format
    }

    /**
     * Format date for display in tables
     */
    public static function formatTableDate(?Carbon $date): string
    {
        if (!$date) return __('common.not_available');

        $locale = App::getLocale();
        
        if ($locale === 'ar') {
            return self::formatArabicDate($date, 'd/m/Y');
        }

        return $date->format('M d, Y');
    }

    /**
     * Format date for forms
     */
    public static function formatFormDate(?Carbon $date): string
    {
        if (!$date) return '';

        return $date->format('Y-m-d');
    }
}
