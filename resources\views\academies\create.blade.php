@extends('layouts.dashboard')

@section('title', 'Create Academy - LEADERS SPORTS SERVICES')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Create New Academy</h1>
                <p class="text-lg text-dark-gray">Add a new sports academy to the system</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('academies.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Academies
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto" x-data="academyForm()" x-init="init()">
        <form @submit.prevent="submitForm()" class="space-y-6">
            @csrf

            <!-- Basic Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                    <p class="bank-card-subtitle">Enter the academy's basic details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Academy Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label-bank required">Academy Name</label>
                            <input type="text" id="name" name="name" x-model="form.name" class="form-input-bank"
                                placeholder="Enter academy name" required>
                            <div x-show="errors.name" class="form-error" x-text="errors.name"></div>
                        </div>

                        <!-- Branch Selection -->
                        <div>
                            <label for="branch_id" class="form-label-bank required">Branch</label>
                            <select id="branch_id" name="branch_id" x-model="form.branch_id" class="form-select-bank"
                                required>
                                <option value="">Select a branch</option>
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ isset($selectedBranchId) && $selectedBranchId == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }} - {{ $branch->location }}</option>
                                @endforeach
                            </select>
                            <div x-show="errors.branch_id" class="form-error" x-text="errors.branch_id"></div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="form-label-bank">Status</label>
                            <select id="status" name="status" x-model="form.status" class="form-select-bank">
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                            <div x-show="errors.status" class="form-error" x-text="errors.status"></div>
                        </div>

                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label for="address" class="form-label-bank">Address</label>
                            <textarea id="address" name="address" x-model="form.address" rows="3" class="form-textarea-bank"
                                placeholder="Enter academy address (optional)"></textarea>
                            <div x-show="errors.address" class="form-error" x-text="errors.address"></div>
                            <p class="form-help">Provide the physical address of the academy</p>
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="description" class="form-label-bank">Description</label>
                            <textarea id="description" name="description" x-model="form.description" rows="4" class="form-textarea-bank"
                                placeholder="Enter academy description (optional)"></textarea>
                            <div x-show="errors.description" class="form-error" x-text="errors.description"></div>
                            <p class="form-help">Provide a brief description of the academy and its programs</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coach Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Coach Information</h3>
                    <p class="bank-card-subtitle">Optional coach details for this academy</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Coach Name -->
                        <div>
                            <label for="coach_name" class="form-label-bank">Coach Name</label>
                            <input type="text" id="coach_name" name="coach_name" x-model="form.coach_name"
                                class="form-input-bank" placeholder="Enter coach name">
                            <div x-show="errors.coach_name" class="form-error" x-text="errors.coach_name"></div>
                        </div>

                        <!-- Coach Phone -->
                        <div>
                            <label for="coach_phone" class="form-label-bank">Coach Phone</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 flex items-center z-10">
                                    <div class="country-code-prefix flex items-center px-3 py-2 rounded-l-md h-full">
                                        <!-- UAE Flag -->
                                        <div class="flex items-center mr-2">
                                            <div
                                                class="uae-flag w-6 h-4 rounded-sm overflow-hidden border border-gray-300">
                                                <svg xmlns="http://www.w3.org/2000/svg"
                                                    shape-rendering="geometricPrecision"
                                                    text-rendering="geometricPrecision" image-rendering="optimizeQuality"
                                                    fill-rule="evenodd" clip-rule="evenodd" viewBox="0 0 512 512"
                                                    class="w-full h-full">
                                                    <g fill-rule="nonzero">
                                                        <path fill="#4D4D4D"
                                                            d="M256-.001c70.684 0 134.69 28.664 181.013 74.988C483.337 121.31 512.001 185.316 512.001 256c0 70.684-28.664 134.69-74.988 181.013C390.69 483.337 326.684 512.001 256 512.001c-70.676 0-134.689-28.664-181.013-74.988C28.663 390.69 0 326.676 0 256c0-70.684 28.663-134.69 74.987-181.013C121.311 28.663 185.316-.001 256-.001z" />
                                                        <path fill="#fff"
                                                            d="M256.002 19.596c65.277 0 124.382 26.466 167.162 69.243 42.777 42.779 69.243 101.884 69.243 167.162s-26.466 124.383-69.246 167.16c-42.777 42.779-101.882 69.246-167.159 69.246-65.279 0-124.384-26.467-167.163-69.243-42.777-42.78-69.243-101.885-69.243-167.163S46.062 131.618 88.839 88.839c42.779-42.777 101.884-69.243 167.163-69.243z" />
                                                        <path fill="#00732F"
                                                            d="M458.658 179.96H53.345C84.13 97.955 163.242 39.594 256.002 39.594c92.756 0 171.871 58.361 202.656 140.366z" />
                                                        <path
                                                            d="M51.468 326.835h409.067c-29.338 84.727-109.826 145.574-204.533 145.574-94.711 0-175.193-60.847-204.534-145.574z" />
                                                        <path fill="red"
                                                            d="M162.9 60.598v390.806C89.979 416.598 39.594 342.186 39.594 256.001c0-86.188 50.385-160.597 123.306-195.403z" />
                                                    </g>
                                                </svg>
                                            </div>
                                        </div>
                                        <!-- Country Code -->
                                        <span class="text-sm font-bold text-red-700">+971</span>
                                    </div>
                                </div>
                                <input type="tel" id="coach_phone_display" name="coach_phone_display"
                                    x-model="phoneDisplay" @input="formatPhoneNumber()" class="form-input-bank"
                                    style="padding-left: 9rem !important; padding-right: 1rem !important;"
                                    placeholder="*********" maxlength="11" pattern="[0-9]{9}">
                                <input type="hidden" name="coach_phone" x-model="form.coach_phone">
                            </div>
                            <div x-show="errors.coach_phone" class="form-error" x-text="errors.coach_phone"></div>
                            <p class="form-help">Enter 9 digits after +971 (e.g., *********)</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button type="submit" :disabled="loading" class="btn-bank"
                                :class="{ 'opacity-50 cursor-not-allowed': loading }">
                                <svg x-show="!loading" class="w-5 h-5" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M5 13l4 4L19 7"></path>
                                </svg>
                                <svg x-show="loading" class="w-5 h-5 animate-spin" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                    </path>
                                </svg>
                                <span x-text="loading ? 'Creating...' : 'Create Academy'">Create Academy</span>
                            </button>
                            <button type="button" @click="resetForm()" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                    </path>
                                </svg>
                                Reset Form
                            </button>
                        </div>
                        <a href="{{ route('academies.index') }}" class="btn-bank btn-bank-secondary">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function academyForm() {
            return {
                loading: false,
                form: {
                    name: '',
                    branch_id: '{{ $selectedBranchId ?? '' }}',
                    description: '',
                    address: '',
                    coach_name: '',
                    coach_phone: '',
                    status: '1'
                },
                phoneDisplay: '',
                errors: {},

                init() {
                    // Initialize phone display if coach_phone exists
                    if (this.form.coach_phone && this.form.coach_phone.startsWith('+971')) {
                        this.phoneDisplay = this.form.coach_phone.substring(4);
                    }
                },

                formatPhoneNumber() {
                    // Only allow digits
                    this.phoneDisplay = this.phoneDisplay.replace(/[^0-9]/g, '');

                    // Limit to 9 digits
                    if (this.phoneDisplay.length > 9) {
                        this.phoneDisplay = this.phoneDisplay.substring(0, 9);
                    }

                    // Update the actual form field with full number
                    this.form.coach_phone = this.phoneDisplay ? '+971' + this.phoneDisplay : '';
                },

                async submitForm() {
                    this.loading = true;
                    this.errors = {};

                    try {
                        console.log('Form data before submission:', this.form);

                        const formData = new FormData();
                        Object.keys(this.form).forEach(key => {
                            // Always include required fields, even if empty (for proper validation)
                            if (['branch_id', 'name'].includes(key) || (this.form[key] !== null && this.form[
                                    key] !== '')) {
                                formData.append(key, this.form[key] || '');
                                console.log(`Added to FormData: ${key} = ${this.form[key] || ''}`);
                            }
                        });

                        console.log('Making request to:', '{{ route('academies.store') }}');

                        const response = await fetch('{{ route('academies.store') }}', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: formData
                        });

                        console.log('Response status:', response.status);
                        console.log('Response headers:', [...response.headers.entries()]);

                        // Check if response is JSON
                        const contentType = response.headers.get('content-type');
                        if (!contentType || !contentType.includes('application/json')) {
                            const text = await response.text();
                            console.error('Expected JSON but received:', text.substring(0, 200));
                            showNotification('error',
                                'Server returned an unexpected response. Please check the console for details.');
                            return;
                        }

                        const result = await response.json();

                        if (response.ok && result.success) {
                            showNotification('success', result.message || 'Academy created successfully!');
                            window.location.href = '{{ route('academies.index') }}';
                        } else {
                            if (result.errors) {
                                this.errors = result.errors;
                            } else {
                                showNotification('error', result.message ||
                                    'An error occurred while creating the academy.');
                            }
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showNotification('error', 'An unexpected error occurred. Please try again.');
                    } finally {
                        this.loading = false;
                    }
                },

                resetForm() {
                    this.form = {
                        name: '',
                        branch_id: '{{ $selectedBranchId ?? '' }}',
                        description: '',
                        address: '',
                        coach_name: '',
                        coach_phone: '',
                        status: '1'
                    };
                    this.phoneDisplay = '';
                    this.errors = {};
                }
            }
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            if (type === 'success') {
                alert('Success: ' + message);
            } else {
                alert('Error: ' + message);
            }
        }
    </script>
@endpush
