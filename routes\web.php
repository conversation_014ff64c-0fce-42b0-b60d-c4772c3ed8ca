<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\AcademyController;
use App\Http\Controllers\ProgramController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\UniformController;
use App\Http\Controllers\UniformInventoryController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SettingController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\VenueController;
use App\Http\Controllers\FieldController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\ReservationController;
use App\Http\Controllers\ReservationPaymentController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Services\LanguageService;

// Language switching routes
Route::get('/language/{locale}', function ($locale) {
    if (LanguageService::isSupported($locale)) {
        LanguageService::switchTo($locale);

        // Update user preference if authenticated
        if (Auth::check()) {
            Auth::user()->update(['language_preference' => $locale]);
        }
    }

    return redirect()->back();
})->name('language.switch');

// API endpoint for language switching (AJAX)
Route::post('/api/language/switch', function () {
    $locale = request('locale');

    // Validate locale
    if (!$locale || !LanguageService::isSupported($locale)) {
        return response()->json([
            'success' => false,
            'message' => __('common.unsupported_language'),
        ], 422);
    }

    LanguageService::switchTo($locale);

    // Update user preference if authenticated
    if (Auth::check()) {
        Auth::user()->update(['language_preference' => $locale]);
    }

    return response()->json([
        'success' => true,
        'locale' => $locale,
        'direction' => LanguageService::getDirection($locale),
        'message' => __('common.language_switched'),
    ]);
})->name('api.language.switch');

// API endpoint for translations
Route::get('/api/translations/{locale}', function ($locale) {
    // Validate locale
    if (!LanguageService::isSupported($locale)) {
        return response()->json([
            'success' => false,
            'message' => 'Unsupported locale',
        ], 422);
    }

    try {
        $translations = [
            'common' => trans('common', [], $locale),
            'dashboard' => trans('dashboard', [], $locale),
        ];

        return response()->json($translations);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Failed to load translations',
        ], 500);
    }
})->name('api.translations');

// API endpoints for name translation
Route::post('/api/students/translate-name', [StudentController::class, 'translateName'])->name('api.students.translate-name');
Route::post('/api/students/bulk-translate-names', [StudentController::class, 'bulkTranslateNames'])->name('api.students.bulk-translate-names');

// Redirect root to dashboard if authenticated, otherwise to login
Route::get('/', function () {
    return Auth::check() ? redirect()->route('dashboard') : redirect()->route('login');
});

Route::get('/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::get('/dashboard/stats', [App\Http\Controllers\DashboardController::class, 'getStats'])
    ->middleware(['auth'])
    ->name('dashboard.stats');

Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // User Management (Admin only)
    Route::middleware('role:admin')->group(function () {
        Route::resource('users', UserController::class);
        Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
        Route::post('users/bulk-action', [UserController::class, 'bulkAction'])->name('users.bulk-action');
        Route::get('users/export/excel', [UserController::class, 'exportExcel'])->name('users.export.excel');
        Route::get('users/export/pdf', [UserController::class, 'exportPdf'])->name('users.export.pdf');
        Route::get('users/academies-by-branch', [UserController::class, 'getAcademiesByBranch'])->name('users.academies-by-branch');
    });

    // Settings Management (Admin only)
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingController::class, 'index'])->name('index');
        Route::get('/edit/{category?}', [SettingController::class, 'edit'])->name('edit');
        Route::put('/update/{category?}', [SettingController::class, 'update'])->name('update');
        Route::post('/store', [SettingController::class, 'store'])->name('store');
        Route::delete('/{setting}', [SettingController::class, 'destroy'])->name('destroy');
        Route::get('/export', [SettingController::class, 'export'])->name('export');
        Route::post('/import', [SettingController::class, 'import'])->name('import');

        // Translation Management
        Route::post('/translations/store', [SettingController::class, 'storeTranslation'])->name('translations.store');
        Route::delete('/translations/delete', [SettingController::class, 'deleteTranslation'])->name('translations.delete');
        Route::post('/translations/export', [SettingController::class, 'exportTranslations'])->name('translations.export');
        Route::post('/translations/import', [SettingController::class, 'importTranslations'])->name('translations.import');
    });

    // Branch Management (Admin and Branch Manager only)
    Route::middleware('role:admin,branch_manager')->group(function () {
        Route::resource('branches', BranchController::class);
        Route::post('branches/{branch}/toggle-status', [BranchController::class, 'toggleStatus'])->name('branches.toggle-status');
        Route::post('branches/bulk-action', [BranchController::class, 'bulkAction'])->name('branches.bulk-action');
        Route::get('branches/export/excel', [BranchController::class, 'exportExcel'])->name('branches.export.excel');
        Route::get('branches/export/pdf', [BranchController::class, 'exportPdf'])->name('branches.export.pdf');
    });

    // Branch API endpoints (for AJAX requests)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('branches', [BranchController::class, 'apiIndex'])->name('branches.index');
        Route::get('branches/statistics', [BranchController::class, 'getStatistics'])->name('branches.statistics');
    });

    // Academy Management (Admin, Branch Manager, and Academy Manager)
    Route::middleware('role:admin,branch_manager,academy_manager')->group(function () {
        Route::resource('academies', AcademyController::class);
        Route::post('academies/{academy}/toggle-status', [AcademyController::class, 'toggleStatus'])->name('academies.toggle-status');
        Route::post('academies/bulk-action', [AcademyController::class, 'bulkAction'])->name('academies.bulk-action');
        Route::get('academies/export/excel', [AcademyController::class, 'exportExcel'])->name('academies.export.excel');
        Route::get('academies/export/pdf', [AcademyController::class, 'exportPdf'])->name('academies.export.pdf');
    });

    // Academy API endpoints (for AJAX requests)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('academies', [AcademyController::class, 'apiIndex'])->name('academies.index');
        Route::get('academies/statistics', [AcademyController::class, 'getStatistics'])->name('academies.statistics');
    });

    // Program Management (Admin, Branch Manager, and Academy Manager)
    Route::middleware('role:admin,branch_manager,academy_manager')->group(function () {
        Route::resource('programs', ProgramController::class);
        Route::post('programs/{program}/toggle-status', [ProgramController::class, 'toggleStatus'])->name('programs.toggle-status');
        Route::post('programs/bulk-action', [ProgramController::class, 'bulkAction'])->name('programs.bulk-action');
        Route::get('programs/export/excel', [ProgramController::class, 'exportExcel'])->name('programs.export.excel');
        Route::get('programs/export/pdf', [ProgramController::class, 'exportPdf'])->name('programs.export.pdf');
    });

    // Program API endpoints (for AJAX requests)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('programs', [ProgramController::class, 'apiIndex'])->name('programs.index');
        Route::get('programs/statistics', [ProgramController::class, 'getStatistics'])->name('programs.statistics');
    });

    // Student Management (Admin, Branch Manager, and Academy Manager)
    Route::middleware('role:admin,branch_manager,academy_manager')->group(function () {
        Route::resource('students', StudentController::class);
        Route::post('students/{student}/toggle-status', [StudentController::class, 'toggleStatus'])->name('students.toggle-status');
        Route::post('students/{student}/profile-image', [StudentController::class, 'uploadProfileImage'])->name('students.upload-profile-image');
        Route::delete('students/{student}/profile-image', [StudentController::class, 'deleteProfileImage'])->name('students.delete-profile-image');
        Route::post('students/bulk-action', [StudentController::class, 'bulkAction'])->name('students.bulk-action');
        Route::get('students/export/excel', [StudentController::class, 'exportExcel'])->name('students.export.excel');
        Route::get('students/export/pdf', [StudentController::class, 'exportPdf'])->name('students.export.pdf');
        Route::get('students/{student}/print', [StudentController::class, 'printProfile'])->name('students.print');
        Route::get('students/academies-by-branch', [StudentController::class, 'getAcademiesByBranch'])->name('students.academies-by-branch');
    });

    // Test route for debugging authorization
    Route::get('test-auth', function () {
        $user = Auth::user();
        if (!$user) {
            return 'Not logged in';
        }

        $canViewAny = $user->can('viewAny', App\Models\Student::class);
        $canCreate = $user->can('create', App\Models\Student::class);

        return [
            'user' => $user->only(['name', 'email', 'role', 'branch_id', 'academy_id']),
            'can_view_any' => $canViewAny,
            'can_create' => $canCreate,
        ];
    })->middleware('auth');

    // Student API endpoints (for AJAX requests)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('students', [StudentController::class, 'apiIndex'])->name('students.index');
        Route::get('students/statistics', [StudentController::class, 'getStatistics'])->name('students.statistics');
    });

    // Payment Management (Admin, Branch Manager, and Academy Manager)
    Route::middleware('role:admin,branch_manager,academy_manager')->group(function () {
        // Custom routes must come before resource routes to avoid conflicts
        Route::get('payments/academies-by-branch', [PaymentController::class, 'getAcademiesByBranch'])->name('payments.academies-by-branch');
        Route::get('payments/students-by-academy', [PaymentController::class, 'getStudentsByAcademy'])->name('payments.students-by-academy');
        Route::get('payments/export/excel', [PaymentController::class, 'exportExcel'])->name('payments.export.excel');
        Route::get('payments/export/pdf', [PaymentController::class, 'exportPdf'])->name('payments.export.pdf');
        Route::get('payments/{payment}/invoice', [PaymentController::class, 'invoice'])->name('payments.invoice');


        // Resource routes
        Route::resource('payments', PaymentController::class);
        Route::post('payments/{payment}/toggle-status', [PaymentController::class, 'toggleStatus'])->name('payments.toggle-status');
        Route::post('payments/bulk-action', [PaymentController::class, 'bulkAction'])->name('payments.bulk-action');
    });

    // Payment API endpoints (for AJAX requests)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('payments', [PaymentController::class, 'apiIndex'])->name('payments.index');
        Route::get('payments/statistics', [PaymentController::class, 'getStatistics'])->name('payments.statistics');
    });

    // Uniform Management (Admin, Branch Manager, and Academy Manager)
    Route::middleware('role:admin,branch_manager,academy_manager')->group(function () {
        Route::resource('uniforms', UniformController::class);
        Route::post('uniforms/{uniform}/toggle-status', [UniformController::class, 'toggleStatus'])->name('uniforms.toggle-status');
        Route::post('uniforms/bulk-action', [UniformController::class, 'bulkAction'])->name('uniforms.bulk-action');
        Route::get('uniforms/export/excel', [UniformController::class, 'exportExcel'])->name('uniforms.export.excel');
        Route::get('uniforms/export/pdf', [UniformController::class, 'exportPdf'])->name('uniforms.export.pdf');
    });

    // Uniform API endpoints (for AJAX requests)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('uniforms', [UniformController::class, 'apiIndex'])->name('uniforms.index');
        Route::get('uniforms/statistics', [UniformController::class, 'getStatistics'])->name('uniforms.statistics');
    });

    // Uniform Inventory Management (Admin, Branch Manager, and Academy Manager)
    Route::middleware('role:admin,branch_manager,academy_manager')->group(function () {
        // Inventory Management
        Route::resource('inventory', UniformInventoryController::class);
        Route::get('inventory/{inventory}/stock-adjustment', [UniformInventoryController::class, 'showStockAdjustment'])->name('inventory.stock-adjustment');
        Route::post('inventory/{inventory}/adjust-stock', [UniformInventoryController::class, 'adjustStock'])->name('inventory.adjust-stock');
        Route::get('inventory/{inventory}/stock-reservation', [UniformInventoryController::class, 'showStockReservation'])->name('inventory.stock-reservation');
        Route::post('inventory/{inventory}/reserve-stock', [UniformInventoryController::class, 'reserveStock'])->name('inventory.reserve-stock');
        Route::delete('inventory/{inventory}/reservations/{reservation}', [UniformInventoryController::class, 'releaseReservation'])->name('inventory.release-reservation');
        Route::get('inventory/export/excel', [UniformInventoryController::class, 'exportExcel'])->name('inventory.export.excel');
        Route::get('inventory/export/pdf', [UniformInventoryController::class, 'exportPdf'])->name('inventory.export.pdf');
    });

    // Reports
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/financial', [ReportController::class, 'financial'])->name('financial');
        Route::get('/uniform', [ReportController::class, 'uniform'])->name('uniform');
        Route::get('/program', [ReportController::class, 'program'])->name('program');
        Route::get('/status', [ReportController::class, 'status'])->name('status');
        Route::get('/daily', [ReportController::class, 'daily'])->name('daily');
        Route::get('/reservations', [ReportController::class, 'reservations'])->name('reservations');
    });

    // Reservation Management (Admin, Branch Manager, and Academy Manager)
    Route::middleware('role:admin,branch_manager,academy_manager')->group(function () {
        // Venue Management
        Route::resource('venues', VenueController::class);
        Route::post('venues/{venue}/toggle-status', [VenueController::class, 'toggleStatus'])->name('venues.toggle-status');
        Route::get('venues/{venue}/statistics', [VenueController::class, 'getStatistics'])->name('venues.statistics');
        Route::get('venues/export/excel', [VenueController::class, 'exportExcel'])->name('venues.export.excel');
        Route::get('venues/export/pdf', [VenueController::class, 'exportPdf'])->name('venues.export.pdf');

        // Field Management
        Route::resource('fields', FieldController::class);
        Route::post('fields/{field}/toggle-status', [FieldController::class, 'toggleStatus'])->name('fields.toggle-status');
        Route::get('fields/{field}/availability', [FieldController::class, 'getAvailability'])->name('fields.availability');
        Route::get('fields/{field}/statistics', [FieldController::class, 'getStatistics'])->name('fields.statistics');
        Route::get('fields/export/excel', [FieldController::class, 'exportExcel'])->name('fields.export.excel');
        Route::get('fields/export/pdf', [FieldController::class, 'exportPdf'])->name('fields.export.pdf');

        // Customer Management
        Route::resource('customers', CustomerController::class);
        Route::post('customers/{customer}/toggle-status', [CustomerController::class, 'toggleStatus'])->name('customers.toggle-status');
        Route::post('customers/{customer}/block', [CustomerController::class, 'block'])->name('customers.block');
        Route::post('customers/{customer}/unblock', [CustomerController::class, 'unblock'])->name('customers.unblock');
        Route::post('customers/bulk-action', [CustomerController::class, 'bulkAction'])->name('customers.bulk-action');
        Route::get('customers/{customer}/statistics', [CustomerController::class, 'getStatistics'])->name('customers.statistics');
        Route::get('customers/export/excel', [CustomerController::class, 'exportExcel'])->name('customers.export.excel');
        Route::get('customers/export/pdf', [CustomerController::class, 'exportPdf'])->name('customers.export.pdf');

        // Reservation Management
        Route::resource('reservations', ReservationController::class);
        Route::post('reservations/bulk-action', [ReservationController::class, 'bulkAction'])->name('reservations.bulk-action');
        Route::post('reservations/{reservation}/confirm', [ReservationController::class, 'confirm'])->name('reservations.confirm');
        Route::post('reservations/{reservation}/cancel', [ReservationController::class, 'cancel'])->name('reservations.cancel');
        Route::post('reservations/{reservation}/complete', [ReservationController::class, 'complete'])->name('reservations.complete');
        Route::post('reservations/{reservation}/no-show', [ReservationController::class, 'markNoShow'])->name('reservations.no-show');
        Route::get('reservations/{reservation}/invoice', [ReservationController::class, 'invoice'])->name('reservations.invoice');
        Route::get('reservations/calendar/view', [ReservationController::class, 'calendar'])->name('reservations.calendar');
        Route::get('reservations/export/excel', [ReservationController::class, 'exportExcel'])->name('reservations.export.excel');
        Route::get('reservations/export/pdf', [ReservationController::class, 'exportPdf'])->name('reservations.export.pdf');

        // Reservation Payment Management
        Route::resource('reservation-payments', ReservationPaymentController::class)->names([
            'index' => 'reservation-payments.index',
            'create' => 'reservation-payments.create',
            'store' => 'reservation-payments.store',
            'show' => 'reservation-payments.show',
            'edit' => 'reservation-payments.edit',
            'update' => 'reservation-payments.update',
            'destroy' => 'reservation-payments.destroy',
        ]);
        Route::post('reservation-payments/{payment}/process', [ReservationPaymentController::class, 'process'])->name('reservation-payments.process');
        Route::post('reservation-payments/{payment}/fail', [ReservationPaymentController::class, 'fail'])->name('reservation-payments.fail');
        Route::post('reservation-payments/{payment}/refund', [ReservationPaymentController::class, 'refund'])->name('reservation-payments.refund');
        Route::get('reservation-payments/{payment}/receipt', [ReservationPaymentController::class, 'receipt'])->name('reservation-payments.receipt');
        Route::post('reservation-payments/{payment}/email-receipt', [ReservationPaymentController::class, 'emailReceipt'])->name('reservation-payments.email-receipt');
        Route::get('reservation-payments/export/excel', [ReservationPaymentController::class, 'exportExcel'])->name('reservation-payments.export.excel');
        Route::get('reservation-payments/export/pdf', [ReservationPaymentController::class, 'exportPdf'])->name('reservation-payments.export.pdf');
    });

    // Reservation API endpoints (for AJAX requests)
    Route::prefix('api')->name('api.')->group(function () {
        Route::get('venues', [VenueController::class, 'apiIndex'])->name('venues.index');
        Route::get('venues/{venue}/fields', [VenueController::class, 'getFields'])->name('venues.fields');
        Route::get('fields/{field}/availability', [FieldController::class, 'apiAvailability'])->name('fields.availability');
        Route::get('customers', [CustomerController::class, 'apiIndex'])->name('customers.index');
        Route::get('reservations', [ReservationController::class, 'apiIndex'])->name('reservations.index');
        Route::get('reservations/calendar', [ReservationController::class, 'apiCalendar'])->name('reservations.calendar');
        Route::get('reservation-payments', [ReservationPaymentController::class, 'apiIndex'])->name('reservation-payments.index');
        Route::get('reservation-statistics', [ReservationController::class, 'getStatistics'])->name('reservations.statistics');
    });
});

// Test route for password component (remove in production)
Route::get('/test-password-component', function () {
    return view('test-password-component');
})->name('test.password.component');

require __DIR__ . '/auth.php';
