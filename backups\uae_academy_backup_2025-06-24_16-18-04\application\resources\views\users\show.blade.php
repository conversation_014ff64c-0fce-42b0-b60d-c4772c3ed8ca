@extends('layouts.dashboard')

@section('title', 'User Details - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-info-blue to-blue-600 rounded-full flex items-center justify-center">
                <span class="text-white font-bold text-xl">
                    {{ strtoupper(substr($user->name, 0, 2)) }}
                </span>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $user->name }}</h1>
                <p class="text-lg text-dark-gray">{{ $user->email }}</p>
                <div class="flex items-center space-x-3 mt-2">
                    <span class="badge-bank {{ $user->role_badge_class }}">
                        {{ $user->role_text }}
                    </span>
                    <span class="badge-bank {{ $user->status_badge_class }}">
                        {{ $user->status_text }}
                    </span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('update', $user)
                <a href="{{ route('users.edit', $user) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit User
                </a>
            @endcan
            <a href="{{ route('users.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Users
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- User Information Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Basic Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">Full Name</span>
                            <span class="text-sm text-charcoal-black">{{ $user->name }}</span>
                        </div>
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">Email Address</span>
                            <span class="text-sm text-charcoal-black">{{ $user->email }}</span>
                        </div>
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">User ID</span>
                            <span class="text-sm text-charcoal-black">#{{ $user->id }}</span>
                        </div>
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">Account Status</span>
                            <span class="badge-bank {{ $user->status_badge_class }}">
                                {{ $user->status_text }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between py-3">
                            <span class="text-sm font-medium text-dark-gray">Email Verified</span>
                            @if($user->email_verified_at)
                                <span class="badge-bank badge-success">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Verified
                                </span>
                            @else
                                <span class="badge-bank badge-warning">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                                        </path>
                                    </svg>
                                    Not Verified
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role & Access Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Role & Access</h3>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">User Role</span>
                            <span class="badge-bank {{ $user->role_badge_class }}">
                                {{ $user->role_text }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">Assigned Branch</span>
                            @if($user->branch)
                                <span class="text-sm text-charcoal-black">{{ $user->branch->name }}</span>
                            @else
                                <span class="text-sm text-medium-gray">Not assigned</span>
                            @endif
                        </div>
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">Assigned Academy</span>
                            @if($user->academy)
                                <span class="text-sm text-charcoal-black">{{ $user->academy->name }}</span>
                            @else
                                <span class="text-sm text-medium-gray">Not assigned</span>
                            @endif
                        </div>
                        <div class="flex items-center justify-between py-3">
                            <span class="text-sm font-medium text-dark-gray">Access Level</span>
                            <span class="text-sm text-charcoal-black">
                                @switch($user->role)
                                    @case('admin')
                                        Full System Access
                                        @break
                                    @case('branch_manager')
                                        Branch Level Access
                                        @break
                                    @case('academy_manager')
                                        Academy Level Access
                                        @break
                                    @default
                                        Limited Access
                                @endswitch
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Activity Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Login Activity -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Login Activity</h3>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">Last Login</span>
                            <span class="text-sm text-charcoal-black">{{ $user->last_login_text }}</span>
                        </div>
                        <div class="flex items-center justify-between py-3 border-b border-light-gray">
                            <span class="text-sm font-medium text-dark-gray">Account Created</span>
                            <span class="text-sm text-charcoal-black">{{ $user->formatted_created_at }}</span>
                        </div>
                        <div class="flex items-center justify-between py-3">
                            <span class="text-sm font-medium text-dark-gray">Last Updated</span>
                            <span class="text-sm text-charcoal-black">{{ $user->updated_at->format('d/m/Y H:i') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Quick Actions</h3>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-3">
                        @can('update', $user)
                            <a href="{{ route('users.edit', $user) }}" class="btn-bank btn-bank-outline w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                                Edit User Information
                            </a>
                        @endcan

                        @can('toggleStatus', $user)
                            <button onclick="toggleUserStatus({{ $user->id }})" 
                                class="btn-bank {{ $user->status ? 'btn-bank-warning' : 'btn-bank-success' }} w-full">
                                @if($user->status)
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728">
                                        </path>
                                    </svg>
                                    Deactivate Account
                                @else
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Activate Account
                                @endif
                            </button>
                        @endcan

                        @can('delete', $user)
                            <button onclick="deleteUser({{ $user->id }})" class="btn-bank btn-bank-danger w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                    </path>
                                </svg>
                                Delete User
                            </button>
                        @endcan
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Description -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="bank-card-title">Role Permissions</h3>
            </div>
            <div class="bank-card-body">
                @switch($user->role)
                    @case('admin')
                        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
                            <h4 class="font-semibold text-red-800 mb-2">System Administrator</h4>
                            <p class="text-red-700 text-sm mb-3">This user has full access to all system features and data.</p>
                            <ul class="text-red-700 text-sm space-y-1">
                                <li>• Manage all users and system settings</li>
                                <li>• Access all branches and academies</li>
                                <li>• View and modify all data</li>
                                <li>• Export and generate reports</li>
                                <li>• Perform bulk operations</li>
                            </ul>
                        </div>
                        @break
                    @case('branch_manager')
                        <div class="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                            <h4 class="font-semibold text-orange-800 mb-2">Branch Manager</h4>
                            <p class="text-orange-700 text-sm mb-3">This user can manage operations within assigned branches.</p>
                            <ul class="text-orange-700 text-sm space-y-1">
                                <li>• Manage academies within assigned branches</li>
                                <li>• Oversee programs, students, and payments</li>
                                <li>• Generate branch-level reports</li>
                                <li>• Manage academy managers</li>
                                <li>• Access branch statistics and analytics</li>
                            </ul>
                        </div>
                        @break
                    @case('academy_manager')
                        <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                            <h4 class="font-semibold text-blue-800 mb-2">Academy Manager</h4>
                            <p class="text-blue-700 text-sm mb-3">This user can manage operations within assigned academy only.</p>
                            <ul class="text-blue-700 text-sm space-y-1">
                                <li>• Manage programs within assigned academy</li>
                                <li>• Handle student enrollment and information</li>
                                <li>• Process payments and uniform orders</li>
                                <li>• Generate academy-level reports</li>
                                <li>• Track attendance and performance</li>
                            </ul>
                        </div>
                        @break
                @endswitch
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    async function toggleUserStatus(userId) {
        const action = {{ $user->status ? 'false' : 'true' }} ? 'activate' : 'deactivate';
        
        if (!confirm(`Are you sure you want to ${action} this user account?`)) {
            return;
        }

        try {
            const response = await fetch(`/users/${userId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while updating user status.');
        }
    }

    async function deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/users/${userId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            if (response.ok) {
                showNotification('success', 'User deleted successfully.');
                window.location.href = '{{ route('users.index') }}';
            } else {
                showNotification('error', 'Failed to delete user.');
            }
        } catch (error) {
            showNotification('error', 'An error occurred while deleting the user.');
        }
    }

    function showNotification(type, message) {
        // This will be implemented with the notification system
        alert(message);
    }
</script>
@endpush
