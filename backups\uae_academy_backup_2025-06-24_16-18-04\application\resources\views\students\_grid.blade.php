<!-- Grid View -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
    @forelse($students as $student)
        <div class="bank-card bank-card-hover relative">
            @can('bulkAction', App\Models\Student::class)
                <div class="absolute top-4 left-4 z-10">
                    <input type="checkbox" name="student_ids[]" value="{{ $student->id }}"
                        @change="toggleStudentSelection({{ $student->id }})"
                        :checked="selectedStudents.includes({{ $student->id }})"
                        class="form-checkbox-bank">
                </div>
            @endcan

            <div class="bank-card-body">
                <!-- Student Avatar and Basic Info -->
                <div class="text-center mb-4">
                    <div class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-bold text-xl mx-auto mb-3">
                        {{ substr($student->full_name, 0, 1) }}
                    </div>
                    <h3 class="text-lg font-semibold text-charcoal-black mb-1">
                        <a href="{{ route('students.show', $student) }}" class="hover:text-leaders-red transition-colors">
                            {{ $student->full_name }}
                        </a>
                    </h3>
                    <p class="text-sm text-dark-gray">ID: {{ $student->id }}</p>
                    @if($student->age)
                        <p class="text-sm text-dark-gray">Age: {{ $student->age }} years</p>
                    @endif
                </div>

                <!-- Status Badge -->
                <div class="text-center mb-4">
                    <span class="badge-bank {{ $student->status_badge_class }}">
                        {{ $student->status_text }}
                    </span>
                </div>

                <!-- Contact Information -->
                <div class="space-y-2 mb-4">
                    @if($student->email)
                        <div class="flex items-center text-sm text-dark-gray">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span class="truncate">{{ $student->email }}</span>
                        </div>
                    @endif
                    <div class="flex items-center text-sm text-dark-gray">
                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                        </svg>
                        <span>{{ $student->formatted_phone }}</span>
                    </div>
                    @if($student->nationality)
                        <div class="flex items-center text-sm text-dark-gray">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ $student->nationality }}</span>
                        </div>
                    @endif
                </div>

                <!-- Academy Information -->
                <div class="border-t border-light-gray pt-4 mb-4">
                    <div class="text-sm">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-dark-gray">Academy:</span>
                            <span class="font-medium text-charcoal-black">{{ $student->academy->name ?? 'N/A' }}</span>
                        </div>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-dark-gray">Branch:</span>
                            <span class="font-medium text-charcoal-black">{{ $student->branch->name ?? 'N/A' }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-dark-gray">Joined:</span>
                            <span class="text-charcoal-black">{{ $student->formatted_join_date }}</span>
                        </div>
                    </div>
                </div>

                <!-- Financial Summary -->
                <div class="border-t border-light-gray pt-4 mb-4">
                    <div class="text-sm">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-dark-gray">Total Payments:</span>
                            <span class="font-medium text-success-green">AED {{ number_format($student->total_payments, 2) }}</span>
                        </div>
                        @if($student->pending_payments > 0)
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-dark-gray">Pending:</span>
                                <span class="font-medium text-warning-orange">AED {{ number_format($student->pending_payments, 2) }}</span>
                            </div>
                        @endif
                        <div class="flex items-center justify-between">
                            <span class="text-dark-gray">Payments:</span>
                            <span class="text-charcoal-black">{{ $student->payments_count }}</span>
                        </div>
                    </div>
                </div>

                <!-- Activity Summary -->
                <div class="border-t border-light-gray pt-4 mb-4">
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-lg font-bold text-blue-600">{{ $student->attendance_rate }}%</div>
                            <div class="text-xs text-dark-gray">Attendance</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-purple-600">{{ $student->uniform_orders_count }}</div>
                            <div class="text-xs text-dark-gray">Uniforms</div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-center space-x-2">
                    @can('view', $student)
                        <a href="{{ route('students.show', $student) }}" 
                           class="btn-bank btn-bank-sm btn-bank-outline flex-1 text-center">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            View
                        </a>
                    @endcan
                    
                    @can('update', $student)
                        <a href="{{ route('students.edit', $student) }}" 
                           class="btn-bank btn-bank-sm flex-1 text-center">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit
                        </a>
                    @endcan
                </div>

                <!-- Additional Actions Dropdown -->
                @if(auth()->user()->can('update', $student) || auth()->user()->can('delete', $student))
                    <div class="mt-3 relative" x-data="{ open: false }">
                        <button @click="open = !open" class="w-full btn-bank btn-bank-outline btn-bank-sm">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                            </svg>
                            More Actions
                        </button>
                        
                        <div x-show="open" @click.away="open = false" x-transition
                             class="absolute bottom-full left-0 right-0 mb-2 bg-white rounded-lg shadow-lg border border-light-gray py-2 z-20">
                            @can('update', $student)
                                <button @click="toggleStatus({{ $student->id }}); open = false" 
                                        class="w-full px-4 py-2 text-left text-sm text-dark-gray hover:bg-off-white transition-colors">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                    </svg>
                                    Toggle Status
                                </button>
                            @endcan
                            
                            @can('delete', $student)
                                <button @click="deleteStudent({{ $student->id }}); open = false" 
                                        class="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 transition-colors">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                    Delete Student
                                </button>
                            @endcan
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @empty
        <div class="col-span-full">
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                    </path>
                </svg>
                <h3 class="text-xl font-medium text-dark-gray mb-2">No students found</h3>
                <p class="text-gray-500 mb-6">No students match your current filters.</p>
                @can('create', App\Models\Student::class)
                    <a href="{{ route('students.create') }}" class="btn-bank">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add First Student
                    </a>
                @endcan
            </div>
        </div>
    @endforelse
</div>
