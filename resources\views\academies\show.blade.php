@extends('layouts.dashboard')

@section('title', 'Academy Details - LEADERS SPORTS SERVICES')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $academy->name }}</h1>
                <p class="text-lg text-dark-gray">Academy Details & Management</p>
                <div class="flex items-center space-x-3 mt-2">
                    <span class="badge-bank badge-info">ID: #{{ $academy->id }}</span>
                    <span class="badge-bank {{ $academy->status ? 'badge-success' : 'badge-neutral' }}">
                        {{ $academy->status ? 'Active' : 'Inactive' }}
                    </span>
                    <span class="badge-bank badge-secondary">{{ $academy->branch->name ?? 'No Branch' }}</span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('create', App\Models\Program::class)
                <a href="{{ route('programs.create', ['academy_id' => $academy->id, 'branch_id' => $academy->branch_id]) }}"
                    class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                        </path>
                    </svg>
                    Add Program
                </a>
            @endcan
            @can('update', $academy)
                <a href="{{ route('academies.edit', $academy) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Academy
                </a>
            @endcan
            <a href="{{ route('academies.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Academies
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="academyDetails()" x-init="init()">
        <!-- Academy Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <!-- Total Programs -->
            <div class="bank-card bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-700 uppercase tracking-wide">Available Programs</p>
                        <p class="text-3xl font-bold text-blue-900">{{ $stats['total_programs'] ?? 0 }}</p>
                        <p class="text-sm text-blue-600 mt-1">{{ $stats['active_programs'] ?? 0 }} active</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Total Students -->
            <div class="bank-card bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-700 uppercase tracking-wide">Total Students</p>
                        <p class="text-3xl font-bold text-green-900">{{ $stats['total_students'] ?? 0 }}</p>
                        <p class="text-sm text-green-600 mt-1">{{ $stats['active_students'] ?? 0 }} active</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="bank-card bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-emerald-700 uppercase tracking-wide">Total Revenue</p>
                        <p class="text-2xl font-bold text-emerald-900">AED
                            {{ number_format($stats['total_revenue'] ?? 0, 2) }}</p>
                        <p class="text-sm text-emerald-600 mt-1">Completed payments</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Pending Payments -->
            <div class="bank-card bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-amber-700 uppercase tracking-wide">Pending Payments</p>
                        <p class="text-2xl font-bold text-amber-900">AED
                            {{ number_format($stats['pending_payments'] ?? 0, 2) }}</p>
                        <p class="text-sm text-amber-600 mt-1">Awaiting collection</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Academy Information -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Basic Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Academy Information</h3>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div>
                            <label class="text-sm font-medium text-dark-gray">Academy Name</label>
                            <p class="text-lg font-semibold text-charcoal-black">{{ $academy->name }}</p>
                        </div>

                        @if ($academy->description)
                            <div>
                                <label class="text-sm font-medium text-dark-gray">Description</label>
                                <p class="text-sm text-charcoal-black">{{ $academy->description }}</p>
                            </div>
                        @endif

                        <div>
                            <label class="text-sm font-medium text-dark-gray">Branch</label>
                            <p class="text-sm text-charcoal-black">{{ $academy->branch->name ?? 'No branch assigned' }}
                            </p>
                            @if ($academy->branch)
                                <p class="text-xs text-medium-gray">{{ $academy->branch->location }}</p>
                            @endif
                        </div>

                        <div>
                            <label class="text-sm font-medium text-dark-gray">Status</label>
                            <span class="badge-bank {{ $academy->status ? 'badge-success' : 'badge-neutral' }}">
                                {{ $academy->status ? 'Active' : 'Inactive' }}
                            </span>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-dark-gray">Created</label>
                            <p class="text-sm text-charcoal-black">{{ $academy->created_at->format('M d, Y H:i') }}</p>
                        </div>

                        <div>
                            <label class="text-sm font-medium text-dark-gray">Last Updated</label>
                            <p class="text-sm text-charcoal-black">{{ $academy->updated_at->format('M d, Y H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coach Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Coach Information</h3>
                </div>
                <div class="bank-card-body">
                    @if ($academy->coach_name)
                        <div class="space-y-4">
                            <div class="flex items-center space-x-4">
                                <div
                                    class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">{{ $academy->coach_name }}</h4>
                                    <p class="text-sm text-dark-gray">Head Coach</p>
                                </div>
                            </div>

                            @if ($academy->formatted_coach_phone)
                                <div>
                                    <label class="text-sm font-medium text-dark-gray">Phone Number</label>
                                    <p class="text-sm text-charcoal-black">{{ $academy->formatted_coach_phone }}</p>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 text-medium-gray mx-auto mb-3" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <p class="text-sm text-medium-gray">No coach assigned</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Quick Statistics</h3>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-dark-gray">Average Student Age</span>
                            <span class="font-semibold text-charcoal-black">
                                {{ $stats['average_age'] ? round($stats['average_age']) . ' years' : 'N/A' }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-dark-gray">Total Uniforms</span>
                            <span class="font-semibold text-charcoal-black">{{ $stats['total_uniforms'] ?? 0 }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-dark-gray">Pending Uniforms</span>
                            <span class="font-semibold text-charcoal-black">{{ $stats['pending_uniforms'] ?? 0 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Available Programs -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="bank-card-title">Available Programs</h3>
                        <p class="bank-card-subtitle">Programs offered by this academy</p>
                    </div>
                    <span class="badge-bank badge-info">{{ count($programs) }} Programs</span>
                </div>
            </div>
            <div class="bank-card-body p-0">
                @if (count($programs) > 0)
                    <div class="overflow-x-auto">
                        <table class="table-bank">
                            <thead>
                                <tr>
                                    <th>Program Name</th>
                                    <th>Days</th>
                                    <th>Time</th>
                                    <th>Price (AED)</th>
                                    <th>Max Students</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($programs as $program)
                                    <tr class="table-row-bank">
                                        <td>
                                            <div class="font-semibold text-charcoal-black">{{ $program->name }}</div>
                                            <div class="text-sm text-dark-gray">{{ $program->classes }} classes</div>
                                        </td>
                                        <td>
                                            <span
                                                class="badge-bank badge-secondary">{{ $program->formatted_days ?? 'N/A' }}</span>
                                        </td>
                                        <td class="text-sm text-charcoal-black">
                                            {{ $program->start_time ? \Carbon\Carbon::parse($program->start_time)->format('H:i') : 'N/A' }}
                                            -
                                            {{ $program->end_time ? \Carbon\Carbon::parse($program->end_time)->format('H:i') : 'N/A' }}
                                        </td>
                                        <td class="font-semibold text-success-green">
                                            {{ number_format($program->price, 2) }}</td>
                                        <td class="text-center">{{ $program->max_students ?? 'Unlimited' }}</td>
                                        <td>
                                            <span
                                                class="badge-bank {{ $program->status ? 'badge-success' : 'badge-neutral' }}">
                                                {{ $program->status ? 'Active' : 'Inactive' }}
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-medium-gray mx-auto mb-4" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                            </path>
                        </svg>
                        <h3 class="text-lg font-medium text-dark-gray mb-2">No programs available</h3>
                        <p class="text-sm text-medium-gray">This academy doesn't have any programs yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Student Lists -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="bank-card-title">Student Lists</h3>
                        <p class="bank-card-subtitle">Students enrolled in this academy with detailed information</p>
                    </div>
                    <span class="badge-bank badge-success">{{ count($students) }} Students</span>
                </div>
            </div>
            <div class="bank-card-body p-0">
                @if (count($students) > 0)
                    <!-- Desktop Table View -->
                    <div class="hidden lg:block overflow-x-auto">
                        <table class="table-bank">
                            <thead>
                                <tr>
                                    <th>Student Name</th>
                                    <th>Birth Date / Age</th>
                                    <th>UAE Phone</th>
                                    <th>Registration Date</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Amount (AED)</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($students as $student)
                                    <tr class="table-row-bank">
                                        <td>
                                            <div class="flex items-center space-x-3">
                                                <div
                                                    class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                                                    <span class="text-white font-semibold text-sm">
                                                        {{ strtoupper(substr($student->full_name, 0, 2)) }}
                                                    </span>
                                                </div>
                                                <div>
                                                    <div class="font-semibold text-charcoal-black">
                                                        {{ $student->full_name }}</div>
                                                    @if ($student->email)
                                                        <div class="text-sm text-dark-gray">{{ $student->email }}</div>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if ($student->birth_date)
                                                <div class="text-sm text-charcoal-black">
                                                    {{ $student->birth_date->format('M d, Y') }}</div>
                                                <div class="text-xs text-dark-gray">{{ $student->age ?? 'N/A' }} years old
                                                </div>
                                            @else
                                                <span class="text-medium-gray">Not provided</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if ($student->phone)
                                                <div class="text-sm text-charcoal-black">{{ $student->phone }}</div>
                                            @else
                                                <span class="text-medium-gray">Not provided</span>
                                            @endif
                                        </td>
                                        <td class="text-sm text-charcoal-black">
                                            {{ $student->join_date ? $student->join_date->format('M d, Y') : 'N/A' }}
                                        </td>
                                        <td class="text-sm text-charcoal-black">
                                            {{ $student->join_date ? $student->join_date->format('M d, Y') : 'N/A' }}
                                        </td>
                                        <td class="text-sm text-charcoal-black">
                                            <!-- Calculate end date based on program duration or set manually -->
                                            {{ $student->join_date ? $student->join_date->addMonths(3)->format('M d, Y') : 'N/A' }}
                                        </td>
                                        <td>
                                            @php
                                                $totalAmount = $student->payments
                                                    ->where('status', 'completed')
                                                    ->sum('amount');
                                                $pendingAmount = $student->payments
                                                    ->where('status', 'pending')
                                                    ->sum('amount');
                                            @endphp
                                            <div class="text-right">
                                                <div class="font-semibold text-success-green">
                                                    {{ number_format($totalAmount, 2) }}</div>
                                                @if ($pendingAmount > 0)
                                                    <div class="text-xs text-warning-orange">
                                                        +{{ number_format($pendingAmount, 2) }} pending</div>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span
                                                class="badge-bank {{ $student->status === 'active' ? 'badge-success' : ($student->status === 'inactive' ? 'badge-neutral' : 'badge-warning') }}">
                                                {{ ucfirst($student->status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ route('students.show', $student) }}"
                                                    class="btn-action btn-action-view" title="View Student">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                        </path>
                                                    </svg>
                                                </a>
                                                <a href="{{ route('students.edit', $student) }}"
                                                    class="btn-action btn-action-edit" title="Edit Student">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                        </path>
                                                    </svg>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View -->
                    <div class="lg:hidden space-y-4 p-4">
                        @foreach ($students as $student)
                            <div class="bg-white border border-medium-gray rounded-xl shadow-sm overflow-hidden">
                                <!-- Card Header -->
                                <div class="p-4 border-b border-light-gray">
                                    <div class="flex items-start justify-between">
                                        <div class="flex items-center space-x-3">
                                            <div
                                                class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                                                <span class="text-white font-semibold">
                                                    {{ strtoupper(substr($student->full_name, 0, 2)) }}
                                                </span>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-charcoal-black">{{ $student->full_name }}
                                                </h4>
                                                @if ($student->email)
                                                    <p class="text-sm text-dark-gray">{{ $student->email }}</p>
                                                @endif
                                            </div>
                                        </div>
                                        <span
                                            class="badge-bank {{ $student->status === 'active' ? 'badge-success' : ($student->status === 'inactive' ? 'badge-neutral' : 'badge-warning') }}">
                                            {{ ucfirst($student->status) }}
                                        </span>
                                    </div>
                                </div>

                                <!-- Card Body -->
                                <div class="p-4 space-y-3">
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <span class="text-sm font-medium text-dark-gray">Birth Date</span>
                                            <p class="text-sm text-charcoal-black">
                                                {{ $student->birth_date ? $student->birth_date->format('M d, Y') : 'Not provided' }}
                                            </p>
                                            @if ($student->age)
                                                <p class="text-xs text-dark-gray">{{ $student->age }} years old</p>
                                            @endif
                                        </div>
                                        <div>
                                            <span class="text-sm font-medium text-dark-gray">Phone</span>
                                            <p class="text-sm text-charcoal-black">{{ $student->phone ?? 'Not provided' }}
                                            </p>
                                        </div>
                                        <div>
                                            <span class="text-sm font-medium text-dark-gray">Registration</span>
                                            <p class="text-sm text-charcoal-black">
                                                {{ $student->join_date ? $student->join_date->format('M d, Y') : 'N/A' }}
                                            </p>
                                        </div>
                                        <div>
                                            <span class="text-sm font-medium text-dark-gray">Days Enrolled</span>
                                            <p class="text-sm text-charcoal-black">{{ $student->days_enrolled ?? 0 }} days
                                            </p>
                                        </div>
                                    </div>

                                    <div>
                                        <span class="text-sm font-medium text-dark-gray">Total Amount</span>
                                        @php
                                            $totalAmount = $student->payments
                                                ->where('status', 'completed')
                                                ->sum('amount');
                                            $pendingAmount = $student->payments
                                                ->where('status', 'pending')
                                                ->sum('amount');
                                        @endphp
                                        <p class="text-lg font-semibold text-success-green">AED
                                            {{ number_format($totalAmount, 2) }}</p>
                                        @if ($pendingAmount > 0)
                                            <p class="text-sm text-warning-orange">+AED
                                                {{ number_format($pendingAmount, 2) }} pending</p>
                                        @endif
                                    </div>
                                </div>

                                <!-- Card Footer -->
                                <div class="px-4 py-3 bg-gray-50 border-t border-gray-100">
                                    <div class="flex items-center justify-between">
                                        <span class="text-xs text-medium-gray">
                                            Enrolled {{ $student->days_enrolled ?? 0 }} days ago
                                        </span>
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ route('students.show', $student) }}"
                                                class="btn-action btn-action-view">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                    </path>
                                                </svg>
                                            </a>
                                            <a href="{{ route('students.edit', $student) }}"
                                                class="btn-action btn-action-edit">
                                                <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                    </path>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="w-16 h-16 text-medium-gray mx-auto mb-4" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                        <h3 class="text-lg font-medium text-dark-gray mb-2">No students enrolled</h3>
                        <p class="text-sm text-medium-gray">This academy doesn't have any students yet.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function academyDetails() {
            return {
                init() {
                    // Initialize any interactive features
                }
            }
        }
    </script>
@endpush
