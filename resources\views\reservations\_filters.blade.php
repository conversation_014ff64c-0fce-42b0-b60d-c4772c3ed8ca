<div class="bank-card">
    <div class="bank-card-header">
        <h3 class="bank-card-title">Search & Filters</h3>
        <button @click="showFilters = !showFilters" class="btn-bank btn-bank-sm">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                </path>
            </svg>
            <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
        </button>
    </div>

    <div class="bank-card-body" x-show="showFilters" x-transition x-data="{ showFilters: true }">
        <form method="GET" action="{{ route('reservations.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="form-label-bank">Search</label>
                    <input type="text" id="search" name="search" value="{{ request('search') }}"
                        placeholder="Search reservations..." class="form-input-bank">
                </div>

                <!-- Venue Filter -->
                <div>
                    <label for="venue_id" class="form-label-bank">Venue</label>
                    <select id="venue_id" name="venue_id" class="form-select-bank">
                        <option value="">All Venues</option>
                        @foreach ($venues as $venue)
                            <option value="{{ $venue->id }}"
                                {{ request('venue_id') == $venue->id ? 'selected' : '' }}>
                                {{ $venue->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Field Filter -->
                <div>
                    <label for="field_id" class="form-label-bank">Field</label>
                    <select id="field_id" name="field_id" class="form-select-bank">
                        <option value="">All Fields</option>
                        @foreach ($fields as $field)
                            <option value="{{ $field->id }}"
                                {{ request('field_id') == $field->id ? 'selected' : '' }}>
                                {{ $field->name }} ({{ $field->venue->name }})
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="form-label-bank">Status</label>
                    <select id="status" name="status" class="form-select-bank">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Confirmed
                        </option>
                        <option value="in_progress" {{ request('status') == 'in_progress' ? 'selected' : '' }}>In
                            Progress</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Completed
                        </option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled
                        </option>
                        <option value="no_show" {{ request('status') == 'no_show' ? 'selected' : '' }}>No Show</option>
                    </select>
                </div>

                <!-- Payment Status Filter -->
                <div>
                    <label for="payment_status" class="form-label-bank">Payment Status</label>
                    <select id="payment_status" name="payment_status" class="form-select-bank">
                        <option value="">All Payment Statuses</option>
                        <option value="unpaid" {{ request('payment_status') == 'unpaid' ? 'selected' : '' }}>Unpaid
                        </option>
                        <option value="partial" {{ request('payment_status') == 'partial' ? 'selected' : '' }}>Partial
                        </option>
                        <option value="paid" {{ request('payment_status') == 'paid' ? 'selected' : '' }}>Paid
                        </option>
                        <option value="refunded" {{ request('payment_status') == 'refunded' ? 'selected' : '' }}>
                            Refunded</option>
                    </select>
                </div>

                <!-- Date From -->
                <div>
                    <label for="date_from" class="form-label-bank">Date From</label>
                    <input type="date" id="date_from" name="date_from" value="{{ request('date_from') }}"
                        class="form-input-bank">
                </div>

                <!-- Date To -->
                <div>
                    <label for="date_to" class="form-label-bank">Date To</label>
                    <input type="date" id="date_to" name="date_to" value="{{ request('date_to') }}"
                        class="form-input-bank">
                </div>

                <!-- Sort By -->
                <div>
                    <label for="sort_by" class="form-label-bank">Sort By</label>
                    <select id="sort_by" name="sort_by" class="form-select-bank">
                        <option value="reservation_date"
                            {{ request('sort_by') == 'reservation_date' ? 'selected' : '' }}>Reservation Date</option>
                        <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Created
                            Date</option>
                        <option value="total_amount" {{ request('sort_by') == 'total_amount' ? 'selected' : '' }}>Total
                            Amount</option>
                        <option value="status" {{ request('sort_by') == 'status' ? 'selected' : '' }}>Status</option>
                    </select>
                </div>
            </div>

            <!-- Quick Filters -->
            <div class="flex flex-wrap gap-2 pt-4 border-t border-light-gray">
                <span class="text-sm font-medium text-dark-gray">Quick Filters:</span>
                <button type="button" onclick="setQuickFilter('today')"
                    class="btn-bank btn-bank-sm {{ request('today') ? 'bg-leaders-red text-white' : '' }}">
                    Today's Reservations
                </button>
                <button type="button" onclick="setQuickFilter('upcoming')"
                    class="btn-bank btn-bank-sm {{ request('upcoming') ? 'bg-leaders-red text-white' : '' }}">
                    Upcoming
                </button>
                <button type="button" onclick="setQuickFilter('pending')"
                    class="btn-bank btn-bank-sm {{ request('status') == 'pending' ? 'bg-leaders-red text-white' : '' }}">
                    Pending Approval
                </button>
                <button type="button" onclick="setQuickFilter('confirmed')"
                    class="btn-bank btn-bank-sm {{ request('status') == 'confirmed' ? 'bg-leaders-red text-white' : '' }}">
                    Confirmed
                </button>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-4 border-t border-light-gray">
                <div class="flex items-center space-x-2">
                    <button type="submit" class="btn-bank">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Apply Filters
                    </button>
                    <a href="{{ route('reservations.index') }}" class="btn-bank btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                            </path>
                        </svg>
                        Clear All
                    </a>
                </div>
                <div class="text-sm text-dark-gray">
                    {{ $reservations->total() }} reservation(s) found
                </div>
            </div>
        </form>
    </div>
</div>

<script>
    function setQuickFilter(filter) {
        const form = document.querySelector('form');
        const url = new URL(form.action);

        // Clear existing filters
        url.searchParams.delete('today');
        url.searchParams.delete('upcoming');
        url.searchParams.delete('status');

        // Set new filter
        if (filter === 'today') {
            url.searchParams.set('today', '1');
        } else if (filter === 'upcoming') {
            url.searchParams.set('upcoming', '1');
        } else if (filter === 'pending' || filter === 'confirmed') {
            url.searchParams.set('status', filter);
        }

        window.location.href = url.toString();
    }
</script>
