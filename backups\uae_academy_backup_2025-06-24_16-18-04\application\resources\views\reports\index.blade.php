@extends('layouts.dashboard')

@section('title', 'Reports Dashboard - UAE English Sports Academy')

@section('header')
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Reports Dashboard</h1>
            <p class="text-gray-600 mt-1">Comprehensive reporting and analytics for UAE English Sports Academy</p>
        </div>
        <div class="flex items-center gap-3">
            <div class="text-sm text-gray-500">
                <span class="font-medium">{{ ucfirst(Auth::user()->role) }}</span> • 
                <span>{{ Auth::user()->name }}</span>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <!-- Overview Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Revenue -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p class="text-2xl font-bold text-green-600">AED {{ number_format($stats['total_revenue'], 2) }}</p>
                </div>
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Pending Payments -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Pending Payments</p>
                    <p class="text-2xl font-bold text-orange-600">AED {{ number_format($stats['pending_payments'], 2) }}</p>
                </div>
                <div class="p-3 bg-orange-100 rounded-lg">
                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Total Students -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Students</p>
                    <p class="text-2xl font-bold text-blue-600">{{ number_format($stats['total_students']) }}</p>
                    <p class="text-xs text-gray-500">{{ number_format($stats['active_students']) }} active</p>
                </div>
                <div class="p-3 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Total Programs -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Total Programs</p>
                    <p class="text-2xl font-bold text-purple-600">{{ number_format($stats['total_programs']) }}</p>
                    <p class="text-xs text-gray-500">{{ number_format($stats['active_programs']) }} active</p>
                </div>
                <div class="p-3 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <!-- Financial Reports -->
        <div class="bank-card hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <a href="{{ route('reports.financial') }}" class="btn-bank-primary">
                    View Report
                </a>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Financial Reports</h3>
            <p class="text-gray-600 text-sm mb-4">Revenue analysis, payment tracking, and financial performance metrics in AED.</p>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Revenue:</span>
                    <span class="font-medium text-green-600">AED {{ number_format($stats['total_revenue'], 2) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Pending:</span>
                    <span class="font-medium text-orange-600">AED {{ number_format($stats['pending_payments'], 2) }}</span>
                </div>
            </div>
        </div>

        <!-- Uniform Reports -->
        <div class="bank-card hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
                <a href="{{ route('reports.uniform') }}" class="btn-bank-primary">
                    View Report
                </a>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Uniform Reports</h3>
            <p class="text-gray-600 text-sm mb-4">Order tracking, size distribution, and uniform status management.</p>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Orders:</span>
                    <span class="font-medium">{{ number_format($stats['total_uniforms']) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Pending:</span>
                    <span class="font-medium text-orange-600">{{ number_format($stats['pending_uniforms']) }}</span>
                </div>
            </div>
        </div>

        <!-- Program Reports -->
        <div class="bank-card hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-purple-100 rounded-lg">
                    <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <a href="{{ route('reports.program') }}" class="btn-bank-primary">
                    View Report
                </a>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Program Reports</h3>
            <p class="text-gray-600 text-sm mb-4">Enrollment statistics, program performance, and capacity analysis.</p>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Programs:</span>
                    <span class="font-medium">{{ number_format($stats['total_programs']) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Active:</span>
                    <span class="font-medium text-green-600">{{ number_format($stats['active_programs']) }}</span>
                </div>
            </div>
        </div>

        <!-- Status Reports -->
        <div class="bank-card hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-indigo-100 rounded-lg">
                    <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <a href="{{ route('reports.status') }}" class="btn-bank-primary">
                    View Report
                </a>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Status Reports</h3>
            <p class="text-gray-600 text-sm mb-4">System-wide status tracking for students, payments, and uniforms.</p>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Active Students:</span>
                    <span class="font-medium text-green-600">{{ number_format($stats['active_students']) }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Total Students:</span>
                    <span class="font-medium">{{ number_format($stats['total_students']) }}</span>
                </div>
            </div>
        </div>

        <!-- Daily Reports -->
        <div class="bank-card hover:shadow-lg transition-shadow duration-200">
            <div class="flex items-center justify-between mb-4">
                <div class="p-3 bg-yellow-100 rounded-lg">
                    <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <a href="{{ route('reports.daily') }}" class="btn-bank-primary">
                    View Report
                </a>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Daily Reports</h3>
            <p class="text-gray-600 text-sm mb-4">Daily activity summaries, registrations, and operational metrics.</p>
            <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                    <span class="text-gray-600">Today's Date:</span>
                    <span class="font-medium">{{ date('M d, Y') }}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600">Report Type:</span>
                    <span class="font-medium text-blue-600">Daily Summary</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bank-card">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Recent Activity</h2>
            <div class="text-sm text-gray-500">
                Last updated: {{ date('M d, Y H:i') }}
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Recent Payments -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Payments</h3>
                <div class="space-y-3">
                    @forelse($recentActivity['payments'] as $payment)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium text-sm">{{ $payment->student->full_name }}</p>
                                <p class="text-xs text-gray-600">{{ $payment->academy->name }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-medium text-green-600">AED {{ number_format($payment->amount, 2) }}</p>
                                <p class="text-xs text-gray-500">{{ $payment->payment_date->format('M d') }}</p>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-sm">No recent payments</p>
                    @endforelse
                </div>
            </div>

            <!-- Recent Uniform Orders -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Uniform Orders</h3>
                <div class="space-y-3">
                    @forelse($recentActivity['uniforms'] as $uniform)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium text-sm">{{ $uniform->student->full_name }}</p>
                                <p class="text-xs text-gray-600">Size: {{ $uniform->size }}</p>
                            </div>
                            <div class="text-right">
                                <p class="font-medium text-blue-600">AED {{ number_format($uniform->amount, 2) }}</p>
                                <p class="text-xs text-gray-500">{{ $uniform->order_date->format('M d') }}</p>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-sm">No recent uniform orders</p>
                    @endforelse
                </div>
            </div>

            <!-- Recent Registrations -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Recent Registrations</h3>
                <div class="space-y-3">
                    @forelse($recentActivity['students'] as $student)
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                                <p class="font-medium text-sm">{{ $student->full_name }}</p>
                                <p class="text-xs text-gray-600">{{ $student->academy->name }}</p>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    {{ ucfirst($student->status) }}
                                </span>
                                <p class="text-xs text-gray-500 mt-1">{{ $student->join_date->format('M d') }}</p>
                            </div>
                        </div>
                    @empty
                        <p class="text-gray-500 text-sm">No recent registrations</p>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
@endsection
