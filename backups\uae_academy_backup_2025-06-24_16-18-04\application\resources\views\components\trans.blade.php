{{-- UAE English Sports Academy - Translation Component --}}
{{-- Smart translation component with RTL support and fallbacks --}}

@props([
    'key' => '',
    'replace' => [],
    'locale' => null,
    'fallback' => null,
    'context' => null,
    'count' => null,
    'class' => '',
    'tag' => 'span',
])

@php
    $locale = $locale ?? app()->getLocale();
    $isRtl = $locale === 'ar';
    
    // Handle pluralization
    if ($count !== null) {
        $translation = trans_choice($key, $count, $replace, $locale);
    } else {
        // Try context-specific translation first
        if ($context) {
            $contextKey = "{$context}.{$key}";
            $contextTranslation = trans($contextKey, $replace, $locale);
            if ($contextTranslation !== $contextKey) {
                $translation = $contextTranslation;
            } else {
                $translation = trans($key, $replace, $locale);
            }
        } else {
            $translation = trans($key, $replace, $locale);
        }
    }
    
    // Use fallback if translation not found
    if ($translation === $key && $fallback) {
        $translation = $fallback;
    }
    
    // If still no translation and not in default locale, try default locale
    if ($translation === $key && $locale !== config('app.locale')) {
        $translation = trans($key, $replace, config('app.locale'));
    }
    
    // Final fallback - use the key itself
    if ($translation === $key) {
        $translation = str_replace(['_', '.'], ' ', $key);
        $translation = ucwords($translation);
    }
    
    // CSS classes
    $classes = [
        $class,
        'trans-component',
        $isRtl ? 'trans-rtl' : 'trans-ltr',
    ];
    
    // Attributes
    $attributes = [
        'dir' => $isRtl ? 'rtl' : 'ltr',
        'lang' => $locale,
        'data-trans-key' => $key,
        'data-locale' => $locale,
    ];
    
    if ($context) {
        $attributes['data-context'] = $context;
    }
    
    if ($count !== null) {
        $attributes['data-count'] = $count;
    }
@endphp

<{{ $tag }} 
    class="{{ implode(' ', array_filter($classes)) }}"
    @foreach($attributes as $attr => $value)
        {{ $attr }}="{{ $value }}"
    @endforeach
>
    {{ $translation }}
</{{ $tag }}>

{{-- Component Styles --}}
@once
@push('styles')
<style>
/* Translation Component Styles */
.trans-component {
    font-family: inherit;
}

.trans-rtl {
    direction: rtl;
    text-align: right;
    font-family: var(--font-family-arabic, 'IBM Plex Sans Arabic', sans-serif);
}

.trans-ltr {
    direction: ltr;
    text-align: left;
    font-family: var(--font-family-primary, 'IBM Plex Sans', sans-serif);
}

/* Translation in different contexts */
.nav-link .trans-component {
    font-weight: 500;
}

.btn .trans-component {
    font-weight: 500;
}

.card-title .trans-component {
    font-weight: 600;
}

.table-header .trans-component {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Form labels */
.form-label .trans-component {
    font-weight: 500;
    color: var(--charcoal-black, #1A202C);
}

/* Error messages */
.error-message .trans-component {
    color: var(--error-red, #E53E3E);
    font-size: 0.875rem;
}

/* Success messages */
.success-message .trans-component {
    color: var(--success-green, #38A169);
    font-size: 0.875rem;
}

/* Warning messages */
.warning-message .trans-component {
    color: var(--warning-orange, #DD6B20);
    font-size: 0.875rem;
}

/* Info messages */
.info-message .trans-component {
    color: var(--info-blue, #3182CE);
    font-size: 0.875rem;
}

/* Breadcrumb translations */
.breadcrumb .trans-component {
    font-size: 0.875rem;
}

/* Modal titles */
.modal-title .trans-component {
    font-weight: 600;
    font-size: 1.125rem;
}

/* Page titles */
.page-title .trans-component {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--charcoal-black, #1A202C);
}

/* Section titles */
.section-title .trans-component {
    font-weight: 600;
    font-size: 1.125rem;
    color: var(--charcoal-black, #1A202C);
}

/* Placeholder text */
.placeholder .trans-component {
    color: var(--dark-gray, #4A5568);
    font-style: italic;
}

/* Help text */
.help-text .trans-component {
    font-size: 0.75rem;
    color: var(--dark-gray, #4A5568);
    line-height: 1.4;
}

/* Status badges */
.badge .trans-component {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

/* Tooltips */
.tooltip .trans-component {
    font-size: 0.75rem;
    line-height: 1.4;
}

/* Loading state */
.trans-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    color: transparent;
    border-radius: 4px;
    min-width: 60px;
    height: 1em;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Missing translation indicator (development only) */
.trans-missing {
    background-color: rgba(229, 62, 62, 0.1);
    border: 1px dashed var(--error-red, #E53E3E);
    padding: 0.125rem 0.25rem;
    border-radius: 2px;
    font-size: 0.75rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .page-title .trans-component {
        font-size: 1.25rem;
    }
    
    .section-title .trans-component {
        font-size: 1rem;
    }
    
    .modal-title .trans-component {
        font-size: 1rem;
    }
}

/* Print styles */
@media print {
    .trans-component {
        color: black !important;
    }
    
    .trans-missing {
        background: none !important;
        border: none !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .trans-component {
        font-weight: 500;
    }
    
    .help-text .trans-component,
    .placeholder .trans-component {
        color: var(--charcoal-black, #1A202C);
    }
}

/* Dark theme support */
.dark .trans-component {
    color: var(--pure-white, #FFFFFF);
}

.dark .help-text .trans-component,
.dark .placeholder .trans-component {
    color: var(--medium-gray, #E2E8F0);
}

/* Accessibility */
.trans-component:focus {
    outline: 2px solid var(--leaders-red, #E53E3E);
    outline-offset: 2px;
    border-radius: 2px;
}

/* Animation for dynamic content */
.trans-component.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Text selection */
.trans-component::selection {
    background-color: rgba(229, 62, 62, 0.2);
}

.trans-component::-moz-selection {
    background-color: rgba(229, 62, 62, 0.2);
}
</style>
@endpush
@endonce
