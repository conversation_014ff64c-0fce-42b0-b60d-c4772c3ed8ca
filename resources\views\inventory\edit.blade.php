@extends('layouts.dashboard')

@section('title', __('Edit Inventory Item'))

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ __('Edit Inventory Item') }}</h1>
                <p class="text-lg text-dark-gray">{{ __('Update inventory item details') }}</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('inventory.show', $inventory) }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                {{ __('View Item') }}
            </a>
            <a href="{{ route('inventory.index') }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                {{ __('Back to Inventory') }}
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Edit Form -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="lg:col-span-2">
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Item Information') }}</h3>
                        <p class="text-sm text-gray-600">SKU: {{ $inventory->sku }}</p>
                    </div>
                    <div class="bank-card-body">
                        <form method="POST" action="{{ route('inventory.update', $inventory) }}" class="space-y-6">
                            @csrf
                            @method('PUT')

                            <!-- Basic Information -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="branch_id" class="form-label">{{ __('Branch') }} <span
                                            class="text-red-500">*</span></label>
                                    <select class="form-select @error('branch_id') border-red-500 @enderror" id="branch_id"
                                        name="branch_id" required>
                                        <option value="">{{ __('Select Branch') }}</option>
                                        @foreach ($branches as $branch)
                                            <option value="{{ $branch->id }}"
                                                {{ old('branch_id', $inventory->branch_id) == $branch->id ? 'selected' : '' }}>
                                                {{ $branch->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('branch_id')
                                        <div class="form-error">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div>
                                    <label for="academy_id" class="form-label">{{ __('Academy') }} <span
                                            class="text-red-500">*</span></label>
                                    <select class="form-select @error('academy_id') border-red-500 @enderror"
                                        id="academy_id" name="academy_id" required>
                                        <option value="">{{ __('Select Academy') }}</option>
                                        @foreach ($academies as $academy)
                                            <option value="{{ $academy->id }}" data-branch="{{ $academy->branch_id }}"
                                                {{ old('academy_id', $inventory->academy_id) == $academy->id ? 'selected' : '' }}>
                                                {{ $academy->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('academy_id')
                                        <div class="form-error">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="uniform_category_id" class="form-label">{{ __('Category') }} <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select @error('uniform_category_id') is-invalid @enderror"
                                        id="uniform_category_id" name="uniform_category_id" required>
                                        <option value="">{{ __('Select Category') }}</option>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->id }}"
                                                {{ old('uniform_category_id', $inventory->uniform_category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->localized_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('uniform_category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="uniform_supplier_id" class="form-label">{{ __('Supplier') }}</label>
                                    <select class="form-select @error('uniform_supplier_id') is-invalid @enderror"
                                        id="uniform_supplier_id" name="uniform_supplier_id">
                                        <option value="">{{ __('Select Supplier') }}</option>
                                        @foreach ($suppliers as $supplier)
                                            <option value="{{ $supplier->id }}"
                                                {{ old('uniform_supplier_id', $inventory->uniform_supplier_id) == $supplier->id ? 'selected' : '' }}>
                                                {{ $supplier->localized_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('uniform_supplier_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">{{ __('Item Name') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror"
                                        id="name" name="name" value="{{ old('name', $inventory->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="name_ar" class="form-label">{{ __('Item Name (Arabic)') }}</label>
                                    <input type="text" class="form-control @error('name_ar') is-invalid @enderror"
                                        id="name_ar" name="name_ar" value="{{ old('name_ar', $inventory->name_ar) }}">
                                    @error('name_ar')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Product Details -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="size" class="form-label">{{ __('Size') }} <span
                                            class="text-danger">*</span></label>
                                    <select class="form-select @error('size') is-invalid @enderror" id="size"
                                        name="size" required>
                                        <option value="">{{ __('Select Size') }}</option>
                                        <option value="XS" {{ old('size', $inventory->size) == 'XS' ? 'selected' : '' }}>XS</option>
                                        <option value="S" {{ old('size', $inventory->size) == 'S' ? 'selected' : '' }}>S</option>
                                        <option value="M" {{ old('size', $inventory->size) == 'M' ? 'selected' : '' }}>M</option>
                                        <option value="L" {{ old('size', $inventory->size) == 'L' ? 'selected' : '' }}>L</option>
                                        <option value="XL" {{ old('size', $inventory->size) == 'XL' ? 'selected' : '' }}>XL</option>
                                        <option value="XXL" {{ old('size', $inventory->size) == 'XXL' ? 'selected' : '' }}>XXL</option>
                                    </select>
                                    @error('size')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="color" class="form-label">{{ __('Color') }}</label>
                                    <input type="text" class="form-control @error('color') is-invalid @enderror"
                                        id="color" name="color" value="{{ old('color', $inventory->color) }}">
                                    @error('color')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="brand" class="form-label">{{ __('Brand') }}</label>
                                    <input type="text" class="form-control @error('brand') is-invalid @enderror"
                                        id="brand" name="brand" value="{{ old('brand', $inventory->brand) }}">
                                    @error('brand')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Stock Information -->
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="current_stock" class="form-label">{{ __('Current Stock') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number"
                                        class="form-control @error('current_stock') is-invalid @enderror"
                                        id="current_stock" name="current_stock" value="{{ old('current_stock', $inventory->current_stock) }}"
                                        min="0" required>
                                    @error('current_stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-3">
                                    <label for="minimum_stock" class="form-label">{{ __('Minimum Stock') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number"
                                        class="form-control @error('minimum_stock') is-invalid @enderror"
                                        id="minimum_stock" name="minimum_stock" value="{{ old('minimum_stock', $inventory->minimum_stock) }}"
                                        min="0" required>
                                    @error('minimum_stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-3">
                                    <label for="maximum_stock" class="form-label">{{ __('Maximum Stock') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number"
                                        class="form-control @error('maximum_stock') is-invalid @enderror"
                                        id="maximum_stock" name="maximum_stock" value="{{ old('maximum_stock', $inventory->maximum_stock) }}"
                                        min="1" required>
                                    @error('maximum_stock')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-3">
                                    <label for="reorder_quantity" class="form-label">{{ __('Reorder Quantity') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number"
                                        class="form-control @error('reorder_quantity') is-invalid @enderror"
                                        id="reorder_quantity" name="reorder_quantity"
                                        value="{{ old('reorder_quantity', $inventory->reorder_quantity) }}" min="1" required>
                                    @error('reorder_quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Pricing -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="cost_price" class="form-label">{{ __('Cost Price (AED)') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number" step="0.01"
                                        class="form-control @error('cost_price') is-invalid @enderror" id="cost_price"
                                        name="cost_price" value="{{ old('cost_price', $inventory->cost_price) }}" min="0" required>
                                    @error('cost_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="selling_price" class="form-label">{{ __('Selling Price (AED)') }} <span
                                            class="text-danger">*</span></label>
                                    <input type="number" step="0.01"
                                        class="form-control @error('selling_price') is-invalid @enderror"
                                        id="selling_price" name="selling_price" value="{{ old('selling_price', $inventory->selling_price) }}"
                                        min="0" required>
                                    @error('selling_price')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Location -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="location" class="form-label">{{ __('Location') }}</label>
                                    <input type="text" class="form-control @error('location') is-invalid @enderror"
                                        id="location" name="location" value="{{ old('location', $inventory->location) }}">
                                    @error('location')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="shelf" class="form-label">{{ __('Shelf') }}</label>
                                    <input type="text" class="form-control @error('shelf') is-invalid @enderror"
                                        id="shelf" name="shelf" value="{{ old('shelf', $inventory->shelf) }}">
                                    @error('shelf')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-4">
                                    <label for="bin" class="form-label">{{ __('Bin') }}</label>
                                    <input type="text" class="form-control @error('bin') is-invalid @enderror"
                                        id="bin" name="bin" value="{{ old('bin', $inventory->bin) }}">
                                    @error('bin')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label for="notes" class="form-label">{{ __('Notes') }}</label>
                                    <textarea class="form-control @error('notes') is-invalid @enderror" id="notes" name="notes" rows="3">{{ old('notes', $inventory->notes) }}</textarea>
                                    @error('notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="flex items-center space-x-4 pt-6 border-t border-light-gray">
                                <button type="submit" class="btn-bank">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12">
                                        </path>
                                    </svg>
                                    {{ __('Update Inventory Item') }}
                                </button>
                                <a href="{{ route('inventory.show', $inventory) }}" class="btn-bank-outline">
                                    {{ __('Cancel') }}
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Item Summary Panel -->
            <div>
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Item Summary') }}</h3>
                    </div>
                    <div class="bank-card-body space-y-4">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h6 class="flex items-center text-blue-800 font-medium mb-2">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                {{ __('Current Status') }}
                            </h6>
                            <div class="text-blue-700 space-y-1 text-sm">
                                <p><strong>SKU:</strong> {{ $inventory->sku }}</p>
                                <p><strong>Current Stock:</strong> {{ $inventory->current_stock }}</p>
                                <p><strong>Available:</strong> {{ $inventory->available_stock }}</p>
                                <p><strong>Reserved:</strong> {{ $inventory->reserved_stock ?? 0 }}</p>
                                <p><strong>Status:</strong> 
                                    <span class="px-2 py-1 rounded text-xs
                                        @if($inventory->current_stock <= 0) bg-red-100 text-red-800
                                        @elseif($inventory->current_stock <= $inventory->minimum_stock) bg-yellow-100 text-yellow-800
                                        @else bg-green-100 text-green-800 @endif">
                                        @if($inventory->current_stock <= 0) Out of Stock
                                        @elseif($inventory->current_stock <= $inventory->minimum_stock) Low Stock
                                        @else In Stock @endif
                                    </span>
                                </p>
                            </div>
                        </div>

                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h6 class="flex items-center text-yellow-800 font-medium mb-2">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                                    </path>
                                </svg>
                                {{ __('Important') }}
                            </h6>
                            <p class="text-yellow-700 text-sm">
                                {{ __('Changing stock levels will not create stock movement records. Use stock adjustment feature for proper tracking.') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            // Filter academies based on selected branch
            document.getElementById('branch_id').addEventListener('change', function() {
                const branchId = this.value;
                const academySelect = document.getElementById('academy_id');
                const academyOptions = academySelect.querySelectorAll('option');

                academyOptions.forEach(option => {
                    if (option.value === '') {
                        option.style.display = 'block';
                        return;
                    }

                    const optionBranchId = option.getAttribute('data-branch');
                    if (branchId === '' || optionBranchId === branchId) {
                        option.style.display = 'block';
                    } else {
                        option.style.display = 'none';
                    }
                });

                // Reset academy selection if current selection is not valid for new branch
                const currentAcademyOption = academySelect.querySelector(`option[value="${academySelect.value}"]`);
                if (currentAcademyOption && currentAcademyOption.style.display === 'none') {
                    academySelect.value = '';
                }
            });

            // Initialize branch filter on page load
            document.addEventListener('DOMContentLoaded', function() {
                const branchSelect = document.getElementById('branch_id');
                if (branchSelect.value) {
                    branchSelect.dispatchEvent(new Event('change'));
                }
            });
        </script>
    @endpush
@endsection
