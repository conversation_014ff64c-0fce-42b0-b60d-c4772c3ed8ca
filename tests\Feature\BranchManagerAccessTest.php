<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Student;
use App\Models\Payment;
use App\Models\Program;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BranchManagerAccessTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $branchManager1;
    protected $branchManager2;
    protected $academyManager;
    protected $branch1;
    protected $branch2;
    protected $academy1;
    protected $academy2;
    protected $student1;
    protected $student2;
    protected $payment1;
    protected $payment2;
    protected $program1;
    protected $program2;

    protected function setUp(): void
    {
        parent::setUp();

        // Create branches
        $this->branch1 = Branch::factory()->create(['name' => 'Branch 1']);
        $this->branch2 = Branch::factory()->create(['name' => 'Branch 2']);

        // Create academies
        $this->academy1 = Academy::factory()->create([
            'name' => 'Academy 1',
            'branch_id' => $this->branch1->id
        ]);
        $this->academy2 = Academy::factory()->create([
            'name' => 'Academy 2',
            'branch_id' => $this->branch2->id
        ]);

        // Create users
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'branch_id' => null,
            'academy_id' => null
        ]);

        $this->branchManager1 = User::factory()->create([
            'role' => 'branch_manager',
            'branch_id' => $this->branch1->id,
            'academy_id' => null
        ]);

        $this->branchManager2 = User::factory()->create([
            'role' => 'branch_manager',
            'branch_id' => $this->branch2->id,
            'academy_id' => null
        ]);

        $this->academyManager = User::factory()->create([
            'role' => 'academy_manager',
            'branch_id' => $this->branch1->id,
            'academy_id' => $this->academy1->id
        ]);

        // Create students without factory relationships to avoid extra data
        $this->student1 = Student::factory()->create([
            'branch_id' => $this->branch1->id,
            'academy_id' => $this->academy1->id,
            'full_name' => 'Student One'
        ]);

        $this->student2 = Student::factory()->create([
            'branch_id' => $this->branch2->id,
            'academy_id' => $this->academy2->id,
            'full_name' => 'Student Two'
        ]);

        // Create payments
        $this->payment1 = Payment::factory()->create([
            'student_id' => $this->student1->id,
            'branch_id' => $this->branch1->id,
            'academy_id' => $this->academy1->id
        ]);

        $this->payment2 = Payment::factory()->create([
            'student_id' => $this->student2->id,
            'branch_id' => $this->branch2->id,
            'academy_id' => $this->academy2->id
        ]);

        // Create programs
        $this->program1 = Program::factory()->create([
            'academy_id' => $this->academy1->id
        ]);

        $this->program2 = Program::factory()->create([
            'academy_id' => $this->academy2->id
        ]);
    }

    /** @test */
    public function admin_can_access_all_branches()
    {
        $this->actingAs($this->admin);

        $response = $this->get('/branches');
        $response->assertStatus(200);

        // Admin should see both branches
        $response->assertSee('Branch 1');
        $response->assertSee('Branch 2');
    }

    /** @test */
    public function branch_manager_can_only_access_their_assigned_branch()
    {
        $this->actingAs($this->branchManager1);

        $response = $this->get('/branches');
        $response->assertStatus(200);

        // Branch manager 1 should only see Branch 1
        $response->assertSee('Branch 1');
        $response->assertDontSee('Branch 2');
    }

    /** @test */
    public function branch_manager_cannot_view_other_branches()
    {
        $this->actingAs($this->branchManager1);

        // Try to access Branch 2 directly
        $response = $this->get("/branches/{$this->branch2->id}");
        $response->assertStatus(403); // Should be forbidden
    }

    /** @test */
    public function branch_manager_can_only_see_academies_in_their_branch()
    {
        $this->actingAs($this->branchManager1);

        $response = $this->get('/academies');
        $response->assertStatus(200);

        // Should only see Academy 1 (in Branch 1)
        $response->assertSee('Academy 1');
        $response->assertDontSee('Academy 2');
    }

    /** @test */
    public function branch_manager_can_only_see_students_in_their_branch()
    {
        $this->actingAs($this->branchManager1);

        $response = $this->get('/students');
        $response->assertStatus(200);

        // Should only see students from Branch 1
        $response->assertSee($this->student1->full_name);
        $response->assertDontSee($this->student2->full_name);
    }

    /** @test */
    public function branch_manager_can_only_see_payments_in_their_branch()
    {
        $this->actingAs($this->branchManager1);

        $response = $this->get('/payments');
        $response->assertStatus(200);

        // Should only see payments from Branch 1
        $response->assertSee($this->payment1->reference_number);
        $response->assertDontSee($this->payment2->reference_number);
    }

    /** @test */
    public function branch_manager_api_endpoints_respect_branch_restrictions()
    {
        $this->actingAs($this->branchManager1);

        // Test branches API
        $response = $this->getJson('/api/branches');
        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(1, $data);
        $this->assertEquals('Branch 1', $data[0]['name']);

        // Test students API
        $response = $this->getJson('/api/students');
        $response->assertStatus(200);
        $responseData = $response->json();
        $students = $responseData['data'] ?? $responseData;

        // Debug: Check what students are being returned
        $studentIds = collect($students)->pluck('id')->toArray();
        $this->assertContains($this->student1->id, $studentIds, 'Student 1 should be in the results');
        $this->assertNotContains($this->student2->id, $studentIds, 'Student 2 should NOT be in the results');

        // For now, just check that student1 is in the results
        $this->assertTrue(collect($students)->contains('id', $this->student1->id));

        // Test payments API
        $response = $this->getJson('/api/payments');
        $response->assertStatus(200);
        $responseData = $response->json();
        $payments = $responseData['data'] ?? $responseData;
        $this->assertCount(1, $payments);
        $this->assertEquals($this->payment1->id, $payments[0]['id']);
    }

    /** @test */
    public function academy_manager_can_only_see_their_academy_data()
    {
        $this->actingAs($this->academyManager);

        // Test academies access
        $response = $this->get('/academies');
        $response->assertStatus(200);
        $response->assertSee('Academy 1');
        $response->assertDontSee('Academy 2');

        // Test students access
        $response = $this->get('/students');
        $response->assertStatus(200);
        $response->assertSee($this->student1->full_name);
        $response->assertDontSee($this->student2->full_name);

        // Test payments access
        $response = $this->get('/payments');
        $response->assertStatus(200);
        $response->assertSee($this->payment1->reference_number);
        $response->assertDontSee($this->payment2->reference_number);
    }

    /** @test */
    public function branch_manager_cannot_create_branches()
    {
        $this->actingAs($this->branchManager1);

        $response = $this->get('/branches/create');
        $response->assertStatus(403); // Should be forbidden
    }

    /** @test */
    public function branch_manager_can_only_update_their_assigned_branch()
    {
        $this->actingAs($this->branchManager1);

        // Can update their own branch
        $response = $this->get("/branches/{$this->branch1->id}/edit");
        $response->assertStatus(200);

        // Cannot update other branch
        $response = $this->get("/branches/{$this->branch2->id}/edit");
        $response->assertStatus(403);
    }
}
