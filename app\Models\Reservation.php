<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class Reservation extends Model
{
    protected $fillable = [
        'reservation_number',
        'venue_id',
        'field_id',
        'customer_id',
        'reservation_date',
        'start_time',
        'end_time',
        'duration_hours',
        'hourly_rate',
        'subtotal',
        'discount_percentage',
        'discount_amount',
        'vat_rate',
        'vat_amount',
        'total_amount',
        'currency',
        'booking_type',
        'status',
        'payment_status',
        'deposit_amount',
        'remaining_amount',
        'payment_method',
        'booking_datetime',
        'confirmed_at',
        'cancelled_at',
        'cancelled_by',
        'cancellation_reason',
        'cancellation_fee',
        'participants',
        'expected_participants',
        'event_type',
        'event_name',
        'special_requirements',
        'special_requirements_ar',
        'equipment_requested',
        'recurring_booking',
        'recurring_pattern',
        'parent_reservation_id',
        'reminder_sent',
        'reminder_sent_at',
        'rating',
        'feedback',
        'internal_notes',
        'internal_notes_ar',
        'created_by',
        'confirmed_by',
    ];

    protected $casts = [
        'reservation_date' => 'date',
        'start_time' => 'datetime:H:i',
        'end_time' => 'datetime:H:i',
        'duration_hours' => 'integer',
        'hourly_rate' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'vat_rate' => 'decimal:2',
        'vat_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'deposit_amount' => 'decimal:2',
        'remaining_amount' => 'decimal:2',
        'cancellation_fee' => 'decimal:2',
        'expected_participants' => 'integer',
        'rating' => 'integer',
        'participants' => 'array',
        'equipment_requested' => 'array',
        'recurring_pattern' => 'array',
        'recurring_booking' => 'boolean',
        'reminder_sent' => 'boolean',
        'booking_datetime' => 'datetime',
        'confirmed_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'reminder_sent_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_total_amount',
        'formatted_deposit_amount',
        'formatted_remaining_amount',
        'status_text',
        'status_badge_class',
        'payment_status_text',
        'payment_status_badge_class',
        'booking_type_text',
        'event_type_text',
        'time_slot_text',
        'duration_text',
        'is_today',
        'is_upcoming',
        'is_past',
        'can_cancel',
        'can_modify',
        'localized_special_requirements',
        'localized_internal_notes',
    ];

    /**
     * Get the venue for this reservation.
     */
    public function venue(): BelongsTo
    {
        return $this->belongsTo(Venue::class);
    }

    /**
     * Get the field for this reservation.
     */
    public function field(): BelongsTo
    {
        return $this->belongsTo(Field::class);
    }

    /**
     * Get the customer for this reservation.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    /**
     * Get the user who created this reservation.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who confirmed this reservation.
     */
    public function confirmedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'confirmed_by');
    }

    /**
     * Get the parent reservation (for recurring bookings).
     */
    public function parentReservation(): BelongsTo
    {
        return $this->belongsTo(Reservation::class, 'parent_reservation_id');
    }

    /**
     * Get child reservations (for recurring bookings).
     */
    public function childReservations(): HasMany
    {
        return $this->hasMany(Reservation::class, 'parent_reservation_id');
    }

    /**
     * Get the payments for this reservation.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(ReservationPayment::class);
    }

    // Computed Properties

    /**
     * Get formatted total amount.
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->total_amount, 2);
    }

    /**
     * Get formatted deposit amount.
     */
    public function getFormattedDepositAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->deposit_amount, 2);
    }

    /**
     * Get formatted remaining amount.
     */
    public function getFormattedRemainingAmountAttribute(): string
    {
        return $this->currency . ' ' . number_format($this->remaining_amount, 2);
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'Pending',
            'confirmed' => 'Confirmed',
            'in_progress' => 'In Progress',
            'completed' => 'Completed',
            'cancelled' => 'Cancelled',
            'no_show' => 'No Show',
            default => 'Unknown',
        };
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'badge-warning',
            'confirmed' => 'badge-info',
            'in_progress' => 'badge-primary',
            'completed' => 'badge-success',
            'cancelled' => 'badge-danger',
            'no_show' => 'badge-dark',
            default => 'badge-secondary',
        };
    }

    /**
     * Get payment status text.
     */
    public function getPaymentStatusTextAttribute(): string
    {
        return match ($this->payment_status) {
            'unpaid' => 'Unpaid',
            'partial' => 'Partial',
            'paid' => 'Paid',
            'refunded' => 'Refunded',
            default => 'Unknown',
        };
    }

    /**
     * Get payment status badge class.
     */
    public function getPaymentStatusBadgeClassAttribute(): string
    {
        return match ($this->payment_status) {
            'unpaid' => 'badge-danger',
            'partial' => 'badge-warning',
            'paid' => 'badge-success',
            'refunded' => 'badge-info',
            default => 'badge-secondary',
        };
    }

    /**
     * Get booking type text.
     */
    public function getBookingTypeTextAttribute(): string
    {
        return match ($this->booking_type) {
            'regular' => 'Regular',
            'peak' => 'Peak Hours',
            'weekend' => 'Weekend',
            'special' => 'Special',
            default => 'Regular',
        };
    }

    /**
     * Get event type text.
     */
    public function getEventTypeTextAttribute(): string
    {
        return match ($this->event_type) {
            'training' => 'Training',
            'match' => 'Match',
            'tournament' => 'Tournament',
            'casual' => 'Casual',
            'corporate' => 'Corporate',
            default => 'Casual',
        };
    }

    /**
     * Get time slot text.
     */
    public function getTimeSlotTextAttribute(): string
    {
        return $this->start_time->format('H:i') . ' - ' . $this->end_time->format('H:i');
    }

    /**
     * Get duration text.
     */
    public function getDurationTextAttribute(): string
    {
        return $this->duration_hours . ' hour' . ($this->duration_hours > 1 ? 's' : '');
    }

    /**
     * Check if reservation is today.
     */
    public function getIsTodayAttribute(): bool
    {
        return $this->reservation_date->isToday();
    }

    /**
     * Check if reservation is upcoming.
     */
    public function getIsUpcomingAttribute(): bool
    {
        return $this->reservation_date->isFuture() || 
               ($this->reservation_date->isToday() && $this->start_time->format('H:i:s') > now()->format('H:i:s'));
    }

    /**
     * Check if reservation is past.
     */
    public function getIsPastAttribute(): bool
    {
        return $this->reservation_date->isPast() || 
               ($this->reservation_date->isToday() && $this->end_time->format('H:i:s') < now()->format('H:i:s'));
    }

    /**
     * Check if reservation can be cancelled.
     */
    public function getCanCancelAttribute(): bool
    {
        if (in_array($this->status, ['cancelled', 'completed', 'no_show'])) {
            return false;
        }

        // Can cancel up to 2 hours before start time
        $reservationDateTime = Carbon::parse($this->reservation_date->format('Y-m-d') . ' ' . $this->start_time->format('H:i:s'));
        return $reservationDateTime->diffInHours(now(), false) >= 2;
    }

    /**
     * Check if reservation can be modified.
     */
    public function getCanModifyAttribute(): bool
    {
        if (in_array($this->status, ['cancelled', 'completed', 'no_show', 'in_progress'])) {
            return false;
        }

        // Can modify up to 4 hours before start time
        $reservationDateTime = Carbon::parse($this->reservation_date->format('Y-m-d') . ' ' . $this->start_time->format('H:i:s'));
        return $reservationDateTime->diffInHours(now(), false) >= 4;
    }

    /**
     * Get localized special requirements.
     */
    public function getLocalizedSpecialRequirementsAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->special_requirements_ar ? 
               $this->special_requirements_ar : $this->special_requirements;
    }

    /**
     * Get localized internal notes.
     */
    public function getLocalizedInternalNotesAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->internal_notes_ar ? 
               $this->internal_notes_ar : $this->internal_notes;
    }

    // Query Scopes

    /**
     * Scope to get confirmed reservations.
     */
    public function scopeConfirmed(Builder $query): Builder
    {
        return $query->where('status', 'confirmed');
    }

    /**
     * Scope to get today's reservations.
     */
    public function scopeToday(Builder $query): Builder
    {
        return $query->whereDate('reservation_date', today());
    }

    /**
     * Scope to get upcoming reservations.
     */
    public function scopeUpcoming(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereDate('reservation_date', '>', today())
              ->orWhere(function ($subQ) {
                  $subQ->whereDate('reservation_date', today())
                       ->whereTime('start_time', '>', now()->format('H:i:s'));
              });
        });
    }

    /**
     * Scope to filter by venue.
     */
    public function scopeByVenue(Builder $query, int $venueId): Builder
    {
        return $query->where('venue_id', $venueId);
    }

    /**
     * Scope to filter by field.
     */
    public function scopeByField(Builder $query, int $fieldId): Builder
    {
        return $query->where('field_id', $fieldId);
    }

    /**
     * Scope to filter by customer.
     */
    public function scopeByCustomer(Builder $query, int $customerId): Builder
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by payment status.
     */
    public function scopeByPaymentStatus(Builder $query, string $paymentStatus): Builder
    {
        return $query->where('payment_status', $paymentStatus);
    }

    // Utility Methods

    /**
     * Generate unique reservation number.
     */
    public static function generateReservationNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $counter = self::whereYear('created_at', $year)
                      ->whereMonth('created_at', $month)
                      ->count() + 1;
        
        return 'RES' . $year . $month . str_pad($counter, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate total amount including VAT and discounts.
     */
    public function calculateTotalAmount(): void
    {
        // Calculate subtotal
        $this->subtotal = $this->duration_hours * $this->hourly_rate;
        
        // Apply discount
        if ($this->discount_percentage > 0) {
            $this->discount_amount = ($this->subtotal * $this->discount_percentage) / 100;
        }
        
        $amountAfterDiscount = $this->subtotal - $this->discount_amount;
        
        // Calculate VAT
        $this->vat_amount = ($amountAfterDiscount * $this->vat_rate) / 100;
        
        // Calculate total
        $this->total_amount = $amountAfterDiscount + $this->vat_amount;
        
        // Calculate remaining amount
        $this->remaining_amount = $this->total_amount - $this->deposit_amount;
    }

    /**
     * Confirm the reservation.
     */
    public function confirm(?int $confirmedBy = null): bool
    {
        $this->status = 'confirmed';
        $this->confirmed_at = now();
        $this->confirmed_by = $confirmedBy ?? auth()->id();
        
        return $this->save();
    }

    /**
     * Cancel the reservation.
     */
    public function cancel(string $reason, ?string $cancelledBy = null, float $cancellationFee = 0): bool
    {
        $this->status = 'cancelled';
        $this->cancelled_at = now();
        $this->cancelled_by = $cancelledBy ?? 'customer';
        $this->cancellation_reason = $reason;
        $this->cancellation_fee = $cancellationFee;
        
        return $this->save();
    }

    /**
     * Mark as completed.
     */
    public function complete(?int $rating = null, ?string $feedback = null): bool
    {
        $this->status = 'completed';
        
        if ($rating) {
            $this->rating = $rating;
        }
        
        if ($feedback) {
            $this->feedback = $feedback;
        }
        
        return $this->save();
    }
}
