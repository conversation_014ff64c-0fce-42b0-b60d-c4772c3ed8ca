<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Setting;

class VatSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $vatSettings = [
            [
                'key' => 'vat_number',
                'value' => '100123456789012',
                'type' => 'string',
                'category' => 'payment',
                'label' => 'VAT Registration Number',
                'description' => 'Company VAT registration number to be displayed on invoices and receipts',
                'validation_rules' => ['required', 'string', 'max:20'],
                'is_public' => true,
                'sort_order' => 10,
            ],
            [
                'key' => 'vat_rate',
                'value' => '5.00',
                'type' => 'decimal',
                'category' => 'payment',
                'label' => 'VAT Rate (%)',
                'description' => 'Default VAT rate percentage applied to payments',
                'validation_rules' => ['required', 'numeric', 'min:0', 'max:100'],
                'is_public' => true,
                'sort_order' => 11,
            ],
            [
                'key' => 'vat_enabled',
                'value' => '1',
                'type' => 'boolean',
                'category' => 'payment',
                'label' => 'Enable VAT Calculation',
                'description' => 'Enable automatic VAT calculation for payments',
                'validation_rules' => ['boolean'],
                'is_public' => true,
                'sort_order' => 12,
            ],
            [
                'key' => 'vat_inclusive_by_default',
                'value' => '0',
                'type' => 'boolean',
                'category' => 'payment',
                'label' => 'VAT Inclusive by Default',
                'description' => 'Whether payment amounts include VAT by default',
                'validation_rules' => ['boolean'],
                'is_public' => true,
                'sort_order' => 13,
            ],
            [
                'key' => 'company_address',
                'value' => 'UAE English Sports Academy\nP.O. Box 12345\nDubai, United Arab Emirates',
                'type' => 'textarea',
                'category' => 'general',
                'label' => 'Company Address',
                'description' => 'Company address to be displayed on invoices and receipts',
                'validation_rules' => ['required', 'string', 'max:500'],
                'is_public' => true,
                'sort_order' => 15,
            ],
            [
                'key' => 'company_phone',
                'value' => '+971-4-123-4567',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Company Phone',
                'description' => 'Company phone number for invoices and receipts',
                'validation_rules' => ['required', 'string', 'max:20'],
                'is_public' => true,
                'sort_order' => 16,
            ],
            [
                'key' => 'company_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Company Email',
                'description' => 'Company email address for invoices and receipts',
                'validation_rules' => ['required', 'email', 'max:100'],
                'is_public' => true,
                'sort_order' => 17,
            ],
        ];

        foreach ($vatSettings as $settingData) {
            Setting::updateOrCreate(
                ['key' => $settingData['key']],
                $settingData
            );
        }

        $this->command->info('VAT settings seeded successfully.');
    }
}
