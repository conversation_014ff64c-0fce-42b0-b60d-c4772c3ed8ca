@extends('layouts.dashboard')

@section('title', 'Payment Details')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Payment Details</h1>
                <p class="text-lg text-dark-gray">
                    Payment for {{ $payment->student->full_name ?? 'N/A' }}
                </p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                        </path>
                    </svg>
                    {{ $payment->reference_number ?? 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <!-- Print Invoice Button -->
            <a href="{{ route('payments.invoice', $payment) }}" target="_blank" class="btn-bank btn-bank-success">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                    </path>
                </svg>
                Print Invoice
            </a>
            @can('update', $payment)
                <a href="{{ route('payments.edit', $payment) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Payment
                </a>
            @endcan
            <a href="{{ route('payments.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Payments
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto space-y-6">
        <!-- Payment Status & Quick Actions -->
        <div class="bank-card">
            <div class="bank-card-body">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span
                            class="badge-bank {{ $payment->status_badge_class }} {{ $payment->status === 'expired' ? 'expired-payment' : '' }} text-lg px-4 py-2">
                            {{ $payment->status_text }}
                        </span>
                        @if ($payment->renewal)
                            <span
                                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                    </path>
                                </svg>
                                Renewal
                            </span>
                        @endif
                        <span
                            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $payment->payment_type_badge_class }}">
                            @if ($payment->payment_type === 'new_entry')
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            @elseif($payment->payment_type === 'renewal')
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                    </path>
                                </svg>
                            @else
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            @endif
                            {{ $payment->payment_type_text }}
                        </span>
                    </div>

                    @can('update', $payment)
                        <div class="flex items-center space-x-2">
                            <button onclick="toggleStatus({{ $payment->id }})" class="btn-bank btn-bank-outline">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                </svg>
                                Toggle Status
                            </button>
                        </div>
                    @endcan
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Payment Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Payment Details -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Payment Information</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="form-label">Reference Number</label>
                                <p class="text-charcoal-black font-medium">
                                    {{ $payment->reference_number ?? 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}
                                </p>
                            </div>

                            <div>
                                <label class="form-label">Payment Method</label>
                                <p class="text-charcoal-black font-medium">{{ $payment->method_text }}</p>
                            </div>

                            <div>
                                <label class="form-label">Payment Type</label>
                                <p class="text-charcoal-black font-medium">{{ $payment->payment_type_text }}</p>
                            </div>

                            <div>
                                <label class="form-label">Amount</label>
                                <p class="text-2xl font-bold text-leaders-red">{{ $payment->formatted_amount }}</p>
                            </div>

                            @if ($payment->discount > 0)
                                <div>
                                    <label class="form-label">Discount</label>
                                    <p class="text-lg font-medium text-green-600">AED
                                        {{ number_format($payment->discount, 2) }}</p>
                                </div>

                                <div class="md:col-span-2">
                                    <label class="form-label">Net Amount</label>
                                    <p class="text-3xl font-bold text-charcoal-black">{{ $payment->formatted_net_amount }}
                                    </p>
                                </div>
                            @endif

                            @if ($payment->reset_num)
                                <div>
                                    <label class="form-label">Reset Number</label>
                                    <p class="text-charcoal-black font-medium">{{ $payment->reset_num }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Dates & Schedule -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Dates & Schedule</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="form-label">Payment Date</label>
                                <p class="text-charcoal-black font-medium">
                                    {{ $payment->formatted_payment_date ?: 'Not paid yet' }}
                                </p>
                            </div>

                            <div>
                                <label class="form-label">Period Start</label>
                                <p class="text-charcoal-black font-medium">{{ $payment->formatted_start_date }}</p>
                            </div>

                            <div>
                                <label class="form-label">Period End</label>
                                <p class="text-charcoal-black font-medium">{{ $payment->formatted_end_date }}</p>
                            </div>

                            @if ($payment->class_time_from && $payment->class_time_to)
                                <div class="md:col-span-3">
                                    <label class="form-label">Class Time</label>
                                    <p class="text-charcoal-black font-medium">
                                        {{ $payment->class_time_from }} - {{ $payment->class_time_to }}
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                @if ($payment->description || $payment->note)
                    <div class="bank-card">
                        <div class="bank-card-header">
                            <h3 class="bank-card-title">Additional Information</h3>
                        </div>
                        <div class="bank-card-body space-y-4">
                            @if ($payment->description)
                                <div>
                                    <label class="form-label">Description</label>
                                    <p class="text-charcoal-black">{{ $payment->description }}</p>
                                </div>
                            @endif

                            @if ($payment->note)
                                <div>
                                    <label class="form-label">Note</label>
                                    <p class="text-charcoal-black">{{ $payment->note }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>

            <!-- Sidebar Information -->
            <div class="space-y-6">
                <!-- Student Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Student Information</h3>
                    </div>
                    <div class="bank-card-body space-y-4">
                        <div>
                            <label class="form-label">Student Name</label>
                            <p class="text-charcoal-black font-medium">{{ $payment->student->full_name ?? 'N/A' }}</p>
                        </div>

                        @if ($payment->student && $payment->student->phone)
                            <div>
                                <label class="form-label">Phone</label>
                                <p class="text-charcoal-black">{{ $payment->student->phone }}</p>
                            </div>
                        @endif

                        @if ($payment->student && $payment->student->email)
                            <div>
                                <label class="form-label">Email</label>
                                <p class="text-charcoal-black">{{ $payment->student->email }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Location Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Location Information</h3>
                    </div>
                    <div class="bank-card-body space-y-4">
                        <div>
                            <label class="form-label">Branch</label>
                            <p class="text-charcoal-black font-medium">{{ $payment->branch->name ?? 'N/A' }}</p>
                        </div>

                        <div>
                            <label class="form-label">Academy</label>
                            <p class="text-charcoal-black font-medium">{{ $payment->academy->name ?? 'N/A' }}</p>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">System Information</h3>
                    </div>
                    <div class="bank-card-body space-y-4">
                        <div>
                            <label class="form-label">Payment ID</label>
                            <p class="text-charcoal-black font-mono">#{{ $payment->id }}</p>
                        </div>

                        <div>
                            <label class="form-label">Created</label>
                            <p class="text-charcoal-black">{{ $payment->created_at->format('M d, Y H:i') }}</p>
                        </div>

                        <div>
                            <label class="form-label">Last Updated</label>
                            <p class="text-charcoal-black">{{ $payment->updated_at->format('M d, Y H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        async function toggleStatus(paymentId) {
            if (!confirm('Are you sure you want to toggle the payment status?')) {
                return;
            }

            try {
                const response = await fetch(`/payments/${paymentId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Reload the page to show updated status
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while updating the payment status.');
            }
        }
    </script>
@endpush

@push('styles')
    <style>
        /* Payment Type Badge Styles */
        .badge-primary {
            background-color: #dbeafe !important;
            color: #1e40af !important;
        }

        .badge-info {
            background-color: #e0f2fe !important;
            color: #0369a1 !important;
        }

        .badge-secondary {
            background-color: #f3f4f6 !important;
            color: #374151 !important;
        }
    </style>
@endpush
