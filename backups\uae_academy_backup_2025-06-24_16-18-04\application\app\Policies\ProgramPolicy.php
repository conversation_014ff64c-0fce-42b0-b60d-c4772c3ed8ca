<?php

namespace App\Policies;

use App\Models\Program;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProgramPolicy
{
    /**
     * Determine whether the user can view any programs.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the program.
     */
    public function view(User $user, Program $program): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            'academy_manager' => $this->userCanAccessProgram($user, $program),
            default => false,
        };
    }

    /**
     * Determine whether the user can create programs.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can update the program.
     */
    public function update(User $user, Program $program): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            'academy_manager' => $this->userCanAccessProgram($user, $program),
            default => false,
        };
    }

    /**
     * Determine whether the user can delete the program.
     */
    public function delete(User $user, Program $program): bool
    {
        // Only admin and branch managers can delete programs
        // Academy managers cannot delete programs to prevent accidental data loss
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            default => false,
        };
    }

    /**
     * Determine whether the user can restore the program.
     */
    public function restore(User $user, Program $program): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can permanently delete the program.
     */
    public function forceDelete(User $user, Program $program): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can export programs.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can perform bulk actions on programs.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can toggle program status.
     */
    public function toggleStatus(User $user, Program $program): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            'academy_manager' => $this->userCanAccessProgram($user, $program),
            default => false,
        };
    }

    /**
     * Determine whether the user can view program statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can manage program enrollments.
     */
    public function manageEnrollments(User $user, Program $program): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            'academy_manager' => $this->userCanAccessProgram($user, $program),
            default => false,
        };
    }

    /**
     * Check if the academy manager can access the specific program.
     */
    private function userCanAccessProgram(User $user, Program $program): bool
    {
        if ($user->role !== 'academy_manager') {
            return false;
        }

        // Load the program's academy and branch relationships
        $program->load(['academy.branch']);

        // Check if user has access to this program's academy
        // This assumes there's a relationship between users and academies/branches
        // You may need to adjust this based on your actual user-academy relationship structure
        
        // For now, we'll allow academy managers to access all programs
        // In a real implementation, you'd check if the user is assigned to the specific academy
        return true;
        
        // Example of more restrictive access:
        // return $user->academies()->where('academies.id', $program->academy_id)->exists();
        // or
        // return $user->academy_id === $program->academy_id;
    }

    /**
     * Determine if the user can create programs for a specific academy.
     */
    public function createForAcademy(User $user, int $academyId): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            'academy_manager' => $this->userCanAccessAcademy($user, $academyId),
            default => false,
        };
    }

    /**
     * Check if the user can access a specific academy.
     */
    private function userCanAccessAcademy(User $user, int $academyId): bool
    {
        if ($user->role !== 'academy_manager') {
            return false;
        }

        // This should be implemented based on your user-academy relationship
        // For now, allowing all academy managers to access all academies
        return true;
        
        // Example implementations:
        // return $user->academies()->where('academies.id', $academyId)->exists();
        // or
        // return $user->academy_id === $academyId;
    }

    /**
     * Determine if the user can view programs from a specific branch.
     */
    public function viewByBranch(User $user, int $branchId): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => $this->userCanAccessBranch($user, $branchId),
            'academy_manager' => $this->userCanAccessBranch($user, $branchId),
            default => false,
        };
    }

    /**
     * Check if the user can access a specific branch.
     */
    private function userCanAccessBranch(User $user, int $branchId): bool
    {
        // This should be implemented based on your user-branch relationship
        // For now, allowing all managers to access all branches
        return true;
        
        // Example implementations:
        // return $user->branches()->where('branches.id', $branchId)->exists();
        // or
        // return $user->branch_id === $branchId;
    }
}
