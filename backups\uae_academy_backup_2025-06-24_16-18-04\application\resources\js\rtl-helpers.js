// UAE English Sports Academy - RTL Helper Utilities
// Advanced RTL support functions for seamless bilingual experience

/**
 * RTL Helper Class
 * Provides utilities for RTL/LTR layout management
 */
class RTLHelpers {
    constructor() {
        this.currentDirection = this.getCurrentDirection();
        this.currentLocale = this.getCurrentLocale();
        this.init();
    }

    init() {
        // Listen for language changes
        window.addEventListener('languageChanged', (event) => {
            this.currentDirection = event.detail.direction;
            this.currentLocale = event.detail.locale;
            this.updateAllElements();
        });

        // Apply RTL fixes on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.applyRTLFixes();
            });
        } else {
            this.applyRTLFixes();
        }
    }

    getCurrentDirection() {
        return document.documentElement.getAttribute('dir') || 'ltr';
    }

    getCurrentLocale() {
        return document.documentElement.getAttribute('lang') || 'en';
    }

    isRTL() {
        return this.currentDirection === 'rtl';
    }

    /**
     * Apply RTL-specific fixes to elements
     */
    applyRTLFixes() {
        this.fixCurrencyElements();
        this.fixNumberElements();
        this.fixTableElements();
        this.fixFormElements();
        this.fixNavigationElements();
        this.fixModalElements();
    }

    /**
     * Update all elements when language changes
     */
    updateAllElements() {
        this.applyRTLFixes();
        this.updateAnimations();
        this.updateScrollbars();
    }

    /**
     * Fix currency display elements
     */
    fixCurrencyElements() {
        const currencyElements = document.querySelectorAll('.currency-amount, .price, .amount');
        currencyElements.forEach(element => {
            if (this.isRTL()) {
                element.style.direction = 'ltr';
                element.style.display = 'inline-block';
                element.style.textAlign = 'right';
            } else {
                element.style.direction = 'ltr';
                element.style.display = 'inline';
                element.style.textAlign = 'left';
            }
        });
    }

    /**
     * Fix number display elements
     */
    fixNumberElements() {
        const numberElements = document.querySelectorAll('.number-display, .stats-value, .count');
        numberElements.forEach(element => {
            if (this.isRTL()) {
                element.style.direction = 'ltr';
                element.style.display = 'inline-block';
            }
        });
    }

    /**
     * Fix table elements for RTL
     */
    fixTableElements() {
        const tables = document.querySelectorAll('.table-bank, .data-table');
        tables.forEach(table => {
            if (this.isRTL()) {
                table.style.direction = 'rtl';
                
                // Fix action columns to stay left-aligned
                const actionCells = table.querySelectorAll('.table-actions, .actions-column');
                actionCells.forEach(cell => {
                    cell.style.textAlign = 'left';
                    cell.style.direction = 'ltr';
                });
            } else {
                table.style.direction = 'ltr';
            }
        });
    }

    /**
     * Fix form elements for RTL
     */
    fixFormElements() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            if (this.isRTL()) {
                // Fix input groups
                const inputGroups = form.querySelectorAll('.input-group');
                inputGroups.forEach(group => {
                    group.style.direction = 'rtl';
                });

                // Fix select elements
                const selects = form.querySelectorAll('select');
                selects.forEach(select => {
                    select.style.textAlign = 'right';
                });

                // Fix checkboxes and radios
                const checkboxes = form.querySelectorAll('input[type="checkbox"], input[type="radio"]');
                checkboxes.forEach(checkbox => {
                    const label = checkbox.closest('label') || checkbox.nextElementSibling;
                    if (label) {
                        label.style.direction = 'rtl';
                    }
                });
            }
        });
    }

    /**
     * Fix navigation elements
     */
    fixNavigationElements() {
        const navItems = document.querySelectorAll('.nav-item, .nav-link');
        navItems.forEach(item => {
            if (this.isRTL()) {
                const icon = item.querySelector('.nav-icon, .icon');
                const text = item.querySelector('.nav-text, .text');
                
                if (icon && text) {
                    icon.style.order = '2';
                    text.style.order = '1';
                    icon.style.marginRight = '0';
                    icon.style.marginLeft = '0.5rem';
                }
            } else {
                const icon = item.querySelector('.nav-icon, .icon');
                const text = item.querySelector('.nav-text, .text');
                
                if (icon && text) {
                    icon.style.order = '1';
                    text.style.order = '2';
                    icon.style.marginLeft = '0';
                    icon.style.marginRight = '0.5rem';
                }
            }
        });
    }

    /**
     * Fix modal elements
     */
    fixModalElements() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            if (this.isRTL()) {
                modal.style.direction = 'rtl';
                
                // Fix close button
                const closeBtn = modal.querySelector('.btn-close, .close');
                if (closeBtn) {
                    closeBtn.style.marginLeft = 'auto';
                    closeBtn.style.marginRight = '0';
                }
            }
        });
    }

    /**
     * Update animations for RTL
     */
    updateAnimations() {
        const animatedElements = document.querySelectorAll('[class*="slide"], [class*="fade"]');
        animatedElements.forEach(element => {
            if (this.isRTL()) {
                // Reverse slide animations
                if (element.classList.contains('slide-in-right')) {
                    element.classList.remove('slide-in-right');
                    element.classList.add('slide-in-left');
                } else if (element.classList.contains('slide-in-left')) {
                    element.classList.remove('slide-in-left');
                    element.classList.add('slide-in-right');
                }
            }
        });
    }

    /**
     * Update scrollbar styles for RTL
     */
    updateScrollbars() {
        if (this.isRTL()) {
            document.body.classList.add('rtl-scrollbar');
        } else {
            document.body.classList.remove('rtl-scrollbar');
        }
    }

    /**
     * Format currency for current locale
     */
    formatCurrency(amount, options = {}) {
        const defaultOptions = {
            style: 'currency',
            currency: 'AED',
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        };

        const formatOptions = { ...defaultOptions, ...options };
        const locale = this.currentLocale === 'ar' ? 'ar-AE' : 'en-AE';

        return new Intl.NumberFormat(locale, formatOptions).format(amount);
    }

    /**
     * Format number for current locale
     */
    formatNumber(number, options = {}) {
        const locale = this.currentLocale === 'ar' ? 'ar-AE' : 'en-AE';
        return new Intl.NumberFormat(locale, options).format(number);
    }

    /**
     * Format date for current locale
     */
    formatDate(date, options = {}) {
        const defaultOptions = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };

        const formatOptions = { ...defaultOptions, ...options };
        const locale = this.currentLocale === 'ar' ? 'ar-AE' : 'en-AE';

        return new Intl.DateTimeFormat(locale, formatOptions).format(new Date(date));
    }

    /**
     * Get text direction for an element
     */
    getElementDirection(element) {
        return window.getComputedStyle(element).direction;
    }

    /**
     * Set text direction for an element
     */
    setElementDirection(element, direction) {
        element.style.direction = direction;
    }

    /**
     * Toggle element direction
     */
    toggleElementDirection(element) {
        const currentDir = this.getElementDirection(element);
        const newDir = currentDir === 'rtl' ? 'ltr' : 'rtl';
        this.setElementDirection(element, newDir);
        return newDir;
    }

    /**
     * Apply RTL-aware margin/padding
     */
    applyRTLSpacing(element, property, value) {
        if (this.isRTL()) {
            // Convert left/right properties for RTL
            const rtlProperty = property
                .replace('left', 'temp')
                .replace('right', 'left')
                .replace('temp', 'right');
            element.style[rtlProperty] = value;
        } else {
            element.style[property] = value;
        }
    }

    /**
     * Get RTL-aware position
     */
    getRTLPosition(position) {
        if (!this.isRTL()) return position;

        const rtlMap = {
            'left': 'right',
            'right': 'left',
            'start': 'end',
            'end': 'start'
        };

        return rtlMap[position] || position;
    }

    /**
     * Create RTL-aware CSS class
     */
    getRTLClass(baseClass) {
        return this.isRTL() ? `${baseClass}-rtl` : `${baseClass}-ltr`;
    }
}

// Initialize RTL Helpers
window.RTLHelpers = new RTLHelpers();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RTLHelpers;
}
