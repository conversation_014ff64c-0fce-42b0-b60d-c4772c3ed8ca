<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('uniforms', function (Blueprint $table) {
            // Link to inventory system
            $table->foreignId('uniform_inventory_id')->nullable()->after('academy_id')->constrained('uniform_inventory')->onDelete('set null');

            // Enhanced tracking
            $table->string('sku')->nullable()->after('item'); // Link to inventory SKU
            $table->string('color')->nullable()->after('size');
            $table->enum('fulfillment_status', ['pending', 'reserved', 'picked', 'packed', 'shipped', 'delivered'])->default('pending')->after('status');

            // Stock reservation
            $table->boolean('stock_reserved')->default(false)->after('fulfillment_status');
            $table->timestamp('stock_reserved_at')->nullable()->after('stock_reserved');
            $table->timestamp('stock_released_at')->nullable()->after('stock_reserved_at');

            // Fulfillment tracking
            $table->foreignId('fulfilled_by')->nullable()->after('stock_released_at')->constrained('users')->onDelete('set null');
            $table->timestamp('fulfilled_at')->nullable()->after('fulfilled_by');
            $table->text('fulfillment_notes')->nullable()->after('fulfilled_at');

            // Add indexes
            $table->index(['uniform_inventory_id', 'fulfillment_status'], 'uniforms_inv_fulfill_idx');
            $table->index(['sku', 'size', 'color'], 'uniforms_sku_size_color_idx');
            $table->index(['stock_reserved', 'stock_reserved_at'], 'uniforms_reserved_idx');
            $table->index('fulfillment_status', 'uniforms_fulfill_status_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('uniforms', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['uniform_inventory_id', 'fulfillment_status']);
            $table->dropIndex(['sku', 'size', 'color']);
            $table->dropIndex(['stock_reserved', 'stock_reserved_at']);
            $table->dropIndex(['fulfillment_status']);

            // Drop foreign key constraints
            $table->dropForeign(['uniform_inventory_id']);
            $table->dropForeign(['fulfilled_by']);

            // Drop columns
            $table->dropColumn([
                'uniform_inventory_id',
                'sku',
                'color',
                'fulfillment_status',
                'stock_reserved',
                'stock_reserved_at',
                'stock_released_at',
                'fulfilled_by',
                'fulfilled_at',
                'fulfillment_notes'
            ]);
        });
    }
};
