<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->index();
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, integer, boolean, json, file
            $table->string('category')->default('general'); // general, academy, payment, notification, security, system
            $table->string('label');
            $table->text('description')->nullable();
            $table->json('validation_rules')->nullable(); // Store validation rules as JSON
            $table->json('options')->nullable(); // For select/radio options
            $table->boolean('is_public')->default(false); // Can be accessed by non-admin users
            $table->boolean('is_encrypted')->default(false); // Should be encrypted
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });

        // Insert default settings
        $this->insertDefaultSettings();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }

    /**
     * Insert default settings for UAE English Sports Academy
     */
    private function insertDefaultSettings(): void
    {
        $defaultSettings = [
            // General Settings
            [
                'key' => 'app_name',
                'value' => 'UAE English Sports Academy',
                'type' => 'string',
                'category' => 'general',
                'label' => 'Application Name',
                'description' => 'The name of your sports academy system',
                'validation_rules' => json_encode(['required', 'string', 'max:255']),
                'is_public' => true,
                'sort_order' => 1,
            ],
            [
                'key' => 'app_logo',
                'value' => 'images/logo.jpg',
                'type' => 'file',
                'category' => 'general',
                'label' => 'Application Logo',
                'description' => 'Upload your academy logo (recommended size: 200x60px)',
                'validation_rules' => json_encode(['nullable', 'image', 'max:2048']),
                'is_public' => true,
                'sort_order' => 2,
            ],
            [
                'key' => 'app_timezone',
                'value' => 'Asia/Dubai',
                'type' => 'select',
                'category' => 'general',
                'label' => 'Default Timezone',
                'description' => 'Default timezone for the application',
                'validation_rules' => json_encode(['required', 'string']),
                'options' => json_encode([
                    'Asia/Dubai' => 'Dubai (UTC+4)',
                    'Asia/Riyadh' => 'Riyadh (UTC+3)',
                    'Asia/Kuwait' => 'Kuwait (UTC+3)',
                    'Asia/Qatar' => 'Qatar (UTC+3)',
                ]),
                'is_public' => true,
                'sort_order' => 3,
            ],
            [
                'key' => 'default_language',
                'value' => 'en',
                'type' => 'select',
                'category' => 'general',
                'label' => 'Default Language',
                'description' => 'Default language for the application',
                'validation_rules' => json_encode(['required', 'string']),
                'options' => json_encode([
                    'en' => 'English',
                    'ar' => 'Arabic (العربية)',
                ]),
                'is_public' => true,
                'sort_order' => 4,
            ],
            [
                'key' => 'app_description',
                'value' => 'Professional Sports Academy Management System',
                'type' => 'textarea',
                'category' => 'general',
                'label' => 'Application Description',
                'description' => 'Brief description of your academy',
                'validation_rules' => json_encode(['nullable', 'string', 'max:500']),
                'is_public' => true,
                'sort_order' => 5,
            ],

            // Academy Settings
            [
                'key' => 'default_program_duration',
                'value' => '30',
                'type' => 'integer',
                'category' => 'academy',
                'label' => 'Default Program Duration (Days)',
                'description' => 'Default duration for new programs in days',
                'validation_rules' => json_encode(['required', 'integer', 'min:1', 'max:365']),
                'is_public' => false,
                'sort_order' => 10,
            ],
            [
                'key' => 'max_students_per_program',
                'value' => '25',
                'type' => 'integer',
                'category' => 'academy',
                'label' => 'Maximum Students per Program',
                'description' => 'Maximum number of students allowed in a single program',
                'validation_rules' => json_encode(['required', 'integer', 'min:1', 'max:100']),
                'is_public' => false,
                'sort_order' => 11,
            ],
            [
                'key' => 'auto_generate_student_id',
                'value' => '1',
                'type' => 'boolean',
                'category' => 'academy',
                'label' => 'Auto Generate Student IDs',
                'description' => 'Automatically generate unique student IDs',
                'validation_rules' => json_encode(['required', 'boolean']),
                'is_public' => false,
                'sort_order' => 12,
            ],

            // Payment Settings
            [
                'key' => 'default_currency',
                'value' => 'AED',
                'type' => 'select',
                'category' => 'payment',
                'label' => 'Default Currency',
                'description' => 'Default currency for all transactions',
                'validation_rules' => json_encode(['required', 'string']),
                'options' => json_encode([
                    'AED' => 'UAE Dirham (AED)',
                    'SAR' => 'Saudi Riyal (SAR)',
                    'USD' => 'US Dollar (USD)',
                ]),
                'is_public' => true,
                'sort_order' => 20,
            ],
            [
                'key' => 'payment_methods',
                'value' => json_encode(['cash', 'card', 'bank_transfer']),
                'type' => 'json',
                'category' => 'payment',
                'label' => 'Available Payment Methods',
                'description' => 'Payment methods available for transactions',
                'validation_rules' => json_encode(['required', 'array']),
                'options' => json_encode([
                    'cash' => 'Cash',
                    'card' => 'Credit/Debit Card',
                    'bank_transfer' => 'Bank Transfer',
                    'online' => 'Online Payment',
                ]),
                'is_public' => false,
                'sort_order' => 21,
            ],
            [
                'key' => 'late_payment_fee',
                'value' => '50',
                'type' => 'decimal',
                'category' => 'payment',
                'label' => 'Late Payment Fee (AED)',
                'description' => 'Fee charged for late payments',
                'validation_rules' => json_encode(['required', 'numeric', 'min:0']),
                'is_public' => false,
                'sort_order' => 22,
            ],

            // Notification Settings
            [
                'key' => 'email_notifications',
                'value' => '1',
                'type' => 'boolean',
                'category' => 'notification',
                'label' => 'Enable Email Notifications',
                'description' => 'Send email notifications for important events',
                'validation_rules' => json_encode(['required', 'boolean']),
                'is_public' => false,
                'sort_order' => 30,
            ],
            [
                'key' => 'sms_notifications',
                'value' => '0',
                'type' => 'boolean',
                'category' => 'notification',
                'label' => 'Enable SMS Notifications',
                'description' => 'Send SMS notifications for important events',
                'validation_rules' => json_encode(['required', 'boolean']),
                'is_public' => false,
                'sort_order' => 31,
            ],

            // Security Settings
            [
                'key' => 'session_timeout',
                'value' => '120',
                'type' => 'integer',
                'category' => 'security',
                'label' => 'Session Timeout (Minutes)',
                'description' => 'Automatic logout after inactivity',
                'validation_rules' => json_encode(['required', 'integer', 'min:15', 'max:480']),
                'is_public' => false,
                'sort_order' => 40,
            ],
            [
                'key' => 'password_min_length',
                'value' => '8',
                'type' => 'integer',
                'category' => 'security',
                'label' => 'Minimum Password Length',
                'description' => 'Minimum required password length',
                'validation_rules' => json_encode(['required', 'integer', 'min:6', 'max:50']),
                'is_public' => false,
                'sort_order' => 41,
            ],

            // System Settings
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'category' => 'system',
                'label' => 'Maintenance Mode',
                'description' => 'Enable maintenance mode to restrict access',
                'validation_rules' => json_encode(['required', 'boolean']),
                'is_public' => false,
                'sort_order' => 50,
            ],
            [
                'key' => 'backup_frequency',
                'value' => 'daily',
                'type' => 'select',
                'category' => 'system',
                'label' => 'Backup Frequency',
                'description' => 'How often to create system backups',
                'validation_rules' => json_encode(['required', 'string']),
                'options' => json_encode([
                    'daily' => 'Daily',
                    'weekly' => 'Weekly',
                    'monthly' => 'Monthly',
                ]),
                'is_public' => false,
                'sort_order' => 51,
            ],
        ];

        foreach ($defaultSettings as $setting) {
            \DB::table('settings')->insert(array_merge($setting, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
};
