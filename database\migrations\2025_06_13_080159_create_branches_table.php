<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branches', function (Blueprint $table) {
            $table->id();
            $table->string('name')->charset('utf8mb4');
            $table->text('location')->charset('utf8mb4');
            $table->string('phone', 15)->nullable(); // UAE format: +971XXXXXXXXX
            $table->string('email')->nullable();
            $table->text('address')->charset('utf8mb4')->nullable();
            $table->boolean('status')->default(true); // active/inactive
            $table->timestamps();

            // Indexes
            $table->index('status');
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branches');
    }
};
