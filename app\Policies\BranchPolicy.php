<?php

namespace App\Policies;

use App\Models\Branch;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class BranchPolicy
{
    /**
     * Determine whether the user can view any branches.
     */
    public function viewAny(User $user): bool
    {
        // Only admin, branch managers, and academy managers can view branches
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the branch.
     */
    public function view(User $user, Branch $branch): bool
    {
        // Admin can view any branch
        if ($user->role === 'admin') {
            return true;
        }

        // Branch managers can only view their assigned branch
        if ($user->role === 'branch_manager') {
            return $user->branch_id === $branch->id;
        }

        // Academy managers can only view their assigned branch
        if ($user->role === 'academy_manager') {
            return $user->branch_id === $branch->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create branches.
     */
    public function create(User $user): bool
    {
        // Only admin can create branches
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can update the branch.
     */
    public function update(User $user, Branch $branch): bool
    {
        // Admin can update any branch
        if ($user->role === 'admin') {
            return true;
        }

        // Branch managers can only update their assigned branch
        if ($user->role === 'branch_manager') {
            return $user->branch_id === $branch->id;
        }

        // Academy managers cannot update branches
        return false;
    }

    /**
     * Determine whether the user can delete the branch.
     */
    public function delete(User $user, Branch $branch): bool
    {
        // Only admin can delete branches
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can restore the branch.
     */
    public function restore(User $user, Branch $branch): bool
    {
        // Only admin can restore branches
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can permanently delete the branch.
     */
    public function forceDelete(User $user, Branch $branch): bool
    {
        // Only admin can permanently delete branches
        return $user->role === 'admin';
    }
}
