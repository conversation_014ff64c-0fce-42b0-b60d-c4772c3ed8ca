<!-- Advanced Search & Filters -->
<div class="bank-card">
    <div class="bank-card-header">
        <h3 class="bank-card-title">Search & Filters</h3>
        <button @click="showFilters = !showFilters"
            class="btn-bank btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                </path>
            </svg>
            <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
        </button>
    </div>

    <div class="bank-card-body" x-show="showFilters" x-transition x-data="{ showFilters: true }">
        <form method="GET" action="{{ route('students.index') }}" class="space-y-4">
            <!-- Search Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="md:col-span-2">
                    <label class="form-label-bank">Search Students</label>
                    <div class="relative">
                        <input type="text" name="search" value="{{ request('search') }}"
                            placeholder="Search by name, email, phone, or nationality..." class="form-input-bank pl-10">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="form-label-bank">Status</label>
                    <select name="status" class="form-select-bank">
                        <option value="">All Statuses</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive
                        </option>
                        <option value="suspended" {{ request('status') === 'suspended' ? 'selected' : '' }}>Suspended
                        </option>
                    </select>
                </div>
            </div>

            <!-- Filter Row -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="form-label-bank">Branch</label>
                    <select name="branch_id" class="form-select-bank" x-data="branchFilter()"
                        @change="onBranchChange($event)">
                        <option value="">All Branches</option>
                        @foreach ($branches as $branch)
                            <option value="{{ $branch->id }}"
                                {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="form-label-bank">Academy</label>
                    <select name="academy_id" class="form-select-bank" id="academy-filter">
                        <option value="">All Academies</option>
                        @foreach ($academies as $academy)
                            <option value="{{ $academy->id }}" data-branch="{{ $academy->branch_id }}"
                                {{ request('academy_id') == $academy->id ? 'selected' : '' }}>
                                {{ $academy->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label class="form-label-bank">Nationality</label>
                    <input type="text" name="nationality" value="{{ request('nationality') }}"
                        placeholder="e.g., UAE, India, Pakistan..." class="form-input-bank">
                </div>
                <div>
                    <label class="form-label-bank">Per Page</label>
                    <select name="per_page" class="form-select-bank">
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>
            </div>

            <!-- Age and Date Filters -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="form-label-bank">Min Age</label>
                    <input type="number" name="min_age" value="{{ request('min_age') }}" placeholder="e.g., 5"
                        min="0" max="100" class="form-input-bank">
                </div>
                <div>
                    <label class="form-label-bank">Max Age</label>
                    <input type="number" name="max_age" value="{{ request('max_age') }}" placeholder="e.g., 18"
                        min="0" max="100" class="form-input-bank">
                </div>
                <div>
                    <label class="form-label-bank">Join Date From</label>
                    <input type="date" name="join_start_date" value="{{ request('join_start_date') }}"
                        class="form-input-bank">
                </div>
                <div>
                    <label class="form-label-bank">Join Date To</label>
                    <input type="date" name="join_end_date" value="{{ request('join_end_date') }}"
                        class="form-input-bank">
                </div>
            </div>

            <!-- Sorting Options -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="form-label-bank">Sort By</label>
                    <select name="sort_by" class="form-select-bank">
                        <option value="created_at"
                            {{ request('sort_by', 'created_at') === 'created_at' ? 'selected' : '' }}>Registration Date
                        </option>
                        <option value="full_name" {{ request('sort_by') === 'full_name' ? 'selected' : '' }}>Name
                        </option>
                        <option value="join_date" {{ request('sort_by') === 'join_date' ? 'selected' : '' }}>Join Date
                        </option>
                        <option value="birth_date" {{ request('sort_by') === 'birth_date' ? 'selected' : '' }}>Birth
                            Date</option>
                        <option value="status" {{ request('sort_by') === 'status' ? 'selected' : '' }}>Status</option>
                    </select>
                </div>
                <div>
                    <label class="form-label-bank">Sort Order</label>
                    <select name="sort_order" class="form-select-bank">
                        <option value="desc" {{ request('sort_order', 'desc') === 'desc' ? 'selected' : '' }}>Newest
                            First</option>
                        <option value="asc" {{ request('sort_order') === 'asc' ? 'selected' : '' }}>Oldest First
                        </option>
                    </select>
                </div>
                <div class="flex items-end space-x-2">
                    <button type="submit" class="btn-bank flex-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Apply Filters
                    </button>
                    <a href="{{ route('students.index') }}"
                        class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                            </path>
                        </svg>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

@push('scripts')
    <script>
        function branchFilter() {
            return {
                onBranchChange(event) {
                    const branchId = event.target.value;
                    const academySelect = document.getElementById('academy-filter');
                    const academyOptions = academySelect.querySelectorAll('option[data-branch]');

                    // Show/hide academy options based on selected branch
                    academyOptions.forEach(option => {
                        if (!branchId || option.dataset.branch === branchId) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                        }
                    });

                    // Reset academy selection if current selection is not valid for new branch
                    if (branchId && academySelect.value) {
                        const selectedOption = academySelect.querySelector(`option[value="${academySelect.value}"]`);
                        if (selectedOption && selectedOption.dataset.branch !== branchId) {
                            academySelect.value = '';
                        }
                    }
                }
            }
        }
    </script>
@endpush
