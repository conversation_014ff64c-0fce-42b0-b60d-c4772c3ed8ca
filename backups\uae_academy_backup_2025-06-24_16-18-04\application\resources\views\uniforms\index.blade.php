@extends('layouts.dashboard')

@section('page-header')
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-charcoal-black">Uniform Management</h1>
            <p class="mt-1 text-sm text-dark-gray">Manage student uniform orders, track delivery status, and monitor
                inventory
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-leaders-red/10 text-leaders-red">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                {{ $uniforms->total() }} Total Orders
            </span>
        </div>
        <div class="flex items-center space-x-3">
            @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                <button @click="$dispatch('open-modal', 'create-uniform')" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                        </path>
                    </svg>
                    New Uniform Order
                </button>
            @endif
            @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                <div class="flex items-center space-x-2">
                    <a href="{{ route('uniforms.export.excel', request()->query()) }}" class="btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Excel
                    </a>
                    <a href="{{ route('uniforms.export.pdf', request()->query()) }}" class="btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                            </path>
                        </svg>
                        PDF
                    </a>
                </div>
            @endif
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="uniformManagement()" x-init="init()">
        <!-- Advanced Search & Filters -->
        @include('uniforms._filters')

        <!-- Statistics Cards -->
        @include('uniforms._stats')

        <!-- Bulk Actions Bar -->
        <div x-show="selectedUniforms.length > 0" x-transition class="bank-card bg-leaders-red/5 border-leaders-red/20">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-sm font-medium text-leaders-red">
                        <span x-text="selectedUniforms.length"></span> orders selected
                    </span>
                    <div class="flex items-center space-x-2">
                        <select x-model="bulkAction" class="form-select-bank text-sm">
                            <option value="">Choose action...</option>
                            <option value="update_status">Update Status</option>
                            <option value="mark_delivered">Mark as Delivered</option>
                            <option value="cancel">Cancel Orders</option>
                            @if (auth()->user()->role === 'admin')
                                <option value="delete">Delete Orders</option>
                            @endif
                        </select>
                        <select x-show="bulkAction === 'update_status'" x-model="bulkStatus"
                            class="form-select-bank text-sm">
                            <option value="">Select status...</option>
                            <option value="ordered">Ordered</option>
                            <option value="processing">Processing</option>
                            <option value="ready">Ready for Pickup</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                        <button @click="executeBulkAction()" :disabled="!bulkAction" class="btn-bank-sm">
                            Execute
                        </button>
                    </div>
                </div>
                <button @click="clearSelection()" class="text-sm text-dark-gray hover:text-leaders-red">
                    Clear selection
                </button>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div class="flex items-center justify-between w-full">
                    <div class="flex items-center space-x-4">
                        <h3 class="text-lg font-semibold text-charcoal-black">Uniform Orders</h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-dark-gray">View:</span>
                            <div class="flex rounded-lg border border-medium-gray overflow-hidden">
                                <button @click="viewMode = 'table'"
                                    :class="viewMode === 'table' ? 'bg-leaders-red text-white' :
                                        'bg-white text-dark-gray hover:bg-light-gray'"
                                    class="px-3 py-1 text-sm transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 10h18M3 6h18m-9 8h9m-9 4h9m-9-8h9m-9 4h9"></path>
                                    </svg>
                                </button>
                                <button @click="viewMode = 'grid'"
                                    :class="viewMode === 'grid' ? 'bg-leaders-red text-white' :
                                        'bg-white text-dark-gray hover:bg-light-gray'"
                                    class="px-3 py-1 text-sm transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                        </path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bank-card-body p-0">
                <!-- Table View -->
                <div x-show="viewMode === 'table'" x-transition>
                    @include('uniforms._table')
                </div>

                <!-- Grid View -->
                <div x-show="viewMode === 'grid'" x-transition>
                    @include('uniforms._grid')
                </div>

                <!-- Pagination -->
                @if ($uniforms->hasPages())
                    <div class="px-6 py-4 border-t border-light-gray">
                        {{ $uniforms->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Create Uniform Modal -->
    @include('uniforms._create_modal')

    <!-- Edit Uniform Modal -->
    @include('uniforms._edit_modal')
@endsection

@push('scripts')
    <script>
        function uniformManagement() {
            return {
                viewMode: 'table',
                selectedUniforms: [],
                bulkAction: '',
                bulkStatus: '',

                init() {
                    // Initialize component
                },

                toggleUniformSelection(uniformId) {
                    const index = this.selectedUniforms.indexOf(uniformId);
                    if (index > -1) {
                        this.selectedUniforms.splice(index, 1);
                    } else {
                        this.selectedUniforms.push(uniformId);
                    }
                },

                selectAllUniforms() {
                    const checkboxes = document.querySelectorAll('input[name="uniform_ids[]"]');
                    if (this.selectedUniforms.length === checkboxes.length) {
                        this.selectedUniforms = [];
                    } else {
                        this.selectedUniforms = Array.from(checkboxes).map(cb => parseInt(cb.value));
                    }
                },

                clearSelection() {
                    this.selectedUniforms = [];
                    this.bulkAction = '';
                    this.bulkStatus = '';
                },

                async executeBulkAction() {
                    if (!this.bulkAction || this.selectedUniforms.length === 0) return;

                    const data = {
                        action: this.bulkAction,
                        uniform_ids: this.selectedUniforms
                    };

                    if (this.bulkAction === 'update_status' && this.bulkStatus) {
                        data.status = this.bulkStatus;
                    }

                    try {
                        const response = await fetch('{{ route('uniforms.bulk-action') }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify(data)
                        });

                        const result = await response.json();

                        if (result.success) {
                            window.location.reload();
                        } else {
                            alert('Error: ' + result.message);
                        }
                    } catch (error) {
                        alert('An error occurred while processing the request');
                    }
                },

                async toggleStatus(uniformId) {
                    try {
                        const response = await fetch(`/uniforms/${uniformId}/toggle-status`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            window.location.reload();
                        } else {
                            alert('Error: ' + result.message);
                        }
                    } catch (error) {
                        alert('An error occurred while updating the status');
                    }
                },

                async deleteUniform(uniformId) {
                    if (!confirm('Are you sure you want to delete this uniform order? This action cannot be undone.')) {
                        return;
                    }

                    try {
                        const response = await fetch(`/uniforms/${uniformId}`, {
                            method: 'DELETE',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            window.location.reload();
                        } else {
                            alert('Error: ' + result.message);
                        }
                    } catch (error) {
                        alert('An error occurred while deleting the uniform order');
                    }
                }
            }
        }
    </script>
@endpush
