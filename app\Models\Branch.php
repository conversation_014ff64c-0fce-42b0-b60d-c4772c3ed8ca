<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\DB;

class Branch extends Model
{
    use HasFactory;
    protected $fillable = [
        'name',
        'location',
        'phone',
        'email',
        'address',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'student_count',
        'academy_count',
        'program_count',
        'coach_count',
        'total_revenue',
        'status_text',
        'formatted_phone',
    ];

    /**
     * Get the academies for the branch.
     */
    public function academies(): HasMany
    {
        return $this->hasMany(Academy::class);
    }

    /**
     * Get the active academies for the branch.
     */
    public function activeAcademies(): HasMany
    {
        return $this->hasMany(Academy::class)->where('status', true);
    }

    /**
     * Get the students for the branch.
     */
    public function students(): Has<PERSON>any
    {
        return $this->hasMany(Student::class);
    }

    /**
     * Get the active students for the branch.
     */
    public function activeStudents(): HasMany
    {
        return $this->hasMany(Student::class)->where('status', 'active');
    }

    /**
     * Get the programs through academies.
     */
    public function programs(): HasManyThrough
    {
        return $this->hasManyThrough(Program::class, Academy::class);
    }

    /**
     * Get the payments for the branch.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the completed payments for the branch.
     */
    public function completedPayments(): HasMany
    {
        return $this->hasMany(Payment::class)->where('status', 'completed');
    }

    /**
     * Get the uniforms for the branch.
     */
    public function uniforms(): HasMany
    {
        return $this->hasMany(Uniform::class);
    }

    /**
     * Get the users for the branch.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the branch managers for the branch.
     */
    public function branchManagers(): HasMany
    {
        return $this->hasMany(User::class)->where('role', 'branch_manager');
    }

    /**
     * Get the academy managers for the branch.
     */
    public function academyManagers(): HasMany
    {
        return $this->hasMany(User::class)->where('role', 'academy_manager');
    }

    /**
     * Get the student count attribute.
     */
    protected function studentCount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->students()->count(),
        );
    }

    /**
     * Get the academy count attribute.
     */
    protected function academyCount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->academies()->count(),
        );
    }

    /**
     * Get the program count attribute.
     */
    protected function programCount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->programs()->count(),
        );
    }

    /**
     * Get the coach count attribute.
     */
    protected function coachCount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->academies()->whereNotNull('coach_name')->count(),
        );
    }

    /**
     * Get the total revenue attribute.
     */
    protected function totalRevenue(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->completedPayments()->sum('amount'),
        );
    }

    /**
     * Get the status text attribute.
     */
    protected function statusText(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->status ? 'Active' : 'Inactive',
        );
    }

    /**
     * Get the formatted phone attribute.
     */
    protected function formattedPhone(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (!$this->phone) return null;

                // Format UAE phone number
                $phone = preg_replace('/[^0-9]/', '', $this->phone);

                if (strlen($phone) === 9) {
                    // Add UAE country code
                    $phone = '971' . $phone;
                }

                if (strlen($phone) === 12 && substr($phone, 0, 3) === '971') {
                    return '+971 ' . substr($phone, 3, 2) . ' ' . substr($phone, 5, 3) . ' ' . substr($phone, 8);
                }

                return $this->phone;
            },
        );
    }

    /**
     * Scope a query to only include active branches.
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include inactive branches.
     */
    public function scopeInactive($query)
    {
        return $query->where('status', false);
    }

    /**
     * Scope a query to search branches by name or location.
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('location', 'like', "%{$search}%")
                ->orWhere('address', 'like', "%{$search}%");
        });
    }

    /**
     * Get branch statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_academies' => $this->academies()->count(),
            'active_academies' => $this->activeAcademies()->count(),
            'total_programs' => $this->programs()->count(),
            'total_students' => $this->students()->count(),
            'active_students' => $this->activeStudents()->count(),
            'total_coaches' => $this->academies()->whereNotNull('coach_name')->count(),
            'total_revenue' => $this->completedPayments()->sum('amount'),
            'pending_payments' => $this->payments()->where('status', 'pending')->sum('amount'),
            'total_uniforms' => $this->uniforms()->count(),
            'pending_uniforms' => $this->uniforms()->where('status', 'pending')->count(),
        ];
    }

    /**
     * Get all programs with their days and prices.
     */
    public function getProgramsWithDetails()
    {
        return $this->programs()
            ->with('academy')
            ->select([
                'programs.*',
                DB::raw('CASE
                    WHEN JSON_CONTAINS(days, \'"sunday"\') THEN "Sun " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"monday"\') THEN "Mon " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"tuesday"\') THEN "Tue " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"wednesday"\') THEN "Wed " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"thursday"\') THEN "Thu " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"friday"\') THEN "Fri " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"saturday"\') THEN "Sat " ELSE "" END
                    as formatted_days')
            ])
            ->get();
    }
}
