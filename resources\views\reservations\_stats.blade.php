@php
    $stats = [
        'total' => $reservations->total(),
        'today' => \App\Models\Reservation::today()->count(),
        'upcoming' => \App\Models\Reservation::upcoming()->count(),
        'confirmed' => \App\Models\Reservation::confirmed()->count(),
        'pending' => \App\Models\Reservation::where('status', 'pending')->count(),
        'completed' => \App\Models\Reservation::where('status', 'completed')->count(),
        'revenue' => \App\Models\Reservation::where('payment_status', 'paid')->sum('total_amount'),
        'pending_revenue' => \App\Models\Reservation::where('payment_status', 'unpaid')->sum('total_amount'),
    ];
@endphp

<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Reservations -->
    <div class="bank-card">
        <div class="bank-card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-8 0H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-dark-gray">Total Reservations</p>
                    <p class="text-2xl font-bold text-charcoal-black">{{ number_format($stats['total']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Reservations -->
    <div class="bank-card">
        <div class="bank-card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-dark-gray">Today's Reservations</p>
                    <p class="text-2xl font-bold text-charcoal-black">{{ number_format($stats['today']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Reservations -->
    <div class="bank-card">
        <div class="bank-card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-dark-gray">Upcoming</p>
                    <p class="text-2xl font-bold text-charcoal-black">{{ number_format($stats['upcoming']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Confirmed Reservations -->
    <div class="bank-card">
        <div class="bank-card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-dark-gray">Confirmed</p>
                    <p class="text-2xl font-bold text-charcoal-black">{{ number_format($stats['confirmed']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Reservations -->
    <div class="bank-card">
        <div class="bank-card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-dark-gray">Pending Approval</p>
                    <p class="text-2xl font-bold text-charcoal-black">{{ number_format($stats['pending']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Completed Reservations -->
    <div class="bank-card">
        <div class="bank-card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-dark-gray">Completed</p>
                    <p class="text-2xl font-bold text-charcoal-black">{{ number_format($stats['completed']) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Revenue -->
    <div class="bank-card">
        <div class="bank-card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-dark-gray">Total Revenue</p>
                    <p class="text-2xl font-bold text-charcoal-black">AED {{ number_format($stats['revenue'], 2) }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Revenue -->
    <div class="bank-card">
        <div class="bank-card-body">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-dark-gray">Pending Revenue</p>
                    <p class="text-2xl font-bold text-charcoal-black">AED
                        {{ number_format($stats['pending_revenue'], 2) }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
