<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class FileUploadService
{
    /**
     * Upload and validate image file with comprehensive error handling.
     *
     * @param UploadedFile $file
     * @param string $directory
     * @param array $options
     * @return array
     */
    public static function uploadImage(UploadedFile $file, string $directory, array $options = []): array
    {
        try {
            // Default options
            $options = array_merge([
                'max_size' => 2048, // KB
                'allowed_types' => ['jpeg', 'jpg', 'png', 'gif'],
                'disk' => 'public',
                'generate_unique_name' => true,
                'old_file_path' => null
            ], $options);

            // Validate file
            $validation = self::validateImageFile($file, $options);
            if (!$validation['success']) {
                return $validation;
            }

            // Ensure directory exists
            $fullDirectory = storage_path('app/public/' . $directory);
            if (!is_dir($fullDirectory)) {
                if (!mkdir($fullDirectory, 0755, true)) {
                    return [
                        'success' => false,
                        'error' => 'Unable to create storage directory.',
                        'error_code' => 'DIRECTORY_CREATION_FAILED'
                    ];
                }
            }

            // Check directory permissions
            if (!is_writable($fullDirectory)) {
                return [
                    'success' => false,
                    'error' => 'Storage directory is not writable.',
                    'error_code' => 'DIRECTORY_NOT_WRITABLE'
                ];
            }

            // Delete old file if specified
            if ($options['old_file_path'] && Storage::disk($options['disk'])->exists($options['old_file_path'])) {
                try {
                    Storage::disk($options['disk'])->delete($options['old_file_path']);
                    Log::info('Old file deleted successfully', ['file_path' => $options['old_file_path']]);
                } catch (\Exception $e) {
                    Log::warning('Failed to delete old file', [
                        'file_path' => $options['old_file_path'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // Generate filename
            $filename = $options['generate_unique_name'] 
                ? time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension()
                : $file->getClientOriginalName();

            // Store file
            $filePath = $file->storeAs($directory, $filename, $options['disk']);

            if (!$filePath) {
                return [
                    'success' => false,
                    'error' => 'Failed to store file.',
                    'error_code' => 'FILE_STORE_FAILED'
                ];
            }

            // Verify file was actually stored
            if (!Storage::disk($options['disk'])->exists($filePath)) {
                return [
                    'success' => false,
                    'error' => 'File was not properly stored.',
                    'error_code' => 'FILE_VERIFICATION_FAILED'
                ];
            }

            Log::info('File uploaded successfully', [
                'original_name' => $file->getClientOriginalName(),
                'stored_name' => $filename,
                'file_path' => $filePath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ]);

            return [
                'success' => true,
                'file_path' => $filePath,
                'filename' => $filename,
                'original_name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType()
            ];

        } catch (\Exception $e) {
            Log::error('File upload failed with exception', [
                'error' => $e->getMessage(),
                'file_name' => $file->getClientOriginalName() ?? 'unknown',
                'file_size' => $file->getSize() ?? 0,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'File upload failed: ' . $e->getMessage(),
                'error_code' => 'UPLOAD_EXCEPTION'
            ];
        }
    }

    /**
     * Validate image file.
     *
     * @param UploadedFile $file
     * @param array $options
     * @return array
     */
    private static function validateImageFile(UploadedFile $file, array $options): array
    {
        // Check if file is valid
        if (!$file->isValid()) {
            return [
                'success' => false,
                'error' => 'Invalid file uploaded.',
                'error_code' => 'INVALID_FILE'
            ];
        }

        // Check file size
        $maxSizeBytes = $options['max_size'] * 1024; // Convert KB to bytes
        if ($file->getSize() > $maxSizeBytes) {
            return [
                'success' => false,
                'error' => 'File size exceeds maximum allowed size of ' . $options['max_size'] . 'KB.',
                'error_code' => 'FILE_TOO_LARGE',
                'max_size' => $options['max_size'],
                'actual_size' => round($file->getSize() / 1024, 2)
            ];
        }

        // Check file type
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $options['allowed_types'])) {
            return [
                'success' => false,
                'error' => 'Invalid file type. Allowed types: ' . implode(', ', $options['allowed_types']),
                'error_code' => 'INVALID_FILE_TYPE',
                'allowed_types' => $options['allowed_types'],
                'actual_type' => $extension
            ];
        }

        // Check MIME type
        $mimeType = $file->getMimeType();
        $allowedMimeTypes = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif'
        ];

        if (!in_array($mimeType, $allowedMimeTypes)) {
            return [
                'success' => false,
                'error' => 'Invalid MIME type. File may be corrupted or not a valid image.',
                'error_code' => 'INVALID_MIME_TYPE',
                'mime_type' => $mimeType
            ];
        }

        return ['success' => true];
    }

    /**
     * Delete file with error handling.
     *
     * @param string $filePath
     * @param string $disk
     * @return array
     */
    public static function deleteFile(string $filePath, string $disk = 'public'): array
    {
        try {
            if (!Storage::disk($disk)->exists($filePath)) {
                return [
                    'success' => false,
                    'error' => 'File does not exist.',
                    'error_code' => 'FILE_NOT_FOUND'
                ];
            }

            $deleted = Storage::disk($disk)->delete($filePath);

            if (!$deleted) {
                return [
                    'success' => false,
                    'error' => 'Failed to delete file.',
                    'error_code' => 'DELETE_FAILED'
                ];
            }

            Log::info('File deleted successfully', ['file_path' => $filePath]);

            return [
                'success' => true,
                'message' => 'File deleted successfully.'
            ];

        } catch (\Exception $e) {
            Log::error('File deletion failed', [
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'File deletion failed: ' . $e->getMessage(),
                'error_code' => 'DELETE_EXCEPTION'
            ];
        }
    }

    /**
     * Get human-readable error message for error codes.
     *
     * @param string $errorCode
     * @return string
     */
    public static function getErrorMessage(string $errorCode): string
    {
        $messages = [
            'INVALID_FILE' => 'The uploaded file is invalid or corrupted.',
            'FILE_TOO_LARGE' => 'The file size is too large.',
            'INVALID_FILE_TYPE' => 'The file type is not allowed.',
            'INVALID_MIME_TYPE' => 'The file format is not supported.',
            'DIRECTORY_CREATION_FAILED' => 'Unable to create storage directory.',
            'DIRECTORY_NOT_WRITABLE' => 'Storage directory permissions error.',
            'FILE_STORE_FAILED' => 'Failed to save the file.',
            'FILE_VERIFICATION_FAILED' => 'File upload verification failed.',
            'UPLOAD_EXCEPTION' => 'An unexpected error occurred during upload.',
            'FILE_NOT_FOUND' => 'The file to delete was not found.',
            'DELETE_FAILED' => 'Failed to delete the file.',
            'DELETE_EXCEPTION' => 'An error occurred while deleting the file.'
        ];

        return $messages[$errorCode] ?? 'An unknown error occurred.';
    }
}
