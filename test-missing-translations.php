<?php

// UAE English Sports Academy - Missing Translation Detector
// This script scans for missing translations in the system

echo "🔍 UAE English Sports Academy - Missing Translation Detector\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Define translation files to check
$translationFiles = [
    'en' => [
        'common' => 'resources/lang/en/common.php',
        'dashboard' => 'resources/lang/en/dashboard.php',
    ],
    'ar' => [
        'common' => 'resources/lang/ar/common.php', 
        'dashboard' => 'resources/lang/ar/dashboard.php',
    ]
];

// Load all translation keys
$translations = [];
foreach ($translationFiles as $locale => $files) {
    $translations[$locale] = [];
    foreach ($files as $context => $file) {
        if (file_exists($file)) {
            $content = include $file;
            if (is_array($content)) {
                $translations[$locale][$context] = $content;
            }
        }
    }
}

echo "📊 Translation File Status:\n";
foreach ($translationFiles as $locale => $files) {
    echo "  {$locale}:\n";
    foreach ($files as $context => $file) {
        $exists = file_exists($file);
        $count = $exists ? count($translations[$locale][$context] ?? []) : 0;
        $status = $exists ? "✅" : "❌";
        echo "    {$status} {$context}: {$count} keys ({$file})\n";
    }
}
echo "\n";

// Extract translation keys from views
echo "🔍 Scanning Views for Translation Keys:\n";

$viewFiles = glob('resources/views/**/*.blade.php');
$usedKeys = [];

foreach ($viewFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // Find __('context.key') patterns
        preg_match_all("/__\(['\"]([^'\"]+)['\"][\),]/", $content, $matches);
        
        foreach ($matches[1] as $key) {
            if (strpos($key, '.') !== false) {
                list($context, $keyName) = explode('.', $key, 2);
                $usedKeys[$context][$keyName] = $file;
            }
        }
        
        // Find data-trans-key patterns
        preg_match_all('/data-trans-key=["\']([^"\']+)["\']/', $content, $dataMatches);
        preg_match_all('/data-context=["\']([^"\']+)["\']/', $content, $contextMatches);
        
        for ($i = 0; $i < count($dataMatches[1]); $i++) {
            $keyName = $dataMatches[1][$i];
            $context = isset($contextMatches[1][$i]) ? $contextMatches[1][$i] : 'common';
            $usedKeys[$context][$keyName] = $file;
        }
    }
}

echo "📋 Found Translation Keys in Views:\n";
foreach ($usedKeys as $context => $keys) {
    echo "  {$context}: " . count($keys) . " keys\n";
}
echo "\n";

// Check for missing translations
echo "❌ Missing Translations:\n";

$missingCount = 0;
foreach ($usedKeys as $context => $keys) {
    $missingInContext = [];
    
    foreach ($keys as $key => $file) {
        // Check if key exists in English
        if (!isset($translations['en'][$context][$key])) {
            $missingInContext['en'][] = $key;
            $missingCount++;
        }
        
        // Check if key exists in Arabic
        if (!isset($translations['ar'][$context][$key])) {
            $missingInContext['ar'][] = $key;
            $missingCount++;
        }
    }
    
    if (!empty($missingInContext)) {
        echo "  📁 {$context}:\n";
        foreach ($missingInContext as $locale => $missingKeys) {
            if (!empty($missingKeys)) {
                echo "    🌐 {$locale}: " . implode(', ', array_slice($missingKeys, 0, 10));
                if (count($missingKeys) > 10) {
                    echo " ... and " . (count($missingKeys) - 10) . " more";
                }
                echo "\n";
            }
        }
    }
}

if ($missingCount === 0) {
    echo "  ✅ No missing translations found!\n";
} else {
    echo "  📊 Total missing: {$missingCount}\n";
}
echo "\n";

// Check for unused translations
echo "🗑️  Potentially Unused Translations:\n";

$unusedCount = 0;
foreach ($translations as $locale => $contexts) {
    foreach ($contexts as $context => $keys) {
        $unusedInContext = [];
        
        foreach ($keys as $key => $value) {
            if (!isset($usedKeys[$context][$key])) {
                $unusedInContext[] = $key;
                $unusedCount++;
            }
        }
        
        if (!empty($unusedInContext) && $locale === 'en') { // Only show for English to avoid duplication
            echo "  📁 {$context}: " . implode(', ', array_slice($unusedInContext, 0, 5));
            if (count($unusedInContext) > 5) {
                echo " ... and " . (count($unusedInContext) - 5) . " more";
            }
            echo "\n";
        }
    }
}

if ($unusedCount === 0) {
    echo "  ✅ All translations are being used!\n";
} else {
    echo "  📊 Total potentially unused: " . ($unusedCount / 2) . " (counting both languages)\n";
}
echo "\n";

// Generate missing translation templates
if ($missingCount > 0) {
    echo "🔧 Missing Translation Templates:\n";
    
    foreach ($usedKeys as $context => $keys) {
        $missingEn = [];
        $missingAr = [];
        
        foreach ($keys as $key => $file) {
            if (!isset($translations['en'][$context][$key])) {
                $missingEn[$key] = ucwords(str_replace(['_', '.'], ' ', $key));
            }
            if (!isset($translations['ar'][$context][$key])) {
                $missingAr[$key] = "// TODO: Add Arabic translation for '{$key}'";
            }
        }
        
        if (!empty($missingEn)) {
            echo "\n  📝 Add to resources/lang/en/{$context}.php:\n";
            foreach ($missingEn as $key => $suggestion) {
                echo "    '{$key}' => '{$suggestion}',\n";
            }
        }
        
        if (!empty($missingAr)) {
            echo "\n  📝 Add to resources/lang/ar/{$context}.php:\n";
            foreach ($missingAr as $key => $comment) {
                echo "    '{$key}' => '{$comment}',\n";
            }
        }
    }
}

echo "\n🎯 Summary:\n";
echo "  ✅ Translation files loaded: " . array_sum(array_map('count', $translationFiles)) . "\n";
echo "  📋 Unique keys found in views: " . array_sum(array_map('count', $usedKeys)) . "\n";
echo "  ❌ Missing translations: {$missingCount}\n";
echo "  🗑️  Potentially unused: " . ($unusedCount / 2) . "\n";

if ($missingCount === 0) {
    echo "\n🎉 All translation keys are properly defined!\n";
} else {
    echo "\n⚠️  Please add the missing translations shown above.\n";
}

echo "\n" . str_repeat("=", 70) . "\n";
echo "Translation scan completed!\n";
