<!-- Payments Grid View -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
    <?php $__empty_1 = true; $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="bank-card hover:shadow-lg transition-shadow duration-200">
            <!-- Card Header -->
            <div class="bank-card-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bulkAction', App\Models\Payment::class)): ?>
                            <input type="checkbox" name="payment_ids[]" value="<?php echo e($payment->id); ?>"
                                @change="togglePaymentSelection(<?php echo e($payment->id); ?>)" class="form-checkbox">
                        <?php endif; ?>
                        <h3 class="bank-card-title text-sm">
                            <?php echo e($payment->reference_number ?? 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT)); ?>

                        </h3>
                    </div>
                    <span class="badge-bank <?php echo e($payment->status_badge_class); ?>">
                        <?php echo e($payment->status_text); ?>

                    </span>
                </div>
            </div>

            <!-- Card Body -->
            <div class="bank-card-body">
                <!-- Student Information -->
                <div class="mb-4">
                    <div class="flex items-center space-x-2 mb-2">
                        <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <span class="font-medium text-charcoal-black"><?php echo e($payment->student->full_name ?? 'N/A'); ?></span>
                    </div>
                    <div class="text-sm text-dark-gray">
                        <div class="flex items-center space-x-1">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                </path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span><?php echo e($payment->branch->name ?? 'N/A'); ?></span>
                        </div>
                        <div class="flex items-center space-x-1 mt-1">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                </path>
                            </svg>
                            <span><?php echo e($payment->academy->name ?? 'N/A'); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Payment Amount -->
                <div class="mb-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-leaders-red">
                            AED <?php echo e(number_format($payment->amount, 2)); ?>

                        </div>
                        <?php if($payment->discount > 0): ?>
                            <div class="text-sm text-green-600">
                                Discount: AED <?php echo e(number_format($payment->discount, 2)); ?>

                            </div>
                            <div class="text-lg font-medium text-charcoal-black">
                                Net: AED <?php echo e(number_format($payment->net_amount, 2)); ?>

                            </div>
                        <?php endif; ?>
                        <div class="text-xs text-dark-gray mt-1">
                            <?php echo e($payment->method_text); ?>

                        </div>
                    </div>
                </div>

                <!-- Payment Dates -->
                <div class="mb-4 space-y-2">
                    <div class="flex justify-between text-sm">
                        <span class="text-dark-gray">Paid:</span>
                        <span class="text-charcoal-black font-medium">
                            <?php echo e($payment->formatted_payment_date ?: 'Not paid'); ?>

                        </span>
                    </div>
                    <div class="flex justify-between text-sm">
                        <span class="text-dark-gray">Period:</span>
                        <span class="text-charcoal-black">
                            <?php echo e($payment->formatted_start_date); ?> - <?php echo e($payment->formatted_end_date); ?>

                        </span>
                    </div>
                    <?php if($payment->class_time_from && $payment->class_time_to): ?>
                        <div class="flex justify-between text-sm">
                            <span class="text-dark-gray">Time:</span>
                            <span class="text-charcoal-black">
                                <?php echo e($payment->class_time_from); ?> - <?php echo e($payment->class_time_to); ?>

                            </span>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Additional Info -->
                <div class="mb-4">
                    <?php if($payment->renewal): ?>
                        <span
                            class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mb-2">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                </path>
                            </svg>
                            Renewal
                        </span>
                    <?php endif; ?>

                    <?php if($payment->description): ?>
                        <div class="text-xs text-gray-500 truncate" title="<?php echo e($payment->description); ?>">
                            <?php echo e($payment->description); ?>

                        </div>
                    <?php endif; ?>

                    <?php if($payment->note): ?>
                        <div class="text-xs text-gray-500 truncate mt-1" title="<?php echo e($payment->note); ?>">
                            Note: <?php echo e($payment->note); ?>

                        </div>
                    <?php endif; ?>
                </div>

                <!-- Actions -->
                <div class="flex items-center justify-between pt-4 border-t border-light-gray">
                    <div class="flex items-center space-x-2">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $payment)): ?>
                            <a href="<?php echo e(route('payments.show', $payment)); ?>"
                                class="text-blue-600 hover:text-blue-900 transition-colors" title="View Details">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                            </a>

                            <a href="<?php echo e(route('payments.invoice', $payment)); ?>" target="_blank"
                                class="text-green-600 hover:text-green-900 transition-colors" title="Print Invoice">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                                    </path>
                                </svg>
                            </a>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $payment)): ?>
                            <a href="<?php echo e(route('payments.edit', $payment)); ?>"
                                class="text-indigo-600 hover:text-indigo-900 transition-colors" title="Edit Payment">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                            </a>

                            <button @click="toggleStatus(<?php echo e($payment->id); ?>)"
                                class="text-yellow-600 hover:text-yellow-900 transition-colors" title="Toggle Status">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                </svg>
                            </button>
                        <?php endif; ?>

                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $payment)): ?>
                            <button @click="deletePayment(<?php echo e($payment->id); ?>)"
                                class="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded transition-all duration-200" title="Delete Payment">
                                <svg class="w-5 h-5" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                    </path>
                                </svg>
                            </button>
                        <?php endif; ?>
                    </div>

                    <div class="text-xs text-gray-500">
                        <?php echo e($payment->created_at->format('M d, Y')); ?>

                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-span-full">
            <div class="bank-card">
                <div class="bank-card-body text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z">
                        </path>
                    </svg>
                    <h3 class="text-lg font-medium text-dark-gray mb-2">No payments found</h3>
                    <p class="text-gray-500 mb-4">No payments match your current filters.</p>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Payment::class)): ?>
                        <a href="<?php echo e(route('payments.create')); ?>" class="btn-bank">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add First Payment
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php $__env->startPush('scripts'); ?>
    <script>
        // Grid-specific JavaScript functions
        async function toggleStatus(paymentId) {
            if (!confirm('Are you sure you want to toggle the payment status?')) {
                return;
            }

            try {
                const response = await fetch(`/payments/${paymentId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Reload the page to show updated status
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while updating the payment status.');
            }
        }


    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\xampp\htdocs\erp-login2\resources\views/payments/_grid.blade.php ENDPATH**/ ?>