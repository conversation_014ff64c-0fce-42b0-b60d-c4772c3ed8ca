{{-- Shared Filters Component for Reports --}}
<div class="bank-card mb-6" x-data="reportFilters()">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Filters</h3>
        <button @click="resetFilters()" class="text-sm text-gray-600 hover:text-gray-800">
            Reset Filters
        </button>
    </div>

    <form method="GET" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Date From -->
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                <input type="date" 
                       id="date_from" 
                       name="date_from" 
                       value="{{ $filters['date_from'] ?? '' }}"
                       class="form-input w-full">
            </div>

            <!-- Date To -->
            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                <input type="date" 
                       id="date_to" 
                       name="date_to" 
                       value="{{ $filters['date_to'] ?? '' }}"
                       class="form-input w-full">
            </div>

            <!-- Branch Filter -->
            @if(Auth::user()->role === 'admin')
                <div>
                    <label for="branch_id" class="block text-sm font-medium text-gray-700 mb-1">Branch</label>
                    <select id="branch_id" 
                            name="branch_id" 
                            class="form-select w-full"
                            @change="onBranchChange($event.target.value)">
                        <option value="">All Branches</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}" 
                                    {{ ($filters['branch_id'] ?? '') == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            @endif

            <!-- Academy Filter -->
            @if(in_array(Auth::user()->role, ['admin', 'branch_manager']))
                <div>
                    <label for="academy_id" class="block text-sm font-medium text-gray-700 mb-1">Academy</label>
                    <select id="academy_id" 
                            name="academy_id" 
                            class="form-select w-full">
                        <option value="">All Academies</option>
                        @foreach($academies as $academy)
                            @if(Auth::user()->role === 'admin' || Auth::user()->branch_id === $academy->branch_id)
                                <option value="{{ $academy->id }}" 
                                        {{ ($filters['academy_id'] ?? '') == $academy->id ? 'selected' : '' }}>
                                    {{ $academy->name }}
                                </option>
                            @endif
                        @endforeach
                    </select>
                </div>
            @endif
        </div>

        <!-- Additional Filters Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Status Filter (for applicable reports) -->
            @if(request()->routeIs('reports.financial') || request()->routeIs('reports.status'))
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="form-select w-full">
                        <option value="">All Statuses</option>
                        @if(request()->routeIs('reports.financial'))
                            <option value="completed" {{ ($filters['status'] ?? '') === 'completed' ? 'selected' : '' }}>Completed</option>
                            <option value="pending" {{ ($filters['status'] ?? '') === 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="failed" {{ ($filters['status'] ?? '') === 'failed' ? 'selected' : '' }}>Failed</option>
                        @else
                            <option value="active" {{ ($filters['status'] ?? '') === 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ ($filters['status'] ?? '') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="suspended" {{ ($filters['status'] ?? '') === 'suspended' ? 'selected' : '' }}>Suspended</option>
                        @endif
                    </select>
                </div>
            @endif

            <!-- Payment Method Filter (for financial reports) -->
            @if(request()->routeIs('reports.financial'))
                <div>
                    <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                    <select id="payment_method" name="payment_method" class="form-select w-full">
                        <option value="">All Methods</option>
                        <option value="cash" {{ ($filters['payment_method'] ?? '') === 'cash' ? 'selected' : '' }}>Cash</option>
                        <option value="card" {{ ($filters['payment_method'] ?? '') === 'card' ? 'selected' : '' }}>Card</option>
                        <option value="bank_transfer" {{ ($filters['payment_method'] ?? '') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                    </select>
                </div>
            @endif

            <!-- Period Filter -->
            <div>
                <label for="period" class="block text-sm font-medium text-gray-700 mb-1">Period</label>
                <select id="period" name="period" class="form-select w-full" @change="onPeriodChange($event.target.value)">
                    <option value="custom" {{ ($filters['period'] ?? 'month') === 'custom' ? 'selected' : '' }}>Custom</option>
                    <option value="today" {{ ($filters['period'] ?? 'month') === 'today' ? 'selected' : '' }}>Today</option>
                    <option value="week" {{ ($filters['period'] ?? 'month') === 'week' ? 'selected' : '' }}>This Week</option>
                    <option value="month" {{ ($filters['period'] ?? 'month') === 'month' ? 'selected' : '' }}>This Month</option>
                    <option value="quarter" {{ ($filters['period'] ?? 'month') === 'quarter' ? 'selected' : '' }}>This Quarter</option>
                    <option value="year" {{ ($filters['period'] ?? 'month') === 'year' ? 'selected' : '' }}>This Year</option>
                </select>
            </div>

            <!-- Apply Filters Button -->
            <div class="flex items-end">
                <button type="submit" class="btn-bank-primary w-full">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"></path>
                    </svg>
                    Apply Filters
                </button>
            </div>
        </div>

        <!-- Quick Filter Buttons -->
        <div class="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
            <span class="text-sm font-medium text-gray-700">Quick Filters:</span>
            <button type="button" @click="setQuickFilter('today')" class="quick-filter-btn">Today</button>
            <button type="button" @click="setQuickFilter('yesterday')" class="quick-filter-btn">Yesterday</button>
            <button type="button" @click="setQuickFilter('week')" class="quick-filter-btn">This Week</button>
            <button type="button" @click="setQuickFilter('month')" class="quick-filter-btn">This Month</button>
            <button type="button" @click="setQuickFilter('quarter')" class="quick-filter-btn">This Quarter</button>
            <button type="button" @click="setQuickFilter('year')" class="quick-filter-btn">This Year</button>
        </div>
    </form>
</div>

@push('styles')
<style>
.quick-filter-btn {
    @apply px-3 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-full hover:bg-gray-200 hover:text-gray-800 transition-colors duration-200;
}
</style>
@endpush

@push('scripts')
<script>
function reportFilters() {
    return {
        init() {
            // Initialize any default values
        },

        onBranchChange(branchId) {
            // Update academies based on selected branch
            const academySelect = document.getElementById('academy_id');
            if (academySelect) {
                // Reset academy selection
                academySelect.value = '';
                
                // You could implement AJAX loading of academies here
                // For now, we'll rely on the existing options
            }
        },

        onPeriodChange(period) {
            const dateFrom = document.getElementById('date_from');
            const dateTo = document.getElementById('date_to');
            const today = new Date();
            
            switch(period) {
                case 'today':
                    dateFrom.value = this.formatDate(today);
                    dateTo.value = this.formatDate(today);
                    break;
                case 'week':
                    const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
                    const weekEnd = new Date(today.setDate(today.getDate() - today.getDay() + 6));
                    dateFrom.value = this.formatDate(weekStart);
                    dateTo.value = this.formatDate(weekEnd);
                    break;
                case 'month':
                    const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                    const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    dateFrom.value = this.formatDate(monthStart);
                    dateTo.value = this.formatDate(monthEnd);
                    break;
                case 'quarter':
                    const quarter = Math.floor(today.getMonth() / 3);
                    const quarterStart = new Date(today.getFullYear(), quarter * 3, 1);
                    const quarterEnd = new Date(today.getFullYear(), quarter * 3 + 3, 0);
                    dateFrom.value = this.formatDate(quarterStart);
                    dateTo.value = this.formatDate(quarterEnd);
                    break;
                case 'year':
                    const yearStart = new Date(today.getFullYear(), 0, 1);
                    const yearEnd = new Date(today.getFullYear(), 11, 31);
                    dateFrom.value = this.formatDate(yearStart);
                    dateTo.value = this.formatDate(yearEnd);
                    break;
            }
        },

        setQuickFilter(period) {
            document.getElementById('period').value = period;
            this.onPeriodChange(period);
        },

        resetFilters() {
            // Reset all form fields
            document.querySelector('form').reset();
            
            // Set default period to month
            document.getElementById('period').value = 'month';
            this.onPeriodChange('month');
        },

        formatDate(date) {
            return date.toISOString().split('T')[0];
        }
    }
}
</script>
@endpush
