@extends('layouts.dashboard')

@section('title', 'Create New Venue - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Create New Venue</h1>
                <p class="text-lg text-dark-gray">Add a new sports venue to the system</p>
                <span class="badge-bank badge-info">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    New Venue Setup
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('venues.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Venues
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto" x-data="venueForm()" x-init="init()">
        <form @submit.prevent="submitForm()" class="space-y-6">
            @csrf

            <!-- Basic Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Basic Information</h3>
                        <p class="bank-card-subtitle">Enter the venue's basic details</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Venue Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label-bank required">Venue Name</label>
                            <input type="text" id="name" name="name" x-model="form.name" class="form-input-bank"
                                placeholder="Enter venue name (e.g., Leaders Sports Complex)" required>
                            <div x-show="errors.name" class="form-error" x-text="errors.name"></div>
                            <p class="form-help">Choose a descriptive name for the sports venue</p>
                        </div>

                        <!-- Venue Name (Arabic) -->
                        <div class="md:col-span-2">
                            <label for="name_ar" class="form-label-bank">Venue Name (Arabic)</label>
                            <input type="text" id="name_ar" name="name_ar" x-model="form.name_ar"
                                class="form-input-bank" placeholder="أدخل اسم المكان بالعربية" dir="rtl">
                            <div x-show="errors.name_ar" class="form-error" x-text="errors.name_ar"></div>
                            <p class="form-help">Optional: Arabic name for the venue</p>
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="description" class="form-label-bank">Description</label>
                            <textarea id="description" name="description" x-model="form.description" rows="3" class="form-textarea-bank"
                                placeholder="Enter venue description and facilities overview"></textarea>
                            <div x-show="errors.description" class="form-error" x-text="errors.description"></div>
                            <p class="form-help">Provide a brief description of the venue and its facilities</p>
                        </div>

                        <!-- Description (Arabic) -->
                        <div class="md:col-span-2">
                            <label for="description_ar" class="form-label-bank">Description (Arabic)</label>
                            <textarea id="description_ar" name="description_ar" x-model="form.description_ar" rows="3"
                                class="form-textarea-bank" placeholder="أدخل وصف المكان بالعربية" dir="rtl"></textarea>
                            <div x-show="errors.description_ar" class="form-error" x-text="errors.description_ar"></div>
                            <p class="form-help">Optional: Arabic description of the venue</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Location Information</h3>
                        <p class="bank-card-subtitle">Venue address and location details</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label for="address" class="form-label-bank required">Address</label>
                            <textarea id="address" name="address" x-model="form.address" rows="3" class="form-textarea-bank"
                                placeholder="Enter complete address including building, street, and landmarks" required></textarea>
                            <div x-show="errors.address" class="form-error" x-text="errors.address"></div>
                            <p class="form-help">Provide detailed address for easy location</p>
                        </div>

                        <!-- Address (Arabic) -->
                        <div class="md:col-span-2">
                            <label for="address_ar" class="form-label-bank">Address (Arabic)</label>
                            <textarea id="address_ar" name="address_ar" x-model="form.address_ar" rows="3" class="form-textarea-bank"
                                placeholder="أدخل العنوان بالعربية" dir="rtl"></textarea>
                            <div x-show="errors.address_ar" class="form-error" x-text="errors.address_ar"></div>
                            <p class="form-help">Optional: Arabic address</p>
                        </div>

                        <!-- City -->
                        <div>
                            <label for="city" class="form-label-bank required">City</label>
                            <input type="text" id="city" name="city" x-model="form.city"
                                class="form-input-bank" placeholder="Enter city (e.g., Dubai, Abu Dhabi)" required>
                            <div x-show="errors.city" class="form-error" x-text="errors.city"></div>
                        </div>

                        <!-- Country -->
                        <div>
                            <label for="country" class="form-label-bank required">Country</label>
                            <select id="country" name="country" x-model="form.country" class="form-select-bank"
                                required>
                                <option value="">Select country</option>
                                <option value="UAE">United Arab Emirates</option>
                                <option value="Saudi Arabia">Saudi Arabia</option>
                                <option value="Qatar">Qatar</option>
                                <option value="Kuwait">Kuwait</option>
                                <option value="Bahrain">Bahrain</option>
                                <option value="Oman">Oman</option>
                            </select>
                            <div x-show="errors.country" class="form-error" x-text="errors.country"></div>
                        </div>

                        <!-- Coordinates -->
                        <div>
                            <label for="latitude" class="form-label-bank">Latitude</label>
                            <input type="number" id="latitude" name="latitude" x-model="form.latitude"
                                class="form-input-bank" placeholder="25.2048" step="any" min="-90"
                                max="90">
                            <div x-show="errors.latitude" class="form-error" x-text="errors.latitude"></div>
                            <p class="form-help">Optional: GPS latitude coordinate</p>
                        </div>

                        <div>
                            <label for="longitude" class="form-label-bank">Longitude</label>
                            <input type="number" id="longitude" name="longitude" x-model="form.longitude"
                                class="form-input-bank" placeholder="55.2708" step="any" min="-180"
                                max="180">
                            <div x-show="errors.longitude" class="form-error" x-text="errors.longitude"></div>
                            <p class="form-help">Optional: GPS longitude coordinate</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Contact Information</h3>
                        <p class="bank-card-subtitle">Venue and manager contact details</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Venue Phone -->
                        <div>
                            <label for="phone" class="form-label-bank">Venue Phone</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-dark-gray text-sm">+971</span>
                                </div>
                                <input type="tel" id="phone" name="phone" x-model="form.phone"
                                    class="form-input-bank pl-12" placeholder="50 123 4567"
                                    pattern="[0-9]{2}[0-9]{3}[0-9]{4}">
                            </div>
                            <div x-show="errors.phone" class="form-error" x-text="errors.phone"></div>
                            <p class="form-help">UAE phone number format: 50 123 4567</p>
                        </div>

                        <!-- Venue Email -->
                        <div>
                            <label for="email" class="form-label-bank">Venue Email</label>
                            <input type="email" id="email" name="email" x-model="form.email"
                                class="form-input-bank" placeholder="<EMAIL>">
                            <div x-show="errors.email" class="form-error" x-text="errors.email"></div>
                            <p class="form-help">Optional: Contact email for this venue</p>
                        </div>

                        <!-- Manager Name -->
                        <div>
                            <label for="manager_name" class="form-label-bank">Manager Name</label>
                            <input type="text" id="manager_name" name="manager_name" x-model="form.manager_name"
                                class="form-input-bank" placeholder="Enter manager's full name">
                            <div x-show="errors.manager_name" class="form-error" x-text="errors.manager_name"></div>
                        </div>

                        <!-- Manager Phone -->
                        <div>
                            <label for="manager_phone" class="form-label-bank">Manager Phone</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-dark-gray text-sm">+971</span>
                                </div>
                                <input type="tel" id="manager_phone" name="manager_phone"
                                    x-model="form.manager_phone" class="form-input-bank pl-12" placeholder="50 123 4567"
                                    pattern="[0-9]{2}[0-9]{3}[0-9]{4}">
                            </div>
                            <div x-show="errors.manager_phone" class="form-error" x-text="errors.manager_phone"></div>
                        </div>

                        <!-- Manager Email -->
                        <div class="md:col-span-2">
                            <label for="manager_email" class="form-label-bank">Manager Email</label>
                            <input type="email" id="manager_email" name="manager_email" x-model="form.manager_email"
                                class="form-input-bank" placeholder="<EMAIL>">
                            <div x-show="errors.manager_email" class="form-error" x-text="errors.manager_email"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing & Configuration Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Pricing & Configuration</h3>
                        <p class="bank-card-subtitle">Base rates and VAT settings</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Base Hourly Rate -->
                        <div>
                            <label for="hourly_rate_base" class="form-label-bank required">Base Hourly Rate (AED)</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-dark-gray text-sm">AED</span>
                                </div>
                                <input type="number" id="hourly_rate_base" name="hourly_rate_base"
                                    x-model="form.hourly_rate_base" class="form-input-bank pl-12" placeholder="100.00"
                                    step="0.01" min="0" required>
                            </div>
                            <div x-show="errors.hourly_rate_base" class="form-error" x-text="errors.hourly_rate_base">
                            </div>
                            <p class="form-help">Base rate per hour for field bookings</p>
                        </div>

                        <!-- VAT Applicable -->
                        <div>
                            <label class="form-label-bank">VAT Applicable</label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" name="vat_applicable" value="1" class="form-radio-bank"
                                        x-model="form.vat_applicable">
                                    <span class="ml-2 text-charcoal-black">Yes</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="vat_applicable" value="0" class="form-radio-bank"
                                        x-model="form.vat_applicable">
                                    <span class="ml-2 text-charcoal-black">No</span>
                                </label>
                            </div>
                            <div x-show="errors.vat_applicable" class="form-error" x-text="errors.vat_applicable"></div>
                        </div>

                        <!-- VAT Rate -->
                        <div x-show="form.vat_applicable === '1'">
                            <label for="vat_rate" class="form-label-bank required">VAT Rate (%)</label>
                            <div class="relative">
                                <input type="number" id="vat_rate" name="vat_rate" x-model="form.vat_rate"
                                    class="form-input-bank pr-8" placeholder="5.00" step="0.01" min="0"
                                    max="100">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <span class="text-dark-gray text-sm">%</span>
                                </div>
                            </div>
                            <div x-show="errors.vat_rate" class="form-error" x-text="errors.vat_rate"></div>
                            <p class="form-help">VAT percentage (e.g., 5% for UAE)</p>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="form-label-bank">Venue Status</label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="1" class="form-radio-bank"
                                        x-model="form.status">
                                    <span class="ml-2 text-charcoal-black">Active</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="0" class="form-radio-bank"
                                        x-model="form.status">
                                    <span class="ml-2 text-charcoal-black">Inactive</span>
                                </label>
                            </div>
                            <div x-show="errors.status" class="form-error" x-text="errors.status"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Additional Information</h3>
                        <p class="bank-card-subtitle">Operating hours, facilities, and notes</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Operating Hours -->
                        <div>
                            <label class="form-label-bank">Operating Hours</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="operating_hours_start" class="form-label-bank text-sm">Opening
                                        Time</label>
                                    <input type="time" id="operating_hours_start" name="operating_hours[start]"
                                        x-model="form.operating_hours.start" class="form-input-bank">
                                </div>
                                <div>
                                    <label for="operating_hours_end" class="form-label-bank text-sm">Closing Time</label>
                                    <input type="time" id="operating_hours_end" name="operating_hours[end]"
                                        x-model="form.operating_hours.end" class="form-input-bank">
                                </div>
                            </div>
                            <p class="form-help">Set the general operating hours for the venue</p>
                        </div>

                        <!-- Facilities -->
                        <div>
                            <label class="form-label-bank">Available Facilities</label>
                            <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                <template x-for="facility in availableFacilities" :key="facility">
                                    <label class="flex items-center">
                                        <input type="checkbox" :name="'facilities[]'" :value="facility"
                                            x-model="form.facilities" class="form-checkbox-bank">
                                        <span class="ml-2 text-sm text-charcoal-black" x-text="facility"></span>
                                    </label>
                                </template>
                            </div>
                            <p class="form-help">Select all facilities available at this venue</p>
                        </div>

                        <!-- Notes -->
                        <div>
                            <label for="notes" class="form-label-bank">Notes</label>
                            <textarea id="notes" name="notes" x-model="form.notes" rows="4" class="form-textarea-bank"
                                placeholder="Enter any additional notes about the venue"></textarea>
                            <div x-show="errors.notes" class="form-error" x-text="errors.notes"></div>
                        </div>

                        <!-- Notes (Arabic) -->
                        <div>
                            <label for="notes_ar" class="form-label-bank">Notes (Arabic)</label>
                            <textarea id="notes_ar" name="notes_ar" x-model="form.notes_ar" rows="4" class="form-textarea-bank"
                                placeholder="أدخل ملاحظات إضافية بالعربية" dir="rtl"></textarea>
                            <div x-show="errors.notes_ar" class="form-error" x-text="errors.notes_ar"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview Card -->
            <div class="bank-card" x-show="showPreview" x-transition>
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Venue Preview</h3>
                        <p class="bank-card-subtitle">Review the venue information before saving</p>
                    </div>
                    <button type="button" @click="showPreview = false" class="btn-bank btn-bank-outline btn-bank-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Hide Preview
                    </button>
                </div>
                <div class="bank-card-body">
                    <div class="bg-off-white rounded-lg p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                    </path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black" x-text="form.name || 'Venue Name'"></h4>
                                <p class="text-dark-gray" x-text="form.city + ', ' + form.country || 'Location'"></p>
                            </div>
                            <span class="badge-bank"
                                :class="form.status === '1' ? 'badge-success' : 'badge-neutral'">
                                <span x-text="form.status === '1' ? 'Active' : 'Inactive'"></span>
                            </span>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div x-show="form.address">
                                <strong class="text-charcoal-black">Address:</strong>
                                <p class="text-dark-gray" x-text="form.address"></p>
                            </div>
                            <div x-show="form.phone">
                                <strong class="text-charcoal-black">Phone:</strong>
                                <p class="text-dark-gray">+971 <span x-text="form.phone"></span></p>
                            </div>
                            <div x-show="form.email">
                                <strong class="text-charcoal-black">Email:</strong>
                                <p class="text-dark-gray" x-text="form.email"></p>
                            </div>
                            <div x-show="form.hourly_rate_base">
                                <strong class="text-charcoal-black">Base Rate:</strong>
                                <p class="text-dark-gray">AED <span x-text="form.hourly_rate_base"></span>/hour</p>
                            </div>
                            <div x-show="form.manager_name">
                                <strong class="text-charcoal-black">Manager:</strong>
                                <p class="text-dark-gray" x-text="form.manager_name"></p>
                            </div>
                            <div x-show="form.vat_applicable === '1'">
                                <strong class="text-charcoal-black">VAT:</strong>
                                <p class="text-dark-gray"><span x-text="form.vat_rate"></span>% applicable</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <button type="button" @click="showPreview = !showPreview" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                                <span x-text="showPreview ? 'Hide Preview' : 'Show Preview'"></span>
                            </button>
                            <button type="button" @click="resetForm()" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                    </path>
                                </svg>
                                Reset Form
                            </button>
                        </div>

                        <div class="flex items-center space-x-3">
                            <a href="{{ route('venues.index') }}" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel
                            </a>
                            <button type="submit" class="btn-bank" :disabled="loading"
                                :class="{ 'opacity-50 cursor-not-allowed': loading }">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                    x-show="!loading">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12">
                                    </path>
                                </svg>
                                <svg class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24" x-show="loading">
                                    <circle class="opacity-25" cx="12" cy="12" r="10"
                                        stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                <span x-text="loading ? 'Creating Venue...' : 'Create Venue'"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function venueForm() {
            return {
                loading: false,
                showPreview: false,
                form: {
                    name: '',
                    name_ar: '',
                    description: '',
                    description_ar: '',
                    address: '',
                    address_ar: '',
                    city: '',
                    country: 'UAE',
                    phone: '',
                    email: '',
                    manager_name: '',
                    manager_phone: '',
                    manager_email: '',
                    latitude: '',
                    longitude: '',
                    operating_hours: {
                        start: '06:00',
                        end: '23:00'
                    },
                    facilities: [],
                    hourly_rate_base: '',
                    vat_applicable: '1',
                    vat_rate: '5.00',
                    status: '1',
                    notes: '',
                    notes_ar: ''
                },
                errors: {},
                availableFacilities: [
                    'Parking',
                    'Changing Rooms',
                    'Showers',
                    'Lighting',
                    'Air Conditioning',
                    'Seating Area',
                    'Cafeteria',
                    'First Aid',
                    'Security',
                    'WiFi',
                    'Sound System',
                    'Equipment Storage'
                ],

                init() {
                    // Initialize form
                },

                async submitForm() {
                    this.loading = true;
                    this.errors = {};

                    try {
                        const formData = new FormData();

                        // Add all form fields
                        Object.keys(this.form).forEach(key => {
                            if (key === 'operating_hours') {
                                formData.append('operating_hours', JSON.stringify(this.form.operating_hours));
                            } else if (key === 'facilities') {
                                this.form.facilities.forEach(facility => {
                                    formData.append('facilities[]', facility);
                                });
                            } else if (this.form[key] !== null && this.form[key] !== '') {
                                formData.append(key, this.form[key]);
                            }
                        });

                        const response = await fetch('{{ route('venues.store') }}', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            },
                            body: formData
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            showNotification('success', result.message || 'Venue created successfully!');
                            window.location.href = '{{ route('venues.index') }}';
                        } else {
                            if (result.errors) {
                                this.errors = result.errors;
                            } else {
                                showNotification('error', result.message ||
                                    'An error occurred while creating the venue.');
                            }
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showNotification('error', 'An unexpected error occurred. Please try again.');
                    } finally {
                        this.loading = false;
                    }
                },

                resetForm() {
                    this.form = {
                        name: '',
                        name_ar: '',
                        description: '',
                        description_ar: '',
                        address: '',
                        address_ar: '',
                        city: '',
                        country: 'UAE',
                        phone: '',
                        email: '',
                        manager_name: '',
                        manager_phone: '',
                        manager_email: '',
                        latitude: '',
                        longitude: '',
                        operating_hours: {
                            start: '06:00',
                            end: '23:00'
                        },
                        facilities: [],
                        hourly_rate_base: '',
                        vat_applicable: '1',
                        vat_rate: '5.00',
                        status: '1',
                        notes: '',
                        notes_ar: ''
                    };
                    this.errors = {};
                    this.showPreview = false;
                }
            }
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            if (type === 'success') {
                alert('Success: ' + message);
            } else {
                alert('Error: ' + message);
            }
        }
    </script>
@endpush
