<?php

namespace App\Helpers;

class CurrencyHelper
{
    /**
     * Format amount to AED currency format
     * 
     * @param float|int $amount
     * @return string
     */
    public static function formatAED($amount)
    {
        if (is_null($amount) || $amount === '') {
            return '0 AED';
        }
        
        return number_format((float)$amount, 2, '.', ',') . ' AED';
    }
    
    /**
     * Parse AED formatted string to float
     * 
     * @param string $formatted
     * @return float
     */
    public static function parseAED($formatted)
    {
        if (is_null($formatted) || $formatted === '') {
            return 0.0;
        }
        
        // Remove AED and commas, convert to float
        $cleaned = str_replace([' AED', ','], '', $formatted);
        return (float) $cleaned;
    }
    
    /**
     * Validate AED amount
     * 
     * @param mixed $amount
     * @return bool
     */
    public static function isValidAED($amount)
    {
        if (is_numeric($amount)) {
            return (float)$amount >= 0;
        }
        
        if (is_string($amount)) {
            $parsed = self::parseAED($amount);
            return $parsed >= 0;
        }
        
        return false;
    }
    
    /**
     * Get currency symbol for display
     * 
     * @param string $locale
     * @return string
     */
    public static function getCurrencySymbol($locale = 'en')
    {
        return $locale === 'ar' ? 'د.إ' : 'AED';
    }
}
