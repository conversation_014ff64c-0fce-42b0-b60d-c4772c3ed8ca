<?php

// UAE English Sports Academy - Academy Management Translations (English)
// Academy management module translations

return [
    // ===== GENERAL TERMS =====
    'academy' => 'Academy',
    'academies' => 'Academies',
    'academy_management' => 'Academy Management',
    'academy_details' => 'Academy Details',
    'academy_information' => 'Academy Information',
    'academy_profile' => 'Academy Profile',
    'academy_overview' => 'Academy Overview',

    // ===== FORM FIELDS =====
    'academy_name' => 'Academy Name',
    'academy_code' => 'Academy Code',
    'academy_description' => 'Academy Description',
    'academy_type' => 'Academy Type',
    'academy_category' => 'Academy Category',
    'academy_level' => 'Academy Level',
    'academy_capacity' => 'Academy Capacity',
    'academy_location' => 'Academy Location',
    'academy_address' => 'Academy Address',
    'academy_phone' => 'Academy Phone',
    'academy_email' => 'Academy Email',
    'academy_website' => 'Academy Website',
    'academy_manager' => 'Academy Manager',
    'academy_coordinator' => 'Academy Coordinator',
    'academy_supervisor' => 'Academy Supervisor',
    'academy_staff' => 'Academy Staff',
    'academy_facilities' => 'Academy Facilities',
    'academy_equipment' => 'Academy Equipment',
    'academy_schedule' => 'Academy Schedule',
    'academy_timing' => 'Academy Timing',
    'academy_duration' => 'Academy Duration',
    'academy_fees' => 'Academy Fees',
    'academy_pricing' => 'Academy Pricing',
    'academy_discount' => 'Academy Discount',
    'academy_requirements' => 'Academy Requirements',
    'academy_prerequisites' => 'Academy Prerequisites',
    'academy_objectives' => 'Academy Objectives',
    'academy_goals' => 'Academy Goals',
    'academy_curriculum' => 'Academy Curriculum',
    'academy_syllabus' => 'Academy Syllabus',
    'academy_certification' => 'Academy Certification',
    'academy_accreditation' => 'Academy Accreditation',

    // ===== ACTIONS =====
    'create_academy' => 'Create Academy',
    'add_academy' => 'Add Academy',
    'new_academy' => 'New Academy',
    'edit_academy' => 'Edit Academy',
    'update_academy' => 'Update Academy',
    'delete_academy' => 'Delete Academy',
    'remove_academy' => 'Remove Academy',
    'view_academy' => 'View Academy',
    'show_academy' => 'Show Academy',
    'list_academies' => 'List Academies',
    'search_academies' => 'Search Academies',
    'filter_academies' => 'Filter Academies',
    'sort_academies' => 'Sort Academies',
    'export_academies' => 'Export Academies',
    'import_academies' => 'Import Academies',
    'duplicate_academy' => 'Duplicate Academy',
    'archive_academy' => 'Archive Academy',
    'restore_academy' => 'Restore Academy',
    'activate_academy' => 'Activate Academy',
    'deactivate_academy' => 'Deactivate Academy',
    'suspend_academy' => 'Suspend Academy',
    'resume_academy' => 'Resume Academy',

    // ===== STATUS =====
    'academy_status' => 'Academy Status',
    'active_academy' => 'Active Academy',
    'inactive_academy' => 'Inactive Academy',
    'pending_academy' => 'Pending Academy',
    'approved_academy' => 'Approved Academy',
    'rejected_academy' => 'Rejected Academy',
    'suspended_academy' => 'Suspended Academy',
    'archived_academy' => 'Archived Academy',
    'draft_academy' => 'Draft Academy',
    'published_academy' => 'Published Academy',

    // ===== ACADEMY TYPES =====
    'sports_academy' => 'Sports Academy',
    'football_academy' => 'Football Academy',
    'basketball_academy' => 'Basketball Academy',
    'tennis_academy' => 'Tennis Academy',
    'swimming_academy' => 'Swimming Academy',
    'martial_arts_academy' => 'Martial Arts Academy',
    'fitness_academy' => 'Fitness Academy',
    'dance_academy' => 'Dance Academy',
    'gymnastics_academy' => 'Gymnastics Academy',
    'athletics_academy' => 'Athletics Academy',

    // ===== ACADEMY LEVELS =====
    'beginner_level' => 'Beginner Level',
    'intermediate_level' => 'Intermediate Level',
    'advanced_level' => 'Advanced Level',
    'professional_level' => 'Professional Level',
    'elite_level' => 'Elite Level',
    'youth_level' => 'Youth Level',
    'adult_level' => 'Adult Level',
    'senior_level' => 'Senior Level',

    // ===== ACADEMY CATEGORIES =====
    'individual_sports' => 'Individual Sports',
    'team_sports' => 'Team Sports',
    'water_sports' => 'Water Sports',
    'combat_sports' => 'Combat Sports',
    'racket_sports' => 'Racket Sports',
    'outdoor_sports' => 'Outdoor Sports',
    'indoor_sports' => 'Indoor Sports',
    'seasonal_sports' => 'Seasonal Sports',

    // ===== ACADEMY FACILITIES =====
    'training_ground' => 'Training Ground',
    'sports_hall' => 'Sports Hall',
    'gymnasium' => 'Gymnasium',
    'swimming_pool' => 'Swimming Pool',
    'tennis_court' => 'Tennis Court',
    'basketball_court' => 'Basketball Court',
    'football_field' => 'Football Field',
    'athletics_track' => 'Athletics Track',
    'fitness_center' => 'Fitness Center',
    'changing_rooms' => 'Changing Rooms',
    'equipment_storage' => 'Equipment Storage',
    'medical_room' => 'Medical Room',
    'cafeteria' => 'Cafeteria',
    'parking_area' => 'Parking Area',

    // ===== ACADEMY STAFF =====
    'head_coach' => 'Head Coach',
    'assistant_coach' => 'Assistant Coach',
    'trainer' => 'Trainer',
    'instructor' => 'Instructor',
    'physiotherapist' => 'Physiotherapist',
    'nutritionist' => 'Nutritionist',
    'sports_psychologist' => 'Sports Psychologist',
    'equipment_manager' => 'Equipment Manager',
    'administrative_staff' => 'Administrative Staff',

    // ===== ACADEMY PROGRAMS =====
    'academy_programs' => 'Academy Programs',
    'training_programs' => 'Training Programs',
    'coaching_programs' => 'Coaching Programs',
    'development_programs' => 'Development Programs',
    'competition_programs' => 'Competition Programs',
    'recreational_programs' => 'Recreational Programs',
    'professional_programs' => 'Professional Programs',
    'youth_programs' => 'Youth Programs',
    'adult_programs' => 'Adult Programs',

    // ===== ACADEMY STUDENTS =====
    'academy_students' => 'Academy Students',
    'enrolled_students' => 'Enrolled Students',
    'active_students' => 'Active Students',
    'graduated_students' => 'Graduated Students',
    'student_capacity' => 'Student Capacity',
    'student_enrollment' => 'Student Enrollment',
    'student_registration' => 'Student Registration',
    'student_admission' => 'Student Admission',
    'student_assessment' => 'Student Assessment',
    'student_progress' => 'Student Progress',
    'student_performance' => 'Student Performance',
    'student_achievements' => 'Student Achievements',

    // ===== ACADEMY SCHEDULE =====
    'academy_schedule' => 'Academy Schedule',
    'training_schedule' => 'Training Schedule',
    'class_schedule' => 'Class Schedule',
    'session_schedule' => 'Session Schedule',
    'weekly_schedule' => 'Weekly Schedule',
    'monthly_schedule' => 'Monthly Schedule',
    'seasonal_schedule' => 'Seasonal Schedule',
    'holiday_schedule' => 'Holiday Schedule',
    'special_events' => 'Special Events',
    'competitions' => 'Competitions',
    'tournaments' => 'Tournaments',
    'matches' => 'Matches',

    // ===== ACADEMY FEES =====
    'academy_fees' => 'Academy Fees',
    'registration_fee' => 'Registration Fee',
    'monthly_fee' => 'Monthly Fee',
    'annual_fee' => 'Annual Fee',
    'training_fee' => 'Training Fee',
    'equipment_fee' => 'Equipment Fee',
    'facility_fee' => 'Facility Fee',
    'membership_fee' => 'Membership Fee',
    'competition_fee' => 'Competition Fee',
    'certification_fee' => 'Certification Fee',
    'late_payment_fee' => 'Late Payment Fee',
    'cancellation_fee' => 'Cancellation Fee',

    // ===== ACADEMY REPORTS =====
    'academy_reports' => 'Academy Reports',
    'enrollment_report' => 'Enrollment Report',
    'attendance_report' => 'Attendance Report',
    'performance_report' => 'Performance Report',
    'financial_report' => 'Financial Report',
    'progress_report' => 'Progress Report',
    'assessment_report' => 'Assessment Report',
    'achievement_report' => 'Achievement Report',
    'staff_report' => 'Staff Report',
    'facility_report' => 'Facility Report',

    // ===== MESSAGES =====
    'academy_created' => 'Academy created successfully',
    'academy_updated' => 'Academy updated successfully',
    'academy_deleted' => 'Academy deleted successfully',
    'academy_activated' => 'Academy activated successfully',
    'academy_deactivated' => 'Academy deactivated successfully',
    'academy_suspended' => 'Academy suspended successfully',
    'academy_restored' => 'Academy restored successfully',
    'academy_not_found' => 'Academy not found',
    'academy_already_exists' => 'Academy already exists',
    'academy_name_required' => 'Academy name is required',
    'academy_code_required' => 'Academy code is required',
    'academy_type_required' => 'Academy type is required',
    'academy_manager_required' => 'Academy manager is required',
    'academy_capacity_required' => 'Academy capacity is required',
    'academy_fees_required' => 'Academy fees are required',
    'academy_schedule_required' => 'Academy schedule is required',
    'academy_location_required' => 'Academy location is required',
    'academy_description_required' => 'Academy description is required',

    // ===== VALIDATION MESSAGES =====
    'invalid_academy_name' => 'Invalid academy name',
    'invalid_academy_code' => 'Invalid academy code',
    'invalid_academy_type' => 'Invalid academy type',
    'invalid_academy_capacity' => 'Invalid academy capacity',
    'invalid_academy_fees' => 'Invalid academy fees',
    'invalid_academy_email' => 'Invalid academy email',
    'invalid_academy_phone' => 'Invalid academy phone',
    'invalid_academy_website' => 'Invalid academy website',
    'academy_code_exists' => 'Academy code already exists',
    'academy_name_exists' => 'Academy name already exists',
    'academy_email_exists' => 'Academy email already exists',

    // ===== SEARCH & FILTER =====
    'search_academy_name' => 'Search by academy name',
    'search_academy_code' => 'Search by academy code',
    'search_academy_type' => 'Search by academy type',
    'filter_by_status' => 'Filter by status',
    'filter_by_type' => 'Filter by type',
    'filter_by_level' => 'Filter by level',
    'filter_by_category' => 'Filter by category',
    'filter_by_manager' => 'Filter by manager',
    'filter_by_location' => 'Filter by location',
    'filter_by_capacity' => 'Filter by capacity',
    'filter_by_fees' => 'Filter by fees',
    'sort_by_name' => 'Sort by name',
    'sort_by_code' => 'Sort by code',
    'sort_by_type' => 'Sort by type',
    'sort_by_status' => 'Sort by status',
    'sort_by_created_date' => 'Sort by created date',
    'sort_by_updated_date' => 'Sort by updated date',

    // ===== PAGINATION =====
    'academies_per_page' => 'Academies per page',
    'showing_academies' => 'Showing :from to :to of :total academies',
    'no_academies_found' => 'No academies found',
    'academies_found' => ':count academies found',

    // ===== EXPORT & IMPORT =====
    'export_academies_excel' => 'Export Academies to Excel',
    'export_academies_pdf' => 'Export Academies to PDF',
    'export_academies_csv' => 'Export Academies to CSV',
    'import_academies_excel' => 'Import Academies from Excel',
    'import_academies_csv' => 'Import Academies from CSV',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',
    'import_successful' => 'Import completed successfully',
    'import_failed' => 'Import failed',
    'export_successful' => 'Export completed successfully',
    'export_failed' => 'Export failed',
];
