@extends('layouts.dashboard')

@section('title', 'Styling Test Page')

@section('header')
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-3xl font-bold text-charcoal-black">Styling Test Page</h1>
            <p class="text-lg text-dark-gray">Test table headers and button text colors</p>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Table Header Test -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="text-lg font-semibold text-charcoal-black">Table Header Test</h3>
            </div>
            <div class="bank-card-body p-0">
                <div class="overflow-x-auto">
                    <table class="table-bank" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Size</th>
                                <th>Color</th>
                                <th>Current Stock</th>
                                <th>Available</th>
                                <th>Status</th>
                                <th>Selling Price</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Test Item 1</td>
                                <td>Test Category</td>
                                <td>M</td>
                                <td>Red</td>
                                <td>10</td>
                                <td>8</td>
                                <td><span class="badge-bank badge-success">Active</span></td>
                                <td>AED 50.00</td>
                                <td>
                                    <div class="flex items-center space-x-2">
                                        <button class="btn-bank-outline btn-sm">View</button>
                                        <button class="btn-bank btn-sm">Edit</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Button Test -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="text-lg font-semibold text-charcoal-black">Button Text Color Test</h3>
            </div>
            <div class="bank-card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Standard buttons -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-dark-gray">Standard Buttons</h4>
                        <button class="btn-bank w-full">Bank Button</button>
                        <button class="btn-primary w-full">Primary Button</button>
                        <button class="btn-danger w-full">Danger Button</button>
                        <button class="btn-leaders w-full">Leaders Button</button>
                    </div>

                    <!-- Red class buttons -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-dark-gray">Red Class Buttons</h4>
                        <button class="btn red-button w-full" style="background-color: #e53e3e;">Red Background</button>
                        <button class="btn Red-Button w-full" style="background-color: #dc2626;">Red Button 2</button>
                        <button class="btn primary-red w-full" style="background-color: #b91c1c;">Primary Red</button>
                        <button class="btn danger-red w-full" style="background-color: #991b1b;">Danger Red</button>
                    </div>

                    <!-- Inline style buttons -->
                    <div class="space-y-3">
                        <h4 class="font-medium text-dark-gray">Inline Style Buttons</h4>
                        <button class="btn w-full" style="background-color: red; color: black;">Inline Red 1</button>
                        <button class="btn w-full" style="background: #e53e3e; color: black;">Inline Red 2</button>
                        <button class="btn w-full" style="background-color: #dc2626; color: #000;">Inline Red 3</button>
                        <button class="btn w-full" style="background: linear-gradient(135deg, #e53e3e 0%, #dc2626 100%); color: black;">Gradient Red</button>
                    </div>
                </div>

                <!-- Buttons with icons -->
                <div class="mt-6">
                    <h4 class="font-medium text-dark-gray mb-3">Buttons with Icons</h4>
                    <div class="flex flex-wrap gap-3">
                        <button class="btn-bank">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Item
                        </button>
                        <button class="btn-primary">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            View Details
                        </button>
                        <button class="btn-danger">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="text-lg font-semibold text-charcoal-black">Test Results</h3>
            </div>
            <div class="bank-card-body">
                <div class="space-y-4">
                    <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <h4 class="font-medium text-blue-800 mb-2">✅ Expected Results:</h4>
                        <ul class="text-blue-700 space-y-1 text-sm">
                            <li>• All table headers should have <strong>white text</strong> on dark gradient background</li>
                            <li>• All red/primary/danger buttons should have <strong>white text</strong></li>
                            <li>• SVG icons in buttons should be <strong>white</strong></li>
                            <li>• Text should be clearly readable with good contrast</li>
                        </ul>
                    </div>
                    
                    <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                        <h4 class="font-medium text-green-800 mb-2">🔧 How to Test:</h4>
                        <ul class="text-green-700 space-y-1 text-sm">
                            <li>• Open browser developer tools (F12)</li>
                            <li>• Check console for "Found X table headers to fix" and "Found X buttons to check"</li>
                            <li>• Verify all red buttons have white text</li>
                            <li>• Verify table headers have white text on dark background</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                console.log('=== STYLING TEST PAGE LOADED ===');
                
                // Test table headers
                const tableHeaders = document.querySelectorAll('table thead th');
                console.log(`Table headers found: ${tableHeaders.length}`);
                tableHeaders.forEach((header, index) => {
                    const computedStyle = window.getComputedStyle(header);
                    console.log(`Header ${index + 1}: "${header.textContent.trim()}" - Color: ${computedStyle.color}`);
                });
                
                // Test buttons
                const buttons = document.querySelectorAll('button, .btn');
                console.log(`Buttons found: ${buttons.length}`);
                buttons.forEach((button, index) => {
                    const computedStyle = window.getComputedStyle(button);
                    console.log(`Button ${index + 1}: "${button.textContent.trim()}" - Color: ${computedStyle.color}, Background: ${computedStyle.backgroundColor}`);
                });
                
                console.log('=== END STYLING TEST ===');
            });
        </script>
    @endpush
@endsection
