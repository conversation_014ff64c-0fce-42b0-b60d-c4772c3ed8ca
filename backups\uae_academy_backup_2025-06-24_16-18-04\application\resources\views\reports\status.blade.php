@extends('layouts.dashboard')

@section('title', 'Status Reports - UAE English Sports Academy')

@section('header')
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Status Reports</h1>
            <p class="text-gray-600 mt-1">System-wide status tracking and monitoring</p>
        </div>
        <div class="flex items-center gap-3">
            <button onclick="exportToPDF()" class="btn-bank-outline">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export PDF
            </button>
            <button onclick="exportToExcel()" class="btn-bank-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export Excel
            </button>
        </div>
    </div>
@endsection

@section('content')
    <!-- Filters -->
    @include('reports._filters')

    <!-- Status Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Student Status Summary -->
        <div class="bank-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Student Status</h3>
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
            </div>
            <div class="space-y-2">
                @foreach($statusData['student_status'] as $status)
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium capitalize">{{ $status->status }}</span>
                        <span class="text-sm font-bold 
                            {{ $status->status === 'active' ? 'text-green-600' : '' }}
                            {{ $status->status === 'inactive' ? 'text-gray-600' : '' }}
                            {{ $status->status === 'suspended' ? 'text-red-600' : '' }}">
                            {{ number_format($status->count) }}
                        </span>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Payment Status Summary -->
        <div class="bank-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Payment Status</h3>
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
            </div>
            <div class="space-y-2">
                @foreach($statusData['payment_status'] as $status)
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium capitalize">{{ $status->status }}</span>
                        <div class="text-right">
                            <span class="text-sm font-bold 
                                {{ $status->status === 'completed' ? 'text-green-600' : '' }}
                                {{ $status->status === 'pending' ? 'text-orange-600' : '' }}
                                {{ $status->status === 'failed' ? 'text-red-600' : '' }}">
                                {{ number_format($status->count) }}
                            </span>
                            <div class="text-xs text-gray-500">AED {{ number_format($status->total, 2) }}</div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Uniform Branch Status -->
        <div class="bank-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Uniform Branch Status</h3>
                <div class="p-2 bg-purple-100 rounded-lg">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
            </div>
            <div class="space-y-2">
                @foreach($statusData['uniform_branch_status'] as $status)
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium capitalize">{{ $status->branch_status }}</span>
                        <span class="text-sm font-bold 
                            {{ $status->branch_status === 'delivered' ? 'text-green-600' : '' }}
                            {{ $status->branch_status === 'received' ? 'text-blue-600' : '' }}
                            {{ $status->branch_status === 'pending' ? 'text-orange-600' : '' }}">
                            {{ number_format($status->count) }}
                        </span>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Uniform Office Status -->
        <div class="bank-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Uniform Office Status</h3>
                <div class="p-2 bg-indigo-100 rounded-lg">
                    <svg class="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>
            </div>
            <div class="space-y-2">
                @foreach($statusData['uniform_office_status'] as $status)
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium capitalize">{{ $status->office_status }}</span>
                        <span class="text-sm font-bold 
                            {{ $status->office_status === 'delivered' ? 'text-green-600' : '' }}
                            {{ $status->office_status === 'received' ? 'text-blue-600' : '' }}
                            {{ $status->office_status === 'pending' ? 'text-orange-600' : '' }}">
                            {{ number_format($status->count) }}
                        </span>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Detailed Status Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Student Status Chart -->
        <div class="bank-card">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Student Status Distribution</h3>
            <div class="space-y-4">
                @php
                    $totalStudents = $statusData['student_status']->sum('count');
                @endphp
                @foreach($statusData['student_status'] as $status)
                    @php
                        $percentage = $totalStudents > 0 ? ($status->count / $totalStudents) * 100 : 0;
                    @endphp
                    <div>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium capitalize">{{ $status->status }}</span>
                            <span class="text-sm text-gray-600">{{ number_format($percentage, 1) }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full 
                                {{ $status->status === 'active' ? 'bg-green-500' : '' }}
                                {{ $status->status === 'inactive' ? 'bg-gray-500' : '' }}
                                {{ $status->status === 'suspended' ? 'bg-red-500' : '' }}" 
                                style="width: {{ $percentage }}%">
                            </div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">{{ number_format($status->count) }} students</div>
                    </div>
                @endforeach
            </div>
        </div>

        <!-- Payment Status Chart -->
        <div class="bank-card">
            <h3 class="text-lg font-semibold text-gray-900 mb-6">Payment Status Distribution</h3>
            <div class="space-y-4">
                @php
                    $totalPayments = $statusData['payment_status']->sum('count');
                    $totalAmount = $statusData['payment_status']->sum('total');
                @endphp
                @foreach($statusData['payment_status'] as $status)
                    @php
                        $percentage = $totalPayments > 0 ? ($status->count / $totalPayments) * 100 : 0;
                        $amountPercentage = $totalAmount > 0 ? ($status->total / $totalAmount) * 100 : 0;
                    @endphp
                    <div>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium capitalize">{{ $status->status }}</span>
                            <div class="text-right">
                                <span class="text-sm text-gray-600">{{ number_format($percentage, 1) }}%</span>
                                <div class="text-xs text-gray-500">AED {{ number_format($status->total, 2) }}</div>
                            </div>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full 
                                {{ $status->status === 'completed' ? 'bg-green-500' : '' }}
                                {{ $status->status === 'pending' ? 'bg-orange-500' : '' }}
                                {{ $status->status === 'failed' ? 'bg-red-500' : '' }}" 
                                style="width: {{ $percentage }}%">
                            </div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">{{ number_format($status->count) }} payments</div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- Program Status -->
    <div class="bank-card mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Program Status Overview</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            @php
                $totalPrograms = $statusData['program_status']->sum('count');
            @endphp
            @foreach($statusData['program_status'] as $status)
                @php
                    $percentage = $totalPrograms > 0 ? ($status->count / $totalPrograms) * 100 : 0;
                @endphp
                <div class="text-center p-6 bg-gray-50 rounded-lg">
                    <div class="text-3xl font-bold mb-2 
                        {{ $status->status ? 'text-green-600' : 'text-red-600' }}">
                        {{ number_format($status->count) }}
                    </div>
                    <div class="text-sm font-medium text-gray-600 mb-2">
                        {{ $status->status ? 'Active Programs' : 'Inactive Programs' }}
                    </div>
                    <div class="text-xs text-gray-500">{{ number_format($percentage, 1) }}% of total</div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Status Summary Table -->
    <div class="bank-card">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Complete Status Summary</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Percentage</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Additional Info</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Student Status Rows -->
                    @foreach($statusData['student_status'] as $status)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Students</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $status->status === 'active' ? 'bg-green-100 text-green-800' : '' }}
                                    {{ $status->status === 'inactive' ? 'bg-gray-100 text-gray-800' : '' }}
                                    {{ $status->status === 'suspended' ? 'bg-red-100 text-red-800' : '' }}">
                                    {{ ucfirst($status->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($status->count) }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ number_format($totalStudents > 0 ? ($status->count / $totalStudents) * 100 : 0, 1) }}%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">-</td>
                        </tr>
                    @endforeach

                    <!-- Payment Status Rows -->
                    @foreach($statusData['payment_status'] as $status)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Payments</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {{ $status->status === 'completed' ? 'bg-green-100 text-green-800' : '' }}
                                    {{ $status->status === 'pending' ? 'bg-orange-100 text-orange-800' : '' }}
                                    {{ $status->status === 'failed' ? 'bg-red-100 text-red-800' : '' }}">
                                    {{ ucfirst($status->status) }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($status->count) }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ number_format($totalPayments > 0 ? ($status->count / $totalPayments) * 100 : 0, 1) }}%
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">AED {{ number_format($status->total, 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open(`{{ route('reports.status') }}?${params.toString()}`, '_blank');
}

function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open(`{{ route('reports.status') }}?${params.toString()}`, '_blank');
}
</script>
@endpush
