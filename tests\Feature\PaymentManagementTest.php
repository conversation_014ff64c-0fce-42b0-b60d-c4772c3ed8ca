<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Payment;
use App\Models\Student;
use App\Models\Branch;
use App\Models\Academy;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PaymentManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $branch;
    protected $academy;
    protected $student;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>'
        ]);

        // Create test branch
        $this->branch = Branch::factory()->create([
            'name' => 'Test Branch',
            'status' => 'active'
        ]);

        // Create test academy
        $this->academy = Academy::factory()->create([
            'name' => 'Test Academy',
            'branch_id' => $this->branch->id,
            'status' => 'active'
        ]);

        // Create test student
        $this->student = Student::factory()->create([
            'academy_id' => $this->academy->id,
            'branch_id' => $this->branch->id,
            'status' => 'active'
        ]);
    }

    /** @test */
    public function admin_can_view_payments_index()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('payments.index'));

        $response->assertStatus(200);
        $response->assertViewIs('payments.index');
        $response->assertViewHas('payments');
    }

    /** @test */
    public function admin_can_delete_payment()
    {
        $this->actingAs($this->admin);

        // Create a test payment that can be deleted (pending status)
        $payment = Payment::factory()->create([
            'student_id' => $this->student->id,
            'branch_id' => $this->branch->id,
            'academy_id' => $this->academy->id,
            'status' => 'pending',
            'amount' => 1000
        ]);

        $response = $this->deleteJson(route('payments.destroy', $payment));

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => 'Payment deleted successfully'
        ]);

        $this->assertDatabaseMissing('payments', ['id' => $payment->id]);
    }

    /** @test */
    public function admin_cannot_delete_completed_payment()
    {
        $this->actingAs($this->admin);

        // Create a completed payment that cannot be deleted
        $payment = Payment::factory()->create([
            'student_id' => $this->student->id,
            'branch_id' => $this->branch->id,
            'academy_id' => $this->academy->id,
            'status' => 'completed',
            'amount' => 1000
        ]);

        $response = $this->deleteJson(route('payments.destroy', $payment));

        $response->assertStatus(422);
        $response->assertJson([
            'success' => false,
            'message' => 'Cannot delete completed or refunded payments. Consider marking as cancelled instead.'
        ]);

        $this->assertDatabaseHas('payments', ['id' => $payment->id]);
    }

    /** @test */
    public function admin_can_perform_bulk_delete_action()
    {
        $this->actingAs($this->admin);

        // Create multiple test payments that can be deleted
        $payments = Payment::factory()->count(3)->create([
            'student_id' => $this->student->id,
            'branch_id' => $this->branch->id,
            'academy_id' => $this->academy->id,
            'status' => 'pending',
            'amount' => 1000
        ]);

        $paymentIds = $payments->pluck('id')->toArray();

        $response = $this->postJson(route('payments.bulk-action'), [
            'action' => 'delete',
            'payment_ids' => $paymentIds
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'success' => true,
            'message' => '3 payments deleted successfully'
        ]);

        foreach ($paymentIds as $paymentId) {
            $this->assertDatabaseMissing('payments', ['id' => $paymentId]);
        }
    }

    /** @test */
    public function bulk_action_validates_required_fields()
    {
        $this->actingAs($this->admin);

        // Test missing action
        $response = $this->postJson(route('payments.bulk-action'), [
            'payment_ids' => [1, 2, 3]
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['action']);

        // Test missing payment_ids
        $response = $this->postJson(route('payments.bulk-action'), [
            'action' => 'delete'
        ]);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['payment_ids']);
    }
}
