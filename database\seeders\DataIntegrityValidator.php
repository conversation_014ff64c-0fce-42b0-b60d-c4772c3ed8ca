<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Student;
use App\Models\Payment;
use App\Models\Program;
use App\Models\Uniform;

class DataIntegrityValidator extends Seeder
{
    /**
     * Validate database integrity and relationships
     */
    public function run(): void
    {
        $this->command->info('🔍 Starting Data Integrity Validation...');
        
        $issues = [];
        
        // Check user duplicates
        $issues = array_merge($issues, $this->checkUserDuplicates());
        
        // Check foreign key relationships
        $issues = array_merge($issues, $this->checkForeignKeyIntegrity());
        
        // Check data relationship consistency
        $issues = array_merge($issues, $this->checkDataRelationships());
        
        // Check orphaned records
        $issues = array_merge($issues, $this->checkOrphanedRecords());
        
        // Report results
        $this->reportResults($issues);
    }
    
    private function checkUserDuplicates(): array
    {
        $issues = [];
        
        $duplicateEmails = User::select('email')
            ->groupBy('email')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('email');
            
        foreach ($duplicateEmails as $email) {
            $count = User::where('email', $email)->count();
            $issues[] = "🚨 DUPLICATE USER: Email '{$email}' has {$count} records";
        }
        
        return $issues;
    }
    
    private function checkForeignKeyIntegrity(): array
    {
        $issues = [];
        
        // Check users with invalid branch_id
        $invalidUserBranches = User::whereNotNull('branch_id')
            ->whereNotExists(function ($query) {
                $query->select('id')
                    ->from('branches')
                    ->whereColumn('branches.id', 'users.branch_id');
            })->count();
            
        if ($invalidUserBranches > 0) {
            $issues[] = "🚨 FK VIOLATION: {$invalidUserBranches} users have invalid branch_id";
        }
        
        // Check users with invalid academy_id
        $invalidUserAcademies = User::whereNotNull('academy_id')
            ->whereNotExists(function ($query) {
                $query->select('id')
                    ->from('academies')
                    ->whereColumn('academies.id', 'users.academy_id');
            })->count();
            
        if ($invalidUserAcademies > 0) {
            $issues[] = "🚨 FK VIOLATION: {$invalidUserAcademies} users have invalid academy_id";
        }
        
        // Check students with invalid branch_id
        $invalidStudentBranches = Student::whereNotExists(function ($query) {
            $query->select('id')
                ->from('branches')
                ->whereColumn('branches.id', 'students.branch_id');
        })->count();
        
        if ($invalidStudentBranches > 0) {
            $issues[] = "🚨 FK VIOLATION: {$invalidStudentBranches} students have invalid branch_id";
        }
        
        // Check payments with invalid student_id
        $invalidPaymentStudents = Payment::whereNotExists(function ($query) {
            $query->select('id')
                ->from('students')
                ->whereColumn('students.id', 'payments.student_id');
        })->count();
        
        if ($invalidPaymentStudents > 0) {
            $issues[] = "🚨 FK VIOLATION: {$invalidPaymentStudents} payments have invalid student_id";
        }
        
        return $issues;
    }
    
    private function checkDataRelationships(): array
    {
        $issues = [];
        
        $studentCount = Student::count();
        $paymentCount = Payment::count();
        $uniformCount = Uniform::count();
        
        $issues[] = "📊 DATA COUNTS: {$studentCount} students, {$paymentCount} payments, {$uniformCount} uniforms";
        
        // Check for students without payments
        $studentsWithoutPayments = Student::whereDoesntHave('payments')->count();
        if ($studentsWithoutPayments > 0) {
            $issues[] = "⚠️  DATA MISMATCH: {$studentsWithoutPayments} students have no payments";
        }
        
        // Check for payments without students
        $paymentsWithoutStudents = Payment::whereDoesntHave('student')->count();
        if ($paymentsWithoutStudents > 0) {
            $issues[] = "🚨 DATA MISMATCH: {$paymentsWithoutStudents} payments have no valid student";
        }
        
        // Check for multiple payments per student
        $studentsWithMultiplePayments = Student::has('payments', '>', 1)->count();
        if ($studentsWithMultiplePayments > 0) {
            $issues[] = "ℹ️  INFO: {$studentsWithMultiplePayments} students have multiple payments";
        }
        
        return $issues;
    }
    
    private function checkOrphanedRecords(): array
    {
        $issues = [];
        
        // Check for academies without branches
        $orphanedAcademies = Academy::whereDoesntHave('branch')->count();
        if ($orphanedAcademies > 0) {
            $issues[] = "🚨 ORPHANED: {$orphanedAcademies} academies have no valid branch";
        }
        
        // Check for programs without academies
        $orphanedPrograms = Program::whereDoesntHave('academy')->count();
        if ($orphanedPrograms > 0) {
            $issues[] = "🚨 ORPHANED: {$orphanedPrograms} programs have no valid academy";
        }
        
        return $issues;
    }
    
    private function reportResults(array $issues): void
    {
        $this->command->info('');
        $this->command->info('📋 DATA INTEGRITY VALIDATION RESULTS');
        $this->command->info('=====================================');
        
        if (empty($issues)) {
            $this->command->info('✅ No data integrity issues found!');
        } else {
            foreach ($issues as $issue) {
                $this->command->warn($issue);
            }
            
            $criticalIssues = array_filter($issues, fn($issue) => str_contains($issue, '🚨'));
            $warningIssues = array_filter($issues, fn($issue) => str_contains($issue, '⚠️'));
            
            $this->command->info('');
            $this->command->error('SUMMARY: ' . count($criticalIssues) . ' critical issues, ' . count($warningIssues) . ' warnings found');
        }
        
        $this->command->info('');
    }
}
