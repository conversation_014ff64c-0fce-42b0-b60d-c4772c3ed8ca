@extends('layouts.dashboard')

@section('title', __('Inventory Item Details'))

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ __('Inventory Item Details') }}</h1>
                <p class="text-lg text-dark-gray">{{ $inventory->localized_name }}</p>
                <span class="badge-bank badge-info">SKU: {{ $inventory->sku }}</span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @if (Route::has('inventory.edit'))
                <a href="{{ route('inventory.edit', $inventory) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    {{ __('Edit Item') }}
                </a>
            @endif
            <a href="{{ route('inventory.index') }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                {{ __('Back to Inventory') }}
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Item Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Basic Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Item Information') }}</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('SKU') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->sku }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Name') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->localized_name }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Category') }}:</span>
                                    <span
                                        class="badge-bank badge-secondary">{{ $inventory->category->localized_name ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Size') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->size }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Color') }}:</span>
                                    @if ($inventory->color)
                                        <span class="badge-bank badge-light">{{ $inventory->localized_color }}</span>
                                    @else
                                        <span class="text-medium-gray">-</span>
                                    @endif
                                </div>
                                <div class="flex justify-between py-2">
                                    <span class="font-medium text-dark-gray">{{ __('Brand') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->brand ?? '-' }}</span>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Branch') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->branch->name ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Academy') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->academy->name ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Supplier') }}:</span>
                                    <span
                                        class="text-charcoal-black">{{ $inventory->supplier->localized_name ?? 'N/A' }}</span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Status') }}:</span>
                                    <span class="badge {{ $inventory->stock_status_badge_class }}">
                                        {{ $inventory->stock_status_text }}
                                    </span>
                                </div>
                                <div class="flex justify-between py-2 border-b border-light-gray">
                                    <span class="font-medium text-dark-gray">{{ __('Location') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->full_location }}</span>
                                </div>
                                <div class="flex justify-between py-2">
                                    <span class="font-medium text-dark-gray">{{ __('Barcode') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->barcode ?? '-' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Stock Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Stock Information') }}</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-blue-600 mb-2">{{ $inventory->current_stock }}</div>
                                <p class="text-dark-gray text-sm">{{ __('Current Stock') }}</p>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-yellow-600 mb-2">{{ $inventory->reserved_stock }}</div>
                                <p class="text-dark-gray text-sm">{{ __('Reserved') }}</p>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-green-600 mb-2">{{ $inventory->available_stock }}</div>
                                <p class="text-dark-gray text-sm">{{ __('Available') }}</p>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-purple-600 mb-2">{{ $inventory->minimum_stock }}</div>
                                <p class="text-dark-gray text-sm">{{ __('Minimum') }}</p>
                            </div>
                        </div>

                        <div class="border-t border-light-gray pt-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="flex justify-between">
                                    <span class="font-medium text-dark-gray">{{ __('Maximum Stock') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->maximum_stock }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="font-medium text-dark-gray">{{ __('Reorder Quantity') }}:</span>
                                    <span class="text-charcoal-black">{{ $inventory->reorder_quantity }}</span>
                                </div>
                                <div class="text-center">
                                    @if ($inventory->needs_reorder)
                                        <span class="badge-bank badge-warning">{{ __('Needs Reorder') }}</span>
                                        <div class="text-sm text-dark-gray mt-1">{{ __('Suggested') }}:
                                            {{ $inventory->reorder_suggestion }}</div>
                                    @else
                                        <span class="badge-bank badge-success">{{ __('Stock OK') }}</span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{ __('Pricing Information') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h5 class="text-primary">{{ $inventory->formatted_cost_price }}</h5>
                                    <p class="text-muted mb-0">{{ __('Cost Price') }}</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h5 class="text-success">{{ $inventory->formatted_selling_price }}</h5>
                                    <p class="text-muted mb-0">{{ __('Selling Price') }}</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h5 class="text-info">{{ number_format($inventory->profit_margin, 1) }}%</h5>
                                    <p class="text-muted mb-0">{{ __('Profit Margin') }}</p>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="text-center">
                                    <h5 class="text-warning">{{ $inventory->formatted_stock_value }}</h5>
                                    <p class="text-muted mb-0">{{ __('Stock Value') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Stock Movements -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{ __('Recent Stock Movements') }}</h6>
                    </div>
                    <div class="card-body">
                        @if ($recentMovements->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>{{ __('Date') }}</th>
                                            <th>{{ __('Type') }}</th>
                                            <th>{{ __('Quantity') }}</th>
                                            <th>{{ __('Reference') }}</th>
                                            <th>{{ __('User') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($recentMovements as $movement)
                                            <tr>
                                                <td>{{ $movement->formatted_movement_date }}</td>
                                                <td>
                                                    <span class="badge {{ $movement->movement_type_badge_class }}">
                                                        {{ $movement->movement_type_text }}
                                                    </span>
                                                </td>
                                                <td>
                                                    <span
                                                        class="{{ $movement->quantity >= 0 ? 'text-success' : 'text-danger' }}">
                                                        {{ $movement->stock_change_text }}
                                                    </span>
                                                </td>
                                                <td>{{ $movement->reference_number }}</td>
                                                <td>{{ $movement->user->name ?? 'System' }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">{{ __('No stock movements recorded yet') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Quick Stats -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Quick Stats') }}</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center p-4 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600">{{ $stats['total_orders'] ?? 0 }}</div>
                                <div class="text-sm text-dark-gray">{{ __('Total Orders') }}</div>
                            </div>
                            <div class="text-center p-4 bg-yellow-50 rounded-lg">
                                <div class="text-2xl font-bold text-yellow-600">{{ $stats['pending_orders'] ?? 0 }}</div>
                                <div class="text-sm text-dark-gray">{{ __('Pending Orders') }}</div>
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">
                                    {{ abs($stats['last_30_days_sales'] ?? 0) }}</div>
                                <div class="text-sm text-dark-gray">{{ __('Sales (30 days)') }}</div>
                            </div>
                            <div class="text-center p-4 bg-purple-50 rounded-lg">
                                <div class="text-2xl font-bold text-purple-600">
                                    {{ number_format($stats['average_monthly_sales'] ?? 0, 1) }}</div>
                                <div class="text-sm text-dark-gray">{{ __('Avg Monthly') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Quick Actions') }}</h3>
                    </div>
                    <div class="bank-card-body space-y-3">
                        <button class="btn-bank-outline w-full" onclick="adjustStock()">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            {{ __('Adjust Stock') }}
                        </button>
                        <button class="btn-bank-outline w-full" onclick="reserveStock()">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                </path>
                            </svg>
                            {{ __('Reserve Stock') }}
                        </button>
                        @if (Route::has('inventory.edit'))
                            <a href="{{ route('inventory.edit', $inventory) }}" class="btn-bank-outline w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                                {{ __('Edit Item') }}
                            </a>
                        @endif
                        @if ($inventory->needs_reorder)
                            <button class="btn-bank w-full" onclick="createPurchaseOrder()">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01">
                                    </path>
                                </svg>
                                {{ __('Create Purchase Order') }}
                            </button>
                        @endif
                    </div>
                </div>

                <!-- Item Details -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{ __('Additional Details') }}</h6>
                    </div>
                    <div class="card-body">
                        @if ($inventory->description)
                            <p><strong>{{ __('Description') }}:</strong></p>
                            <p class="text-muted">{{ $inventory->localized_description }}</p>
                        @endif

                        @if ($inventory->material)
                            <p><strong>{{ __('Material') }}:</strong> {{ $inventory->material }}</p>
                        @endif

                        @if ($inventory->model)
                            <p><strong>{{ __('Model') }}:</strong> {{ $inventory->model }}</p>
                        @endif

                        @if ($inventory->last_restocked_at)
                            <p><strong>{{ __('Last Restocked') }}:</strong>
                                {{ $inventory->last_restocked_at->format('M d, Y') }}</p>
                        @endif

                        @if ($inventory->last_sold_at)
                            <p><strong>{{ __('Last Sold') }}:</strong> {{ $inventory->last_sold_at->format('M d, Y') }}
                            </p>
                        @endif

                        @if ($inventory->notes)
                            <p><strong>{{ __('Notes') }}:</strong></p>
                            <p class="text-muted">{{ $inventory->localized_notes }}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('styles')
        <style>
            /* Force white text on table headers */
            .table-bank thead {
                background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
            }

            .table-bank thead th {
                color: white !important;
                font-weight: 600 !important;
                font-size: 0.75rem !important;
                text-transform: uppercase !important;
                letter-spacing: 0.1em !important;
                padding: 1rem !important;
                border-bottom: 2px solid #e53e3e !important;
                background: transparent !important;
            }

            .table-bank thead th * {
                color: white !important;
                background: transparent !important;
            }
        </style>
    @endpush

    @push('scripts')
        <script>
            function adjustStock() {
                // This would open a modal for stock adjustment
                alert('{{ __('Stock adjustment feature will be implemented soon!') }}');
            }

            function reserveStock() {
                // This would open a modal for stock reservation
                alert('{{ __('Stock reservation feature will be implemented soon!') }}');
            }

            function createPurchaseOrder() {
                // This would redirect to create purchase order with this item pre-filled
                alert('{{ __('Purchase order creation will be implemented soon!') }}');
            }

            // Ensure table headers have white text
            document.addEventListener('DOMContentLoaded', function() {
                const tableHeaders = document.querySelectorAll('.table-bank thead th');
                tableHeaders.forEach(header => {
                    header.style.setProperty('color', 'white', 'important');
                    header.style.setProperty('background', 'transparent', 'important');

                    const childElements = header.querySelectorAll('*');
                    childElements.forEach(child => {
                        child.style.setProperty('color', 'white', 'important');
                        child.style.setProperty('background', 'transparent', 'important');
                    });
                });
            });
        </script>
    @endpush
@endsection
