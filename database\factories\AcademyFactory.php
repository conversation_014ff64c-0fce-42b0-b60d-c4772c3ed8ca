<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Academy;
use App\Models\Branch;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Academy>
 */
class AcademyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $sports = ['Swimming', 'Football', 'Basketball', 'Tennis', 'Volleyball', 'Badminton', 'Table Tennis'];
        $sport = fake()->randomElement($sports);
        
        return [
            'branch_id' => Branch::factory(),
            'name' => $sport . ' Academy',
            'description' => 'Professional ' . strtolower($sport) . ' training for all ages and skill levels.',
            'coach_name' => 'Coach ' . fake()->firstName(),
            'coach_phone' => '+971' . fake()->numerify('5########'),
            'status' => true,
        ];
    }

    /**
     * Indicate that the academy is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }
}
