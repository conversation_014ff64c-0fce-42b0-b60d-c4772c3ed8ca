{{-- Sidebar Navigation for UAE English Sports Academy --}}
{{-- Bank-style navigation with Quick Actions styling and RTL support --}}

@php
    $isRtl = app()->getLocale() === 'ar';
    $textDirection = $textDirection ?? ($isRtl ? 'rtl' : 'ltr');
@endphp

{{-- Dashboard --}}
<div class="nav-item mb-2" dir="{{ $textDirection }}">
    <a href="{{ route('dashboard') }}"
        class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('dashboard') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
        </svg>
        <span data-trans-key="Dashboard" data-context="dashboard">{{ __('dashboard.Dashboard') }}</span>
    </a>
</div>

@if (Auth::user()->role === 'admin' || Auth::user()->role === 'branch_manager')
    {{-- Branch Management --}}
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('branches.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('branches.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                </path>
            </svg>
            <span data-trans-key="Branch Management"
                data-context="dashboard">{{ __('dashboard.Branch Management') }}</span>
        </a>
    </div>
@endif

{{-- Academy Management --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('academies.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('academies.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z">
                </path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                </path>
            </svg>
            <span>{{ __('dashboard.Academy Management') }}</span>
        </a>
    </div>
@endif

{{-- Venue Management --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('venues.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('venues.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span data-trans-key="Venue Management"
                data-context="dashboard">{{ __('dashboard.Venue Management') }}</span>
        </a>
    </div>
@endif

{{-- Program Management --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('programs.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('programs.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                </path>
            </svg>
            <span data-trans-key="Program Management"
                data-context="dashboard">{{ __('dashboard.Program Management') }}</span>
        </a>
    </div>
@endif

{{-- Student Management --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('students.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('students.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                </path>
            </svg>
            <span data-trans-key="Student Management"
                data-context="dashboard">{{ __('dashboard.Student Management') }}</span>
        </a>
    </div>
@endif

{{-- Payment Management --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('payments.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('payments.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span data-trans-key="Payment Management"
                data-context="dashboard">{{ __('dashboard.Payment Management') }}</span>
        </a>
    </div>
@endif

{{-- Uniform Management --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('uniforms.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('uniforms.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            <span data-trans-key="Uniform Management"
                data-context="dashboard">{{ __('dashboard.Uniform Management') }}</span>
        </a>
    </div>
@endif

{{-- Inventory Management --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('inventory.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('inventory.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
            </svg>
            <span data-trans-key="Inventory Management"
                data-context="dashboard">{{ __('Inventory Management') }}</span>
        </a>
    </div>
@endif

{{-- Field Reservations --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('reservations.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('reservations.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <span data-trans-key="Field Reservations"
                data-context="dashboard">{{ __('dashboard.Field Reservations') }}</span>
        </a>
    </div>
@endif

{{-- Manage Customers --}}
@if (Auth::user()->role === 'admin' ||
        Auth::user()->role === 'branch_manager' ||
        Auth::user()->role === 'academy_manager')
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('customers.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('customers.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                </path>
            </svg>
            <span data-trans-key="Manage Customers"
                data-context="dashboard">{{ __('dashboard.Manage Customers') }}</span>
        </a>
    </div>
@endif

{{-- Reports Section Header --}}
<div class="nav-item mt-6 mb-3">
    <div class="px-4 py-2">
        <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">{{ __('dashboard.Reports') }}</h4>
    </div>
</div>

{{-- Reports Dashboard --}}
@if (in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager']))
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('reports.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('reports.index') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                </path>
            </svg>
            <span data-trans-key="Reports Dashboard"
                data-context="dashboard">{{ __('dashboard.Reports Dashboard') }}</span>
        </a>
    </div>
@endif

{{-- Admin Section Header --}}
@if (Auth::user()->role === 'admin')
    <div class="nav-item mt-6 mb-3">
        <div class="px-4 py-2">
            <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">
                {{ __('dashboard.Administration') }}</h4>
        </div>
    </div>

    {{-- User Management --}}
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('users.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('users.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                </path>
            </svg>
            <span data-trans-key="User Management"
                data-context="dashboard">{{ __('dashboard.User Management') }}</span>
        </a>
    </div>

    {{-- System Settings --}}
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('settings.index') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full {{ request()->routeIs('settings.*') ? 'ring-2 ring-white ring-opacity-50' : '' }}">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                </path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                </path>
            </svg>
            <span data-trans-key="system_settings"
                data-context="dashboard">{{ __('dashboard.system_settings') }}</span>
        </a>
    </div>
@endif

{{-- Quick Actions Section Header --}}
<div class="nav-item mt-6 mb-3">
    <div class="px-4 py-2">
        <h4 class="text-xs font-semibold text-gray-400 uppercase tracking-wider">{{ __('dashboard.Quick Actions') }}
        </h4>
    </div>
</div>

{{-- Add Student --}}
@can('create', App\Models\Student::class)
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('students.create') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                </path>
            </svg>
            <span data-trans-key="Add Student" data-context="dashboard">{{ __('dashboard.Add Student') }}</span>
        </a>
    </div>
@endcan

{{-- Add Payment --}}
@can('create', App\Models\Payment::class)
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('payments.create') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
            <span data-trans-key="Add Payment" data-context="dashboard">{{ __('dashboard.Add Payment') }}</span>
        </a>
    </div>
@endcan

{{-- Order Uniform --}}
@if (in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager']))
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('uniforms.create') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
            <span data-trans-key="Order Uniform" data-context="dashboard">{{ __('dashboard.Order Uniform') }}</span>
        </a>
    </div>
@endif

{{-- Add Venue --}}
@can('create', App\Models\Venue::class)
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('venues.create') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                </path>
            </svg>
            <span data-trans-key="Add Venue" data-context="dashboard">{{ __('dashboard.Add Venue') }}</span>
        </a>
    </div>
@endcan

{{-- Add Customer --}}
@can('create', App\Models\Customer::class)
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('customers.create') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                </path>
            </svg>
            <span data-trans-key="Add Customer" data-context="dashboard">{{ __('dashboard.Add Customer') }}</span>
        </a>
    </div>
@endcan

{{-- Add Inventory --}}
@can('create', App\Models\UniformInventory::class)
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('inventory.create') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                </path>
            </svg>
            <span data-trans-key="Add Inventory" data-context="dashboard">{{ __('dashboard.Add Inventory') }}</span>
        </a>
    </div>
@endcan

{{-- Add Reservation --}}
@can('create', App\Models\Reservation::class)
    <div class="nav-item mb-2" dir="{{ $textDirection }}">
        <a href="{{ route('reservations.create') }}"
            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                </path>
            </svg>
            <span data-trans-key="Add Reservation" data-context="dashboard">{{ __('dashboard.Add Reservation') }}</span>
        </a>
    </div>
@endcan
