<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Program;
use App\Models\Academy;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Program>
 */
class ProgramFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $levels = ['Beginner', 'Intermediate', 'Advanced', 'Professional'];
        $times = [
            ['16:00:00', '17:00:00'],
            ['17:00:00', '18:00:00'],
            ['18:00:00', '19:00:00'],
            ['19:00:00', '20:00:00'],
        ];
        $timeSlot = fake()->randomElement($times);
        
        $days = [
            ['SAT', 'MON', 'WED'],
            ['SUN', 'TUE', 'THU'],
            ['FRI', 'SAT'],
            ['SAT', 'SUN'],
        ];
        
        return [
            'academy_id' => Academy::factory(),
            'name' => fake()->randomElement($levels) . ' Training Program',
            'description' => 'Comprehensive training program for ' . strtolower(fake()->randomElement($levels)) . ' level athletes.',
            'days' => json_encode(fake()->randomElement($days)),
            'classes' => fake()->numberBetween(2, 5),
            'price' => fake()->randomFloat(2, 200, 800),
            'start_time' => $timeSlot[0],
            'end_time' => $timeSlot[1],
            'max_students' => fake()->numberBetween(10, 25),
            'status' => true,
        ];
    }

    /**
     * Indicate that the program is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }
}
