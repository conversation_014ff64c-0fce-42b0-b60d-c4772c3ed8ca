@extends('layouts.dashboard')

@section('title', 'Venue Management - LEADERS SPORTS SERVICES')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Venue Management</h1>
                <p class="text-lg text-dark-gray">Manage sports venues and facilities</p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $venues->total() }} Total Venues
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('create', App\Models\Venue::class)
                <a href="{{ route('venues.create') }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                        </path>
                    </svg>
                    Add New Venue
                </a>
            @endcan
            @can('export', App\Models\Venue::class)
                <div class="flex items-center space-x-2">
                    <button onclick="exportData('excel')" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Export Excel
                    </button>
                    <button onclick="exportData('pdf')" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                            </path>
                        </svg>
                        Export PDF
                    </button>
                </div>
            @endcan
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="venueManagement()" x-init="init()">

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <!-- Total Venues -->
            <div class="stats-card scale-in" style="animation-delay: 0.1s;">
                <div class="stats-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_venues'] ?? 0 }}</div>
                <div class="stats-label">Total Venues</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $stats['active_venues'] ?? 0 }} Active
                </div>
            </div>

            <!-- Total Fields -->
            <div class="stats-card scale-in" style="animation-delay: 0.2s;">
                <div class="stats-icon bg-gradient-to-br from-success-green to-green-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_fields'] ?? 0 }}</div>
                <div class="stats-label">Total Fields</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    {{ $stats['active_fields'] ?? 0 }} Active
                </div>
            </div>

            <!-- Total Reservations -->
            <div class="stats-card scale-in" style="animation-delay: 0.3s;">
                <div class="stats-icon bg-gradient-to-br from-info-blue to-blue-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_reservations'] ?? 0 }}</div>
                <div class="stats-label">Total Reservations</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    {{ $stats['today_reservations'] ?? 0 }} Today
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="stats-card scale-in" style="animation-delay: 0.4s;">
                <div class="stats-icon bg-gradient-to-br from-gold-yellow to-yellow-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value text-leaders-red">{{ number_format($stats['total_revenue'] ?? 0, 0) }}</div>
                <div class="stats-label">Total Revenue (AED)</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    {{ number_format($stats['monthly_revenue'] ?? 0, 0) }} This Month
                </div>
            </div>
        </div>

        <!-- Advanced Search & Filters -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">Search & Filters</h3>
                    <p class="bank-card-subtitle">Find venues by location, status, and other criteria</p>
                </div>
                <div
                    class="w-12 h-12 bg-gradient-to-br from-info-blue to-blue-600 rounded-lg flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z">
                        </path>
                    </svg>
                </div>
            </div>
            <div class="bank-card-body">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label for="search" class="form-label-bank">Search</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="w-5 h-5 text-medium-gray" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <input type="text" id="search" name="search" value="{{ request('search') }}"
                                class="form-input-bank pl-10" placeholder="Search venues...">
                        </div>
                    </div>

                    <div>
                        <label for="city" class="form-label-bank">City</label>
                        <select id="city" name="city" class="form-select-bank">
                            <option value="">All Cities</option>
                            @foreach ($cities as $city)
                                <option value="{{ $city }}" {{ request('city') == $city ? 'selected' : '' }}>
                                    {{ $city }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div>
                        <label for="status" class="form-label-bank">Status</label>
                        <select id="status" name="status" class="form-select-bank">
                            <option value="">All Status</option>
                            <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive
                            </option>
                        </select>
                    </div>

                    <div class="flex items-end space-x-2">
                        <button type="submit" class="btn-bank">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z">
                                </path>
                            </svg>
                            Filter
                        </button>
                        <a href="{{ route('venues.index') }}" class="btn-bank btn-bank-outline">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                </path>
                            </svg>
                            Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">All Venues</h3>
                    <p class="bank-card-subtitle">
                        Showing {{ $venues->firstItem() ?? 0 }} to {{ $venues->lastItem() ?? 0 }}
                        of {{ $venues->total() }} venues
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- View Toggle -->
                    <div class="flex items-center bg-off-white rounded-lg p-1">
                        <button @click="viewMode = 'table'" :class="viewMode === 'table' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M3 6h18m-9 10h9"></path>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bank-card-body p-0">
                <!-- Table View -->
                <div x-show="viewMode === 'table'" x-transition>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-light-gray">
                            <thead class="bg-off-white">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-medium-gray uppercase tracking-wider">
                                        Venue
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-medium-gray uppercase tracking-wider">
                                        Location
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-medium-gray uppercase tracking-wider">
                                        Fields
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-medium-gray uppercase tracking-wider">
                                        Base Rate
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-medium-gray uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-medium-gray uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($venues as $venue)
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 h-10 w-10">
                                                    <div
                                                        class="h-10 w-10 rounded-full bg-leaders-red flex items-center justify-center">
                                                        <span class="text-white font-medium text-sm">
                                                            {{ strtoupper(substr($venue->name, 0, 2)) }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-gray-900">
                                                        {{ $venue->localized_name }}
                                                    </div>
                                                    <div class="text-sm text-gray-500">{{ $venue->code }}</div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $venue->city }}</div>
                                            <div class="text-sm text-gray-500">
                                                {{ Str::limit($venue->localized_address, 30) }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900">{{ $venue->fields_count }}
                                                {{ __('Fields') }}
                                            </div>
                                            <div class="text-sm text-gray-500">{{ $venue->active_fields_count }}
                                                {{ __('Active') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                {{ $venue->formatted_hourly_rate }}
                                            </div>
                                            <div class="text-sm text-gray-500">{{ __('per hour') }}</div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span
                                                class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {{ $venue->status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                                {{ $venue->status_text }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex space-x-2">
                                                <a href="{{ route('venues.show', $venue) }}"
                                                    class="text-indigo-600 hover:text-indigo-900">{{ __('View') }}</a>
                                                <a href="{{ route('venues.edit', $venue) }}"
                                                    class="text-yellow-600 hover:text-yellow-900">{{ __('Edit') }}</a>
                                                @if ($venue->status)
                                                    <button onclick="toggleStatus({{ $venue->id }})"
                                                        class="text-red-600 hover:text-red-900">{{ __('Deactivate') }}</button>
                                                @else
                                                    <button onclick="toggleStatus({{ $venue->id }})"
                                                        class="text-green-600 hover:text-green-900">{{ __('Activate') }}</button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-12 text-center">
                                            <div class="flex flex-col items-center">
                                                <svg class="w-12 h-12 text-medium-gray mb-4" fill="none"
                                                    stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                                    </path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <p class="text-dark-gray mb-4">No venues found</p>
                                                @can('create', App\Models\Venue::class)
                                                    <a href="{{ route('venues.create') }}" class="btn-bank btn-bank-sm">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                        </svg>
                                                        Create First Venue
                                                    </a>
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Grid View -->
                <div x-show="viewMode === 'grid'" x-transition>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
                        @forelse($venues as $venue)
                            <div
                                class="bg-white border border-light-gray rounded-lg p-6 hover:shadow-md transition-shadow">
                                <div class="flex items-center space-x-4 mb-4">
                                    <div
                                        class="w-12 h-12 rounded-full bg-gradient-to-br from-leaders-red to-leaders-deep-red flex items-center justify-center">
                                        <span class="text-white font-medium text-sm">
                                            {{ strtoupper(substr($venue->name, 0, 2)) }}
                                        </span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-charcoal-black">{{ $venue->localized_name }}</h4>
                                        <p class="text-sm text-medium-gray">{{ $venue->code }}</p>
                                    </div>
                                    <span class="badge-bank {{ $venue->status ? 'badge-success' : 'badge-error' }}">
                                        {{ $venue->status_text }}
                                    </span>
                                </div>

                                <div class="space-y-3 mb-4">
                                    <div class="flex items-center text-sm">
                                        <svg class="w-4 h-4 text-medium-gray mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                            </path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        </svg>
                                        <span class="text-dark-gray">{{ $venue->city }}</span>
                                    </div>
                                    <div class="flex items-center text-sm">
                                        <svg class="w-4 h-4 text-medium-gray mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2">
                                            </path>
                                        </svg>
                                        <span class="text-dark-gray">{{ $venue->fields_count }} Fields
                                            ({{ $venue->active_fields_count }} Active)
                                        </span>
                                    </div>
                                    <div class="flex items-center text-sm">
                                        <svg class="w-4 h-4 text-medium-gray mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                            </path>
                                        </svg>
                                        <span
                                            class="text-dark-gray font-medium">{{ $venue->formatted_hourly_rate }}/hour</span>
                                    </div>
                                </div>

                                <div class="flex items-center justify-between pt-4 border-t border-light-gray">
                                    <div class="flex items-center space-x-2">
                                        <a href="{{ route('venues.show', $venue) }}"
                                            class="btn-bank btn-bank-sm btn-bank-outline">
                                            View
                                        </a>
                                        @can('update', $venue)
                                            <a href="{{ route('venues.edit', $venue) }}" class="btn-bank btn-bank-sm">
                                                Edit
                                            </a>
                                        @endcan
                                    </div>
                                    @can('update', $venue)
                                        <button onclick="toggleStatus({{ $venue->id }})"
                                            class="text-sm {{ $venue->status ? 'text-error-red hover:text-red-700' : 'text-success-green hover:text-green-700' }} font-medium">
                                            {{ $venue->status ? 'Deactivate' : 'Activate' }}
                                        </button>
                                    @endcan
                                </div>
                            </div>
                        @empty
                            <div class="col-span-full text-center py-12">
                                <svg class="w-12 h-12 text-medium-gray mx-auto mb-4" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                    </path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                <p class="text-dark-gray mb-4">No venues found</p>
                                @can('create', App\Models\Venue::class)
                                    <a href="{{ route('venues.create') }}" class="btn-bank">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Create First Venue
                                    </a>
                                @endcan
                            </div>
                        @endforelse
                    </div>
                </div>

                <!-- Pagination -->
                @if ($venues->hasPages())
                    <div class="px-6 py-4 border-t border-light-gray">
                        {{ $venues->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function venueManagement() {
            return {
                viewMode: localStorage.getItem('venueViewMode') || 'table',

                init() {
                    this.$watch('viewMode', (value) => {
                        localStorage.setItem('venueViewMode', value);
                    });
                }
            }
        }

        function toggleStatus(venueId) {
            if (confirm('Are you sure you want to change the venue status?')) {
                fetch(`/venues/${venueId}/toggle-status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert(data.message || 'An error occurred');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred');
                    });
            }
        }

        function exportData(format) {
            const url = format === 'excel' ? '{{ route('venues.export.excel') }}' : '{{ route('venues.export.pdf') }}';
            const params = new URLSearchParams(window.location.search);
            window.open(`${url}?${params.toString()}`, '_blank');
        }
    </script>
@endpush
