<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;
use App\Models\Setting;

class Payment extends Model
{
    use HasFactory;
    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Automatically determine payment type before saving
        static::saving(function ($payment) {
            $payment->setPaymentTypeAutomatically();
        });
    }

    protected $fillable = [
        'student_id',
        'branch_id',
        'academy_id',
        'amount',
        'subtotal',
        'vat_rate',
        'vat_amount',
        'total_amount',
        'vat_inclusive',
        'discount',
        'currency',
        'payment_method',
        'payment_date',
        'start_date',
        'end_date',
        'status',
        'reset_num',
        'class_time_from',
        'class_time_to',
        'renewal',
        'payment_type',
        'note',
        'reference_number',
        'description',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'subtotal' => 'decimal:2',
        'vat_rate' => 'decimal:2',
        'vat_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'vat_inclusive' => 'boolean',
        'discount' => 'decimal:2',
        'payment_date' => 'date',
        'start_date' => 'date',
        'end_date' => 'date',
        'renewal' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_amount',
        'formatted_subtotal',
        'formatted_vat_amount',
        'formatted_total_amount',
        'net_amount',
        'formatted_net_amount',
        'status_text',
        'status_badge_class',
        'method_text',
        'payment_type_text',
        'payment_type_badge_class',
        'formatted_payment_date',
        'formatted_start_date',
        'formatted_end_date',
    ];

    /**
     * Get the student that owns the payment.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the branch that owns the payment.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the payment.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    // Computed Properties

    /**
     * Get formatted amount in AED.
     */
    public function getFormattedAmountAttribute(): string
    {
        return 'AED ' . number_format($this->amount, 2);
    }

    /**
     * Get formatted subtotal in AED.
     */
    public function getFormattedSubtotalAttribute(): string
    {
        return 'AED ' . number_format($this->subtotal ?? $this->amount, 2);
    }

    /**
     * Get formatted VAT amount in AED.
     */
    public function getFormattedVatAmountAttribute(): string
    {
        return 'AED ' . number_format($this->vat_amount ?? 0, 2);
    }

    /**
     * Get formatted total amount in AED.
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return 'AED ' . number_format($this->total_amount ?? $this->amount, 2);
    }

    /**
     * Get net amount after discount.
     */
    public function getNetAmountAttribute(): float
    {
        return $this->amount - ($this->discount ?? 0);
    }

    /**
     * Get formatted net amount in AED.
     */
    public function getFormattedNetAmountAttribute(): string
    {
        return 'AED ' . number_format($this->net_amount, 2);
    }

    /**
     * Get human-readable status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'active' => 'Active',
            'expired' => 'Expired',
            'pending' => 'Pending',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
            'cancelled' => 'Cancelled',
            default => 'Unknown'
        };
    }

    /**
     * Get status badge CSS class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'active' => 'badge-success',
            'expired' => 'badge-danger',
            'pending' => 'badge-warning',
            'failed' => 'badge-danger',
            'refunded' => 'badge-info',
            'cancelled' => 'badge-secondary',
            default => 'badge-secondary'
        };
    }

    /**
     * Get human-readable payment method text.
     */
    public function getMethodTextAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'Cash',
            'card' => 'Credit/Debit Card',
            'bank_transfer' => 'Bank Transfer',
            'online' => 'Online Payment',
            'cheque' => 'Cheque',
            default => 'Other'
        };
    }

    /**
     * Get human-readable payment type text.
     */
    public function getPaymentTypeTextAttribute(): string
    {
        return match ($this->payment_type) {
            'new_entry' => 'New Entry',
            'renewal' => 'Renewal',
            'regular' => 'Regular',
            default => 'Regular'
        };
    }

    /**
     * Get payment type badge CSS class.
     */
    public function getPaymentTypeBadgeClassAttribute(): string
    {
        return match ($this->payment_type) {
            'new_entry' => 'badge-primary',
            'renewal' => 'badge-info',
            'regular' => 'badge-secondary',
            default => 'badge-secondary'
        };
    }

    /**
     * Get formatted payment date.
     */
    public function getFormattedPaymentDateAttribute(): string
    {
        return $this->payment_date ? $this->payment_date->format('M d, Y') : '';
    }

    /**
     * Get formatted start date.
     */
    public function getFormattedStartDateAttribute(): string
    {
        return $this->start_date ? $this->start_date->format('M d, Y') : '';
    }

    /**
     * Get formatted end date.
     */
    public function getFormattedEndDateAttribute(): string
    {
        return $this->end_date ? $this->end_date->format('M d, Y') : '';
    }

    // Query Scopes

    /**
     * Scope a query to only include completed payments (now active payments).
     */
    public function scopeCompleted(Builder $query): Builder
    {
        // 'completed' now maps to 'active' status
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include pending payments.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include active payments.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include expired payments.
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('status', 'expired');
    }

    /**
     * Scope a query to include payments that should be expired.
     */
    public function scopeShouldBeExpired(Builder $query): Builder
    {
        return $query->where('status', 'active')
            ->where('end_date', '<', now()->startOfDay());
    }

    /**
     * Scope a query to filter by payment method.
     */
    public function scopeByMethod(Builder $query, string $method): Builder
    {
        return $query->where('payment_method', $method);
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope a query to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope a query to filter by date range.
     */
    public function scopeByDateRange(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('payment_date', [$startDate, $endDate]);
    }

    /**
     * Scope a query to only include new entry payments.
     */
    public function scopeNewEntry(Builder $query): Builder
    {
        return $query->where('payment_type', 'new_entry');
    }

    /**
     * Scope a query to only include renewal payments.
     */
    public function scopeRenewal(Builder $query): Builder
    {
        return $query->where('payment_type', 'renewal');
    }

    /**
     * Scope a query to only include regular payments.
     */
    public function scopeRegular(Builder $query): Builder
    {
        return $query->where('payment_type', 'regular');
    }

    /**
     * Scope a query to filter by payment type.
     */
    public function scopeByPaymentType(Builder $query, string $paymentType): Builder
    {
        return $query->where('payment_type', $paymentType);
    }

    // Utility Methods

    /**
     * Check if payment is completed (now checks for active status).
     */
    public function isCompleted(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if payment is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Check if payment is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired';
    }

    /**
     * Check if payment should be expired based on end date.
     */
    public function shouldBeExpired(): bool
    {
        return $this->end_date && $this->end_date->isPast() && $this->isActive();
    }

    /**
     * Mark payment as completed (now sets to active).
     */
    public function markAsCompleted(): bool
    {
        $this->status = 'active';
        $this->payment_date = now();
        return $this->save();
    }

    /**
     * Mark payment as active.
     */
    public function markAsActive(): bool
    {
        $this->status = 'active';
        return $this->save();
    }

    /**
     * Mark payment as expired.
     */
    public function markAsExpired(): bool
    {
        $this->status = 'expired';
        return $this->save();
    }

    /**
     * Mark payment as failed.
     */
    public function markAsFailed(): bool
    {
        $this->status = 'failed';
        return $this->save();
    }

    /**
     * Generate reference number.
     */
    public static function generateReferenceNumber(): string
    {
        return 'PAY-' . date('Ymd') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate VAT amounts based on the payment amount and settings.
     */
    public function calculateVat(): void
    {
        $vatEnabled = Setting::get('vat_enabled', true);
        $vatRate = Setting::get('vat_rate', 5.00);
        $vatInclusiveByDefault = Setting::get('vat_inclusive_by_default', false);

        if (!$vatEnabled) {
            $this->subtotal = $this->amount;
            $this->vat_rate = 0;
            $this->vat_amount = 0;
            $this->total_amount = $this->amount;
            $this->vat_inclusive = false;
            return;
        }

        // Set VAT rate if not already set
        if (!$this->vat_rate) {
            $this->vat_rate = $vatRate;
        }

        // Set VAT inclusive flag if not already set
        if ($this->vat_inclusive === null) {
            $this->vat_inclusive = $vatInclusiveByDefault;
        }

        if ($this->vat_inclusive) {
            // Amount includes VAT - calculate subtotal and VAT amount
            $this->total_amount = $this->amount;
            $this->subtotal = $this->amount / (1 + ($this->vat_rate / 100));
            $this->vat_amount = $this->amount - $this->subtotal;
        } else {
            // Amount excludes VAT - calculate VAT amount and total
            $this->subtotal = $this->amount;
            $this->vat_amount = $this->amount * ($this->vat_rate / 100);
            $this->total_amount = $this->amount + $this->vat_amount;
        }

        // Round to 2 decimal places
        $this->subtotal = round($this->subtotal, 2);
        $this->vat_amount = round($this->vat_amount, 2);
        $this->total_amount = round($this->total_amount, 2);
    }

    /**
     * Get the final amount to be paid (considering VAT).
     */
    public function getFinalAmountAttribute(): float
    {
        return $this->total_amount ?? $this->amount;
    }

    /**
     * Get the formatted final amount.
     */
    public function getFormattedFinalAmountAttribute(): string
    {
        return 'AED ' . number_format($this->final_amount, 2);
    }

    /**
     * Check if VAT is enabled for this payment.
     */
    public function hasVat(): bool
    {
        return $this->vat_amount > 0;
    }

    /**
     * Get VAT details for display.
     */
    public function getVatDetails(): array
    {
        return [
            'enabled' => $this->hasVat(),
            'rate' => $this->vat_rate ?? 0,
            'subtotal' => $this->subtotal ?? $this->amount,
            'vat_amount' => $this->vat_amount ?? 0,
            'total_amount' => $this->total_amount ?? $this->amount,
            'inclusive' => $this->vat_inclusive ?? false,
            'formatted_subtotal' => $this->formatted_subtotal,
            'formatted_vat_amount' => $this->formatted_vat_amount,
            'formatted_total_amount' => $this->formatted_total_amount,
        ];
    }

    // Payment Type Methods

    /**
     * Automatically determine payment type based on student's payment history and status.
     */
    public function determinePaymentType(): string
    {
        if (!$this->student_id) {
            return 'regular';
        }

        $student = $this->student;
        if (!$student) {
            return 'regular';
        }

        // Check if student has any previous completed payments
        $previousPayments = $student->payments()
            ->where('id', '!=', $this->id ?? 0) // Exclude current payment if updating
            ->whereIn('status', ['completed', 'active'])
            ->count();

        // New Entry: No previous payments
        if ($previousPayments === 0) {
            return 'new_entry';
        }

        // Check if student was inactive/suspended and is now returning
        // Get the last completed payment
        $lastPayment = $student->payments()
            ->where('id', '!=', $this->id ?? 0)
            ->whereIn('status', ['completed', 'active'])
            ->orderBy('end_date', 'desc')
            ->first();

        if ($lastPayment) {
            // If there's a gap of more than 30 days between last payment end date and current payment start date
            $daysSinceLastPayment = $lastPayment->end_date->diffInDays($this->start_date ?? now());

            // If student was inactive/suspended OR there's a significant gap, consider it a renewal
            if ($student->status === 'inactive' || $student->status === 'suspended' || $daysSinceLastPayment > 30) {
                return 'renewal';
            }
        }

        // Regular payment for active continuing students
        return 'regular';
    }

    /**
     * Set payment type automatically if not already set.
     */
    public function setPaymentTypeAutomatically(): void
    {
        if (empty($this->payment_type) || $this->payment_type === 'regular') {
            $this->payment_type = $this->determinePaymentType();
        }
    }

    /**
     * Check if this is a new entry payment.
     */
    public function isNewEntry(): bool
    {
        return $this->payment_type === 'new_entry';
    }

    /**
     * Check if this is a renewal payment.
     */
    public function isRenewal(): bool
    {
        return $this->payment_type === 'renewal';
    }

    /**
     * Check if this is a regular payment.
     */
    public function isRegular(): bool
    {
        return $this->payment_type === 'regular';
    }

    /**
     * Get available payment types.
     */
    public static function getPaymentTypes(): array
    {
        return [
            'new_entry' => 'New Entry',
            'renewal' => 'Renewal',
            'regular' => 'Regular'
        ];
    }
}
