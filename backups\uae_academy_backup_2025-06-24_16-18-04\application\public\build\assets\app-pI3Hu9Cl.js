var So=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var mc=So((Fc,Z)=>{function Mn(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ao}=Object.prototype,{getPrototypeOf:It}=Object,{iterator:Je,toStringTag:Bn}=Symbol,Ve=(e=>t=>{const n=Ao.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),F=e=>(e=e.toLowerCase(),t=>Ve(t)===e),Xe=e=>t=>typeof t===e,{isArray:le}=Array,Se=Xe("undefined");function vo(e){return e!==null&&!Se(e)&&e.constructor!==null&&!Se(e.constructor)&&N(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const In=F("ArrayBuffer");function To(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&In(e.buffer),t}const Co=Xe("string"),N=Xe("function"),jn=Xe("number"),Ge=e=>e!==null&&typeof e=="object",Ro=e=>e===!0||e===!1,Me=e=>{if(Ve(e)!=="object")return!1;const t=It(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Bn in e)&&!(Je in e)},Oo=F("Date"),Lo=F("File"),No=F("Blob"),ko=F("FileList"),Po=e=>Ge(e)&&N(e.pipe),Do=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||N(e.append)&&((t=Ve(e))==="formdata"||t==="object"&&N(e.toString)&&e.toString()==="[object FormData]"))},Fo=F("URLSearchParams"),[Mo,Bo,Io,jo]=["ReadableStream","Request","Response","Headers"].map(F),$o=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ve(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,o;if(typeof e!="object"&&(e=[e]),le(e))for(r=0,o=e.length;r<o;r++)t.call(null,e[r],r,e);else{const s=n?Object.getOwnPropertyNames(e):Object.keys(e),i=s.length;let a;for(r=0;r<i;r++)a=s[r],t.call(null,e[a],a,e)}}function $n(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,o;for(;r-- >0;)if(o=n[r],t===o.toLowerCase())return o;return null}const W=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,qn=e=>!Se(e)&&e!==W;function pt(){const{caseless:e}=qn(this)&&this||{},t={},n=(r,o)=>{const s=e&&$n(t,o)||o;Me(t[s])&&Me(r)?t[s]=pt(t[s],r):Me(r)?t[s]=pt({},r):le(r)?t[s]=r.slice():t[s]=r};for(let r=0,o=arguments.length;r<o;r++)arguments[r]&&ve(arguments[r],n);return t}const qo=(e,t,n,{allOwnKeys:r}={})=>(ve(t,(o,s)=>{n&&N(o)?e[s]=Mn(o,n):e[s]=o},{allOwnKeys:r}),e),Uo=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ho=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},zo=(e,t,n,r)=>{let o,s,i;const a={};if(t=t||{},e==null)return t;do{for(o=Object.getOwnPropertyNames(e),s=o.length;s-- >0;)i=o[s],(!r||r(i,e,t))&&!a[i]&&(t[i]=e[i],a[i]=!0);e=n!==!1&&It(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Ko=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},Wo=e=>{if(!e)return null;if(le(e))return e;let t=e.length;if(!jn(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Jo=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&It(Uint8Array)),Vo=(e,t)=>{const r=(e&&e[Je]).call(e);let o;for(;(o=r.next())&&!o.done;){const s=o.value;t.call(e,s[0],s[1])}},Xo=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Go=F("HTMLFormElement"),Qo=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,o){return r.toUpperCase()+o}),ln=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Yo=F("RegExp"),Un=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};ve(n,(o,s)=>{let i;(i=t(o,s,e))!==!1&&(r[s]=i||o)}),Object.defineProperties(e,r)},Zo=e=>{Un(e,(t,n)=>{if(N(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(N(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},es=(e,t)=>{const n={},r=o=>{o.forEach(s=>{n[s]=!0})};return le(e)?r(e):r(String(e).split(t)),n},ts=()=>{},ns=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function rs(e){return!!(e&&N(e.append)&&e[Bn]==="FormData"&&e[Je])}const os=e=>{const t=new Array(10),n=(r,o)=>{if(Ge(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[o]=r;const s=le(r)?[]:{};return ve(r,(i,a)=>{const c=n(i,o+1);!Se(c)&&(s[a]=c)}),t[o]=void 0,s}}return r};return n(e,0)},ss=F("AsyncFunction"),is=e=>e&&(Ge(e)||N(e))&&N(e.then)&&N(e.catch),Hn=((e,t)=>e?setImmediate:t?((n,r)=>(W.addEventListener("message",({source:o,data:s})=>{o===W&&s===n&&r.length&&r.shift()()},!1),o=>{r.push(o),W.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",N(W.postMessage)),as=typeof queueMicrotask<"u"?queueMicrotask.bind(W):typeof process<"u"&&process.nextTick||Hn,cs=e=>e!=null&&N(e[Je]),d={isArray:le,isArrayBuffer:In,isBuffer:vo,isFormData:Do,isArrayBufferView:To,isString:Co,isNumber:jn,isBoolean:Ro,isObject:Ge,isPlainObject:Me,isReadableStream:Mo,isRequest:Bo,isResponse:Io,isHeaders:jo,isUndefined:Se,isDate:Oo,isFile:Lo,isBlob:No,isRegExp:Yo,isFunction:N,isStream:Po,isURLSearchParams:Fo,isTypedArray:Jo,isFileList:ko,forEach:ve,merge:pt,extend:qo,trim:$o,stripBOM:Uo,inherits:Ho,toFlatObject:zo,kindOf:Ve,kindOfTest:F,endsWith:Ko,toArray:Wo,forEachEntry:Vo,matchAll:Xo,isHTMLForm:Go,hasOwnProperty:ln,hasOwnProp:ln,reduceDescriptors:Un,freezeMethods:Zo,toObjectSet:es,toCamelCase:Qo,noop:ts,toFiniteNumber:ns,findKey:$n,global:W,isContextDefined:qn,isSpecCompliantForm:rs,toJSONObject:os,isAsyncFn:ss,isThenable:is,setImmediate:Hn,asap:as,isIterable:cs};function b(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}d.inherits(b,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:d.toJSONObject(this.config),code:this.code,status:this.status}}});const zn=b.prototype,Kn={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Kn[e]={value:e}});Object.defineProperties(b,Kn);Object.defineProperty(zn,"isAxiosError",{value:!0});b.from=(e,t,n,r,o,s)=>{const i=Object.create(zn);return d.toFlatObject(e,i,function(c){return c!==Error.prototype},a=>a!=="isAxiosError"),b.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,s&&Object.assign(i,s),i};const ls=null;function gt(e){return d.isPlainObject(e)||d.isArray(e)}function Wn(e){return d.endsWith(e,"[]")?e.slice(0,-2):e}function un(e,t,n){return e?e.concat(t).map(function(o,s){return o=Wn(o),!n&&s?"["+o+"]":o}).join(n?".":""):t}function us(e){return d.isArray(e)&&!e.some(gt)}const ds=d.toFlatObject(d,{},null,function(t){return/^is[A-Z]/.test(t)});function Qe(e,t,n){if(!d.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=d.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,f){return!d.isUndefined(f[y])});const r=n.metaTokens,o=n.visitor||u,s=n.dots,i=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&d.isSpecCompliantForm(t);if(!d.isFunction(o))throw new TypeError("visitor must be a function");function l(h){if(h===null)return"";if(d.isDate(h))return h.toISOString();if(!c&&d.isBlob(h))throw new b("Blob is not supported. Use a Buffer instead.");return d.isArrayBuffer(h)||d.isTypedArray(h)?c&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function u(h,y,f){let g=h;if(h&&!f&&typeof h=="object"){if(d.endsWith(y,"{}"))y=r?y:y.slice(0,-2),h=JSON.stringify(h);else if(d.isArray(h)&&us(h)||(d.isFileList(h)||d.endsWith(y,"[]"))&&(g=d.toArray(h)))return y=Wn(y),g.forEach(function(E,A){!(d.isUndefined(E)||E===null)&&t.append(i===!0?un([y],A,s):i===null?y:y+"[]",l(E))}),!1}return gt(h)?!0:(t.append(un(f,y,s),l(h)),!1)}const m=[],p=Object.assign(ds,{defaultVisitor:u,convertValue:l,isVisitable:gt});function w(h,y){if(!d.isUndefined(h)){if(m.indexOf(h)!==-1)throw Error("Circular reference detected in "+y.join("."));m.push(h),d.forEach(h,function(g,_){(!(d.isUndefined(g)||g===null)&&o.call(t,g,d.isString(_)?_.trim():_,y,p))===!0&&w(g,y?y.concat(_):[_])}),m.pop()}}if(!d.isObject(e))throw new TypeError("data must be an object");return w(e),t}function dn(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function jt(e,t){this._pairs=[],e&&Qe(e,this,t)}const Jn=jt.prototype;Jn.append=function(t,n){this._pairs.push([t,n])};Jn.toString=function(t){const n=t?function(r){return t.call(this,r,dn)}:dn;return this._pairs.map(function(o){return n(o[0])+"="+n(o[1])},"").join("&")};function fs(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Vn(e,t,n){if(!t)return e;const r=n&&n.encode||fs;d.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let s;if(o?s=o(t,n):s=d.isURLSearchParams(t)?t.toString():new jt(t,n).toString(r),s){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+s}return e}class fn{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){d.forEach(this.handlers,function(r){r!==null&&t(r)})}}const Xn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ms=typeof URLSearchParams<"u"?URLSearchParams:jt,hs=typeof FormData<"u"?FormData:null,ps=typeof Blob<"u"?Blob:null,gs={isBrowser:!0,classes:{URLSearchParams:ms,FormData:hs,Blob:ps},protocols:["http","https","file","blob","url","data"]},$t=typeof window<"u"&&typeof document<"u",yt=typeof navigator=="object"&&navigator||void 0,ys=$t&&(!yt||["ReactNative","NativeScript","NS"].indexOf(yt.product)<0),bs=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",_s=$t&&window.location.href||"http://localhost",ws=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:$t,hasStandardBrowserEnv:ys,hasStandardBrowserWebWorkerEnv:bs,navigator:yt,origin:_s},Symbol.toStringTag,{value:"Module"})),R={...ws,...gs};function Es(e,t){return Qe(e,new R.classes.URLSearchParams,Object.assign({visitor:function(n,r,o,s){return R.isNode&&d.isBuffer(n)?(this.append(r,n.toString("base64")),!1):s.defaultVisitor.apply(this,arguments)}},t))}function xs(e){return d.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Ss(e){const t={},n=Object.keys(e);let r;const o=n.length;let s;for(r=0;r<o;r++)s=n[r],t[s]=e[s];return t}function Gn(e){function t(n,r,o,s){let i=n[s++];if(i==="__proto__")return!0;const a=Number.isFinite(+i),c=s>=n.length;return i=!i&&d.isArray(o)?o.length:i,c?(d.hasOwnProp(o,i)?o[i]=[o[i],r]:o[i]=r,!a):((!o[i]||!d.isObject(o[i]))&&(o[i]=[]),t(n,r,o[i],s)&&d.isArray(o[i])&&(o[i]=Ss(o[i])),!a)}if(d.isFormData(e)&&d.isFunction(e.entries)){const n={};return d.forEachEntry(e,(r,o)=>{t(xs(r),o,n,0)}),n}return null}function As(e,t,n){if(d.isString(e))try{return(t||JSON.parse)(e),d.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const Te={transitional:Xn,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",o=r.indexOf("application/json")>-1,s=d.isObject(t);if(s&&d.isHTMLForm(t)&&(t=new FormData(t)),d.isFormData(t))return o?JSON.stringify(Gn(t)):t;if(d.isArrayBuffer(t)||d.isBuffer(t)||d.isStream(t)||d.isFile(t)||d.isBlob(t)||d.isReadableStream(t))return t;if(d.isArrayBufferView(t))return t.buffer;if(d.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(s){if(r.indexOf("application/x-www-form-urlencoded")>-1)return Es(t,this.formSerializer).toString();if((a=d.isFileList(t))||r.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Qe(a?{"files[]":t}:t,c&&new c,this.formSerializer)}}return s||o?(n.setContentType("application/json",!1),As(t)):t}],transformResponse:[function(t){const n=this.transitional||Te.transitional,r=n&&n.forcedJSONParsing,o=this.responseType==="json";if(d.isResponse(t)||d.isReadableStream(t))return t;if(t&&d.isString(t)&&(r&&!this.responseType||o)){const i=!(n&&n.silentJSONParsing)&&o;try{return JSON.parse(t)}catch(a){if(i)throw a.name==="SyntaxError"?b.from(a,b.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:R.classes.FormData,Blob:R.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};d.forEach(["delete","get","head","post","put","patch"],e=>{Te.headers[e]={}});const vs=d.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ts=e=>{const t={};let n,r,o;return e&&e.split(`
`).forEach(function(i){o=i.indexOf(":"),n=i.substring(0,o).trim().toLowerCase(),r=i.substring(o+1).trim(),!(!n||t[n]&&vs[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},mn=Symbol("internals");function ge(e){return e&&String(e).trim().toLowerCase()}function Be(e){return e===!1||e==null?e:d.isArray(e)?e.map(Be):String(e)}function Cs(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const Rs=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function at(e,t,n,r,o){if(d.isFunction(r))return r.call(this,t,n);if(o&&(t=n),!!d.isString(t)){if(d.isString(r))return t.indexOf(r)!==-1;if(d.isRegExp(r))return r.test(t)}}function Os(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function Ls(e,t){const n=d.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(o,s,i){return this[r].call(this,t,o,s,i)},configurable:!0})})}let k=class{constructor(t){t&&this.set(t)}set(t,n,r){const o=this;function s(a,c,l){const u=ge(c);if(!u)throw new Error("header name must be a non-empty string");const m=d.findKey(o,u);(!m||o[m]===void 0||l===!0||l===void 0&&o[m]!==!1)&&(o[m||c]=Be(a))}const i=(a,c)=>d.forEach(a,(l,u)=>s(l,u,c));if(d.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(d.isString(t)&&(t=t.trim())&&!Rs(t))i(Ts(t),n);else if(d.isObject(t)&&d.isIterable(t)){let a={},c,l;for(const u of t){if(!d.isArray(u))throw TypeError("Object iterator must return a key-value pair");a[l=u[0]]=(c=a[l])?d.isArray(c)?[...c,u[1]]:[c,u[1]]:u[1]}i(a,n)}else t!=null&&s(n,t,r);return this}get(t,n){if(t=ge(t),t){const r=d.findKey(this,t);if(r){const o=this[r];if(!n)return o;if(n===!0)return Cs(o);if(d.isFunction(n))return n.call(this,o,r);if(d.isRegExp(n))return n.exec(o);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=ge(t),t){const r=d.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||at(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let o=!1;function s(i){if(i=ge(i),i){const a=d.findKey(r,i);a&&(!n||at(r,r[a],a,n))&&(delete r[a],o=!0)}}return d.isArray(t)?t.forEach(s):s(t),o}clear(t){const n=Object.keys(this);let r=n.length,o=!1;for(;r--;){const s=n[r];(!t||at(this,this[s],s,t,!0))&&(delete this[s],o=!0)}return o}normalize(t){const n=this,r={};return d.forEach(this,(o,s)=>{const i=d.findKey(r,s);if(i){n[i]=Be(o),delete n[s];return}const a=t?Os(s):String(s).trim();a!==s&&delete n[s],n[a]=Be(o),r[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return d.forEach(this,(r,o)=>{r!=null&&r!==!1&&(n[o]=t&&d.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(o=>r.set(o)),r}static accessor(t){const r=(this[mn]=this[mn]={accessors:{}}).accessors,o=this.prototype;function s(i){const a=ge(i);r[a]||(Ls(o,i),r[a]=!0)}return d.isArray(t)?t.forEach(s):s(t),this}};k.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);d.reduceDescriptors(k.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});d.freezeMethods(k);function ct(e,t){const n=this||Te,r=t||n,o=k.from(r.headers);let s=r.data;return d.forEach(e,function(a){s=a.call(n,s,o.normalize(),t?t.status:void 0)}),o.normalize(),s}function Qn(e){return!!(e&&e.__CANCEL__)}function ue(e,t,n){b.call(this,e??"canceled",b.ERR_CANCELED,t,n),this.name="CanceledError"}d.inherits(ue,b,{__CANCEL__:!0});function Yn(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new b("Request failed with status code "+n.status,[b.ERR_BAD_REQUEST,b.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Ns(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function ks(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o=0,s=0,i;return t=t!==void 0?t:1e3,function(c){const l=Date.now(),u=r[s];i||(i=l),n[o]=c,r[o]=l;let m=s,p=0;for(;m!==o;)p+=n[m++],m=m%e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),l-i<t)return;const w=u&&l-u;return w?Math.round(p*1e3/w):void 0}}function Ps(e,t){let n=0,r=1e3/t,o,s;const i=(l,u=Date.now())=>{n=u,o=null,s&&(clearTimeout(s),s=null),e.apply(null,l)};return[(...l)=>{const u=Date.now(),m=u-n;m>=r?i(l,u):(o=l,s||(s=setTimeout(()=>{s=null,i(o)},r-m)))},()=>o&&i(o)]}const qe=(e,t,n=3)=>{let r=0;const o=ks(50,250);return Ps(s=>{const i=s.loaded,a=s.lengthComputable?s.total:void 0,c=i-r,l=o(c),u=i<=a;r=i;const m={loaded:i,total:a,progress:a?i/a:void 0,bytes:c,rate:l||void 0,estimated:l&&a&&u?(a-i)/l:void 0,event:s,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(m)},n)},hn=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},pn=e=>(...t)=>d.asap(()=>e(...t)),Ds=R.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,R.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(R.origin),R.navigator&&/(msie|trident)/i.test(R.navigator.userAgent)):()=>!0,Fs=R.hasStandardBrowserEnv?{write(e,t,n,r,o,s){const i=[e+"="+encodeURIComponent(t)];d.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),d.isString(r)&&i.push("path="+r),d.isString(o)&&i.push("domain="+o),s===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Ms(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Bs(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Zn(e,t,n){let r=!Ms(t);return e&&(r||n==!1)?Bs(e,t):t}const gn=e=>e instanceof k?{...e}:e;function ee(e,t){t=t||{};const n={};function r(l,u,m,p){return d.isPlainObject(l)&&d.isPlainObject(u)?d.merge.call({caseless:p},l,u):d.isPlainObject(u)?d.merge({},u):d.isArray(u)?u.slice():u}function o(l,u,m,p){if(d.isUndefined(u)){if(!d.isUndefined(l))return r(void 0,l,m,p)}else return r(l,u,m,p)}function s(l,u){if(!d.isUndefined(u))return r(void 0,u)}function i(l,u){if(d.isUndefined(u)){if(!d.isUndefined(l))return r(void 0,l)}else return r(void 0,u)}function a(l,u,m){if(m in t)return r(l,u);if(m in e)return r(void 0,l)}const c={url:s,method:s,data:s,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:a,headers:(l,u,m)=>o(gn(l),gn(u),m,!0)};return d.forEach(Object.keys(Object.assign({},e,t)),function(u){const m=c[u]||o,p=m(e[u],t[u],u);d.isUndefined(p)&&m!==a||(n[u]=p)}),n}const er=e=>{const t=ee({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:o,xsrfCookieName:s,headers:i,auth:a}=t;t.headers=i=k.from(i),t.url=Vn(Zn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&i.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let c;if(d.isFormData(n)){if(R.hasStandardBrowserEnv||R.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((c=i.getContentType())!==!1){const[l,...u]=c?c.split(";").map(m=>m.trim()).filter(Boolean):[];i.setContentType([l||"multipart/form-data",...u].join("; "))}}if(R.hasStandardBrowserEnv&&(r&&d.isFunction(r)&&(r=r(t)),r||r!==!1&&Ds(t.url))){const l=o&&s&&Fs.read(s);l&&i.set(o,l)}return t},Is=typeof XMLHttpRequest<"u",js=Is&&function(e){return new Promise(function(n,r){const o=er(e);let s=o.data;const i=k.from(o.headers).normalize();let{responseType:a,onUploadProgress:c,onDownloadProgress:l}=o,u,m,p,w,h;function y(){w&&w(),h&&h(),o.cancelToken&&o.cancelToken.unsubscribe(u),o.signal&&o.signal.removeEventListener("abort",u)}let f=new XMLHttpRequest;f.open(o.method.toUpperCase(),o.url,!0),f.timeout=o.timeout;function g(){if(!f)return;const E=k.from("getAllResponseHeaders"in f&&f.getAllResponseHeaders()),v={data:!a||a==="text"||a==="json"?f.responseText:f.response,status:f.status,statusText:f.statusText,headers:E,config:e,request:f};Yn(function(B){n(B),y()},function(B){r(B),y()},v),f=null}"onloadend"in f?f.onloadend=g:f.onreadystatechange=function(){!f||f.readyState!==4||f.status===0&&!(f.responseURL&&f.responseURL.indexOf("file:")===0)||setTimeout(g)},f.onabort=function(){f&&(r(new b("Request aborted",b.ECONNABORTED,e,f)),f=null)},f.onerror=function(){r(new b("Network Error",b.ERR_NETWORK,e,f)),f=null},f.ontimeout=function(){let A=o.timeout?"timeout of "+o.timeout+"ms exceeded":"timeout exceeded";const v=o.transitional||Xn;o.timeoutErrorMessage&&(A=o.timeoutErrorMessage),r(new b(A,v.clarifyTimeoutError?b.ETIMEDOUT:b.ECONNABORTED,e,f)),f=null},s===void 0&&i.setContentType(null),"setRequestHeader"in f&&d.forEach(i.toJSON(),function(A,v){f.setRequestHeader(v,A)}),d.isUndefined(o.withCredentials)||(f.withCredentials=!!o.withCredentials),a&&a!=="json"&&(f.responseType=o.responseType),l&&([p,h]=qe(l,!0),f.addEventListener("progress",p)),c&&f.upload&&([m,w]=qe(c),f.upload.addEventListener("progress",m),f.upload.addEventListener("loadend",w)),(o.cancelToken||o.signal)&&(u=E=>{f&&(r(!E||E.type?new ue(null,e,f):E),f.abort(),f=null)},o.cancelToken&&o.cancelToken.subscribe(u),o.signal&&(o.signal.aborted?u():o.signal.addEventListener("abort",u)));const _=Ns(o.url);if(_&&R.protocols.indexOf(_)===-1){r(new b("Unsupported protocol "+_+":",b.ERR_BAD_REQUEST,e));return}f.send(s||null)})},$s=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,o;const s=function(l){if(!o){o=!0,a();const u=l instanceof Error?l:this.reason;r.abort(u instanceof b?u:new ue(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,s(new b(`timeout ${t} of ms exceeded`,b.ETIMEDOUT))},t);const a=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(s):l.removeEventListener("abort",s)}),e=null)};e.forEach(l=>l.addEventListener("abort",s));const{signal:c}=r;return c.unsubscribe=()=>d.asap(a),c}},qs=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,o;for(;r<n;)o=r+t,yield e.slice(r,o),r=o},Us=async function*(e,t){for await(const n of Hs(e))yield*qs(n,t)},Hs=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},yn=(e,t,n,r)=>{const o=Us(e,t);let s=0,i,a=c=>{i||(i=!0,r&&r(c))};return new ReadableStream({async pull(c){try{const{done:l,value:u}=await o.next();if(l){a(),c.close();return}let m=u.byteLength;if(n){let p=s+=m;n(p)}c.enqueue(new Uint8Array(u))}catch(l){throw a(l),l}},cancel(c){return a(c),o.return()}},{highWaterMark:2})},Ye=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",tr=Ye&&typeof ReadableStream=="function",zs=Ye&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),nr=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Ks=tr&&nr(()=>{let e=!1;const t=new Request(R.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),bn=64*1024,bt=tr&&nr(()=>d.isReadableStream(new Response("").body)),Ue={stream:bt&&(e=>e.body)};Ye&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Ue[t]&&(Ue[t]=d.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new b(`Response type '${t}' is not supported`,b.ERR_NOT_SUPPORT,r)})})})(new Response);const Ws=async e=>{if(e==null)return 0;if(d.isBlob(e))return e.size;if(d.isSpecCompliantForm(e))return(await new Request(R.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(d.isArrayBufferView(e)||d.isArrayBuffer(e))return e.byteLength;if(d.isURLSearchParams(e)&&(e=e+""),d.isString(e))return(await zs(e)).byteLength},Js=async(e,t)=>{const n=d.toFiniteNumber(e.getContentLength());return n??Ws(t)},Vs=Ye&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:s,timeout:i,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:m="same-origin",fetchOptions:p}=er(e);l=l?(l+"").toLowerCase():"text";let w=$s([o,s&&s.toAbortSignal()],i),h;const y=w&&w.unsubscribe&&(()=>{w.unsubscribe()});let f;try{if(c&&Ks&&n!=="get"&&n!=="head"&&(f=await Js(u,r))!==0){let v=new Request(t,{method:"POST",body:r,duplex:"half"}),L;if(d.isFormData(r)&&(L=v.headers.get("content-type"))&&u.setContentType(L),v.body){const[B,re]=hn(f,qe(pn(c)));r=yn(v.body,bn,B,re)}}d.isString(m)||(m=m?"include":"omit");const g="credentials"in Request.prototype;h=new Request(t,{...p,signal:w,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:g?m:void 0});let _=await fetch(h);const E=bt&&(l==="stream"||l==="response");if(bt&&(a||E&&y)){const v={};["status","statusText","headers"].forEach(Le=>{v[Le]=_[Le]});const L=d.toFiniteNumber(_.headers.get("content-length")),[B,re]=a&&hn(L,qe(pn(a),!0))||[];_=new Response(yn(_.body,bn,B,()=>{re&&re(),y&&y()}),v)}l=l||"text";let A=await Ue[d.findKey(Ue,l)||"text"](_,e);return!E&&y&&y(),await new Promise((v,L)=>{Yn(v,L,{data:A,headers:k.from(_.headers),status:_.status,statusText:_.statusText,config:e,request:h})})}catch(g){throw y&&y(),g&&g.name==="TypeError"&&/Load failed|fetch/i.test(g.message)?Object.assign(new b("Network Error",b.ERR_NETWORK,e,h),{cause:g.cause||g}):b.from(g,g&&g.code,e,h)}}),_t={http:ls,xhr:js,fetch:Vs};d.forEach(_t,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const _n=e=>`- ${e}`,Xs=e=>d.isFunction(e)||e===null||e===!1,rr={getAdapter:e=>{e=d.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let s=0;s<t;s++){n=e[s];let i;if(r=n,!Xs(n)&&(r=_t[(i=String(n)).toLowerCase()],r===void 0))throw new b(`Unknown adapter '${i}'`);if(r)break;o[i||"#"+s]=r}if(!r){const s=Object.entries(o).map(([a,c])=>`adapter ${a} `+(c===!1?"is not supported by the environment":"is not available in the build"));let i=t?s.length>1?`since :
`+s.map(_n).join(`
`):" "+_n(s[0]):"as no adapter specified";throw new b("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:_t};function lt(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ue(null,e)}function wn(e){return lt(e),e.headers=k.from(e.headers),e.data=ct.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),rr.getAdapter(e.adapter||Te.adapter)(e).then(function(r){return lt(e),r.data=ct.call(e,e.transformResponse,r),r.headers=k.from(r.headers),r},function(r){return Qn(r)||(lt(e),r&&r.response&&(r.response.data=ct.call(e,e.transformResponse,r.response),r.response.headers=k.from(r.response.headers))),Promise.reject(r)})}const or="1.9.0",Ze={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ze[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const En={};Ze.transitional=function(t,n,r){function o(s,i){return"[Axios v"+or+"] Transitional option '"+s+"'"+i+(r?". "+r:"")}return(s,i,a)=>{if(t===!1)throw new b(o(i," has been removed"+(n?" in "+n:"")),b.ERR_DEPRECATED);return n&&!En[i]&&(En[i]=!0,console.warn(o(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(s,i,a):!0}};Ze.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Gs(e,t,n){if(typeof e!="object")throw new b("options must be an object",b.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const s=r[o],i=t[s];if(i){const a=e[s],c=a===void 0||i(a,s,e);if(c!==!0)throw new b("option "+s+" must be "+c,b.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new b("Unknown option "+s,b.ERR_BAD_OPTION)}}const Ie={assertOptions:Gs,validators:Ze},I=Ie.validators;let V=class{constructor(t){this.defaults=t||{},this.interceptors={request:new fn,response:new fn}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let o={};Error.captureStackTrace?Error.captureStackTrace(o):o=new Error;const s=o.stack?o.stack.replace(/^.+\n/,""):"";try{r.stack?s&&!String(r.stack).endsWith(s.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+s):r.stack=s}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=ee(this.defaults,n);const{transitional:r,paramsSerializer:o,headers:s}=n;r!==void 0&&Ie.assertOptions(r,{silentJSONParsing:I.transitional(I.boolean),forcedJSONParsing:I.transitional(I.boolean),clarifyTimeoutError:I.transitional(I.boolean)},!1),o!=null&&(d.isFunction(o)?n.paramsSerializer={serialize:o}:Ie.assertOptions(o,{encode:I.function,serialize:I.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Ie.assertOptions(n,{baseUrl:I.spelling("baseURL"),withXsrfToken:I.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=s&&d.merge(s.common,s[n.method]);s&&d.forEach(["delete","get","head","post","put","patch","common"],h=>{delete s[h]}),n.headers=k.concat(i,s);const a=[];let c=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(c=c&&y.synchronous,a.unshift(y.fulfilled,y.rejected))});const l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let u,m=0,p;if(!c){const h=[wn.bind(this),void 0];for(h.unshift.apply(h,a),h.push.apply(h,l),p=h.length,u=Promise.resolve(n);m<p;)u=u.then(h[m++],h[m++]);return u}p=a.length;let w=n;for(m=0;m<p;){const h=a[m++],y=a[m++];try{w=h(w)}catch(f){y.call(this,f);break}}try{u=wn.call(this,w)}catch(h){return Promise.reject(h)}for(m=0,p=l.length;m<p;)u=u.then(l[m++],l[m++]);return u}getUri(t){t=ee(this.defaults,t);const n=Zn(t.baseURL,t.url,t.allowAbsoluteUrls);return Vn(n,t.params,t.paramsSerializer)}};d.forEach(["delete","get","head","options"],function(t){V.prototype[t]=function(n,r){return this.request(ee(r||{},{method:t,url:n,data:(r||{}).data}))}});d.forEach(["post","put","patch"],function(t){function n(r){return function(s,i,a){return this.request(ee(a||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:s,data:i}))}}V.prototype[t]=n(),V.prototype[t+"Form"]=n(!0)});let Qs=class sr{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(s){n=s});const r=this;this.promise.then(o=>{if(!r._listeners)return;let s=r._listeners.length;for(;s-- >0;)r._listeners[s](o);r._listeners=null}),this.promise.then=o=>{let s;const i=new Promise(a=>{r.subscribe(a),s=a}).then(o);return i.cancel=function(){r.unsubscribe(s)},i},t(function(s,i,a){r.reason||(r.reason=new ue(s,i,a),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new sr(function(o){t=o}),cancel:t}}};function Ys(e){return function(n){return e.apply(null,n)}}function Zs(e){return d.isObject(e)&&e.isAxiosError===!0}const wt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(wt).forEach(([e,t])=>{wt[t]=e});function ir(e){const t=new V(e),n=Mn(V.prototype.request,t);return d.extend(n,V.prototype,t,{allOwnKeys:!0}),d.extend(n,t,null,{allOwnKeys:!0}),n.create=function(o){return ir(ee(e,o))},n}const T=ir(Te);T.Axios=V;T.CanceledError=ue;T.CancelToken=Qs;T.isCancel=Qn;T.VERSION=or;T.toFormData=Qe;T.AxiosError=b;T.Cancel=T.CanceledError;T.all=function(t){return Promise.all(t)};T.spread=Ys;T.isAxiosError=Zs;T.mergeConfig=ee;T.AxiosHeaders=k;T.formToJSON=e=>Gn(d.isHTMLForm(e)?new FormData(e):e);T.getAdapter=rr.getAdapter;T.HttpStatusCode=wt;T.default=T;const{Axios:yc,AxiosError:bc,CanceledError:_c,isCancel:wc,CancelToken:Ec,VERSION:xc,all:Sc,Cancel:Ac,isAxiosError:vc,spread:Tc,toFormData:Cc,AxiosHeaders:Rc,HttpStatusCode:Oc,formToJSON:Lc,getAdapter:Nc,mergeConfig:kc}=T;window.axios=T;window.axios.defaults.headers.common["X-Requested-With"]="XMLHttpRequest";class ar{constructor(){this.currentDirection=this.getCurrentDirection(),this.currentLocale=this.getCurrentLocale(),this.init()}init(){window.addEventListener("languageChanged",t=>{this.currentDirection=t.detail.direction,this.currentLocale=t.detail.locale,this.updateAllElements()}),document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{this.applyRTLFixes()}):this.applyRTLFixes()}getCurrentDirection(){return document.documentElement.getAttribute("dir")||"ltr"}getCurrentLocale(){return document.documentElement.getAttribute("lang")||"en"}isRTL(){return this.currentDirection==="rtl"}applyRTLFixes(){this.fixCurrencyElements(),this.fixNumberElements(),this.fixTableElements(),this.fixFormElements(),this.fixNavigationElements(),this.fixModalElements()}updateAllElements(){this.applyRTLFixes(),this.updateAnimations(),this.updateScrollbars()}fixCurrencyElements(){document.querySelectorAll(".currency-amount, .price, .amount").forEach(n=>{this.isRTL()?(n.style.direction="ltr",n.style.display="inline-block",n.style.textAlign="right"):(n.style.direction="ltr",n.style.display="inline",n.style.textAlign="left")})}fixNumberElements(){document.querySelectorAll(".number-display, .stats-value, .count").forEach(n=>{this.isRTL()&&(n.style.direction="ltr",n.style.display="inline-block")})}fixTableElements(){document.querySelectorAll(".table-bank, .data-table").forEach(n=>{this.isRTL()?(n.style.direction="rtl",n.querySelectorAll(".table-actions, .actions-column").forEach(o=>{o.style.textAlign="left",o.style.direction="ltr"})):n.style.direction="ltr"})}fixFormElements(){document.querySelectorAll("form").forEach(n=>{this.isRTL()&&(n.querySelectorAll(".input-group").forEach(i=>{i.style.direction="rtl"}),n.querySelectorAll("select").forEach(i=>{i.style.textAlign="right"}),n.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(i=>{const a=i.closest("label")||i.nextElementSibling;a&&(a.style.direction="rtl")}))})}fixNavigationElements(){document.querySelectorAll(".nav-item, .nav-link").forEach(n=>{if(this.isRTL()){const r=n.querySelector(".nav-icon, .icon"),o=n.querySelector(".nav-text, .text");r&&o&&(r.style.order="2",o.style.order="1",r.style.marginRight="0",r.style.marginLeft="0.5rem")}else{const r=n.querySelector(".nav-icon, .icon"),o=n.querySelector(".nav-text, .text");r&&o&&(r.style.order="1",o.style.order="2",r.style.marginLeft="0",r.style.marginRight="0.5rem")}})}fixModalElements(){document.querySelectorAll(".modal").forEach(n=>{if(this.isRTL()){n.style.direction="rtl";const r=n.querySelector(".btn-close, .close");r&&(r.style.marginLeft="auto",r.style.marginRight="0")}})}updateAnimations(){document.querySelectorAll('[class*="slide"], [class*="fade"]').forEach(n=>{this.isRTL()&&(n.classList.contains("slide-in-right")?(n.classList.remove("slide-in-right"),n.classList.add("slide-in-left")):n.classList.contains("slide-in-left")&&(n.classList.remove("slide-in-left"),n.classList.add("slide-in-right")))})}updateScrollbars(){this.isRTL()?document.body.classList.add("rtl-scrollbar"):document.body.classList.remove("rtl-scrollbar")}formatCurrency(t,n={}){const o={...{style:"currency",currency:"AED",minimumFractionDigits:0,maximumFractionDigits:2},...n},s=this.currentLocale==="ar"?"ar-AE":"en-AE";return new Intl.NumberFormat(s,o).format(t)}formatNumber(t,n={}){const r=this.currentLocale==="ar"?"ar-AE":"en-AE";return new Intl.NumberFormat(r,n).format(t)}formatDate(t,n={}){const o={...{year:"numeric",month:"long",day:"numeric"},...n},s=this.currentLocale==="ar"?"ar-AE":"en-AE";return new Intl.DateTimeFormat(s,o).format(new Date(t))}getElementDirection(t){return window.getComputedStyle(t).direction}setElementDirection(t,n){t.style.direction=n}toggleElementDirection(t){const r=this.getElementDirection(t)==="rtl"?"ltr":"rtl";return this.setElementDirection(t,r),r}applyRTLSpacing(t,n,r){if(this.isRTL()){const o=n.replace("left","temp").replace("right","left").replace("temp","right");t.style[o]=r}else t.style[n]=r}getRTLPosition(t){return this.isRTL()&&{left:"right",right:"left",start:"end",end:"start"}[t]||t}getRTLClass(t){return this.isRTL()?`${t}-rtl`:`${t}-ltr`}}window.RTLHelpers=new ar;typeof Z<"u"&&Z.exports&&(Z.exports=ar);class cr{constructor(){this.currentLocale=this.getCurrentLocale(),this.currentDirection=this.getCurrentDirection(),this.isLoading=!1,this.apiEndpoint="/api/language/switch",this.csrfToken=this.getCSRFToken(),this.languages={en:{code:"en",name:"English",native:"English",flag:"🇺🇸",direction:"ltr"},ar:{code:"ar",name:"Arabic",native:"العربية",flag:"🇦🇪",direction:"rtl"}},this.init()}init(){this.bindEvents(),this.updateUI(),this.applyStoredPreferences()}getCurrentLocale(){return document.documentElement.getAttribute("lang")||"en"}getCurrentDirection(){return document.documentElement.getAttribute("dir")||"ltr"}getCSRFToken(){const t=document.querySelector('meta[name="csrf-token"]');return t?t.getAttribute("content"):""}bindEvents(){document.addEventListener("click",t=>{const n=t.target.closest("[data-language-switch]");if(n){t.preventDefault();const r=n.getAttribute("data-language-switch");this.switchLanguage(r)}}),document.addEventListener("keydown",t=>{t.altKey&&t.key==="l"&&(t.preventDefault(),this.toggleLanguage())}),window.addEventListener("storage",t=>{(t.key==="language"||t.key==="direction")&&this.syncWithStorage()})}async switchLanguage(t){if(this.isLoading||t===this.currentLocale)return!1;if(!this.languages[t])return console.error(`Unsupported locale: ${t}`),!1;this.setLoadingState(!0);try{const n=await this.makeAPIRequest(t);return n.success?(await this.handleSuccessfulSwitch(n),!0):(this.handleSwitchError(n.message||"Failed to switch language"),!1)}catch(n){return console.error("Language switch error:",n),this.handleSwitchError("Network error occurred"),!1}finally{this.setLoadingState(!1)}}async toggleLanguage(){const t=this.currentLocale==="en"?"ar":"en";return await this.switchLanguage(t)}async makeAPIRequest(t){const n=await fetch(this.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":this.csrfToken,Accept:"application/json","X-Requested-With":"XMLHttpRequest"},body:JSON.stringify({locale:t})});if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);return await n.json()}async handleSuccessfulSwitch(t){this.currentLocale=t.locale,this.currentDirection=t.direction,this.updateDocumentAttributes(t),this.storePreferences(t),this.updateUI(),this.dispatchLanguageChangeEvent(t),this.showNotification(t.message||"Language switched successfully","success"),await this.delayedReload()}updateDocumentAttributes(t){document.documentElement.setAttribute("lang",t.locale),document.documentElement.setAttribute("dir",t.direction),t.direction==="rtl"?document.body.classList.add("arabic-text"):document.body.classList.remove("arabic-text")}storePreferences(t){localStorage.setItem("language",t.locale.toUpperCase()),localStorage.setItem("direction",t.direction),localStorage.setItem("locale",t.locale),localStorage.setItem("language_switched_at",Date.now().toString())}applyStoredPreferences(){const t=localStorage.getItem("locale"),n=localStorage.getItem("direction");t&&n&&(this.currentLocale=t,this.currentDirection=n,this.updateDocumentAttributes({locale:t,direction:n}))}syncWithStorage(){const t=localStorage.getItem("locale"),n=localStorage.getItem("direction");(t!==this.currentLocale||n!==this.currentDirection)&&(this.currentLocale=t,this.currentDirection=n,this.updateUI(),window.location.reload())}updateUI(){const t=document.querySelectorAll(".language-switcher-text"),n=this.languages[this.currentLocale],r=this.languages[this.currentLocale==="en"?"ar":"en"];t.forEach(s=>{s.closest("[data-show-current]")?s.textContent=`${n.flag} ${n.native}`:s.textContent=`${r.flag} ${r.code==="ar"?"عربي":"EN"}`}),document.querySelectorAll(".language-switcher-menu-item").forEach(s=>{s.getAttribute("data-locale")===this.currentLocale?s.classList.add("active"):s.classList.remove("active")})}setLoadingState(t){this.isLoading=t,document.querySelectorAll(".language-switcher").forEach(r=>{t?(r.classList.add("loading"),r.style.pointerEvents="none"):(r.classList.remove("loading"),r.style.pointerEvents="")})}handleSwitchError(t){console.error("Language switch failed:",t),this.showNotification(t,"error")}dispatchLanguageChangeEvent(t){const n=new CustomEvent("languageChanged",{detail:{locale:t.locale,direction:t.direction,message:t.message,timestamp:Date.now()}});window.dispatchEvent(n)}showNotification(t,n="info"){document.querySelectorAll(".language-notification").forEach(s=>s.remove());const o=document.createElement("div");o.className=`language-notification notification-${n}`,o.style.cssText=`
            position: fixed;
            top: 20px;
            ${this.currentDirection==="rtl"?"left":"right"}: 20px;
            background: ${n==="success"?"#38A169":n==="error"?"#E53E3E":"#3182CE"};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            font-size: 14px;
            font-weight: 500;
            transform: translateX(${this.currentDirection==="rtl"?"-":""}100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            font-family: var(--font-family-primary);
        `,o.textContent=t,document.body.appendChild(o),setTimeout(()=>{o.style.transform="translateX(0)"},100),setTimeout(()=>{o.style.transform=`translateX(${this.currentDirection==="rtl"?"-":""}100%)`,setTimeout(()=>{o.parentNode&&o.parentNode.removeChild(o)},300)},3e3)}async delayedReload(t=500){return new Promise(n=>{setTimeout(()=>{window.location.reload(),n()},t)})}getLanguageInfo(t=null){return t=t||this.currentLocale,this.languages[t]||null}isSupported(t){return!!this.languages[t]}getAvailableLanguages(){return Object.values(this.languages)}}window.LanguageSwitcher=new cr;typeof Z<"u"&&Z.exports&&(Z.exports=cr);class ei{constructor(){this.currentLocale=document.documentElement.lang||"en",this.translations={},this.translationElements=new Map,this.init()}init(){this.loadTranslations(),this.scanTranslatableElements(),this.setupEventListeners(),this.applyTranslations()}loadTranslations(){window.commonTranslations&&(this.translations.common=window.commonTranslations),window.dashboardTranslations&&(this.translations.dashboard=window.dashboardTranslations)}scanTranslatableElements(){document.querySelectorAll("[data-trans-key]").forEach(n=>{const r=n.getAttribute("data-trans-key"),o=n.getAttribute("data-context")||"common";r&&this.translationElements.set(n,{key:r,context:o,originalText:n.textContent.trim()})}),this.scanHardcodedText()}scanHardcodedText(){this.scanTableHeaders(),this.scanButtons(),this.scanFormLabels(),this.scanStatusBadges();const t={"Total Branches":"dashboard.total_branches","Total Academies":"dashboard.total_academies","Total Students":"dashboard.total_students","Total Payments":"dashboard.total_payments","Monthly Revenue (AED)":"dashboard.monthly_revenue","Welcome Back!":"dashboard.welcome_back","Recent Students":"dashboard.recent_students","Recent Payments":"dashboard.recent_payments","Quick Actions":"dashboard.Quick Actions","View All":"dashboard.view_all",Dashboard:"dashboard.Dashboard","Branch Management":"dashboard.Branch Management","Academy Management":"dashboard.Academy Management","Student Management":"dashboard.Student Management","Payment Management":"dashboard.Payment Management","Uniform Management":"dashboard.Uniform Management","User Management":"dashboard.User Management",Settings:"dashboard.Settings",Reports:"dashboard.Reports",Administration:"dashboard.Administration","Add Student":"dashboard.Add Student","Add Payment":"dashboard.Add Payment","Order Uniform":"dashboard.Order Uniform","View Details":"dashboard.view_details","View Reports":"dashboard.view_details","Export Report":"dashboard.export_report","Add New Student":"dashboard.Add Student","Create Order":"common.create_order",Search:"common.search",Filter:"common.filter",Save:"common.save",Cancel:"common.cancel",Delete:"common.delete",Edit:"common.edit",Add:"common.add",Create:"common.create",Update:"common.update",Back:"common.back",Next:"common.next",Previous:"common.previous","Loading...":"common.loading","Please wait...":"common.please_wait",Submit:"common.submit",Reset:"common.reset",Close:"common.close",Confirm:"common.confirm",Yes:"common.yes",No:"common.no",ID:"common.id",Name:"common.name",Student:"common.student",Academy:"common.academy",Branch:"common.branch",Status:"common.status",Actions:"common.actions",Date:"common.date",Amount:"common.amount","Amount (AED)":"common.amount_aed","Student Info":"common.student_info","Contact & Location":"common.contact_location","Academy Name":"common.academy_name","Branch Name":"common.branch_name",Coach:"common.coach",Programs:"common.programs",Students:"common.students","Revenue (AED)":"common.revenue_aed",Created:"common.created","Join Date":"common.join_date","Order Date":"common.order_date","Delivery Date":"common.delivery_date",Item:"common.item",Size:"common.size",Quantity:"common.quantity",Active:"common.active",Inactive:"common.inactive",Pending:"common.pending",Expired:"common.expired",Completed:"common.completed",Processing:"common.processing",Cancelled:"common.cancelled",Approved:"common.approved",Rejected:"common.rejected","No students found.":"common.no_students_found","No payments found.":"common.no_payments_found","No data available":"common.no_data_available","No results found":"common.no_results_found","Data updated successfully":"common.data_updated_successfully","Operation completed successfully":"common.operation_completed_successfully","Error occurred":"common.error_occurred","Please try again":"common.please_try_again","Full Name":"common.full_name",Email:"common.email",Phone:"common.phone",Address:"common.address",Nationality:"common.nationality","Birth Date":"common.birth_date",Password:"common.password","Confirm Password":"common.confirm_password",Role:"common.role",Description:"common.description",Notes:"common.notes",Location:"common.location",Manager:"common.manager","Contact Person":"common.contact_person",Price:"common.price",Days:"common.days",Classes:"common.classes","Start Date":"common.start_date","End Date":"common.end_date","Payment Method":"common.payment_method",Discount:"common.discount","Class Time":"common.class_time",From:"common.from",To:"common.to"};Object.keys(t).forEach(n=>{this.findElementsWithText(n).forEach(o=>{const s=t[n],[i,a]=s.split(".");o.setAttribute("data-trans-key",a),o.setAttribute("data-context",i),this.translationElements.set(o,{key:a,context:i,originalText:n})})})}findElementsWithText(t){const n=[],r=document.createTreeWalker(document.body,NodeFilter.SHOW_TEXT,null,!1);let o;for(;o=r.nextNode();)if(o.textContent.trim()===t){const s=o.parentElement;s&&!s.hasAttribute("data-trans-key")&&n.push(s)}return n}scanTableHeaders(){document.querySelectorAll("th").forEach(n=>{const r=n.textContent.trim();if(r&&!n.hasAttribute("data-trans-key")){const o=this.getTranslationMapping(r);if(o){const[s,i]=o.split(".");n.setAttribute("data-trans-key",i),n.setAttribute("data-context",s),this.translationElements.set(n,{key:i,context:s,originalText:r})}}})}scanButtons(){document.querySelectorAll("button, a.btn, .btn-bank, .btn-bank-outline, .btn-bank-secondary").forEach(n=>{const r=n.textContent.trim();if(r&&!n.hasAttribute("data-trans-key")){const o=this.getTranslationMapping(r);if(o){const[s,i]=o.split(".");n.setAttribute("data-trans-key",i),n.setAttribute("data-context",s),this.translationElements.set(n,{key:i,context:s,originalText:r})}}})}scanFormLabels(){document.querySelectorAll("label, .form-label, .input-label").forEach(n=>{const r=n.textContent.trim();if(r&&!n.hasAttribute("data-trans-key")){const o=this.getTranslationMapping(r);if(o){const[s,i]=o.split(".");n.setAttribute("data-trans-key",i),n.setAttribute("data-context",s),this.translationElements.set(n,{key:i,context:s,originalText:r})}}})}scanStatusBadges(){document.querySelectorAll(".badge, .badge-bank, .status, .badge-success, .badge-error, .badge-warning, .badge-neutral").forEach(n=>{const r=n.textContent.trim();if(r&&!n.hasAttribute("data-trans-key")){const o=this.getTranslationMapping(r);if(o){const[s,i]=o.split(".");n.setAttribute("data-trans-key",i),n.setAttribute("data-context",s),this.translationElements.set(n,{key:i,context:s,originalText:r})}}})}getTranslationMapping(t){return{"Total Branches":"dashboard.total_branches","Total Academies":"dashboard.total_academies","Total Students":"dashboard.total_students","Total Payments":"dashboard.total_payments","Monthly Revenue (AED)":"dashboard.monthly_revenue","Welcome Back!":"dashboard.welcome_back","Recent Students":"dashboard.recent_students","Recent Payments":"dashboard.recent_payments","Quick Actions":"dashboard.Quick Actions","View All":"dashboard.view_all",Dashboard:"dashboard.Dashboard","Branch Management":"dashboard.Branch Management","Academy Management":"dashboard.Academy Management","Student Management":"dashboard.Student Management","Payment Management":"dashboard.Payment Management","Uniform Management":"dashboard.Uniform Management","User Management":"dashboard.User Management",Settings:"dashboard.Settings",Reports:"dashboard.Reports",Administration:"dashboard.Administration","Add Student":"dashboard.Add Student","Add Payment":"dashboard.Add Payment","Order Uniform":"dashboard.Order Uniform","View Details":"dashboard.view_details","View Reports":"dashboard.view_details","Export Report":"dashboard.export_report","Add New Student":"dashboard.Add Student","Create Order":"common.create_order",Search:"common.search",Filter:"common.filter",Save:"common.save",Cancel:"common.cancel",Delete:"common.delete",Edit:"common.edit",Add:"common.add",Create:"common.create",Update:"common.update",Back:"common.back",Next:"common.next",Previous:"common.previous","Loading...":"common.loading","Please wait...":"common.please_wait",Submit:"common.submit",Reset:"common.reset",Close:"common.close",Confirm:"common.confirm",Yes:"common.yes",No:"common.no",ID:"common.id",Name:"common.name",Student:"common.student",Academy:"common.academy",Branch:"common.branch",Status:"common.status",Actions:"common.actions",Date:"common.date",Amount:"common.amount","Amount (AED)":"common.amount_aed","Student Info":"common.student_info","Contact & Location":"common.contact_location","Academy Name":"common.academy_name","Branch Name":"common.branch_name",Coach:"common.coach",Programs:"common.programs",Students:"common.students","Revenue (AED)":"common.revenue_aed",Created:"common.created","Join Date":"common.join_date","Order Date":"common.order_date","Delivery Date":"common.delivery_date",Item:"common.item",Size:"common.size",Quantity:"common.quantity",Active:"common.active",Inactive:"common.inactive",Pending:"common.pending",Expired:"common.expired",Completed:"common.completed",Processing:"common.processing",Cancelled:"common.cancelled",Approved:"common.approved",Rejected:"common.rejected","No students found.":"common.no_students_found","No payments found.":"common.no_payments_found","No data available":"common.no_data_available","No results found":"common.no_results_found","Data updated successfully":"common.data_updated_successfully","Operation completed successfully":"common.operation_completed_successfully","Error occurred":"common.error_occurred","Please try again":"common.please_try_again","Full Name":"common.full_name",Email:"common.email",Phone:"common.phone",Address:"common.address",Nationality:"common.nationality","Birth Date":"common.birth_date",Password:"common.password","Confirm Password":"common.confirm_password",Role:"common.role",Description:"common.description",Notes:"common.notes",Location:"common.location",Manager:"common.manager","Contact Person":"common.contact_person",Price:"common.price",Days:"common.days",Classes:"common.classes","Start Date":"common.start_date","End Date":"common.end_date","Payment Method":"common.payment_method",Discount:"common.discount","Class Time":"common.class_time",From:"common.from",To:"common.to","Reports Dashboard":"dashboard.Reports Dashboard","Financial Reports":"dashboard.Financial Reports","Uniform Reports":"dashboard.Uniform Reports","Program Reports":"dashboard.Program Reports","Status Reports":"dashboard.Status Reports","Daily Reports":"dashboard.Daily Reports","Active & Growing":"dashboard.active_growing","Excellence in Sports":"dashboard.excellence_sports","Future Champions":"dashboard.future_champions",AED:"common.aed","N/A":"common.not_available",Administration:"dashboard.Administration","Quick Actions":"dashboard.Quick Actions","Latest student registrations":"dashboard.new_registrations","Latest payment transactions":"dashboard.recent_payments","Frequently used operations":"dashboard.quick_stats"}[t]||null}setupEventListeners(){window.addEventListener("languageChanged",n=>{this.currentLocale=n.detail.locale,this.loadNewTranslations(n.detail.locale)}),new MutationObserver(n=>{let r=!1;n.forEach(o=>{o.type==="childList"&&o.addedNodes.forEach(s=>{s.nodeType===Node.ELEMENT_NODE&&(this.scanNewElement(s),r=!0)})}),r&&setTimeout(()=>{this.scanTranslatableElements(),this.applyTranslations()},100)}).observe(document.body,{childList:!0,subtree:!0}),document.addEventListener("ajaxComplete",()=>{setTimeout(()=>{this.scanTranslatableElements(),this.applyTranslations()},200)}),document.addEventListener("visibilitychange",()=>{document.hidden||this.applyTranslations()})}scanNewElement(t){t.querySelectorAll("[data-trans-key]").forEach(r=>{const o=r.getAttribute("data-trans-key"),s=r.getAttribute("data-context")||"common";o&&!this.translationElements.has(r)&&(this.translationElements.set(r,{key:o,context:s,originalText:r.textContent.trim()}),this.translateElement(r))})}async loadNewTranslations(t){try{const n=await fetch(`/api/translations/${t}`,{headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/json"}});if(n.ok){const r=await n.json();this.translations=r,this.applyTranslations()}}catch(n){console.warn("Failed to load translations:",n),this.applyTranslations()}}applyTranslations(){this.translationElements.forEach((t,n)=>{this.translateElement(n)})}translateElement(t){const n=this.translationElements.get(t);if(!n)return;const{key:r,context:o}=n,s=this.getTranslation(r,o);s&&s!==r&&(t.textContent=s)}getTranslation(t,n="common"){return this.translations[n]&&this.translations[n][t]||t}translate(t,n="common",r=null){const o=this.getTranslation(t,n);return o!==t?o:r||t}addTranslation(t,n,r="common"){t.setAttribute("data-trans-key",n),t.setAttribute("data-context",r),this.translationElements.set(t,{key:n,context:r,originalText:t.textContent.trim()}),this.translateElement(t)}}document.addEventListener("DOMContentLoaded",()=>{window.autoTranslation=new ei});class ti{constructor(){this.apiEndpoint="/api/students/translate-name",this.cache=new Map,this.init()}init(){this.setupFormListeners(),this.setupBulkTranslation()}setupFormListeners(){document.addEventListener("input",t=>{const n=t.target;this.isNameField(n)&&this.handleNameInput(n)}),document.addEventListener("blur",t=>{const n=t.target;this.isNameField(n)&&this.translateField(n)})}isNameField(t){return["full_name","first_name","last_name","nationality"].some(r=>t.name===r||t.id===r||t.classList.contains(`${r}-input`))}handleNameInput(t){t.translationTimeout&&clearTimeout(t.translationTimeout),t.translationTimeout=setTimeout(()=>{this.translateField(t)},1e3)}async translateField(t){const n=t.value.trim();if(!n)return;const r=`${t.name}_${n}`;if(this.cache.has(r)){this.fillArabicField(t,this.cache.get(r));return}try{const s=await(await fetch(this.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content,Accept:"application/json"},body:JSON.stringify({name:n,type:t.name==="nationality"?"nationality":"name"})})).json();s.success&&s.translation&&(this.cache.set(r,s.translation),this.fillArabicField(t,s.translation),s.suggestions&&s.suggestions.length>1&&this.showSuggestions(t,s.suggestions))}catch(o){console.warn("Translation failed:",o)}}fillArabicField(t,n){const r=`${t.name}_ar`,o=document.querySelector(`[name="${r}"], #${r}`);o&&!o.value&&(o.value=n,o.dispatchEvent(new Event("change",{bubbles:!0})),this.showTranslationFeedback(o))}showTranslationFeedback(t){t.classList.add("auto-translated"),setTimeout(()=>{t.classList.remove("auto-translated")},2e3)}showSuggestions(t,n){this.removeSuggestions(t);const r=document.createElement("div");r.className="translation-suggestions",r.innerHTML=`
            <div class="suggestions-header">
                <span>Arabic suggestions:</span>
                <button type="button" class="close-suggestions">&times;</button>
            </div>
            <div class="suggestions-list">
                ${n.map(o=>`<button type="button" class="suggestion-item" data-suggestion="${o}">
                        ${o}
                    </button>`).join("")}
            </div>
        `,t.parentNode.style.position="relative",t.parentNode.appendChild(r),r.addEventListener("click",o=>{if(o.target.classList.contains("suggestion-item")){const s=o.target.dataset.suggestion;this.fillArabicField(t,s),this.removeSuggestions(t)}else o.target.classList.contains("close-suggestions")&&this.removeSuggestions(t)}),setTimeout(()=>{this.removeSuggestions(t)},1e4)}removeSuggestions(t){const n=t.parentNode.querySelector(".translation-suggestions");n&&n.remove()}setupBulkTranslation(){const t=document.querySelector("#bulk-translate-names");t&&t.addEventListener("click",()=>{this.handleBulkTranslation()})}async handleBulkTranslation(){const t=Array.from(document.querySelectorAll('input[name="student_ids[]"]:checked')).map(n=>n.value);if(t.length===0){alert("Please select students to translate");return}try{const r=await(await fetch("/api/students/bulk-translate-names",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content,Accept:"application/json"},body:JSON.stringify({student_ids:t})})).json();r.success?(alert(`${r.translated_count} students translated successfully`),window.location.reload()):alert("Translation failed: "+r.message)}catch(n){console.error("Bulk translation failed:",n),alert("Bulk translation failed. Please try again.")}}async translateName(t,n="name"){try{const o=await(await fetch(this.apiEndpoint,{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').content,Accept:"application/json"},body:JSON.stringify({name:t,type:n})})).json();return o.success?o.translation:null}catch(r){return console.error("Translation failed:",r),null}}clearCache(){this.cache.clear()}}document.addEventListener("DOMContentLoaded",()=>{window.nameTranslation=new ti});var Et=!1,xt=!1,X=[],St=-1;function ni(e){ri(e)}function ri(e){X.includes(e)||X.push(e),si()}function oi(e){let t=X.indexOf(e);t!==-1&&t>St&&X.splice(t,1)}function si(){!xt&&!Et&&(Et=!0,queueMicrotask(ii))}function ii(){Et=!1,xt=!0;for(let e=0;e<X.length;e++)X[e](),St=e;X.length=0,St=-1,xt=!1}var de,ne,fe,lr,At=!0;function ai(e){At=!1,e(),At=!0}function ci(e){de=e.reactive,fe=e.release,ne=t=>e.effect(t,{scheduler:n=>{At?ni(n):n()}}),lr=e.raw}function xn(e){ne=e}function li(e){let t=()=>{};return[r=>{let o=ne(r);return e._x_effects||(e._x_effects=new Set,e._x_runEffects=()=>{e._x_effects.forEach(s=>s())}),e._x_effects.add(o),t=()=>{o!==void 0&&(e._x_effects.delete(o),fe(o))},o},()=>{t()}]}function ur(e,t){let n=!0,r,o=ne(()=>{let s=e();JSON.stringify(s),n?r=s:queueMicrotask(()=>{t(s,r),r=s}),n=!1});return()=>fe(o)}var dr=[],fr=[],mr=[];function ui(e){mr.push(e)}function qt(e,t){typeof t=="function"?(e._x_cleanups||(e._x_cleanups=[]),e._x_cleanups.push(t)):(t=e,fr.push(t))}function hr(e){dr.push(e)}function pr(e,t,n){e._x_attributeCleanups||(e._x_attributeCleanups={}),e._x_attributeCleanups[t]||(e._x_attributeCleanups[t]=[]),e._x_attributeCleanups[t].push(n)}function gr(e,t){e._x_attributeCleanups&&Object.entries(e._x_attributeCleanups).forEach(([n,r])=>{(t===void 0||t.includes(n))&&(r.forEach(o=>o()),delete e._x_attributeCleanups[n])})}function di(e){var t,n;for((t=e._x_effects)==null||t.forEach(oi);(n=e._x_cleanups)!=null&&n.length;)e._x_cleanups.pop()()}var Ut=new MutationObserver(Wt),Ht=!1;function zt(){Ut.observe(document,{subtree:!0,childList:!0,attributes:!0,attributeOldValue:!0}),Ht=!0}function yr(){fi(),Ut.disconnect(),Ht=!1}var ye=[];function fi(){let e=Ut.takeRecords();ye.push(()=>e.length>0&&Wt(e));let t=ye.length;queueMicrotask(()=>{if(ye.length===t)for(;ye.length>0;)ye.shift()()})}function S(e){if(!Ht)return e();yr();let t=e();return zt(),t}var Kt=!1,He=[];function mi(){Kt=!0}function hi(){Kt=!1,Wt(He),He=[]}function Wt(e){if(Kt){He=He.concat(e);return}let t=[],n=new Set,r=new Map,o=new Map;for(let s=0;s<e.length;s++)if(!e[s].target._x_ignoreMutationObserver&&(e[s].type==="childList"&&(e[s].removedNodes.forEach(i=>{i.nodeType===1&&i._x_marker&&n.add(i)}),e[s].addedNodes.forEach(i=>{if(i.nodeType===1){if(n.has(i)){n.delete(i);return}i._x_marker||t.push(i)}})),e[s].type==="attributes")){let i=e[s].target,a=e[s].attributeName,c=e[s].oldValue,l=()=>{r.has(i)||r.set(i,[]),r.get(i).push({name:a,value:i.getAttribute(a)})},u=()=>{o.has(i)||o.set(i,[]),o.get(i).push(a)};i.hasAttribute(a)&&c===null?l():i.hasAttribute(a)?(u(),l()):u()}o.forEach((s,i)=>{gr(i,s)}),r.forEach((s,i)=>{dr.forEach(a=>a(i,s))});for(let s of n)t.some(i=>i.contains(s))||fr.forEach(i=>i(s));for(let s of t)s.isConnected&&mr.forEach(i=>i(s));t=null,n=null,r=null,o=null}function br(e){return Re(ae(e))}function Ce(e,t,n){return e._x_dataStack=[t,...ae(n||e)],()=>{e._x_dataStack=e._x_dataStack.filter(r=>r!==t)}}function ae(e){return e._x_dataStack?e._x_dataStack:typeof ShadowRoot=="function"&&e instanceof ShadowRoot?ae(e.host):e.parentNode?ae(e.parentNode):[]}function Re(e){return new Proxy({objects:e},pi)}var pi={ownKeys({objects:e}){return Array.from(new Set(e.flatMap(t=>Object.keys(t))))},has({objects:e},t){return t==Symbol.unscopables?!1:e.some(n=>Object.prototype.hasOwnProperty.call(n,t)||Reflect.has(n,t))},get({objects:e},t,n){return t=="toJSON"?gi:Reflect.get(e.find(r=>Reflect.has(r,t))||{},t,n)},set({objects:e},t,n,r){const o=e.find(i=>Object.prototype.hasOwnProperty.call(i,t))||e[e.length-1],s=Object.getOwnPropertyDescriptor(o,t);return s!=null&&s.set&&(s!=null&&s.get)?s.set.call(r,n)||!0:Reflect.set(o,t,n)}};function gi(){return Reflect.ownKeys(this).reduce((t,n)=>(t[n]=Reflect.get(this,n),t),{})}function _r(e){let t=r=>typeof r=="object"&&!Array.isArray(r)&&r!==null,n=(r,o="")=>{Object.entries(Object.getOwnPropertyDescriptors(r)).forEach(([s,{value:i,enumerable:a}])=>{if(a===!1||i===void 0||typeof i=="object"&&i!==null&&i.__v_skip)return;let c=o===""?s:`${o}.${s}`;typeof i=="object"&&i!==null&&i._x_interceptor?r[s]=i.initialize(e,c,s):t(i)&&i!==r&&!(i instanceof Element)&&n(i,c)})};return n(e)}function wr(e,t=()=>{}){let n={initialValue:void 0,_x_interceptor:!0,initialize(r,o,s){return e(this.initialValue,()=>yi(r,o),i=>vt(r,o,i),o,s)}};return t(n),r=>{if(typeof r=="object"&&r!==null&&r._x_interceptor){let o=n.initialize.bind(n);n.initialize=(s,i,a)=>{let c=r.initialize(s,i,a);return n.initialValue=c,o(s,i,a)}}else n.initialValue=r;return n}}function yi(e,t){return t.split(".").reduce((n,r)=>n[r],e)}function vt(e,t,n){if(typeof t=="string"&&(t=t.split(".")),t.length===1)e[t[0]]=n;else{if(t.length===0)throw error;return e[t[0]]||(e[t[0]]={}),vt(e[t[0]],t.slice(1),n)}}var Er={};function M(e,t){Er[e]=t}function Tt(e,t){let n=bi(t);return Object.entries(Er).forEach(([r,o])=>{Object.defineProperty(e,`$${r}`,{get(){return o(t,n)},enumerable:!1})}),e}function bi(e){let[t,n]=Cr(e),r={interceptor:wr,...t};return qt(e,n),r}function _i(e,t,n,...r){try{return n(...r)}catch(o){Ae(o,e,t)}}function Ae(e,t,n=void 0){e=Object.assign(e??{message:"No error message given."},{el:t,expression:n}),console.warn(`Alpine Expression Error: ${e.message}

${n?'Expression: "'+n+`"

`:""}`,t),setTimeout(()=>{throw e},0)}var je=!0;function xr(e){let t=je;je=!1;let n=e();return je=t,n}function G(e,t,n={}){let r;return O(e,t)(o=>r=o,n),r}function O(...e){return Sr(...e)}var Sr=Ar;function wi(e){Sr=e}function Ar(e,t){let n={};Tt(n,e);let r=[n,...ae(e)],o=typeof t=="function"?Ei(r,t):Si(r,t,e);return _i.bind(null,e,t,o)}function Ei(e,t){return(n=()=>{},{scope:r={},params:o=[]}={})=>{let s=t.apply(Re([r,...e]),o);ze(n,s)}}var ut={};function xi(e,t){if(ut[e])return ut[e];let n=Object.getPrototypeOf(async function(){}).constructor,r=/^[\n\s]*if.*\(.*\)/.test(e.trim())||/^(let|const)\s/.test(e.trim())?`(async()=>{ ${e} })()`:e,s=(()=>{try{let i=new n(["__self","scope"],`with (scope) { __self.result = ${r} }; __self.finished = true; return __self.result;`);return Object.defineProperty(i,"name",{value:`[Alpine] ${e}`}),i}catch(i){return Ae(i,t,e),Promise.resolve()}})();return ut[e]=s,s}function Si(e,t,n){let r=xi(t,n);return(o=()=>{},{scope:s={},params:i=[]}={})=>{r.result=void 0,r.finished=!1;let a=Re([s,...e]);if(typeof r=="function"){let c=r(r,a).catch(l=>Ae(l,n,t));r.finished?(ze(o,r.result,a,i,n),r.result=void 0):c.then(l=>{ze(o,l,a,i,n)}).catch(l=>Ae(l,n,t)).finally(()=>r.result=void 0)}}}function ze(e,t,n,r,o){if(je&&typeof t=="function"){let s=t.apply(n,r);s instanceof Promise?s.then(i=>ze(e,i,n,r)).catch(i=>Ae(i,o,t)):e(s)}else typeof t=="object"&&t instanceof Promise?t.then(s=>e(s)):e(t)}var Jt="x-";function me(e=""){return Jt+e}function Ai(e){Jt=e}var Ke={};function C(e,t){return Ke[e]=t,{before(n){if(!Ke[n]){console.warn(String.raw`Cannot find directive \`${n}\`. \`${e}\` will use the default order of execution`);return}const r=J.indexOf(n);J.splice(r>=0?r:J.indexOf("DEFAULT"),0,e)}}}function vi(e){return Object.keys(Ke).includes(e)}function Vt(e,t,n){if(t=Array.from(t),e._x_virtualDirectives){let s=Object.entries(e._x_virtualDirectives).map(([a,c])=>({name:a,value:c})),i=vr(s);s=s.map(a=>i.find(c=>c.name===a.name)?{name:`x-bind:${a.name}`,value:`"${a.value}"`}:a),t=t.concat(s)}let r={};return t.map(Lr((s,i)=>r[s]=i)).filter(kr).map(Ri(r,n)).sort(Oi).map(s=>Ci(e,s))}function vr(e){return Array.from(e).map(Lr()).filter(t=>!kr(t))}var Ct=!1,we=new Map,Tr=Symbol();function Ti(e){Ct=!0;let t=Symbol();Tr=t,we.set(t,[]);let n=()=>{for(;we.get(t).length;)we.get(t).shift()();we.delete(t)},r=()=>{Ct=!1,n()};e(n),r()}function Cr(e){let t=[],n=a=>t.push(a),[r,o]=li(e);return t.push(o),[{Alpine:Oe,effect:r,cleanup:n,evaluateLater:O.bind(O,e),evaluate:G.bind(G,e)},()=>t.forEach(a=>a())]}function Ci(e,t){let n=()=>{},r=Ke[t.type]||n,[o,s]=Cr(e);pr(e,t.original,s);let i=()=>{e._x_ignore||e._x_ignoreSelf||(r.inline&&r.inline(e,t,o),r=r.bind(r,e,t,o),Ct?we.get(Tr).push(r):r())};return i.runCleanups=s,i}var Rr=(e,t)=>({name:n,value:r})=>(n.startsWith(e)&&(n=n.replace(e,t)),{name:n,value:r}),Or=e=>e;function Lr(e=()=>{}){return({name:t,value:n})=>{let{name:r,value:o}=Nr.reduce((s,i)=>i(s),{name:t,value:n});return r!==t&&e(r,t),{name:r,value:o}}}var Nr=[];function Xt(e){Nr.push(e)}function kr({name:e}){return Pr().test(e)}var Pr=()=>new RegExp(`^${Jt}([^:^.]+)\\b`);function Ri(e,t){return({name:n,value:r})=>{let o=n.match(Pr()),s=n.match(/:([a-zA-Z0-9\-_:]+)/),i=n.match(/\.[^.\]]+(?=[^\]]*$)/g)||[],a=t||e[n]||n;return{type:o?o[1]:null,value:s?s[1]:null,modifiers:i.map(c=>c.replace(".","")),expression:r,original:a}}}var Rt="DEFAULT",J=["ignore","ref","data","id","anchor","bind","init","for","model","modelable","transition","show","if",Rt,"teleport"];function Oi(e,t){let n=J.indexOf(e.type)===-1?Rt:e.type,r=J.indexOf(t.type)===-1?Rt:t.type;return J.indexOf(n)-J.indexOf(r)}function Ee(e,t,n={}){e.dispatchEvent(new CustomEvent(t,{detail:n,bubbles:!0,composed:!0,cancelable:!0}))}function te(e,t){if(typeof ShadowRoot=="function"&&e instanceof ShadowRoot){Array.from(e.children).forEach(o=>te(o,t));return}let n=!1;if(t(e,()=>n=!0),n)return;let r=e.firstElementChild;for(;r;)te(r,t),r=r.nextElementSibling}function P(e,...t){console.warn(`Alpine Warning: ${e}`,...t)}var Sn=!1;function Li(){Sn&&P("Alpine has already been initialized on this page. Calling Alpine.start() more than once can cause problems."),Sn=!0,document.body||P("Unable to initialize. Trying to load Alpine before `<body>` is available. Did you forget to add `defer` in Alpine's `<script>` tag?"),Ee(document,"alpine:init"),Ee(document,"alpine:initializing"),zt(),ui(t=>$(t,te)),qt(t=>pe(t)),hr((t,n)=>{Vt(t,n).forEach(r=>r())});let e=t=>!et(t.parentElement,!0);Array.from(document.querySelectorAll(Mr().join(","))).filter(e).forEach(t=>{$(t)}),Ee(document,"alpine:initialized"),setTimeout(()=>{Di()})}var Gt=[],Dr=[];function Fr(){return Gt.map(e=>e())}function Mr(){return Gt.concat(Dr).map(e=>e())}function Br(e){Gt.push(e)}function Ir(e){Dr.push(e)}function et(e,t=!1){return he(e,n=>{if((t?Mr():Fr()).some(o=>n.matches(o)))return!0})}function he(e,t){if(e){if(t(e))return e;if(e._x_teleportBack&&(e=e._x_teleportBack),!!e.parentElement)return he(e.parentElement,t)}}function Ni(e){return Fr().some(t=>e.matches(t))}var jr=[];function ki(e){jr.push(e)}var Pi=1;function $(e,t=te,n=()=>{}){he(e,r=>r._x_ignore)||Ti(()=>{t(e,(r,o)=>{r._x_marker||(n(r,o),jr.forEach(s=>s(r,o)),Vt(r,r.attributes).forEach(s=>s()),r._x_ignore||(r._x_marker=Pi++),r._x_ignore&&o())})})}function pe(e,t=te){t(e,n=>{di(n),gr(n),delete n._x_marker})}function Di(){[["ui","dialog",["[x-dialog], [x-popover]"]],["anchor","anchor",["[x-anchor]"]],["sort","sort",["[x-sort]"]]].forEach(([t,n,r])=>{vi(n)||r.some(o=>{if(document.querySelector(o))return P(`found "${o}", but missing ${t} plugin`),!0})})}var Ot=[],Qt=!1;function Yt(e=()=>{}){return queueMicrotask(()=>{Qt||setTimeout(()=>{Lt()})}),new Promise(t=>{Ot.push(()=>{e(),t()})})}function Lt(){for(Qt=!1;Ot.length;)Ot.shift()()}function Fi(){Qt=!0}function Zt(e,t){return Array.isArray(t)?An(e,t.join(" ")):typeof t=="object"&&t!==null?Mi(e,t):typeof t=="function"?Zt(e,t()):An(e,t)}function An(e,t){let n=o=>o.split(" ").filter(s=>!e.classList.contains(s)).filter(Boolean),r=o=>(e.classList.add(...o),()=>{e.classList.remove(...o)});return t=t===!0?t="":t||"",r(n(t))}function Mi(e,t){let n=a=>a.split(" ").filter(Boolean),r=Object.entries(t).flatMap(([a,c])=>c?n(a):!1).filter(Boolean),o=Object.entries(t).flatMap(([a,c])=>c?!1:n(a)).filter(Boolean),s=[],i=[];return o.forEach(a=>{e.classList.contains(a)&&(e.classList.remove(a),i.push(a))}),r.forEach(a=>{e.classList.contains(a)||(e.classList.add(a),s.push(a))}),()=>{i.forEach(a=>e.classList.add(a)),s.forEach(a=>e.classList.remove(a))}}function tt(e,t){return typeof t=="object"&&t!==null?Bi(e,t):Ii(e,t)}function Bi(e,t){let n={};return Object.entries(t).forEach(([r,o])=>{n[r]=e.style[r],r.startsWith("--")||(r=ji(r)),e.style.setProperty(r,o)}),setTimeout(()=>{e.style.length===0&&e.removeAttribute("style")}),()=>{tt(e,n)}}function Ii(e,t){let n=e.getAttribute("style",t);return e.setAttribute("style",t),()=>{e.setAttribute("style",n||"")}}function ji(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function Nt(e,t=()=>{}){let n=!1;return function(){n?t.apply(this,arguments):(n=!0,e.apply(this,arguments))}}C("transition",(e,{value:t,modifiers:n,expression:r},{evaluate:o})=>{typeof r=="function"&&(r=o(r)),r!==!1&&(!r||typeof r=="boolean"?qi(e,n,t):$i(e,r,t))});function $i(e,t,n){$r(e,Zt,""),{enter:o=>{e._x_transition.enter.during=o},"enter-start":o=>{e._x_transition.enter.start=o},"enter-end":o=>{e._x_transition.enter.end=o},leave:o=>{e._x_transition.leave.during=o},"leave-start":o=>{e._x_transition.leave.start=o},"leave-end":o=>{e._x_transition.leave.end=o}}[n](t)}function qi(e,t,n){$r(e,tt);let r=!t.includes("in")&&!t.includes("out")&&!n,o=r||t.includes("in")||["enter"].includes(n),s=r||t.includes("out")||["leave"].includes(n);t.includes("in")&&!r&&(t=t.filter((g,_)=>_<t.indexOf("out"))),t.includes("out")&&!r&&(t=t.filter((g,_)=>_>t.indexOf("out")));let i=!t.includes("opacity")&&!t.includes("scale"),a=i||t.includes("opacity"),c=i||t.includes("scale"),l=a?0:1,u=c?be(t,"scale",95)/100:1,m=be(t,"delay",0)/1e3,p=be(t,"origin","center"),w="opacity, transform",h=be(t,"duration",150)/1e3,y=be(t,"duration",75)/1e3,f="cubic-bezier(0.4, 0.0, 0.2, 1)";o&&(e._x_transition.enter.during={transformOrigin:p,transitionDelay:`${m}s`,transitionProperty:w,transitionDuration:`${h}s`,transitionTimingFunction:f},e._x_transition.enter.start={opacity:l,transform:`scale(${u})`},e._x_transition.enter.end={opacity:1,transform:"scale(1)"}),s&&(e._x_transition.leave.during={transformOrigin:p,transitionDelay:`${m}s`,transitionProperty:w,transitionDuration:`${y}s`,transitionTimingFunction:f},e._x_transition.leave.start={opacity:1,transform:"scale(1)"},e._x_transition.leave.end={opacity:l,transform:`scale(${u})`})}function $r(e,t,n={}){e._x_transition||(e._x_transition={enter:{during:n,start:n,end:n},leave:{during:n,start:n,end:n},in(r=()=>{},o=()=>{}){kt(e,t,{during:this.enter.during,start:this.enter.start,end:this.enter.end},r,o)},out(r=()=>{},o=()=>{}){kt(e,t,{during:this.leave.during,start:this.leave.start,end:this.leave.end},r,o)}})}window.Element.prototype._x_toggleAndCascadeWithTransitions=function(e,t,n,r){const o=document.visibilityState==="visible"?requestAnimationFrame:setTimeout;let s=()=>o(n);if(t){e._x_transition&&(e._x_transition.enter||e._x_transition.leave)?e._x_transition.enter&&(Object.entries(e._x_transition.enter.during).length||Object.entries(e._x_transition.enter.start).length||Object.entries(e._x_transition.enter.end).length)?e._x_transition.in(n):s():e._x_transition?e._x_transition.in(n):s();return}e._x_hidePromise=e._x_transition?new Promise((i,a)=>{e._x_transition.out(()=>{},()=>i(r)),e._x_transitioning&&e._x_transitioning.beforeCancel(()=>a({isFromCancelledTransition:!0}))}):Promise.resolve(r),queueMicrotask(()=>{let i=qr(e);i?(i._x_hideChildren||(i._x_hideChildren=[]),i._x_hideChildren.push(e)):o(()=>{let a=c=>{let l=Promise.all([c._x_hidePromise,...(c._x_hideChildren||[]).map(a)]).then(([u])=>u==null?void 0:u());return delete c._x_hidePromise,delete c._x_hideChildren,l};a(e).catch(c=>{if(!c.isFromCancelledTransition)throw c})})})};function qr(e){let t=e.parentNode;if(t)return t._x_hidePromise?t:qr(t)}function kt(e,t,{during:n,start:r,end:o}={},s=()=>{},i=()=>{}){if(e._x_transitioning&&e._x_transitioning.cancel(),Object.keys(n).length===0&&Object.keys(r).length===0&&Object.keys(o).length===0){s(),i();return}let a,c,l;Ui(e,{start(){a=t(e,r)},during(){c=t(e,n)},before:s,end(){a(),l=t(e,o)},after:i,cleanup(){c(),l()}})}function Ui(e,t){let n,r,o,s=Nt(()=>{S(()=>{n=!0,r||t.before(),o||(t.end(),Lt()),t.after(),e.isConnected&&t.cleanup(),delete e._x_transitioning})});e._x_transitioning={beforeCancels:[],beforeCancel(i){this.beforeCancels.push(i)},cancel:Nt(function(){for(;this.beforeCancels.length;)this.beforeCancels.shift()();s()}),finish:s},S(()=>{t.start(),t.during()}),Fi(),requestAnimationFrame(()=>{if(n)return;let i=Number(getComputedStyle(e).transitionDuration.replace(/,.*/,"").replace("s",""))*1e3,a=Number(getComputedStyle(e).transitionDelay.replace(/,.*/,"").replace("s",""))*1e3;i===0&&(i=Number(getComputedStyle(e).animationDuration.replace("s",""))*1e3),S(()=>{t.before()}),r=!0,requestAnimationFrame(()=>{n||(S(()=>{t.end()}),Lt(),setTimeout(e._x_transitioning.finish,i+a),o=!0)})})}function be(e,t,n){if(e.indexOf(t)===-1)return n;const r=e[e.indexOf(t)+1];if(!r||t==="scale"&&isNaN(r))return n;if(t==="duration"||t==="delay"){let o=r.match(/([0-9]+)ms/);if(o)return o[1]}return t==="origin"&&["top","right","left","center","bottom"].includes(e[e.indexOf(t)+2])?[r,e[e.indexOf(t)+2]].join(" "):r}var U=!1;function z(e,t=()=>{}){return(...n)=>U?t(...n):e(...n)}function Hi(e){return(...t)=>U&&e(...t)}var Ur=[];function nt(e){Ur.push(e)}function zi(e,t){Ur.forEach(n=>n(e,t)),U=!0,Hr(()=>{$(t,(n,r)=>{r(n,()=>{})})}),U=!1}var Pt=!1;function Ki(e,t){t._x_dataStack||(t._x_dataStack=e._x_dataStack),U=!0,Pt=!0,Hr(()=>{Wi(t)}),U=!1,Pt=!1}function Wi(e){let t=!1;$(e,(r,o)=>{te(r,(s,i)=>{if(t&&Ni(s))return i();t=!0,o(s,i)})})}function Hr(e){let t=ne;xn((n,r)=>{let o=t(n);return fe(o),()=>{}}),e(),xn(t)}function zr(e,t,n,r=[]){switch(e._x_bindings||(e._x_bindings=de({})),e._x_bindings[t]=n,t=r.includes("camel")?ea(t):t,t){case"value":Ji(e,n);break;case"style":Xi(e,n);break;case"class":Vi(e,n);break;case"selected":case"checked":Gi(e,t,n);break;default:Kr(e,t,n);break}}function Ji(e,t){if(Vr(e))e.attributes.value===void 0&&(e.value=t),window.fromModel&&(typeof t=="boolean"?e.checked=$e(e.value)===t:e.checked=vn(e.value,t));else if(en(e))Number.isInteger(t)?e.value=t:!Array.isArray(t)&&typeof t!="boolean"&&![null,void 0].includes(t)?e.value=String(t):Array.isArray(t)?e.checked=t.some(n=>vn(n,e.value)):e.checked=!!t;else if(e.tagName==="SELECT")Zi(e,t);else{if(e.value===t)return;e.value=t===void 0?"":t}}function Vi(e,t){e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedClasses=Zt(e,t)}function Xi(e,t){e._x_undoAddedStyles&&e._x_undoAddedStyles(),e._x_undoAddedStyles=tt(e,t)}function Gi(e,t,n){Kr(e,t,n),Yi(e,t,n)}function Kr(e,t,n){[null,void 0,!1].includes(n)&&na(t)?e.removeAttribute(t):(Wr(t)&&(n=t),Qi(e,t,n))}function Qi(e,t,n){e.getAttribute(t)!=n&&e.setAttribute(t,n)}function Yi(e,t,n){e[t]!==n&&(e[t]=n)}function Zi(e,t){const n=[].concat(t).map(r=>r+"");Array.from(e.options).forEach(r=>{r.selected=n.includes(r.value)})}function ea(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function vn(e,t){return e==t}function $e(e){return[1,"1","true","on","yes",!0].includes(e)?!0:[0,"0","false","off","no",!1].includes(e)?!1:e?!!e:null}var ta=new Set(["allowfullscreen","async","autofocus","autoplay","checked","controls","default","defer","disabled","formnovalidate","inert","ismap","itemscope","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","selected","shadowrootclonable","shadowrootdelegatesfocus","shadowrootserializable"]);function Wr(e){return ta.has(e)}function na(e){return!["aria-pressed","aria-checked","aria-expanded","aria-selected"].includes(e)}function ra(e,t,n){return e._x_bindings&&e._x_bindings[t]!==void 0?e._x_bindings[t]:Jr(e,t,n)}function oa(e,t,n,r=!0){if(e._x_bindings&&e._x_bindings[t]!==void 0)return e._x_bindings[t];if(e._x_inlineBindings&&e._x_inlineBindings[t]!==void 0){let o=e._x_inlineBindings[t];return o.extract=r,xr(()=>G(e,o.expression))}return Jr(e,t,n)}function Jr(e,t,n){let r=e.getAttribute(t);return r===null?typeof n=="function"?n():n:r===""?!0:Wr(t)?!![t,"true"].includes(r):r}function en(e){return e.type==="checkbox"||e.localName==="ui-checkbox"||e.localName==="ui-switch"}function Vr(e){return e.type==="radio"||e.localName==="ui-radio"}function Xr(e,t){var n;return function(){var r=this,o=arguments,s=function(){n=null,e.apply(r,o)};clearTimeout(n),n=setTimeout(s,t)}}function Gr(e,t){let n;return function(){let r=this,o=arguments;n||(e.apply(r,o),n=!0,setTimeout(()=>n=!1,t))}}function Qr({get:e,set:t},{get:n,set:r}){let o=!0,s,i=ne(()=>{let a=e(),c=n();if(o)r(dt(a)),o=!1;else{let l=JSON.stringify(a),u=JSON.stringify(c);l!==s?r(dt(a)):l!==u&&t(dt(c))}s=JSON.stringify(e()),JSON.stringify(n())});return()=>{fe(i)}}function dt(e){return typeof e=="object"?JSON.parse(JSON.stringify(e)):e}function sa(e){(Array.isArray(e)?e:[e]).forEach(n=>n(Oe))}var K={},Tn=!1;function ia(e,t){if(Tn||(K=de(K),Tn=!0),t===void 0)return K[e];K[e]=t,_r(K[e]),typeof t=="object"&&t!==null&&t.hasOwnProperty("init")&&typeof t.init=="function"&&K[e].init()}function aa(){return K}var Yr={};function ca(e,t){let n=typeof t!="function"?()=>t:t;return e instanceof Element?Zr(e,n()):(Yr[e]=n,()=>{})}function la(e){return Object.entries(Yr).forEach(([t,n])=>{Object.defineProperty(e,t,{get(){return(...r)=>n(...r)}})}),e}function Zr(e,t,n){let r=[];for(;r.length;)r.pop()();let o=Object.entries(t).map(([i,a])=>({name:i,value:a})),s=vr(o);return o=o.map(i=>s.find(a=>a.name===i.name)?{name:`x-bind:${i.name}`,value:`"${i.value}"`}:i),Vt(e,o,n).map(i=>{r.push(i.runCleanups),i()}),()=>{for(;r.length;)r.pop()()}}var eo={};function ua(e,t){eo[e]=t}function da(e,t){return Object.entries(eo).forEach(([n,r])=>{Object.defineProperty(e,n,{get(){return(...o)=>r.bind(t)(...o)},enumerable:!1})}),e}var fa={get reactive(){return de},get release(){return fe},get effect(){return ne},get raw(){return lr},version:"3.14.9",flushAndStopDeferringMutations:hi,dontAutoEvaluateFunctions:xr,disableEffectScheduling:ai,startObservingMutations:zt,stopObservingMutations:yr,setReactivityEngine:ci,onAttributeRemoved:pr,onAttributesAdded:hr,closestDataStack:ae,skipDuringClone:z,onlyDuringClone:Hi,addRootSelector:Br,addInitSelector:Ir,interceptClone:nt,addScopeToNode:Ce,deferMutations:mi,mapAttributes:Xt,evaluateLater:O,interceptInit:ki,setEvaluator:wi,mergeProxies:Re,extractProp:oa,findClosest:he,onElRemoved:qt,closestRoot:et,destroyTree:pe,interceptor:wr,transition:kt,setStyles:tt,mutateDom:S,directive:C,entangle:Qr,throttle:Gr,debounce:Xr,evaluate:G,initTree:$,nextTick:Yt,prefixed:me,prefix:Ai,plugin:sa,magic:M,store:ia,start:Li,clone:Ki,cloneNode:zi,bound:ra,$data:br,watch:ur,walk:te,data:ua,bind:ca},Oe=fa;function ma(e,t){const n=Object.create(null),r=e.split(",");for(let o=0;o<r.length;o++)n[r[o]]=!0;return o=>!!n[o]}var ha=Object.freeze({}),pa=Object.prototype.hasOwnProperty,rt=(e,t)=>pa.call(e,t),Q=Array.isArray,xe=e=>to(e)==="[object Map]",ga=e=>typeof e=="string",tn=e=>typeof e=="symbol",ot=e=>e!==null&&typeof e=="object",ya=Object.prototype.toString,to=e=>ya.call(e),no=e=>to(e).slice(8,-1),nn=e=>ga(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ba=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},_a=ba(e=>e.charAt(0).toUpperCase()+e.slice(1)),ro=(e,t)=>e!==t&&(e===e||t===t),Dt=new WeakMap,_e=[],j,Y=Symbol("iterate"),Ft=Symbol("Map key iterate");function wa(e){return e&&e._isEffect===!0}function Ea(e,t=ha){wa(e)&&(e=e.raw);const n=Aa(e,t);return t.lazy||n(),n}function xa(e){e.active&&(oo(e),e.options.onStop&&e.options.onStop(),e.active=!1)}var Sa=0;function Aa(e,t){const n=function(){if(!n.active)return e();if(!_e.includes(n)){oo(n);try{return Ta(),_e.push(n),j=n,e()}finally{_e.pop(),so(),j=_e[_e.length-1]}}};return n.id=Sa++,n.allowRecurse=!!t.allowRecurse,n._isEffect=!0,n.active=!0,n.raw=e,n.deps=[],n.options=t,n}function oo(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}var ce=!0,rn=[];function va(){rn.push(ce),ce=!1}function Ta(){rn.push(ce),ce=!0}function so(){const e=rn.pop();ce=e===void 0?!0:e}function D(e,t,n){if(!ce||j===void 0)return;let r=Dt.get(e);r||Dt.set(e,r=new Map);let o=r.get(n);o||r.set(n,o=new Set),o.has(j)||(o.add(j),j.deps.push(o),j.options.onTrack&&j.options.onTrack({effect:j,target:e,type:t,key:n}))}function H(e,t,n,r,o,s){const i=Dt.get(e);if(!i)return;const a=new Set,c=u=>{u&&u.forEach(m=>{(m!==j||m.allowRecurse)&&a.add(m)})};if(t==="clear")i.forEach(c);else if(n==="length"&&Q(e))i.forEach((u,m)=>{(m==="length"||m>=r)&&c(u)});else switch(n!==void 0&&c(i.get(n)),t){case"add":Q(e)?nn(n)&&c(i.get("length")):(c(i.get(Y)),xe(e)&&c(i.get(Ft)));break;case"delete":Q(e)||(c(i.get(Y)),xe(e)&&c(i.get(Ft)));break;case"set":xe(e)&&c(i.get(Y));break}const l=u=>{u.options.onTrigger&&u.options.onTrigger({effect:u,target:e,key:n,type:t,newValue:r,oldValue:o,oldTarget:s}),u.options.scheduler?u.options.scheduler(u):u()};a.forEach(l)}var Ca=ma("__proto__,__v_isRef,__isVue"),io=new Set(Object.getOwnPropertyNames(Symbol).map(e=>Symbol[e]).filter(tn)),Ra=ao(),Oa=ao(!0),Cn=La();function La(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const r=x(this);for(let s=0,i=this.length;s<i;s++)D(r,"get",s+"");const o=r[t](...n);return o===-1||o===!1?r[t](...n.map(x)):o}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){va();const r=x(this)[t].apply(this,n);return so(),r}}),e}function ao(e=!1,t=!1){return function(r,o,s){if(o==="__v_isReactive")return!e;if(o==="__v_isReadonly")return e;if(o==="__v_raw"&&s===(e?t?za:fo:t?Ha:uo).get(r))return r;const i=Q(r);if(!e&&i&&rt(Cn,o))return Reflect.get(Cn,o,s);const a=Reflect.get(r,o,s);return(tn(o)?io.has(o):Ca(o))||(e||D(r,"get",o),t)?a:Mt(a)?!i||!nn(o)?a.value:a:ot(a)?e?mo(a):cn(a):a}}var Na=ka();function ka(e=!1){return function(n,r,o,s){let i=n[r];if(!e&&(o=x(o),i=x(i),!Q(n)&&Mt(i)&&!Mt(o)))return i.value=o,!0;const a=Q(n)&&nn(r)?Number(r)<n.length:rt(n,r),c=Reflect.set(n,r,o,s);return n===x(s)&&(a?ro(o,i)&&H(n,"set",r,o,i):H(n,"add",r,o)),c}}function Pa(e,t){const n=rt(e,t),r=e[t],o=Reflect.deleteProperty(e,t);return o&&n&&H(e,"delete",t,void 0,r),o}function Da(e,t){const n=Reflect.has(e,t);return(!tn(t)||!io.has(t))&&D(e,"has",t),n}function Fa(e){return D(e,"iterate",Q(e)?"length":Y),Reflect.ownKeys(e)}var Ma={get:Ra,set:Na,deleteProperty:Pa,has:Da,ownKeys:Fa},Ba={get:Oa,set(e,t){return console.warn(`Set operation on key "${String(t)}" failed: target is readonly.`,e),!0},deleteProperty(e,t){return console.warn(`Delete operation on key "${String(t)}" failed: target is readonly.`,e),!0}},on=e=>ot(e)?cn(e):e,sn=e=>ot(e)?mo(e):e,an=e=>e,st=e=>Reflect.getPrototypeOf(e);function Ne(e,t,n=!1,r=!1){e=e.__v_raw;const o=x(e),s=x(t);t!==s&&!n&&D(o,"get",t),!n&&D(o,"get",s);const{has:i}=st(o),a=r?an:n?sn:on;if(i.call(o,t))return a(e.get(t));if(i.call(o,s))return a(e.get(s));e!==o&&e.get(t)}function ke(e,t=!1){const n=this.__v_raw,r=x(n),o=x(e);return e!==o&&!t&&D(r,"has",e),!t&&D(r,"has",o),e===o?n.has(e):n.has(e)||n.has(o)}function Pe(e,t=!1){return e=e.__v_raw,!t&&D(x(e),"iterate",Y),Reflect.get(e,"size",e)}function Rn(e){e=x(e);const t=x(this);return st(t).has.call(t,e)||(t.add(e),H(t,"add",e,e)),this}function On(e,t){t=x(t);const n=x(this),{has:r,get:o}=st(n);let s=r.call(n,e);s?lo(n,r,e):(e=x(e),s=r.call(n,e));const i=o.call(n,e);return n.set(e,t),s?ro(t,i)&&H(n,"set",e,t,i):H(n,"add",e,t),this}function Ln(e){const t=x(this),{has:n,get:r}=st(t);let o=n.call(t,e);o?lo(t,n,e):(e=x(e),o=n.call(t,e));const s=r?r.call(t,e):void 0,i=t.delete(e);return o&&H(t,"delete",e,void 0,s),i}function Nn(){const e=x(this),t=e.size!==0,n=xe(e)?new Map(e):new Set(e),r=e.clear();return t&&H(e,"clear",void 0,void 0,n),r}function De(e,t){return function(r,o){const s=this,i=s.__v_raw,a=x(i),c=t?an:e?sn:on;return!e&&D(a,"iterate",Y),i.forEach((l,u)=>r.call(o,c(l),c(u),s))}}function Fe(e,t,n){return function(...r){const o=this.__v_raw,s=x(o),i=xe(s),a=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,l=o[e](...r),u=n?an:t?sn:on;return!t&&D(s,"iterate",c?Ft:Y),{next(){const{value:m,done:p}=l.next();return p?{value:m,done:p}:{value:a?[u(m[0]),u(m[1])]:u(m),done:p}},[Symbol.iterator](){return this}}}}function q(e){return function(...t){{const n=t[0]?`on key "${t[0]}" `:"";console.warn(`${_a(e)} operation ${n}failed: target is readonly.`,x(this))}return e==="delete"?!1:this}}function Ia(){const e={get(s){return Ne(this,s)},get size(){return Pe(this)},has:ke,add:Rn,set:On,delete:Ln,clear:Nn,forEach:De(!1,!1)},t={get(s){return Ne(this,s,!1,!0)},get size(){return Pe(this)},has:ke,add:Rn,set:On,delete:Ln,clear:Nn,forEach:De(!1,!0)},n={get(s){return Ne(this,s,!0)},get size(){return Pe(this,!0)},has(s){return ke.call(this,s,!0)},add:q("add"),set:q("set"),delete:q("delete"),clear:q("clear"),forEach:De(!0,!1)},r={get(s){return Ne(this,s,!0,!0)},get size(){return Pe(this,!0)},has(s){return ke.call(this,s,!0)},add:q("add"),set:q("set"),delete:q("delete"),clear:q("clear"),forEach:De(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(s=>{e[s]=Fe(s,!1,!1),n[s]=Fe(s,!0,!1),t[s]=Fe(s,!1,!0),r[s]=Fe(s,!0,!0)}),[e,n,t,r]}var[ja,$a,Pc,Dc]=Ia();function co(e,t){const n=e?$a:ja;return(r,o,s)=>o==="__v_isReactive"?!e:o==="__v_isReadonly"?e:o==="__v_raw"?r:Reflect.get(rt(n,o)&&o in r?n:r,o,s)}var qa={get:co(!1)},Ua={get:co(!0)};function lo(e,t,n){const r=x(n);if(r!==n&&t.call(e,r)){const o=no(e);console.warn(`Reactive ${o} contains both the raw and reactive versions of the same object${o==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}var uo=new WeakMap,Ha=new WeakMap,fo=new WeakMap,za=new WeakMap;function Ka(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Wa(e){return e.__v_skip||!Object.isExtensible(e)?0:Ka(no(e))}function cn(e){return e&&e.__v_isReadonly?e:ho(e,!1,Ma,qa,uo)}function mo(e){return ho(e,!0,Ba,Ua,fo)}function ho(e,t,n,r,o){if(!ot(e))return console.warn(`value cannot be made reactive: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const s=o.get(e);if(s)return s;const i=Wa(e);if(i===0)return e;const a=new Proxy(e,i===2?r:n);return o.set(e,a),a}function x(e){return e&&x(e.__v_raw)||e}function Mt(e){return!!(e&&e.__v_isRef===!0)}M("nextTick",()=>Yt);M("dispatch",e=>Ee.bind(Ee,e));M("watch",(e,{evaluateLater:t,cleanup:n})=>(r,o)=>{let s=t(r),a=ur(()=>{let c;return s(l=>c=l),c},o);n(a)});M("store",aa);M("data",e=>br(e));M("root",e=>et(e));M("refs",e=>(e._x_refs_proxy||(e._x_refs_proxy=Re(Ja(e))),e._x_refs_proxy));function Ja(e){let t=[];return he(e,n=>{n._x_refs&&t.push(n._x_refs)}),t}var ft={};function po(e){return ft[e]||(ft[e]=0),++ft[e]}function Va(e,t){return he(e,n=>{if(n._x_ids&&n._x_ids[t])return!0})}function Xa(e,t){e._x_ids||(e._x_ids={}),e._x_ids[t]||(e._x_ids[t]=po(t))}M("id",(e,{cleanup:t})=>(n,r=null)=>{let o=`${n}${r?`-${r}`:""}`;return Ga(e,o,t,()=>{let s=Va(e,n),i=s?s._x_ids[n]:po(n);return r?`${n}-${i}-${r}`:`${n}-${i}`})});nt((e,t)=>{e._x_id&&(t._x_id=e._x_id)});function Ga(e,t,n,r){if(e._x_id||(e._x_id={}),e._x_id[t])return e._x_id[t];let o=r();return e._x_id[t]=o,n(()=>{delete e._x_id[t]}),o}M("el",e=>e);go("Focus","focus","focus");go("Persist","persist","persist");function go(e,t,n){M(t,r=>P(`You can't use [$${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}C("modelable",(e,{expression:t},{effect:n,evaluateLater:r,cleanup:o})=>{let s=r(t),i=()=>{let u;return s(m=>u=m),u},a=r(`${t} = __placeholder`),c=u=>a(()=>{},{scope:{__placeholder:u}}),l=i();c(l),queueMicrotask(()=>{if(!e._x_model)return;e._x_removeModelListeners.default();let u=e._x_model.get,m=e._x_model.set,p=Qr({get(){return u()},set(w){m(w)}},{get(){return i()},set(w){c(w)}});o(p)})});C("teleport",(e,{modifiers:t,expression:n},{cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&P("x-teleport can only be used on a <template> tag",e);let o=kn(n),s=e.content.cloneNode(!0).firstElementChild;e._x_teleport=s,s._x_teleportBack=e,e.setAttribute("data-teleport-template",!0),s.setAttribute("data-teleport-target",!0),e._x_forwardEvents&&e._x_forwardEvents.forEach(a=>{s.addEventListener(a,c=>{c.stopPropagation(),e.dispatchEvent(new c.constructor(c.type,c))})}),Ce(s,{},e);let i=(a,c,l)=>{l.includes("prepend")?c.parentNode.insertBefore(a,c):l.includes("append")?c.parentNode.insertBefore(a,c.nextSibling):c.appendChild(a)};S(()=>{i(s,o,t),z(()=>{$(s)})()}),e._x_teleportPutBack=()=>{let a=kn(n);S(()=>{i(e._x_teleport,a,t)})},r(()=>S(()=>{s.remove(),pe(s)}))});var Qa=document.createElement("div");function kn(e){let t=z(()=>document.querySelector(e),()=>Qa)();return t||P(`Cannot find x-teleport element for selector: "${e}"`),t}var yo=()=>{};yo.inline=(e,{modifiers:t},{cleanup:n})=>{t.includes("self")?e._x_ignoreSelf=!0:e._x_ignore=!0,n(()=>{t.includes("self")?delete e._x_ignoreSelf:delete e._x_ignore})};C("ignore",yo);C("effect",z((e,{expression:t},{effect:n})=>{n(O(e,t))}));function Bt(e,t,n,r){let o=e,s=c=>r(c),i={},a=(c,l)=>u=>l(c,u);if(n.includes("dot")&&(t=Ya(t)),n.includes("camel")&&(t=Za(t)),n.includes("passive")&&(i.passive=!0),n.includes("capture")&&(i.capture=!0),n.includes("window")&&(o=window),n.includes("document")&&(o=document),n.includes("debounce")){let c=n[n.indexOf("debounce")+1]||"invalid-wait",l=We(c.split("ms")[0])?Number(c.split("ms")[0]):250;s=Xr(s,l)}if(n.includes("throttle")){let c=n[n.indexOf("throttle")+1]||"invalid-wait",l=We(c.split("ms")[0])?Number(c.split("ms")[0]):250;s=Gr(s,l)}return n.includes("prevent")&&(s=a(s,(c,l)=>{l.preventDefault(),c(l)})),n.includes("stop")&&(s=a(s,(c,l)=>{l.stopPropagation(),c(l)})),n.includes("once")&&(s=a(s,(c,l)=>{c(l),o.removeEventListener(t,s,i)})),(n.includes("away")||n.includes("outside"))&&(o=document,s=a(s,(c,l)=>{e.contains(l.target)||l.target.isConnected!==!1&&(e.offsetWidth<1&&e.offsetHeight<1||e._x_isShown!==!1&&c(l))})),n.includes("self")&&(s=a(s,(c,l)=>{l.target===e&&c(l)})),(tc(t)||bo(t))&&(s=a(s,(c,l)=>{nc(l,n)||c(l)})),o.addEventListener(t,s,i),()=>{o.removeEventListener(t,s,i)}}function Ya(e){return e.replace(/-/g,".")}function Za(e){return e.toLowerCase().replace(/-(\w)/g,(t,n)=>n.toUpperCase())}function We(e){return!Array.isArray(e)&&!isNaN(e)}function ec(e){return[" ","_"].includes(e)?e:e.replace(/([a-z])([A-Z])/g,"$1-$2").replace(/[_\s]/,"-").toLowerCase()}function tc(e){return["keydown","keyup"].includes(e)}function bo(e){return["contextmenu","click","mouse"].some(t=>e.includes(t))}function nc(e,t){let n=t.filter(s=>!["window","document","prevent","stop","once","capture","self","away","outside","passive"].includes(s));if(n.includes("debounce")){let s=n.indexOf("debounce");n.splice(s,We((n[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.includes("throttle")){let s=n.indexOf("throttle");n.splice(s,We((n[s+1]||"invalid-wait").split("ms")[0])?2:1)}if(n.length===0||n.length===1&&Pn(e.key).includes(n[0]))return!1;const o=["ctrl","shift","alt","meta","cmd","super"].filter(s=>n.includes(s));return n=n.filter(s=>!o.includes(s)),!(o.length>0&&o.filter(i=>((i==="cmd"||i==="super")&&(i="meta"),e[`${i}Key`])).length===o.length&&(bo(e.type)||Pn(e.key).includes(n[0])))}function Pn(e){if(!e)return[];e=ec(e);let t={ctrl:"control",slash:"/",space:" ",spacebar:" ",cmd:"meta",esc:"escape",up:"arrow-up",down:"arrow-down",left:"arrow-left",right:"arrow-right",period:".",comma:",",equal:"=",minus:"-",underscore:"_"};return t[e]=e,Object.keys(t).map(n=>{if(t[n]===e)return n}).filter(n=>n)}C("model",(e,{modifiers:t,expression:n},{effect:r,cleanup:o})=>{let s=e;t.includes("parent")&&(s=e.parentNode);let i=O(s,n),a;typeof n=="string"?a=O(s,`${n} = __placeholder`):typeof n=="function"&&typeof n()=="string"?a=O(s,`${n()} = __placeholder`):a=()=>{};let c=()=>{let p;return i(w=>p=w),Dn(p)?p.get():p},l=p=>{let w;i(h=>w=h),Dn(w)?w.set(p):a(()=>{},{scope:{__placeholder:p}})};typeof n=="string"&&e.type==="radio"&&S(()=>{e.hasAttribute("name")||e.setAttribute("name",n)});var u=e.tagName.toLowerCase()==="select"||["checkbox","radio"].includes(e.type)||t.includes("lazy")?"change":"input";let m=U?()=>{}:Bt(e,u,t,p=>{l(mt(e,t,p,c()))});if(t.includes("fill")&&([void 0,null,""].includes(c())||en(e)&&Array.isArray(c())||e.tagName.toLowerCase()==="select"&&e.multiple)&&l(mt(e,t,{target:e},c())),e._x_removeModelListeners||(e._x_removeModelListeners={}),e._x_removeModelListeners.default=m,o(()=>e._x_removeModelListeners.default()),e.form){let p=Bt(e.form,"reset",[],w=>{Yt(()=>e._x_model&&e._x_model.set(mt(e,t,{target:e},c())))});o(()=>p())}e._x_model={get(){return c()},set(p){l(p)}},e._x_forceModelUpdate=p=>{p===void 0&&typeof n=="string"&&n.match(/\./)&&(p=""),window.fromModel=!0,S(()=>zr(e,"value",p)),delete window.fromModel},r(()=>{let p=c();t.includes("unintrusive")&&document.activeElement.isSameNode(e)||e._x_forceModelUpdate(p)})});function mt(e,t,n,r){return S(()=>{if(n instanceof CustomEvent&&n.detail!==void 0)return n.detail!==null&&n.detail!==void 0?n.detail:n.target.value;if(en(e))if(Array.isArray(r)){let o=null;return t.includes("number")?o=ht(n.target.value):t.includes("boolean")?o=$e(n.target.value):o=n.target.value,n.target.checked?r.includes(o)?r:r.concat([o]):r.filter(s=>!rc(s,o))}else return n.target.checked;else{if(e.tagName.toLowerCase()==="select"&&e.multiple)return t.includes("number")?Array.from(n.target.selectedOptions).map(o=>{let s=o.value||o.text;return ht(s)}):t.includes("boolean")?Array.from(n.target.selectedOptions).map(o=>{let s=o.value||o.text;return $e(s)}):Array.from(n.target.selectedOptions).map(o=>o.value||o.text);{let o;return Vr(e)?n.target.checked?o=n.target.value:o=r:o=n.target.value,t.includes("number")?ht(o):t.includes("boolean")?$e(o):t.includes("trim")?o.trim():o}}})}function ht(e){let t=e?parseFloat(e):null;return oc(t)?t:e}function rc(e,t){return e==t}function oc(e){return!Array.isArray(e)&&!isNaN(e)}function Dn(e){return e!==null&&typeof e=="object"&&typeof e.get=="function"&&typeof e.set=="function"}C("cloak",e=>queueMicrotask(()=>S(()=>e.removeAttribute(me("cloak")))));Ir(()=>`[${me("init")}]`);C("init",z((e,{expression:t},{evaluate:n})=>typeof t=="string"?!!t.trim()&&n(t,{},!1):n(t,{},!1)));C("text",(e,{expression:t},{effect:n,evaluateLater:r})=>{let o=r(t);n(()=>{o(s=>{S(()=>{e.textContent=s})})})});C("html",(e,{expression:t},{effect:n,evaluateLater:r})=>{let o=r(t);n(()=>{o(s=>{S(()=>{e.innerHTML=s,e._x_ignoreSelf=!0,$(e),delete e._x_ignoreSelf})})})});Xt(Rr(":",Or(me("bind:"))));var _o=(e,{value:t,modifiers:n,expression:r,original:o},{effect:s,cleanup:i})=>{if(!t){let c={};la(c),O(e,r)(u=>{Zr(e,u,o)},{scope:c});return}if(t==="key")return sc(e,r);if(e._x_inlineBindings&&e._x_inlineBindings[t]&&e._x_inlineBindings[t].extract)return;let a=O(e,r);s(()=>a(c=>{c===void 0&&typeof r=="string"&&r.match(/\./)&&(c=""),S(()=>zr(e,t,c,n))})),i(()=>{e._x_undoAddedClasses&&e._x_undoAddedClasses(),e._x_undoAddedStyles&&e._x_undoAddedStyles()})};_o.inline=(e,{value:t,modifiers:n,expression:r})=>{t&&(e._x_inlineBindings||(e._x_inlineBindings={}),e._x_inlineBindings[t]={expression:r,extract:!1})};C("bind",_o);function sc(e,t){e._x_keyExpression=t}Br(()=>`[${me("data")}]`);C("data",(e,{expression:t},{cleanup:n})=>{if(ic(e))return;t=t===""?"{}":t;let r={};Tt(r,e);let o={};da(o,r);let s=G(e,t,{scope:o});(s===void 0||s===!0)&&(s={}),Tt(s,e);let i=de(s);_r(i);let a=Ce(e,i);i.init&&G(e,i.init),n(()=>{i.destroy&&G(e,i.destroy),a()})});nt((e,t)=>{e._x_dataStack&&(t._x_dataStack=e._x_dataStack,t.setAttribute("data-has-alpine-state",!0))});function ic(e){return U?Pt?!0:e.hasAttribute("data-has-alpine-state"):!1}C("show",(e,{modifiers:t,expression:n},{effect:r})=>{let o=O(e,n);e._x_doHide||(e._x_doHide=()=>{S(()=>{e.style.setProperty("display","none",t.includes("important")?"important":void 0)})}),e._x_doShow||(e._x_doShow=()=>{S(()=>{e.style.length===1&&e.style.display==="none"?e.removeAttribute("style"):e.style.removeProperty("display")})});let s=()=>{e._x_doHide(),e._x_isShown=!1},i=()=>{e._x_doShow(),e._x_isShown=!0},a=()=>setTimeout(i),c=Nt(m=>m?i():s(),m=>{typeof e._x_toggleAndCascadeWithTransitions=="function"?e._x_toggleAndCascadeWithTransitions(e,m,i,s):m?a():s()}),l,u=!0;r(()=>o(m=>{!u&&m===l||(t.includes("immediate")&&(m?a():s()),c(m),l=m,u=!1)}))});C("for",(e,{expression:t},{effect:n,cleanup:r})=>{let o=cc(t),s=O(e,o.items),i=O(e,e._x_keyExpression||"index");e._x_prevKeys=[],e._x_lookup={},n(()=>ac(e,o,s,i)),r(()=>{Object.values(e._x_lookup).forEach(a=>S(()=>{pe(a),a.remove()})),delete e._x_prevKeys,delete e._x_lookup})});function ac(e,t,n,r){let o=i=>typeof i=="object"&&!Array.isArray(i),s=e;n(i=>{lc(i)&&i>=0&&(i=Array.from(Array(i).keys(),f=>f+1)),i===void 0&&(i=[]);let a=e._x_lookup,c=e._x_prevKeys,l=[],u=[];if(o(i))i=Object.entries(i).map(([f,g])=>{let _=Fn(t,g,f,i);r(E=>{u.includes(E)&&P("Duplicate key on x-for",e),u.push(E)},{scope:{index:f,..._}}),l.push(_)});else for(let f=0;f<i.length;f++){let g=Fn(t,i[f],f,i);r(_=>{u.includes(_)&&P("Duplicate key on x-for",e),u.push(_)},{scope:{index:f,...g}}),l.push(g)}let m=[],p=[],w=[],h=[];for(let f=0;f<c.length;f++){let g=c[f];u.indexOf(g)===-1&&w.push(g)}c=c.filter(f=>!w.includes(f));let y="template";for(let f=0;f<u.length;f++){let g=u[f],_=c.indexOf(g);if(_===-1)c.splice(f,0,g),m.push([y,f]);else if(_!==f){let E=c.splice(f,1)[0],A=c.splice(_-1,1)[0];c.splice(f,0,A),c.splice(_,0,E),p.push([E,A])}else h.push(g);y=g}for(let f=0;f<w.length;f++){let g=w[f];g in a&&(S(()=>{pe(a[g]),a[g].remove()}),delete a[g])}for(let f=0;f<p.length;f++){let[g,_]=p[f],E=a[g],A=a[_],v=document.createElement("div");S(()=>{A||P('x-for ":key" is undefined or invalid',s,_,a),A.after(v),E.after(A),A._x_currentIfEl&&A.after(A._x_currentIfEl),v.before(E),E._x_currentIfEl&&E.after(E._x_currentIfEl),v.remove()}),A._x_refreshXForScope(l[u.indexOf(_)])}for(let f=0;f<m.length;f++){let[g,_]=m[f],E=g==="template"?s:a[g];E._x_currentIfEl&&(E=E._x_currentIfEl);let A=l[_],v=u[_],L=document.importNode(s.content,!0).firstElementChild,B=de(A);Ce(L,B,s),L._x_refreshXForScope=re=>{Object.entries(re).forEach(([Le,xo])=>{B[Le]=xo})},S(()=>{E.after(L),z(()=>$(L))()}),typeof v=="object"&&P("x-for key cannot be an object, it must be a string or an integer",s),a[v]=L}for(let f=0;f<h.length;f++)a[h[f]]._x_refreshXForScope(l[u.indexOf(h[f])]);s._x_prevKeys=u})}function cc(e){let t=/,([^,\}\]]*)(?:,([^,\}\]]*))?$/,n=/^\s*\(|\)\s*$/g,r=/([\s\S]*?)\s+(?:in|of)\s+([\s\S]*)/,o=e.match(r);if(!o)return;let s={};s.items=o[2].trim();let i=o[1].replace(n,"").trim(),a=i.match(t);return a?(s.item=i.replace(t,"").trim(),s.index=a[1].trim(),a[2]&&(s.collection=a[2].trim())):s.item=i,s}function Fn(e,t,n,r){let o={};return/^\[.*\]$/.test(e.item)&&Array.isArray(t)?e.item.replace("[","").replace("]","").split(",").map(i=>i.trim()).forEach((i,a)=>{o[i]=t[a]}):/^\{.*\}$/.test(e.item)&&!Array.isArray(t)&&typeof t=="object"?e.item.replace("{","").replace("}","").split(",").map(i=>i.trim()).forEach(i=>{o[i]=t[i]}):o[e.item]=t,e.index&&(o[e.index]=n),e.collection&&(o[e.collection]=r),o}function lc(e){return!Array.isArray(e)&&!isNaN(e)}function wo(){}wo.inline=(e,{expression:t},{cleanup:n})=>{let r=et(e);r._x_refs||(r._x_refs={}),r._x_refs[t]=e,n(()=>delete r._x_refs[t])};C("ref",wo);C("if",(e,{expression:t},{effect:n,cleanup:r})=>{e.tagName.toLowerCase()!=="template"&&P("x-if can only be used on a <template> tag",e);let o=O(e,t),s=()=>{if(e._x_currentIfEl)return e._x_currentIfEl;let a=e.content.cloneNode(!0).firstElementChild;return Ce(a,{},e),S(()=>{e.after(a),z(()=>$(a))()}),e._x_currentIfEl=a,e._x_undoIf=()=>{S(()=>{pe(a),a.remove()}),delete e._x_currentIfEl},a},i=()=>{e._x_undoIf&&(e._x_undoIf(),delete e._x_undoIf)};n(()=>o(a=>{a?s():i()})),r(()=>e._x_undoIf&&e._x_undoIf())});C("id",(e,{expression:t},{evaluate:n})=>{n(t).forEach(o=>Xa(e,o))});nt((e,t)=>{e._x_ids&&(t._x_ids=e._x_ids)});Xt(Rr("@",Or(me("on:"))));C("on",z((e,{value:t,modifiers:n,expression:r},{cleanup:o})=>{let s=r?O(e,r):()=>{};e.tagName.toLowerCase()==="template"&&(e._x_forwardEvents||(e._x_forwardEvents=[]),e._x_forwardEvents.includes(t)||e._x_forwardEvents.push(t));let i=Bt(e,t,n,a=>{s(()=>{},{scope:{$event:a},params:[a]})});o(()=>i())}));it("Collapse","collapse","collapse");it("Intersect","intersect","intersect");it("Focus","trap","focus");it("Mask","mask","mask");function it(e,t,n){C(t,r=>P(`You can't use [x-${t}] without first installing the "${e}" plugin here: https://alpinejs.dev/plugins/${n}`,r))}Oe.setEvaluator(Ar);Oe.setReactivityEngine({reactive:cn,effect:Ea,release:xa,raw:x});var uc=Oe,Eo=uc;window.Alpine=Eo;function oe(){const e=document.querySelectorAll('button, .btn, input[type="button"], input[type="submit"], input[type="reset"], a.btn, a.button, [role="button"]');console.log(`Found ${e.length} buttons to check`),e.forEach((t,n)=>{const r=window.getComputedStyle(t),o=r.backgroundColor,s=r.backgroundImage,i=r.color;dc(o,s,t)&&fc(i)&&(t.style.setProperty("color","white","important"),t.querySelectorAll("*").forEach(m=>{m.style.setProperty("color","white","important"),m!==t&&m.style.setProperty("background","transparent","important")}),t.querySelectorAll("svg").forEach(m=>{m.style.setProperty("color","white","important"),m.style.setProperty("fill","white","important"),m.style.setProperty("stroke","white","important")}),console.log(`Fixed button ${n+1}: "${t.textContent.trim()}" - Changed from ${i} to white`))})}function dc(e,t,n){if(e&&e!=="rgba(0, 0, 0, 0)"&&e!=="transparent"){const s=e.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);if(s){const[,a,c,l]=s.map(Number);if(a>150&&a>c+50&&a>l+50)return!0}const i=e.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/);if(i){const[,a,c,l]=i.map(Number);if(a>150&&a>c+50&&a>l+50)return!0}}if(t&&t!=="none"&&(t.includes("red")||t.includes("#e53e3e")||t.includes("#dc2626")||t.includes("#b91c1c")))return!0;const r=n.className||"";return["red","Red","RED","primary","danger","error","warning","leaders","Leaders","btn-bank","btn-primary","btn-danger","bg-leaders-red","bg-leaders-deep-red","bg-red"].some(s=>r.includes(s))}function fc(e){if(!e||e==="transparent")return!1;const t=e.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);if(t){const[,o,s,i]=t.map(Number);return(.299*o+.587*s+.114*i)/255<.5}const n=e.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/);if(n){const[,o,s,i]=n.map(Number);return(.299*o+.587*s+.114*i)/255<.5}return["black","dark","gray","grey"].some(o=>e.includes(o))}function se(){const e=document.querySelectorAll(".bg-leaders-red, .bg-leaders-deep-red, a.bg-leaders-red, a.bg-leaders-deep-red, .btn-bank.bg-leaders-red");console.log(`Found ${e.length} Quick Actions buttons to fix`),e.forEach((t,n)=>{t.style.setProperty("color","white","important"),t.querySelectorAll("*").forEach(c=>{c.style.setProperty("color","white","important"),c!==t&&c.style.setProperty("background","transparent","important")}),t.querySelectorAll("svg").forEach(c=>{c.style.setProperty("color","white","important"),c.style.setProperty("fill","white","important"),c.style.setProperty("stroke","white","important")});const s=[],i=document.createTreeWalker(t,NodeFilter.SHOW_TEXT,null,!1);let a;for(;a=i.nextNode();)a.nodeValue.trim()&&s.push(a);s.forEach(c=>{c.parentElement&&c.parentElement.style.setProperty("color","white","important")}),console.log(`Fixed Quick Action button ${n+1}: "${t.textContent.trim()}"`)})}function ie(){const e=document.querySelectorAll("table thead th, .table-bank thead th, .table thead th, thead th, .min-w-full thead th");console.log(`Found ${e.length} table headers to fix`),e.forEach((n,r)=>{n.style.setProperty("color","white","important"),n.style.setProperty("background","transparent","important");const o=["text-dark","text-black","text-gray-800","text-gray-900","text-dark-gray"];o.forEach(i=>{n.classList.remove(i)}),n.querySelectorAll("*").forEach(i=>{i.style.setProperty("color","white","important"),i.style.setProperty("background","transparent","important"),o.forEach(a=>{i.classList.remove(a)})}),console.log(`Fixed header ${r+1}: ${n.textContent.trim()}`)}),document.querySelectorAll("table thead, .table-bank thead, .table thead, thead, .min-w-full thead, .bg-off-white").forEach((n,r)=>{n.style.setProperty("background","linear-gradient(135deg, #1f2937 0%, #374151 100%)","important"),n.classList.remove("bg-off-white"),console.log(`Fixed thead ${r+1}`)})}window.dashboardLayout=function(){return{sidebarOpen:!1,sidebarCollapsed:localStorage.getItem("sidebarCollapsed")==="true",currentLang:localStorage.getItem("language")||"EN",currentDirection:localStorage.getItem("direction")||"ltr",init(){window.addEventListener("resize",()=>{window.innerWidth>=1024&&(this.sidebarOpen=!1)}),window.addEventListener("languageChanged",e=>{this.handleLanguageChange(e.detail)}),this.applyLanguageState(),this.$watch("sidebarCollapsed",e=>{localStorage.setItem("sidebarCollapsed",e)})},handleLanguageChange(e){this.currentLang=e.locale.toUpperCase(),this.currentDirection=e.direction,localStorage.setItem("language",this.currentLang),localStorage.setItem("direction",this.currentDirection),this.applyLanguageState(),this.updateRTLContent()},applyLanguageState(){const e=document.documentElement;e.setAttribute("lang",this.currentLang.toLowerCase()),e.setAttribute("dir",this.currentDirection),this.currentDirection==="rtl"?document.body.classList.add("arabic-text"):document.body.classList.remove("arabic-text")},updateRTLContent(){document.querySelectorAll(".currency-amount, .number-display").forEach(n=>{this.currentDirection==="rtl"&&(n.style.direction="ltr",n.style.display="inline-block")}),document.querySelectorAll(".table-bank").forEach(n=>{this.currentDirection==="rtl"?n.style.direction="rtl":n.style.direction="ltr"})},toggleLanguage(){const e=this.currentLang==="EN"?"ar":"en";this.switchLanguage(e)},async switchLanguage(e){try{const n=await(await fetch("/api/language/switch",{method:"POST",headers:{"Content-Type":"application/json","X-CSRF-TOKEN":document.querySelector('meta[name="csrf-token"]').getAttribute("content"),Accept:"application/json"},body:JSON.stringify({locale:e})})).json();n.success&&(window.dispatchEvent(new CustomEvent("languageChanged",{detail:n})),setTimeout(()=>{window.location.reload()},500))}catch(t){console.error("Language switch error:",t)}}}};window.formatCurrency=function(e,t="AED"){return isNaN(e)||e===""?"":`${parseFloat(e).toLocaleString("en-US",{minimumFractionDigits:0,maximumFractionDigits:2})} ${t}`};window.openModal=function(e,t,n=null){console.log(`Opening ${e} modal for ${t}`,n),alert(`${e.charAt(0).toUpperCase()+e.slice(1)} ${t} modal will be implemented soon!`)};document.addEventListener("DOMContentLoaded",function(){document.querySelectorAll(".stats-card").forEach((o,s)=>{o.style.animationDelay=`${s*.1}s`}),document.querySelectorAll(".bank-card").forEach(o=>{o.addEventListener("mouseenter",function(){this.style.transform="translateY(-4px)"}),o.addEventListener("mouseleave",function(){this.style.transform="translateY(0)"})}),document.querySelectorAll(".table-bank tbody tr").forEach(o=>{o.addEventListener("mouseenter",function(){this.style.backgroundColor="var(--off-white)"}),o.addEventListener("mouseleave",function(){this.style.backgroundColor=""})}),console.log("UAE English Sports Academy - Bank-style dashboard loaded successfully"),ie(),oe(),se(),setTimeout(ie,500),setTimeout(oe,500),setTimeout(se,500),setTimeout(ie,1e3),setTimeout(oe,1e3),setTimeout(se,1e3),setTimeout(ie,2e3),setTimeout(oe,2e3),setTimeout(se,2e3),new MutationObserver(function(o){o.forEach(function(s){if(s.type==="childList"&&s.addedNodes.length>0){const i=Array.from(s.addedNodes).some(c=>c.nodeType===1&&(c.tagName==="TABLE"||c.querySelector&&c.querySelector("table"))),a=Array.from(s.addedNodes).some(c=>c.nodeType===1&&(c.tagName==="BUTTON"||c.classList&&c.classList.contains("btn")||c.querySelector&&(c.querySelector("button")||c.querySelector(".btn"))));i&&setTimeout(ie,100),a&&(setTimeout(oe,100),setTimeout(se,100))}})}).observe(document.body,{childList:!0,subtree:!0}),window.addEventListener("load",function(){setTimeout(ie,100),setTimeout(oe,100),setTimeout(se,100)})});Eo.start()});export default mc();
