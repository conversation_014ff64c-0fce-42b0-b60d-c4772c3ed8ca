<!-- Grid View -->
<div class="p-6">
    @if ($uniforms->count() > 0)
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            @foreach ($uniforms as $uniform)
                <div class="bank-card hover:shadow-lg transition-all duration-300 group">
                    <!-- Card Header -->
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex items-center space-x-3">
                            @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                                <input type="checkbox" name="uniform_ids[]" value="{{ $uniform->id }}"
                                    @change="toggleUniformSelection({{ $uniform->id }})"
                                    :checked="selectedUniforms.includes({{ $uniform->id }})" class="form-checkbox-bank">
                            @endif
                            <div
                                class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-xl flex items-center justify-center">
                                <span class="text-white font-bold text-lg">
                                    {{ strtoupper(substr($uniform->student->full_name, 0, 2)) }}
                                </span>
                            </div>
                        </div>
                        <button @click="toggleStatus({{ $uniform->id }})"
                            class="badge-bank {{ $uniform->status_badge_class }} cursor-pointer hover:opacity-80 transition-opacity">
                            {{ $uniform->status_text }}
                        </button>
                    </div>

                    <!-- Student Info -->
                    <div class="mb-4">
                        <h3 class="font-semibold text-charcoal-black text-lg mb-1">{{ $uniform->student->full_name }}
                        </h3>
                        <p class="text-sm text-dark-gray">{{ $uniform->student->email }}</p>
                        <p class="text-xs text-medium-gray">{{ $uniform->student->formatted_phone }}</p>
                    </div>

                    <!-- Order Details -->
                    <div class="space-y-3 mb-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Order ID:</span>
                            <span
                                class="font-mono text-sm text-charcoal-black">#{{ str_pad($uniform->id, 4, '0', STR_PAD_LEFT) }}</span>
                        </div>

                        @if ($uniform->reference_number)
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-medium-gray">Reference:</span>
                                <span
                                    class="font-mono text-sm text-charcoal-black">{{ $uniform->reference_number }}</span>
                            </div>
                        @endif

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Item:</span>
                            <span class="badge-bank badge-info">{{ ucfirst($uniform->item) }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Size:</span>
                            <span class="badge-bank badge-neutral">{{ $uniform->size }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Quantity:</span>
                            <span class="font-semibold text-charcoal-black">{{ $uniform->quantity }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Amount:</span>
                            <span
                                class="font-semibold text-success-green">{{ $uniform->formatted_total_amount }}</span>
                        </div>
                    </div>

                    <!-- Branch & Academy -->
                    <div class="space-y-2 mb-4 p-3 bg-light-gray rounded-lg">
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-medium-gray uppercase tracking-wide">Branch:</span>
                            <span class="text-sm font-medium text-charcoal-black">{{ $uniform->branch->name }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-medium-gray uppercase tracking-wide">Academy:</span>
                            <span class="text-sm font-medium text-charcoal-black">{{ $uniform->academy->name }}</span>
                        </div>
                    </div>

                    <!-- Status Tracking -->
                    <div class="space-y-2 mb-4">
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-medium-gray uppercase tracking-wide">Branch Status:</span>
                            <span
                                class="badge-bank badge-sm {{ $uniform->branch_status === 'delivered' ? 'badge-success' : ($uniform->branch_status === 'received' ? 'badge-warning' : 'badge-neutral') }}">
                                {{ $uniform->branch_status_text }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-medium-gray uppercase tracking-wide">Office Status:</span>
                            <span
                                class="badge-bank badge-sm {{ $uniform->office_status === 'delivered' ? 'badge-success' : ($uniform->office_status === 'received' ? 'badge-warning' : 'badge-neutral') }}">
                                {{ $uniform->office_status_text }}
                            </span>
                        </div>
                    </div>

                    <!-- Dates -->
                    <div class="space-y-2 mb-4 text-sm">
                        <div class="flex items-center justify-between">
                            <span class="text-medium-gray">Order Date:</span>
                            <span class="text-charcoal-black">{{ $uniform->formatted_order_date }}</span>
                        </div>
                        @if ($uniform->delivery_date)
                            <div class="flex items-center justify-between">
                                <span class="text-medium-gray">Delivery:</span>
                                <span class="text-charcoal-black">{{ $uniform->formatted_delivery_date }}</span>
                            </div>
                        @endif
                    </div>

                    <!-- Progress Bar -->
                    <div class="mb-4">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-xs text-medium-gray uppercase tracking-wide">Progress</span>
                            <span class="text-xs text-dark-gray">
                                @switch($uniform->status)
                                    @case('ordered')
                                        25%
                                    @break

                                    @case('processing')
                                        50%
                                    @break

                                    @case('ready')
                                        75%
                                    @break

                                    @case('delivered')
                                        100%
                                    @break

                                    @case('cancelled')
                                        0%
                                    @break

                                    @default
                                        0%
                                @endswitch
                            </span>
                        </div>
                        <div class="w-full bg-light-gray rounded-full h-2">
                            <div
                                class="h-2 rounded-full transition-all duration-300
                                @switch($uniform->status)
                                    @case('ordered')
                                        bg-warning-orange w-1/4
                                        @break
                                    @case('processing')
                                        bg-info-blue w-1/2
                                        @break
                                    @case('ready')
                                        bg-purple-600 w-3/4
                                        @break
                                    @case('delivered')
                                        bg-success-green w-full
                                        @break
                                    @case('cancelled')
                                        bg-error-red w-0
                                        @break
                                    @default
                                        bg-medium-gray w-0
                                @endswitch
                            ">
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex items-center justify-between pt-4 border-t border-light-gray">
                        <div class="text-xs text-medium-gray">
                            {{ $uniform->created_at->diffForHumans() }}
                        </div>
                        <div class="flex items-center space-x-2">
                            <!-- View Button -->
                            <a href="{{ route('uniforms.show', $uniform) }}"
                                class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white"
                                title="View Details">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                            </a>

                            <!-- Edit Button -->
                            @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                                <button @click="$dispatch('open-modal', 'edit-uniform-{{ $uniform->id }}')"
                                    class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white"
                                    title="Edit Order">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </button>
                            @endif

                            <!-- Delete Button (Admin only) -->
                            @if (auth()->user()->role === 'admin')
                                <button @click="deleteUniform({{ $uniform->id }})"
                                    class="btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white"
                                    title="Delete Order">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @else
        <div class="text-center py-12">
            <div class="flex flex-col items-center justify-center space-y-4">
                <svg class="w-16 h-16 text-medium-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                <div class="text-lg font-medium text-dark-gray">No uniform orders found</div>
                <div class="text-sm text-medium-gray text-center">
                    @if (request()->hasAny([
                            'search',
                            'branch_id',
                            'academy_id',
                            'status',
                            'size',
                            'item',
                            'payment_method',
                            'date_from',
                            'date_to',
                        ]))
                        Try adjusting your search criteria or
                        <a href="{{ route('uniforms.index') }}" class="text-leaders-red hover:underline">clear all
                            filters</a>
                    @else
                        Get started by creating your first uniform order
                    @endif
                </div>
                @if (in_array(auth()->user()->role, ['admin', 'branch_manager', 'academy_manager']))
                    <button @click="$dispatch('open-modal', 'create-uniform')" class="btn-bank mt-4">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                            </path>
                        </svg>
                        Create First Order
                    </button>
                @endif
            </div>
        </div>
    @endif
</div>
