<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Profile - {{ $student->full_name }} - LEADERS SPORTS SERVICES LLC SP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 20px;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
        }

        /* Print Header */
        .print-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #d32f2f;
        }

        .print-logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 15px;
            object-fit: contain;
        }

        .print-logo-fallback {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background: #d32f2f;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        .print-title {
            font-size: 24pt;
            font-weight: bold;
            color: #d32f2f;
            margin-bottom: 10px;
        }

        .print-subtitle {
            font-size: 14pt;
            color: #666;
            margin-bottom: 5px;
        }

        .print-date {
            font-size: 10pt;
            color: #999;
        }

        /* Student Profile Section */
        .student-profile-section {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }

        .student-profile-section img,
        .student-profile-section .profile-placeholder {
            width: 100px;
            height: 100px;
            margin-right: 20px;
            border-radius: 50%;
            border: 2px solid #d32f2f;
        }

        .student-profile-section .profile-placeholder {
            background: #d32f2f;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-weight: bold;
        }

        .student-info h1 {
            font-size: 20pt;
            color: #333;
            margin-bottom: 5px;
        }

        .student-info p {
            font-size: 12pt;
            color: #666;
            margin-bottom: 3px;
        }

        /* Information Cards */
        .info-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .info-card h3 {
            font-size: 14pt;
            color: #d32f2f;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #d32f2f;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            color: #333;
            font-size: 10pt;
            margin-bottom: 2px;
            text-transform: uppercase;
        }

        .info-value {
            color: #000;
            font-size: 11pt;
            margin-bottom: 8px;
        }

        /* Tables */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 10pt;
        }

        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .data-table th {
            background: #f5f5f5;
            color: #333;
            font-weight: bold;
        }

        .data-table td {
            color: #000;
        }

        /* Status badges */
        .status-badge {
            border: 1px solid #333;
            padding: 2px 6px;
            font-size: 9pt;
            color: #000;
            background: white;
            border-radius: 4px;
            display: inline-block;
        }

        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-size: 18pt;
            color: #d32f2f;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 10pt;
            color: #666;
            text-transform: uppercase;
        }

        /* Print Footer */
        .print-footer {
            text-align: center;
            font-size: 9pt;
            color: #666;
            padding: 20px 0;
            border-top: 1px solid #ddd;
            margin-top: 30px;
        }

        .footer-logo {
            width: 30px;
            height: 30px;
            margin: 0 auto 10px;
            border-radius: 4px;
            object-fit: contain;
        }

        /* Print Styles */
        @media print {
            @page {
                margin: 0.4in;
                size: A4;
            }

            body {
                background: white !important;
                color: black !important;
                font-size: 11pt;
                line-height: 1.3;
            }

            /* Ensure logos print correctly */
            .print-logo,
            .footer-logo {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
            }

            .print-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }

            /* Compact header for single page */
            .print-header {
                margin-bottom: 20px;
                padding-bottom: 15px;
            }

            .print-logo {
                width: 70px;
                height: 70px;
                margin-bottom: 10px;
            }

            .print-title {
                font-size: 18pt;
                margin-bottom: 5px;
            }

            .print-subtitle {
                font-size: 12pt;
                margin-bottom: 3px;
            }

            .print-date {
                font-size: 9pt;
            }

            /* Compact student profile section */
            .student-profile-section {
                padding: 15px;
                margin-bottom: 20px;
            }

            .student-profile-section img,
            .student-profile-section .profile-placeholder {
                width: 80px;
                height: 80px;
            }

            .student-info h1 {
                font-size: 16pt;
                margin-bottom: 3px;
            }

            .student-info p {
                font-size: 10pt;
                margin-bottom: 2px;
            }

            /* Compact info cards */
            .info-card {
                padding: 10px;
                margin-bottom: 15px;
            }

            .info-card h3 {
                font-size: 12pt;
                margin-bottom: 8px;
                padding-bottom: 3px;
            }

            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }

            .info-item {
                margin-bottom: 6px;
            }

            .info-label {
                font-size: 9pt;
            }

            .info-value {
                font-size: 10pt;
                margin-bottom: 4px;
            }

            /* Compact stats grid */
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 10px;
                margin-bottom: 15px;
            }

            .stat-card {
                padding: 10px;
                margin-bottom: 0;
            }

            .stat-value {
                font-size: 14pt;
            }

            .stat-label {
                font-size: 8pt;
            }

            /* Compact tables */
            .data-table {
                font-size: 9pt;
                margin-bottom: 15px;
            }

            .data-table th,
            .data-table td {
                padding: 4px;
            }

            /* Hide footer for single page layout */
            .print-footer {
                position: static;
                margin-top: 20px;
                padding: 10px 0;
                font-size: 8pt;
            }

            .footer-logo {
                width: 25px;
                height: 25px;
                margin-bottom: 5px;
            }

            /* Force single page */
            .page-break {
                page-break-before: avoid;
            }

            .no-page-break {
                page-break-inside: avoid;
            }

            /* Ensure everything fits on one page */
            .print-container {
                height: auto;
                overflow: visible;
            }
        }

        /* Print Button */
        .print-button {
            background: #d32f2f;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin-bottom: 20px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .print-button:hover {
            background: #b71c1c;
        }

        @media print {
            .print-button {
                display: none;
            }
        }
    </style>
</head>

<body>
    <div class="print-container">
        <!-- Print Button -->
        <button onclick="window.print()" class="print-button">
            <svg width="18" height="18" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                </path>
            </svg>
            Print Profile
        </button>

        <!-- Print Header -->
        <div class="print-header">
            @if (file_exists(public_path('images/logo.jpg')))
                <img src="{{ asset('images/logo.jpg') }}" alt="LEADERS SPORTS SERVICES LLC SP Logo" class="print-logo">
            @else
                <div class="print-logo-fallback">
                    LEADERS
                </div>
            @endif
            <div class="print-title">LEADERS SPORTS SERVICES LLC SP</div>
            <div class="print-subtitle">Student Profile Report</div>
            <div class="print-date">Generated on: {{ now()->format('F d, Y - h:i A') }}</div>
        </div>

        <!-- Student Profile Summary -->
        <div class="student-profile-section">
            @if ($student->hasProfileImage())
                <img src="{{ $student->profile_image_url }}" alt="{{ $student->full_name }}">
            @else
                <div class="profile-placeholder">
                    {{ $student->initials }}
                </div>
            @endif
            <div class="student-info">
                <h1>{{ $student->full_name }}</h1>
                <p><strong>Student ID:</strong> {{ $student->id }}</p>
                <p><strong>Status:</strong> <span class="status-badge">{{ $student->status_text }}</span></p>
                <p><strong>Branch:</strong> {{ $student->branch->name ?? 'N/A' }}</p>
                <p><strong>Academy:</strong> {{ $student->academy->name ?? 'N/A' }}</p>
                @if ($student->program)
                    <p><strong>Program:</strong> {{ $student->program->name }}</p>
                @endif
                <p><strong>Join Date:</strong> {{ $student->formatted_join_date }}</p>
            </div>
        </div>

        <!-- Quick Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">AED {{ number_format($stats['total_payments'] ?? 0) }}</div>
                <div class="stat-label">Total Payments</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $student->attendance_rate }}%</div>
                <div class="stat-label">Attendance</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $student->uniform_orders_count }}</div>
                <div class="stat-label">Uniforms</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{{ $student->days_since_joined }}</div>
                <div class="stat-label">Days Enrolled</div>
            </div>
        </div>

        <!-- Essential Information -->
        <div class="info-card">
            <h3>Student Details</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Phone</div>
                    <div class="info-value">{{ $student->formatted_phone }}</div>
                </div>
                @if ($student->email)
                    <div class="info-item">
                        <div class="info-label">Email</div>
                        <div class="info-value">{{ $student->email }}</div>
                    </div>
                @endif
                <div class="info-item">
                    <div class="info-label">Age</div>
                    <div class="info-value">{{ $student->age }} years ({{ $student->formatted_birth_date }})</div>
                </div>
                @if ($student->nationality)
                    <div class="info-item">
                        <div class="info-label">Nationality</div>
                        <div class="info-value">{{ $student->nationality }}</div>
                    </div>
                @endif
                @if ($student->address)
                    <div class="info-item">
                        <div class="info-label">Address</div>
                        <div class="info-value">{{ $student->address }}</div>
                    </div>
                @endif
                <div class="info-item">
                    <div class="info-label">Enrollment</div>
                    <div class="info-value">{{ $student->formatted_join_date }} ({{ $student->days_since_joined }}
                        days)</div>
                </div>
            </div>
            @if ($student->notes)
                <div class="info-item" style="margin-top: 10px;">
                    <div class="info-label">Notes</div>
                    <div class="info-value">{{ Str::limit($student->notes, 100) }}</div>
                </div>
            @endif
        </div>

        <!-- Enrollment Info -->
        <div class="info-card">
            <h3>Enrollment Info</h3>

            <!-- Hierarchical Structure -->
            <div style="background: #f9f9f9; padding: 15px; border-left: 4px solid #d32f2f; margin-bottom: 15px;">
                <div style="margin-bottom: 10px;">
                    <div style="display: flex; align-items: center; margin-bottom: 8px;">
                        <div
                            style="width: 12px; height: 12px; background: #d32f2f; border-radius: 50%; margin-right: 8px;">
                        </div>
                        <div>
                            <div class="info-label" style="margin-bottom: 2px;">Branch</div>
                            <div style="font-weight: bold; font-size: 14pt;">{{ $student->branch->name ?? 'N/A' }}
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; margin-left: 20px; margin-bottom: 8px;">
                        <div
                            style="width: 8px; height: 8px; background: #e57373; border-radius: 50%; margin-right: 8px;">
                        </div>
                        <div>
                            <div class="info-label" style="margin-bottom: 2px;">Academy</div>
                            <div style="font-weight: 600;">{{ $student->academy->name ?? 'N/A' }}</div>
                        </div>
                    </div>

                    <div style="display: flex; align-items: center; margin-left: 40px;">
                        <div
                            style="width: 6px; height: 6px; background: #ffab91; border-radius: 50%; margin-right: 8px;">
                        </div>
                        <div>
                            <div class="info-label" style="margin-bottom: 2px;">Program</div>
                            @if ($student->program)
                                <div style="font-weight: 500;">{{ $student->program->name }}</div>
                            @else
                                <div style="color: #999;">Not Assigned</div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Join Date</div>
                    <div class="info-value">{{ $student->formatted_join_date }}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Days Since Joined</div>
                    <div class="info-value">{{ $student->days_since_joined }} days</div>
                </div>
                @if ($student->program)
                    <div class="info-item">
                        <div class="info-label">Program Schedule</div>
                        <div class="info-value">
                            {{ $student->program->formatted_days ?? 'N/A' }}
                            @if ($student->program->start_time && $student->program->end_time)
                                <br>
                                <small>{{ $student->program->start_time->format('H:i') }} -
                                    {{ $student->program->end_time->format('H:i') }}</small>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Payment History -->
        @if (count($paymentHistory) > 0)
            <div class="info-card">
                <h3>Payment History (Latest 5)</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Academy</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($paymentHistory->take(5) as $payment)
                            <tr>
                                <td>{{ $payment->payment_date ? $payment->payment_date->format('M d, Y') : 'N/A' }}
                                </td>
                                <td>AED {{ number_format($payment->amount, 2) }}</td>
                                <td>
                                    <span class="status-badge">{{ ucfirst($payment->status) }}</span>
                                </td>
                                <td>{{ $payment->academy->name ?? 'N/A' }}</td>
                                <td>{{ $payment->notes ? Str::limit($payment->notes, 30) : '-' }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif

        <!-- Attendance Summary -->
        @if (!empty($attendanceSummary))
            <div class="info-card">
                <h3>Attendance Summary</h3>
                <div class="info-grid">
                    @foreach ($attendanceSummary as $key => $value)
                        <div class="info-item">
                            <div class="info-label">{{ ucwords(str_replace('_', ' ', $key)) }}</div>
                            <div class="info-value">{{ $value }}</div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Uniform Orders -->
        @if (count($uniformOrders) > 0)
            <div class="info-card">
                <h3>Uniform Orders</h3>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Order Date</th>
                            <th>Item & Size</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Quantity</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($uniformOrders as $order)
                            <tr>
                                <td>{{ $order['order_date'] ?? 'N/A' }}</td>
                                <td>{{ $order['item'] ?? 'N/A' }} ({{ $order['size'] ?? 'N/A' }})</td>
                                <td>AED {{ number_format($order['amount'] ?? 0, 2) }}</td>
                                <td>
                                    <span class="status-badge">{{ ucfirst($order['status'] ?? 'pending') }}</span>
                                </td>
                                <td>Qty: {{ $order['quantity'] ?? 1 }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif

        <!-- Print Footer -->
        <div class="print-footer">
            @if (file_exists(public_path('images/logo.jpg')))
                <img src="{{ asset('images/logo.jpg') }}" alt="LEADERS SPORTS SERVICES LLC SP Logo"
                    class="footer-logo">
            @endif
            <p><strong>LEADERS SPORTS SERVICES LLC SP</strong> - Student Profile Report</p>
            <p>SHARJAH AL MAMZAR | <EMAIL> | +971501051321</p>
            <p>Printed on: {{ now()->format('F d, Y - h:i A') }} | Page 1 of 1</p>
            <p>This is an official document generated by the LEADERS SPORTS SERVICES LLC SP management system.</p>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        // window.onload = function() {
        //     window.print();
        // };

        // Print function
        function printProfile() {
            window.print();
        }

        // Keyboard shortcut for printing (Ctrl+P)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printProfile();
            }
        });
    </script>
</body>

</html>
