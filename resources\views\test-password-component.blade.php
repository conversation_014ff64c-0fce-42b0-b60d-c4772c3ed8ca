@extends('layouts.app')

@section('title', 'Password Component Test')

@section('content')
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Password Input Component Test</h3>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Basic Password Field</h5>
                                    <x-password-input 
                                        name="basic_password" 
                                        placeholder="Enter password"
                                    />
                                </div>
                                
                                <div class="col-md-6">
                                    <h5>Required Password Field</h5>
                                    <x-password-input 
                                        name="required_password" 
                                        placeholder="Enter required password"
                                        label="Required Password"
                                        required
                                    />
                                </div>
                            </div>
                            
                            <hr class="my-4">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Password with Help Text</h5>
                                    <x-password-input 
                                        name="help_password" 
                                        placeholder="Enter password"
                                        label="Password with Help"
                                        helpText="Password must be at least 8 characters long"
                                    />
                                </div>
                                
                                <div class="col-md-6">
                                    <h5>Password without Toggle</h5>
                                    <x-password-input 
                                        name="no_toggle_password" 
                                        placeholder="No toggle button"
                                        label="No Toggle Password"
                                        :showToggle="false"
                                    />
                                </div>
                            </div>
                            
                            <hr class="my-4">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>Confirm Password</h5>
                                    <x-password-input 
                                        id="password_confirmation"
                                        name="password_confirmation" 
                                        placeholder="Confirm your password"
                                        label="Confirm Password"
                                        autocomplete="new-password"
                                    />
                                </div>
                                
                                <div class="col-md-6">
                                    <h5>Custom Class Password</h5>
                                    <x-password-input 
                                        name="custom_password" 
                                        placeholder="Custom styled password"
                                        label="Custom Password"
                                        class="border-primary"
                                    />
                                </div>
                            </div>
                            
                            <hr class="my-4">
                            
                            <div class="row">
                                <div class="col-md-12">
                                    <h5>Password without Label</h5>
                                    <x-password-input 
                                        name="no_label_password" 
                                        placeholder="Password field without label"
                                        :showLabel="false"
                                    />
                                </div>
                            </div>
                            
                            <div class="mt-4">
                                <button type="button" class="btn btn-primary">Test Submit</button>
                                <button type="button" class="btn btn-secondary" onclick="testAllFields()">Test All Fields</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function testAllFields() {
    // Test function to verify all password fields work
    const passwordFields = document.querySelectorAll('input[type="password"], input[type="text"]');
    const toggleButtons = document.querySelectorAll('.password-toggle-btn');
    
    console.log('Found password fields:', passwordFields.length);
    console.log('Found toggle buttons:', toggleButtons.length);
    
    // Test each toggle button
    toggleButtons.forEach((btn, index) => {
        console.log(`Testing toggle button ${index + 1}`);
        btn.click();
        setTimeout(() => {
            btn.click(); // Toggle back
        }, 500);
    });
    
    alert(`Test completed! Found ${passwordFields.length} password fields and ${toggleButtons.length} toggle buttons.`);
}
</script>
@endpush
