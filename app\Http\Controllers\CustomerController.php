<?php

namespace App\Http\Controllers;

use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use App\Services\FileUploadService;

class CustomerController extends Controller
{
    /**
     * Display a listing of customers with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Customer::class);

        $query = Customer::with(['reservations'])
            ->withCount(['reservations']);

        // Search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                    ->orWhere('full_name_ar', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('customer_number', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->get('status') === 'active') {
                $query->where('status', true);
            } else {
                $query->where('status', false);
            }
        }

        // Customer type filter
        if ($request->filled('customer_type')) {
            $query->where('customer_type', $request->get('customer_type'));
        }

        // VIP status filter
        if ($request->filled('vip_status')) {
            $query->where('vip_status', $request->get('vip_status') === '1');
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('registration_date', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('registration_date', '<=', $request->get('date_to'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'registration_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $customers = $query->paginate(15)->withQueryString();

        // Calculate statistics for the stats cards
        $stats = [
            'total_customers' => Customer::count(),
            'active_customers' => Customer::where('status', 'active')->count(),
            'inactive_customers' => Customer::where('status', 'inactive')->count(),
            'blocked_customers' => Customer::where('status', 'blocked')->count(),
            'individual_customers' => Customer::where('customer_type', 'individual')->count(),
            'corporate_customers' => Customer::where('customer_type', 'corporate')->count(),
            'vip_customers' => Customer::where('vip_status', true)->count(),
            'total_reservations' => Customer::withCount('reservations')->get()->sum('reservations_count'),
            'total_revenue' => Customer::sum('total_spent'),
        ];

        if ($request->ajax()) {
            return response()->json([
                'customers' => $customers,
                'html' => view('customers.partials.table', compact('customers'))->render()
            ]);
        }

        return view('customers.index', compact('customers', 'stats'));
    }

    /**
     * Show the form for creating a new customer.
     */
    public function create(): View
    {
        Gate::authorize('create', Customer::class);

        return view('customers.create');
    }

    /**
     * Store a newly created customer in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Customer::class);

        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'full_name_ar' => 'nullable|string|max:255',
            'first_name' => 'required|string|max:255',
            'first_name_ar' => 'nullable|string|max:255',
            'last_name' => 'required|string|max:255',
            'last_name_ar' => 'nullable|string|max:255',
            'email' => 'required|email|unique:customers,email',


            'nationality' => 'required|string|max:100',
            'address' => 'nullable|string|max:500',
            'address_ar' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'emirate' => 'nullable|string|max:100',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'required|in:male,female',
            'customer_type' => 'required|in:individual,corporate',
            'company_name' => 'nullable|string|max:255',
            'company_name_ar' => 'nullable|string|max:255',
            'trade_license' => 'nullable|string|max:100',
            'tax_number' => 'nullable|string|max:100',
            'preferred_language' => 'required|in:en,ar',
            'preferred_contact_method' => 'required|in:email,phone,sms,whatsapp',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|in:cash,credit_7,credit_15,credit_30',
            'vip_status' => 'boolean',
            'status' => 'nullable|in:active,inactive,blocked',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $validator->validated();

            // Handle empty phone number
            if (empty($data['phone']) || $data['phone'] === '+971') {
                $data['phone'] = null;
            }

            // Generate customer number
            $data['customer_number'] = $this->generateCustomerNumber();
            $data['registration_date'] = now();
            $data['status'] = true;

            // Handle profile image upload
            if ($request->hasFile('profile_image')) {
                $result = FileUploadService::uploadImage(
                    $request->file('profile_image'),
                    'customers/profiles'
                );

                if (!$result['success']) {
                    throw new \Exception($result['error']);
                }

                $data['profile_image'] = $result['file_path'];
            }

            $customer = Customer::create($data);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Customer created successfully',
                    'customer' => $customer
                ]);
            }

            return redirect()->route('customers.show', $customer)
                ->with('success', 'Customer created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating customer: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error creating customer'
                ], 500);
            }

            return back()->with('error', 'Error creating customer')->withInput();
        }
    }

    /**
     * Display the specified customer.
     */
    public function show(Customer $customer, Request $request): View|JsonResponse
    {
        Gate::authorize('view', $customer);

        $customer->load([
            'reservations.venue',
            'reservations.field',
            'reservations.reservationPayments'
        ]);

        // Get recent reservations
        $recentReservations = $customer->reservations()
            ->with(['venue', 'field'])
            ->orderBy('reservation_date', 'desc')
            ->orderBy('start_time', 'desc')
            ->limit(10)
            ->get();

        // Get statistics
        $stats = [
            'total_reservations' => $customer->reservations()->count(),
            'active_reservations' => $customer->reservations()->whereIn('status', ['pending', 'confirmed', 'in_progress'])->count(),
            'completed_reservations' => $customer->reservations()->where('status', 'completed')->count(),
            'cancelled_reservations' => $customer->reservations()->where('status', 'cancelled')->count(),
            'total_spent' => $customer->total_spent,
            'average_booking_value' => $customer->total_bookings > 0 ? $customer->total_spent / $customer->total_bookings : 0,
        ];

        if ($request->ajax()) {
            return response()->json([
                'customer' => $customer,
                'recent_reservations' => $recentReservations,
                'stats' => $stats
            ]);
        }

        return view('customers.show', compact('customer', 'recentReservations', 'stats'));
    }

    /**
     * Show the form for editing the specified customer.
     */
    public function edit(Customer $customer): View
    {
        Gate::authorize('update', $customer);

        return view('customers.edit', compact('customer'));
    }

    /**
     * Update the specified customer in storage.
     */
    public function update(Request $request, Customer $customer): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $customer);



        $validator = Validator::make($request->all(), [
            'full_name' => 'required|string|max:255',
            'full_name_ar' => 'nullable|string|max:255',
            'first_name' => 'required|string|max:255',
            'first_name_ar' => 'nullable|string|max:255',
            'last_name' => 'required|string|max:255',
            'last_name_ar' => 'nullable|string|max:255',
            'email' => ['required', 'email', Rule::unique('customers')->ignore($customer->id)],


            'nationality' => 'required|string|max:100',
            'nationality_ar' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:500',
            'address_ar' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'emirate' => 'nullable|string|max:100',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'required|in:male,female',
            'id_type' => 'required|in:emirates_id,passport,visa',
            'id_number' => ['required', 'string', 'max:50', Rule::unique('customers')->ignore($customer->id)],
            'id_expiry_date' => 'nullable|date|after:today',
            'customer_type' => 'required|in:individual,corporate',
            'company_name' => 'nullable|string|max:255',
            'company_name_ar' => 'nullable|string|max:255',
            'trade_license' => 'nullable|string|max:100',
            'tax_number' => 'nullable|string|max:100',
            'preferred_language' => 'required|in:en,ar',
            'preferred_contact_method' => 'required|in:email,phone,sms,whatsapp',
            'status' => 'required|in:active,inactive,blocked',
            'emergency_contact' => 'nullable|array',
            'emergency_contact.name' => 'nullable|string|max:255',

            'emergency_contact.relationship' => 'nullable|string|max:100',
            'credit_limit' => 'nullable|numeric|min:0',
            'payment_terms' => 'nullable|in:cash,credit_7,credit_15,credit_30',
            'vip_status' => 'boolean',
            'notes' => 'nullable|string|max:1000',
            'notes_ar' => 'nullable|string|max:1000',
            'profile_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $validator->validated();



            // Handle profile image upload
            if ($request->hasFile('profile_image')) {
                $result = FileUploadService::uploadImage(
                    $request->file('profile_image'),
                    'customers/profiles',
                    ['old_file_path' => $customer->profile_image]
                );

                if (!$result['success']) {
                    throw new \Exception($result['error']);
                }

                $data['profile_image'] = $result['file_path'];
            }

            $customer->update($data);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Customer updated successfully',
                    'customer' => $customer->fresh()
                ]);
            }

            return redirect()->route('customers.show', $customer)
                ->with('success', 'Customer updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating customer: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating customer'
                ], 500);
            }

            return back()->with('error', 'Error updating customer')->withInput();
        }
    }

    /**
     * Remove the specified customer from storage.
     */
    public function destroy(Customer $customer): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $customer);

        try {
            DB::beginTransaction();

            // Check if customer has active reservations
            $activeReservations = $customer->reservations()
                ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
                ->count();

            if ($activeReservations > 0) {
                $message = 'Cannot delete customer with active reservations. Please cancel or complete all active reservations first.';

                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 422);
                }

                return back()->with('error', $message);
            }

            // Check if customer has any reservations at all
            $totalReservations = $customer->reservations()->count();
            $hasPayments = $customer->reservationPayments()->exists();

            if ($totalReservations > 0 || $hasPayments) {
                // Soft delete by setting status to inactive instead of hard delete
                $customer->update(['status' => 'inactive']);
                $message = 'Customer deactivated successfully (has reservation history)';

                DB::commit();

                if (request()->ajax()) {
                    return response()->json([
                        'success' => true,
                        'message' => $message
                    ]);
                }

                return redirect()->route('customers.index')
                    ->with('success', $message);
            }

            // Safe to hard delete - no reservations or payments
            // Delete profile image if exists
            if ($customer->profile_image) {
                $deleteResult = FileUploadService::deleteFile($customer->profile_image);
                if (!$deleteResult['success']) {
                    Log::warning('Failed to delete customer profile image during deletion', [
                        'customer_id' => $customer->id,
                        'image_path' => $customer->profile_image,
                        'error' => $deleteResult['error']
                    ]);
                    // Continue with deletion even if image deletion fails
                }
            }

            $customer->delete();

            DB::commit();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Customer deleted successfully'
                ]);
            }

            return redirect()->route('customers.index')
                ->with('success', 'Customer deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting customer: ' . $e->getMessage(), [
                'customer_id' => $customer->id,
                'trace' => $e->getTraceAsString()
            ]);

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while deleting the customer'
                ], 500);
            }

            return back()->with('error', 'An error occurred while deleting the customer');
        }
    }

    /**
     * Toggle customer status (active/inactive).
     */
    public function toggleStatus(Customer $customer): JsonResponse
    {
        Gate::authorize('update', $customer);

        try {
            $newStatus = $customer->status === 'active' ? 'inactive' : 'active';
            $customer->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => 'Customer status updated successfully',
                'status' => $customer->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error toggling customer status: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error updating customer status'
            ], 500);
        }
    }

    /**
     * Block a customer.
     */
    public function block(Customer $customer): JsonResponse
    {
        Gate::authorize('update', $customer);

        try {
            $customer->update(['status' => 'blocked']);

            return response()->json([
                'success' => true,
                'message' => 'Customer blocked successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error blocking customer: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error blocking customer'
            ], 500);
        }
    }

    /**
     * Unblock a customer.
     */
    public function unblock(Customer $customer): JsonResponse
    {
        Gate::authorize('update', $customer);

        try {
            $customer->update(['status' => true]);

            return response()->json([
                'success' => true,
                'message' => 'Customer unblocked successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error unblocking customer: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error unblocking customer'
            ], 500);
        }
    }

    /**
     * Handle bulk actions on customers.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', Customer::class);

        $validator = Validator::make($request->all(), [
            'action' => 'required|in:activate,deactivate,block,delete',
            'customer_ids' => 'required|array|min:1',
            'customer_ids.*' => 'exists:customers,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $customerIds = $request->get('customer_ids');
            $action = $request->get('action');
            $count = 0;

            foreach ($customerIds as $customerId) {
                $customer = Customer::find($customerId);
                if (!$customer) continue;

                Gate::authorize('update', $customer);

                switch ($action) {
                    case 'activate':
                        $customer->update(['status' => 'active']);
                        $count++;
                        break;
                    case 'deactivate':
                        $customer->update(['status' => 'inactive']);
                        $count++;
                        break;
                    case 'block':
                        $customer->update(['status' => 'blocked']);
                        $count++;
                        break;
                    case 'delete':
                        // Check if customer has related records
                        if ($customer->reservations()->exists()) {
                            $customer->update(['status' => 'inactive']);
                        } else {
                            $customer->delete();
                        }
                        $count++;
                        break;
                }
            }

            $actionText = match ($action) {
                'activate' => 'activated',
                'deactivate' => 'deactivated',
                'block' => 'blocked',
                'delete' => 'deleted/deactivated',
                default => 'processed'
            };

            return response()->json([
                'success' => true,
                'message' => "{$count} customers {$actionText} successfully"
            ]);
        } catch (\Exception $e) {
            Log::error('Customer bulk action failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get customer statistics.
     */
    public function getStatistics(Customer $customer): JsonResponse
    {
        Gate::authorize('view', $customer);

        $stats = [
            'total_reservations' => $customer->reservations()->count(),
            'active_reservations' => $customer->reservations()->whereIn('status', ['pending', 'confirmed', 'in_progress'])->count(),
            'completed_reservations' => $customer->reservations()->where('status', 'completed')->count(),
            'cancelled_reservations' => $customer->reservations()->where('status', 'cancelled')->count(),
            'total_spent' => $customer->total_spent,
            'average_booking_value' => $customer->total_bookings > 0 ? $customer->total_spent / $customer->total_bookings : 0,
            'days_since_registration' => $customer->days_since_registration,
            'days_since_last_booking' => $customer->days_since_last_booking,
        ];

        return response()->json($stats);
    }

    /**
     * Export customers to Excel.
     */
    public function exportExcel(Request $request): \Symfony\Component\HttpFoundation\Response
    {
        Gate::authorize('export', Customer::class);

        // Implementation for Excel export would go here
        // For now, return a simple response
        return response()->make('Excel content', 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="customers.xlsx"'
        ]);
    }

    /**
     * Export customers to PDF.
     */
    public function exportPdf(Request $request): \Symfony\Component\HttpFoundation\Response
    {
        Gate::authorize('export', Customer::class);

        // Implementation for PDF export would go here
        // For now, return a simple response
        return response()->make('PDF content', 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="customers.pdf"'
        ]);
    }

    /**
     * API endpoint for customers (AJAX requests).
     */
    public function apiIndex(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', Customer::class);

        $query = Customer::query();

        // Search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('full_name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('customer_number', 'like', "%{$search}%");
            });
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status') === 'active');
        }

        $customers = $query->orderBy('full_name')->get();

        return response()->json($customers);
    }

    /**
     * Generate a unique customer number.
     */
    private function generateCustomerNumber(): string
    {
        $prefix = 'CUS';
        $year = date('Y');
        $month = date('m');

        // Get the last customer number for this month
        $lastCustomer = Customer::where('customer_number', 'like', "{$prefix}{$year}{$month}%")
            ->orderBy('customer_number', 'desc')
            ->first();

        if ($lastCustomer) {
            $lastNumber = (int) substr($lastCustomer->customer_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }
}
