@extends('layouts.dashboard')

@section('title', $program->name . ' - Program Details')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $program->name }}</h1>
                <p class="text-lg text-dark-gray">{{ $program->academy->name }} - {{ $program->academy->branch->name }}</p>
                <div class="flex items-center space-x-3 mt-2">
                    <span class="badge-bank badge-info">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        ID: #{{ $program->id }}
                    </span>
                    <span class="badge-bank {{ $program->status ? 'badge-success' : 'badge-neutral' }}">
                        {{ $program->status ? 'Active' : 'Inactive' }}
                    </span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('create', App\Models\Student::class)
                <a href="{{ route('students.create', ['program_id' => $program->id, 'academy_id' => $program->academy_id, 'branch_id' => $program->academy->branch_id]) }}"
                    class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                        </path>
                    </svg>
                    Add Student
                </a>
            @endcan
            @can('update', $program)
                <a href="{{ route('programs.edit', $program) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Program
                </a>
            @endcan
            <a href="{{ route('programs.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Programs
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Program Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-5 gap-6">
            <!-- Classes Card -->
            <div class="bank-card bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-700 uppercase tracking-wide">Classes</p>
                        <p class="text-3xl font-bold text-blue-900">{{ $program->classes }}</p>
                        <p class="text-xs text-blue-600 mt-1">Total Sessions</p>
                    </div>
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Price Card -->
            <div class="bank-card bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-700 uppercase tracking-wide">Price</p>
                        <p class="text-3xl font-bold text-green-900">{{ number_format($program->price, 2) }}</p>
                        <p class="text-xs text-green-600 mt-1">AED</p>
                    </div>
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Students Card -->
            <div class="bank-card bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-700 uppercase tracking-wide">Students</p>
                        <p class="text-3xl font-bold text-purple-900">{{ $program->student_count }}</p>
                        <p class="text-xs text-purple-600 mt-1">
                            @if ($program->max_students)
                                of {{ $program->max_students }} max
                            @else
                                Enrolled
                            @endif
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Duration Card -->
            <div class="bank-card bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-orange-700 uppercase tracking-wide">Duration</p>
                        <p class="text-3xl font-bold text-orange-900">{{ $program->duration_hours }}</p>
                        <p class="text-xs text-orange-600 mt-1">Hours per session</p>
                    </div>
                    <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Total Revenue Card -->
            <div class="bank-card bg-gradient-to-br from-red-50 to-red-100 border-red-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-red-700 uppercase tracking-wide">Revenue</p>
                        <p class="text-3xl font-bold text-red-900">
                            {{ number_format($paymentStats['total_revenue'] ?? 0, 2) }}</p>
                        <p class="text-xs text-red-600 mt-1">AED Total</p>
                    </div>
                    <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Program Details -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Program Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Program Information</h3>
                        <p class="bank-card-subtitle">Detailed program specifications</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Program Name:</span>
                            <span class="text-sm text-charcoal-black font-medium">{{ $program->name }}</span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Academy:</span>
                            <span class="text-sm text-charcoal-black">{{ $program->academy->name }}</span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Branch:</span>
                            <span class="text-sm text-charcoal-black">{{ $program->academy->branch->name }}</span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Status:</span>
                            <span class="badge-bank {{ $program->status ? 'badge-success' : 'badge-neutral' }}">
                                {{ $program->status ? 'Active' : 'Inactive' }}
                            </span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Number of Classes:</span>
                            <span class="text-sm text-charcoal-black font-medium">{{ $program->classes }}</span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Price:</span>
                            <span
                                class="text-sm text-charcoal-black font-bold text-leaders-red">{{ number_format($program->price, 2) }}
                                AED</span>
                        </div>

                        @if ($program->max_students)
                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">Max Students:</span>
                                <span class="text-sm text-charcoal-black">{{ $program->max_students }}</span>
                            </div>

                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">Available Spots:</span>
                                <span
                                    class="text-sm text-charcoal-black font-medium {{ $program->getAvailableSpots() > 0 ? 'text-success-green' : 'text-error-red' }}">
                                    {{ $program->getAvailableSpots() }}
                                </span>
                            </div>
                        @endif

                        @if ($program->description)
                            <div class="pt-4 border-t border-light-gray">
                                <span class="text-sm font-medium text-dark-gray block mb-2">Description:</span>
                                <p class="text-sm text-charcoal-black">{{ $program->description }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Schedule Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Schedule Information</h3>
                        <p class="bank-card-subtitle">Program timing and schedule details</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div>
                            <span class="text-sm font-medium text-dark-gray block mb-2">Program Days:</span>
                            <div class="flex flex-wrap gap-2">
                                @php $days = $program->getDaysArray(); @endphp
                                @if (!empty($days))
                                    @foreach ($days as $day)
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                            {{ $day }}
                                        </span>
                                    @endforeach
                                @else
                                    <span class="text-sm text-medium-gray">No schedule set</span>
                                @endif
                            </div>
                        </div>

                        @if ($program->start_time && $program->end_time)
                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">Start Time:</span>
                                <span
                                    class="text-sm text-charcoal-black font-medium">{{ \Carbon\Carbon::parse($program->start_time)->format('g:i A') }}</span>
                            </div>

                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">End Time:</span>
                                <span
                                    class="text-sm text-charcoal-black font-medium">{{ \Carbon\Carbon::parse($program->end_time)->format('g:i A') }}</span>
                            </div>

                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">Duration:</span>
                                <span class="text-sm text-charcoal-black font-medium">{{ $program->duration_hours }}
                                    hours</span>
                            </div>
                        @endif

                        @if ($program->getNextClassDate())
                            <div class="pt-4 border-t border-light-gray">
                                <div class="flex justify-between items-start">
                                    <span class="text-sm font-medium text-dark-gray">Next Class:</span>
                                    <span class="text-sm text-charcoal-black font-medium text-success-green">
                                        {{ $program->getNextClassDate()->format('l, M d, Y') }}
                                    </span>
                                </div>
                            </div>
                        @endif

                        <div class="pt-4 border-t border-light-gray">
                            <span class="text-sm font-medium text-dark-gray block mb-2">Full Schedule:</span>
                            <p class="text-sm text-charcoal-black bg-off-white p-3 rounded-lg">
                                {{ $program->getScheduleDisplay() }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <!-- Total Payments Card -->
            <div class="bank-card bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-indigo-700 uppercase tracking-wide">Total Payments</p>
                        <p class="text-3xl font-bold text-indigo-900">{{ $paymentStats['total_payments'] ?? 0 }}</p>
                        <p class="text-xs text-indigo-600 mt-1">All Payments</p>
                    </div>
                    <div class="w-16 h-16 bg-indigo-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Active Payments Card -->
            <div class="bank-card bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-emerald-700 uppercase tracking-wide">Active</p>
                        <p class="text-3xl font-bold text-emerald-900">{{ $paymentStats['active_payments'] ?? 0 }}</p>
                        <p class="text-xs text-emerald-600 mt-1">Active Payments</p>
                    </div>
                    <div class="w-16 h-16 bg-emerald-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Expired Payments Card -->
            <div class="bank-card bg-gradient-to-br from-rose-50 to-rose-100 border-rose-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-rose-700 uppercase tracking-wide">Expired</p>
                        <p class="text-3xl font-bold text-rose-900">{{ $paymentStats['expired_payments'] ?? 0 }}</p>
                        <p class="text-xs text-rose-600 mt-1">Expired Payments</p>
                    </div>
                    <div class="w-16 h-16 bg-rose-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Average Payment Card -->
            <div class="bank-card bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-amber-700 uppercase tracking-wide">Average</p>
                        <p class="text-3xl font-bold text-amber-900">
                            {{ number_format($paymentStats['average_payment'] ?? 0, 0) }}</p>
                        <p class="text-xs text-amber-600 mt-1">AED per Payment</p>
                    </div>
                    <div class="w-16 h-16 bg-amber-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enrolled Students Section -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="bank-card-title">Enrolled Students</h3>
                        <p class="bank-card-subtitle">Students enrolled in this program</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <span class="badge-bank badge-info">{{ $students->count() }} Students</span>
                        @can('create', App\Models\Student::class)
                            <a href="{{ route('students.create', ['program_id' => $program->id, 'academy_id' => $program->academy_id, 'branch_id' => $program->academy->branch_id]) }}"
                                class="btn-bank btn-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                    </path>
                                </svg>
                                Add Student
                            </a>
                        @endcan
                    </div>
                </div>
            </div>
            <div class="bank-card-body">
                @if ($students && $students->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-light-gray">
                            <thead class="bg-medium-gray">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Student Info</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Contact</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Status</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Join Date</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Payments</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-light-gray">
                                @foreach ($students as $student)
                                    <tr class="hover:bg-off-white">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                @if ($student->hasProfileImage())
                                                    <img src="{{ $student->profile_image_url }}"
                                                        alt="{{ $student->full_name }}"
                                                        class="w-10 h-10 rounded-full object-cover border-2 border-gray-200">
                                                @else
                                                    <div
                                                        class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                                        {{ $student->initials }}
                                                    </div>
                                                @endif
                                                <div class="ml-4">
                                                    <div class="text-sm font-medium text-charcoal-black">
                                                        <a href="{{ route('students.show', $student) }}"
                                                            class="text-leaders-red hover:text-leaders-deep-red">
                                                            {{ $student->full_name }}
                                                        </a>
                                                    </div>
                                                    <div class="text-sm text-dark-gray">
                                                        ID: #{{ $student->id }}
                                                        @if ($student->age)
                                                            • Age: {{ $student->age }}
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-charcoal-black">{{ $student->formatted_phone }}</div>
                                            @if ($student->email)
                                                <div class="text-sm text-dark-gray">{{ $student->email }}</div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="badge-bank {{ $student->status_badge_class }}">
                                                {{ $student->status_text }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-charcoal-black">
                                            {{ $student->formatted_join_date }}
                                            <div class="text-xs text-dark-gray">
                                                {{ $student->days_since_joined }} days ago
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-charcoal-black">
                                                {{ number_format($student->total_payments, 2) }} AED
                                            </div>
                                            @if ($student->pending_payments > 0)
                                                <div class="text-xs text-error-red">
                                                    {{ number_format($student->pending_payments, 2) }} AED pending
                                                </div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ route('students.show', $student) }}" class="btn-bank btn-sm">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                        </path>
                                                    </svg>
                                                </a>
                                                @can('update', $student)
                                                    <a href="{{ route('students.edit', $student) }}" class="btn-bank btn-sm">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                            </path>
                                                        </svg>
                                                    </a>
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-medium-gray" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                            </path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-charcoal-black">No students enrolled</h3>
                        <p class="mt-1 text-sm text-dark-gray">No students have been enrolled in this program yet.</p>
                        <div class="mt-6">
                            @can('create', App\Models\Student::class)
                                <a href="{{ route('students.create', ['program_id' => $program->id, 'academy_id' => $program->academy_id, 'branch_id' => $program->academy->branch_id]) }}"
                                    class="btn-bank">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                        </path>
                                    </svg>
                                    Enroll First Student
                                </a>
                            @endcan
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Program Payments Section -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="bank-card-title">Program Payments</h3>
                        <p class="bank-card-subtitle">All payments related to this program</p>
                    </div>
                    <div class="flex items-center space-x-3">
                        <a href="{{ route('payments.create', ['program_id' => $program->id, 'academy_id' => $program->academy_id, 'branch_id' => $program->academy->branch->id]) }}"
                            class="btn-bank btn-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Payment
                        </a>
                    </div>
                </div>
            </div>
            <div class="bank-card-body">
                @if ($payments && $payments->count() > 0)
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-light-gray">
                            <thead class="bg-medium-gray">
                                <tr>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Student</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Amount</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Status</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Type</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Payment Date</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Period</th>
                                    <th
                                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-light-gray">
                                @foreach ($payments as $payment)
                                    <tr class="hover:bg-off-white">
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div>
                                                    <div class="text-sm font-medium text-charcoal-black">
                                                        <a href="{{ route('students.show', $payment->student) }}"
                                                            class="text-leaders-red hover:text-leaders-deep-red">
                                                            {{ $payment->student->full_name }}
                                                        </a>
                                                    </div>
                                                    <div class="text-sm text-dark-gray">ID: #{{ $payment->student->id }}
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-charcoal-black">
                                                {{ $payment->formatted_total_amount }}</div>
                                            @if ($payment->hasVat())
                                                <div class="text-xs text-dark-gray">
                                                    Subtotal: {{ $payment->formatted_subtotal }}<br>
                                                    VAT ({{ $payment->vat_rate }}%): {{ $payment->formatted_vat_amount }}
                                                </div>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="badge-bank {{ $payment->status_badge_class }}">
                                                {{ $payment->status_text }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="badge-bank {{ $payment->payment_type_badge_class }}">
                                                {{ $payment->payment_type_text }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-charcoal-black">
                                            {{ $payment->formatted_payment_date }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-charcoal-black">
                                            <div>{{ $payment->formatted_start_date }}</div>
                                            <div class="text-xs text-dark-gray">to {{ $payment->formatted_end_date }}
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <div class="flex items-center space-x-2">
                                                <a href="{{ route('payments.show', $payment) }}" class="btn-bank btn-sm">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                        </path>
                                                    </svg>
                                                </a>
                                                @can('update', $payment)
                                                    <a href="{{ route('payments.edit', $payment) }}" class="btn-bank btn-sm">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                            </path>
                                                        </svg>
                                                    </a>
                                                @endcan
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if ($payments->hasPages())
                        <div class="mt-6">
                            {{ $payments->links() }}
                        </div>
                    @endif
                @else
                    <div class="text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-medium-gray" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-charcoal-black">No payments found</h3>
                        <p class="mt-1 text-sm text-dark-gray">No payments have been made for this program yet.</p>
                        <div class="mt-6">
                            <a href="{{ route('payments.create', ['program_id' => $program->id, 'academy_id' => $program->academy_id, 'branch_id' => $program->academy->branch->id]) }}"
                                class="btn-bank">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add First Payment
                            </a>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Timestamps -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">Record Information</h3>
                    <p class="bank-card-subtitle">Creation and modification timestamps</p>
                </div>
            </div>
            <div class="bank-card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-dark-gray">Created:</span>
                        <span
                            class="text-sm text-charcoal-black">{{ $program->created_at ? $program->created_at->format('M d, Y \a\t g:i A') : 'N/A' }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-dark-gray">Last Updated:</span>
                        <span
                            class="text-sm text-charcoal-black">{{ $program->updated_at ? $program->updated_at->format('M d, Y \a\t g:i A') : 'N/A' }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
