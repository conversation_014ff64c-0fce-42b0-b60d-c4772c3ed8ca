<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;

class SystemHealthCheck extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'system:health-check {--fix : Attempt to fix issues automatically}';

    /**
     * The console command description.
     */
    protected $description = 'Perform comprehensive system health check including database, migrations, and console commands';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🏥 Starting System Health Check...');
        $this->newLine();
        
        $issues = [];
        $fixMode = $this->option('fix');
        
        // Check database connection
        $issues = array_merge($issues, $this->checkDatabaseConnection());
        
        // Check migrations status
        $issues = array_merge($issues, $this->checkMigrations($fixMode));
        
        // Check console commands
        $issues = array_merge($issues, $this->checkConsoleCommands());
        
        // Check Laravel version compatibility
        $issues = array_merge($issues, $this->checkVersionCompatibility());
        
        // Check file permissions
        $issues = array_merge($issues, $this->checkFilePermissions($fixMode));
        
        // Check environment configuration
        $issues = array_merge($issues, $this->checkEnvironmentConfig());
        
        // Run data integrity validation
        $issues = array_merge($issues, $this->runDataIntegrityCheck());
        
        // Report results
        $this->reportResults($issues);
        
        return count(array_filter($issues, fn($issue) => str_contains($issue, '🚨'))) > 0 ? 1 : 0;
    }
    
    private function checkDatabaseConnection(): array
    {
        $issues = [];
        
        try {
            DB::connection()->getPdo();
            $issues[] = "✅ Database connection: OK";
        } catch (\Exception $e) {
            $issues[] = "🚨 Database connection: FAILED - " . $e->getMessage();
        }
        
        return $issues;
    }
    
    private function checkMigrations(bool $fix = false): array
    {
        $issues = [];
        
        try {
            // Check if migrations table exists
            if (!Schema::hasTable('migrations')) {
                $issues[] = "🚨 Migrations table does not exist";
                
                if ($fix) {
                    Artisan::call('migrate:install');
                    $issues[] = "   ✅ Created migrations table";
                }
            } else {
                $issues[] = "✅ Migrations table: OK";
            }
            
            // Check for pending migrations
            $pendingMigrations = Artisan::call('migrate:status', ['--pending' => true]);
            $output = Artisan::output();
            
            if (str_contains($output, 'Pending')) {
                $issues[] = "⚠️  Pending migrations detected";
                
                if ($fix) {
                    Artisan::call('migrate');
                    $issues[] = "   ✅ Ran pending migrations";
                }
            } else {
                $issues[] = "✅ All migrations: UP TO DATE";
            }
            
        } catch (\Exception $e) {
            $issues[] = "🚨 Migration check failed: " . $e->getMessage();
        }
        
        return $issues;
    }
    
    private function checkConsoleCommands(): array
    {
        $issues = [];
        
        // Test critical Artisan commands
        $commands = [
            'route:list' => 'Route listing',
            'config:cache' => 'Config caching',
            'view:cache' => 'View caching',
            'migrate:status' => 'Migration status',
        ];
        
        foreach ($commands as $command => $description) {
            try {
                Artisan::call($command);
                $issues[] = "✅ Command '{$command}': OK";
            } catch (\Exception $e) {
                $issues[] = "🚨 Command '{$command}': FAILED - " . $e->getMessage();
            }
        }
        
        return $issues;
    }
    
    private function checkVersionCompatibility(): array
    {
        $issues = [];
        
        // Check PHP version
        $phpVersion = PHP_VERSION;
        $issues[] = "📋 PHP Version: {$phpVersion}";
        
        if (version_compare($phpVersion, '8.2.0', '<')) {
            $issues[] = "🚨 PHP version too old. Laravel 12 requires PHP 8.2+";
        } else {
            $issues[] = "✅ PHP version: Compatible";
        }
        
        // Check Laravel version
        $laravelVersion = app()->version();
        $issues[] = "📋 Laravel Version: {$laravelVersion}";
        
        // Check Composer dependencies
        if (file_exists(base_path('composer.lock'))) {
            $issues[] = "✅ Composer dependencies: Locked";
        } else {
            $issues[] = "⚠️  Composer lock file missing - run 'composer install'";
        }
        
        return $issues;
    }
    
    private function checkFilePermissions(bool $fix = false): array
    {
        $issues = [];
        
        $directories = [
            'storage',
            'storage/app',
            'storage/framework',
            'storage/logs',
            'bootstrap/cache',
        ];
        
        foreach ($directories as $dir) {
            $path = base_path($dir);
            
            if (!is_dir($path)) {
                $issues[] = "🚨 Directory missing: {$dir}";
                
                if ($fix) {
                    mkdir($path, 0755, true);
                    $issues[] = "   ✅ Created directory: {$dir}";
                }
            } elseif (!is_writable($path)) {
                $issues[] = "🚨 Directory not writable: {$dir}";
                
                if ($fix) {
                    chmod($path, 0755);
                    $issues[] = "   ✅ Fixed permissions: {$dir}";
                }
            } else {
                $issues[] = "✅ Directory permissions OK: {$dir}";
            }
        }
        
        return $issues;
    }
    
    private function checkEnvironmentConfig(): array
    {
        $issues = [];
        
        // Check .env file
        if (!file_exists(base_path('.env'))) {
            $issues[] = "🚨 .env file missing";
        } else {
            $issues[] = "✅ .env file: Present";
        }
        
        // Check critical environment variables
        $requiredEnvVars = [
            'APP_KEY',
            'DB_CONNECTION',
            'DB_HOST',
            'DB_DATABASE',
        ];
        
        foreach ($requiredEnvVars as $var) {
            if (empty(env($var))) {
                $issues[] = "🚨 Missing environment variable: {$var}";
            } else {
                $issues[] = "✅ Environment variable OK: {$var}";
            }
        }
        
        return $issues;
    }
    
    private function runDataIntegrityCheck(): array
    {
        $issues = [];
        
        try {
            // Run our data integrity validator
            Artisan::call('db:validate-integrity');
            $output = Artisan::output();
            
            if (str_contains($output, '🚨')) {
                $issues[] = "⚠️  Data integrity issues detected - run 'php artisan db:validate-integrity' for details";
            } else {
                $issues[] = "✅ Data integrity: OK";
            }
            
        } catch (\Exception $e) {
            $issues[] = "⚠️  Could not run data integrity check: " . $e->getMessage();
        }
        
        return $issues;
    }
    
    private function reportResults(array $issues): void
    {
        $this->newLine();
        $this->info('🏥 SYSTEM HEALTH CHECK RESULTS');
        $this->info('==============================');
        
        foreach ($issues as $issue) {
            if (str_contains($issue, '🚨')) {
                $this->error($issue);
            } elseif (str_contains($issue, '⚠️')) {
                $this->warn($issue);
            } elseif (str_contains($issue, '✅')) {
                $this->info($issue);
            } else {
                $this->line($issue);
            }
        }
        
        $criticalIssues = array_filter($issues, fn($issue) => str_contains($issue, '🚨'));
        $warningIssues = array_filter($issues, fn($issue) => str_contains($issue, '⚠️'));
        $successIssues = array_filter($issues, fn($issue) => str_contains($issue, '✅'));
        
        $this->newLine();
        $this->info('SUMMARY:');
        $this->info('- ' . count($successIssues) . ' checks passed');
        $this->info('- ' . count($warningIssues) . ' warnings');
        $this->info('- ' . count($criticalIssues) . ' critical issues');
        
        if (count($criticalIssues) === 0) {
            $this->info('🎉 System health: GOOD');
        } else {
            $this->error('💥 System health: NEEDS ATTENTION');
        }
        
        $this->newLine();
    }
}
