<?php

namespace App\Http\Controllers;

use App\Models\Academy;
use App\Models\Branch;
use App\Models\Payment;
use App\Models\Program;
use App\Models\Student;
use App\Models\Uniform;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;

class DashboardController extends Controller
{
    /**
     * Display the dashboard with real-time statistics.
     */
    public function index(): View
    {
        $user = Auth::user();

        // Get statistics based on user role
        $stats = $this->getStatistics($user);

        // Get recent activity
        $recentActivity = $this->getRecentActivity($user);

        return view('dashboard', compact('stats', 'recentActivity'));
    }

    /**
     * Get statistics based on user role.
     */
    private function getStatistics($user): array
    {
        $stats = [];

        if ($user->role === 'admin') {
            // Admin sees all data
            $stats = [
                'total_branches' => Branch::count(),
                'active_branches' => Branch::where('status', true)->count(),
                'total_academies' => Academy::count(),
                'active_academies' => Academy::where('status', true)->count(),
                'total_students' => Student::count(),
                'active_students' => Student::where('status', 'active')->count(),
                'total_programs' => Program::count(),
                'active_programs' => Program::where('status', true)->count(),
                'total_revenue' => Payment::whereIn('status', ['completed', 'active'])->sum('amount'),
                'monthly_revenue' => Payment::whereIn('status', ['completed', 'active'])
                    ->whereMonth('payment_date', now()->month)
                    ->whereYear('payment_date', now()->year)
                    ->sum('amount'),
                'pending_payments' => Payment::where('status', 'pending')->sum('amount'),
                'total_uniforms' => Uniform::count(),
                'pending_uniforms' => Uniform::whereIn('status', ['ordered', 'processing'])->count(),
                'new_students_this_month' => Student::whereMonth('join_date', now()->month)
                    ->whereYear('join_date', now()->year)
                    ->count(),
            ];
        } elseif ($user->role === 'branch_manager') {
            // Branch manager sees only their branch data
            $branchId = $user->branch_id;

            $stats = [
                'total_branches' => 1, // Only their branch
                'active_branches' => Branch::where('id', $branchId)->where('status', true)->count(),
                'total_academies' => Academy::where('branch_id', $branchId)->count(),
                'active_academies' => Academy::where('branch_id', $branchId)->where('status', true)->count(),
                'total_students' => Student::where('branch_id', $branchId)->count(),
                'active_students' => Student::where('branch_id', $branchId)->where('status', 'active')->count(),
                'total_programs' => Program::whereHas('academy', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })->count(),
                'active_programs' => Program::whereHas('academy', function ($query) use ($branchId) {
                    $query->where('branch_id', $branchId);
                })->where('status', true)->count(),
                'total_revenue' => Payment::where('branch_id', $branchId)
                    ->whereIn('status', ['completed', 'active'])
                    ->sum('amount'),
                'monthly_revenue' => Payment::where('branch_id', $branchId)
                    ->whereIn('status', ['completed', 'active'])
                    ->whereMonth('payment_date', now()->month)
                    ->whereYear('payment_date', now()->year)
                    ->sum('amount'),
                'pending_payments' => Payment::where('branch_id', $branchId)
                    ->where('status', 'pending')
                    ->sum('amount'),
                'total_uniforms' => Uniform::where('branch_id', $branchId)->count(),
                'pending_uniforms' => Uniform::where('branch_id', $branchId)
                    ->whereIn('status', ['ordered', 'processing'])
                    ->count(),
                'new_students_this_month' => Student::where('branch_id', $branchId)
                    ->whereMonth('join_date', now()->month)
                    ->whereYear('join_date', now()->year)
                    ->count(),
            ];
        } elseif ($user->role === 'academy_manager') {
            // Academy manager sees only their academy data
            $academyId = $user->academy_id;

            $stats = [
                'total_branches' => 1, // Their academy's branch
                'active_branches' => 1,
                'total_academies' => 1, // Only their academy
                'active_academies' => Academy::where('id', $academyId)->where('status', true)->count(),
                'total_students' => Student::where('academy_id', $academyId)->count(),
                'active_students' => Student::where('academy_id', $academyId)->where('status', 'active')->count(),
                'total_programs' => Program::where('academy_id', $academyId)->count(),
                'active_programs' => Program::where('academy_id', $academyId)->where('status', true)->count(),
                'total_revenue' => Payment::where('academy_id', $academyId)
                    ->whereIn('status', ['completed', 'active'])
                    ->sum('amount'),
                'monthly_revenue' => Payment::where('academy_id', $academyId)
                    ->whereIn('status', ['completed', 'active'])
                    ->whereMonth('payment_date', now()->month)
                    ->whereYear('payment_date', now()->year)
                    ->sum('amount'),
                'pending_payments' => Payment::where('academy_id', $academyId)
                    ->where('status', 'pending')
                    ->sum('amount'),
                'total_uniforms' => Uniform::whereHas('student', function ($query) use ($academyId) {
                    $query->where('academy_id', $academyId);
                })->count(),
                'pending_uniforms' => Uniform::whereHas('student', function ($query) use ($academyId) {
                    $query->where('academy_id', $academyId);
                })->whereIn('status', ['ordered', 'processing'])->count(),
                'new_students_this_month' => Student::where('academy_id', $academyId)
                    ->whereMonth('join_date', now()->month)
                    ->whereYear('join_date', now()->year)
                    ->count(),
            ];
        }

        // Calculate percentages and additional metrics
        $stats['branch_active_rate'] = $stats['total_branches'] > 0
            ? round(($stats['active_branches'] / $stats['total_branches']) * 100, 1)
            : 0;

        $stats['academy_active_rate'] = $stats['total_academies'] > 0
            ? round(($stats['active_academies'] / $stats['total_academies']) * 100, 1)
            : 0;

        $stats['student_active_rate'] = $stats['total_students'] > 0
            ? round(($stats['active_students'] / $stats['total_students']) * 100, 1)
            : 0;

        return $stats;
    }

    /**
     * Get recent activity based on user role.
     */
    private function getRecentActivity($user): array
    {
        $activity = [];

        if ($user->role === 'admin') {
            $activity = [
                'recent_students' => Student::with(['branch', 'academy'])
                    ->latest()
                    ->limit(5)
                    ->get(),
                'recent_payments' => Payment::with(['student', 'academy'])
                    ->latest()
                    ->limit(5)
                    ->get(),
                'recent_uniforms' => Uniform::with(['student'])
                    ->latest()
                    ->limit(5)
                    ->get(),
            ];
        } elseif ($user->role === 'branch_manager') {
            $branchId = $user->branch_id;

            $activity = [
                'recent_students' => Student::with(['branch', 'academy'])
                    ->where('branch_id', $branchId)
                    ->latest()
                    ->limit(5)
                    ->get(),
                'recent_payments' => Payment::with(['student', 'academy'])
                    ->where('branch_id', $branchId)
                    ->latest()
                    ->limit(5)
                    ->get(),
                'recent_uniforms' => Uniform::with(['student'])
                    ->where('branch_id', $branchId)
                    ->latest()
                    ->limit(5)
                    ->get(),
            ];
        } elseif ($user->role === 'academy_manager') {
            $academyId = $user->academy_id;

            $activity = [
                'recent_students' => Student::with(['branch', 'academy'])
                    ->where('academy_id', $academyId)
                    ->latest()
                    ->limit(5)
                    ->get(),
                'recent_payments' => Payment::with(['student', 'academy'])
                    ->where('academy_id', $academyId)
                    ->latest()
                    ->limit(5)
                    ->get(),
                'recent_uniforms' => Uniform::with(['student'])
                    ->whereHas('student', function ($query) use ($academyId) {
                        $query->where('academy_id', $academyId);
                    })
                    ->latest()
                    ->limit(5)
                    ->get(),
            ];
        }

        return $activity;
    }

    /**
     * Get real-time statistics for AJAX requests.
     */
    public function getStats(Request $request)
    {
        $user = Auth::user();
        $stats = $this->getStatistics($user);

        return response()->json([
            'success' => true,
            'stats' => $stats,
            'timestamp' => now()->toISOString()
        ]);
    }
}
