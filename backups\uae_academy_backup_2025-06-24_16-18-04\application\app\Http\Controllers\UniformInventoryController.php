<?php

namespace App\Http\Controllers;

use App\Models\UniformInventory;
use App\Models\UniformCategory;
use App\Models\UniformSupplier;
use App\Models\Branch;
use App\Models\Academy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class UniformInventoryController extends Controller
{
    /**
     * Display a listing of inventory items with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', UniformInventory::class);

        $query = UniformInventory::with(['branch', 'academy', 'category', 'supplier'])
            ->withCount(['uniformOrders', 'stockMovements']);

        // Advanced search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->search($search);
        }

        // Branch filter
        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }

        // Academy filter
        if ($request->filled('academy_id')) {
            $query->byAcademy($request->get('academy_id'));
        }

        // Category filter
        if ($request->filled('category_id')) {
            $query->byCategory($request->get('category_id'));
        }

        // Supplier filter
        if ($request->filled('supplier_id')) {
            $query->bySupplier($request->get('supplier_id'));
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->active();
            } elseif ($status === 'low_stock') {
                $query->lowStock();
            } elseif ($status === 'out_of_stock') {
                $query->outOfStock();
            } elseif ($status === 'needs_reorder') {
                $query->needsReorder();
            } else {
                $query->where('status', $status);
            }
        }

        // Size filter
        if ($request->filled('size')) {
            $query->where('size', $request->get('size'));
        }

        // Color filter
        if ($request->filled('color')) {
            $query->where('color', 'like', '%' . $request->get('color') . '%');
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        $allowedSorts = ['name', 'sku', 'current_stock', 'selling_price', 'cost_price', 'last_restocked_at', 'created_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('name', 'asc');
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $inventory = $query->paginate($perPage)->withQueryString();

        // Get filter options
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();
        $categories = UniformCategory::active()->ordered()->get();
        $suppliers = UniformSupplier::active()->orderBy('name')->get();

        // Calculate statistics
        $stats = [
            'total_items' => UniformInventory::count(),
            'active_items' => UniformInventory::active()->count(),
            'low_stock_items' => UniformInventory::lowStock()->count(),
            'out_of_stock_items' => UniformInventory::outOfStock()->count(),
            'total_stock_value' => UniformInventory::active()->get()->sum('stock_value'),
            'needs_reorder_count' => UniformInventory::needsReorder()->count(),
        ];

        if ($request->ajax()) {
            return response()->json([
                'inventory' => $inventory,
                'stats' => $stats
            ]);
        }

        return view('inventory.index', compact(
            'inventory', 
            'branches', 
            'academies', 
            'categories', 
            'suppliers', 
            'stats'
        ));
    }

    /**
     * Show the form for creating a new inventory item.
     */
    public function create(): View
    {
        Gate::authorize('create', UniformInventory::class);

        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();
        $categories = UniformCategory::active()->ordered()->get();
        $suppliers = UniformSupplier::active()->orderBy('name')->get();

        return view('inventory.create', compact('branches', 'academies', 'categories', 'suppliers'));
    }

    /**
     * Store a newly created inventory item.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', UniformInventory::class);

        $validator = validator($request->all(), [
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'uniform_category_id' => 'required|exists:uniform_categories,id',
            'uniform_supplier_id' => 'nullable|exists:uniform_suppliers,id',
            'name' => 'required|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'size' => 'required|string|max:50',
            'color' => 'nullable|string|max:100',
            'color_ar' => 'nullable|string|max:100',
            'material' => 'nullable|string|max:100',
            'brand' => 'nullable|string|max:100',
            'model' => 'nullable|string|max:100',
            'barcode' => 'nullable|string|max:100|unique:uniform_inventory,barcode',
            'current_stock' => 'required|integer|min:0',
            'minimum_stock' => 'required|integer|min:0',
            'maximum_stock' => 'required|integer|min:1',
            'reorder_quantity' => 'required|integer|min:1',
            'cost_price' => 'required|numeric|min:0',
            'selling_price' => 'required|numeric|min:0',
            'location' => 'nullable|string|max:100',
            'shelf' => 'nullable|string|max:50',
            'bin' => 'nullable|string|max:50',
            'is_trackable' => 'boolean',
            'allow_backorder' => 'boolean',
            'supplier_sku' => 'nullable|string|max:100',
            'supplier_price' => 'nullable|numeric|min:0',
            'supplier_lead_time' => 'nullable|integer|min:0',
            'supplier_minimum_order' => 'nullable|integer|min:0',
            'notes' => 'nullable|string',
            'notes_ar' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = $validator->validated();

            // Generate SKU if not provided
            if (empty($data['sku'])) {
                $data['sku'] = $this->generateSku($data);
            }

            // Calculate available stock
            $data['available_stock'] = $data['current_stock'];
            
            // Calculate markup percentage
            if ($data['cost_price'] > 0) {
                $data['markup_percentage'] = (($data['selling_price'] - $data['cost_price']) / $data['cost_price']) * 100;
            }

            // Set currency to AED
            $data['currency'] = 'AED';
            $data['status'] = 'active';

            $inventoryItem = UniformInventory::create($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Inventory item created successfully',
                    'inventory_item' => $inventoryItem->load(['branch', 'academy', 'category', 'supplier'])
                ]);
            }

            return redirect()->route('inventory.index')
                ->with('success', 'Inventory item created successfully');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create inventory item: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to create inventory item: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified inventory item.
     */
    public function show(UniformInventory $inventory): View|JsonResponse
    {
        Gate::authorize('view', $inventory);

        $inventory->load([
            'branch',
            'academy', 
            'category',
            'supplier',
            'uniformOrders.student',
            'stockMovements.user',
            'purchaseOrderItems.purchaseOrder'
        ]);

        // Get recent stock movements
        $recentMovements = $inventory->stockMovements()
            ->with(['user', 'purchaseOrder', 'uniformOrder.student'])
            ->orderBy('movement_date', 'desc')
            ->limit(10)
            ->get();

        // Get statistics
        $stats = [
            'total_orders' => $inventory->uniformOrders()->count(),
            'pending_orders' => $inventory->uniformOrders()->where('fulfillment_status', 'pending')->count(),
            'total_movements' => $inventory->stockMovements()->count(),
            'last_30_days_sales' => $inventory->stockMovements()
                ->where('movement_type', 'sale')
                ->where('movement_date', '>=', now()->subDays(30))
                ->sum('quantity'),
            'average_monthly_sales' => $inventory->stockMovements()
                ->where('movement_type', 'sale')
                ->where('movement_date', '>=', now()->subMonths(6))
                ->avg('quantity') ?? 0,
        ];

        if (request()->ajax()) {
            return response()->json([
                'inventory' => $inventory,
                'recent_movements' => $recentMovements,
                'stats' => $stats
            ]);
        }

        return view('inventory.show', compact('inventory', 'recentMovements', 'stats'));
    }

    /**
     * Generate SKU for inventory item.
     */
    private function generateSku(array $data): string
    {
        $category = UniformCategory::find($data['uniform_category_id']);
        $categoryCode = $category ? $category->code : 'UNI';
        
        $sizeCode = strtoupper(substr($data['size'], 0, 3));
        $colorCode = $data['color'] ? strtoupper(substr($data['color'], 0, 2)) : 'XX';
        
        $counter = 1;
        do {
            $sku = $categoryCode . '-' . $sizeCode . '-' . $colorCode . '-' . str_pad($counter, 3, '0', STR_PAD_LEFT);
            $counter++;
        } while (UniformInventory::where('sku', $sku)->exists());
        
        return $sku;
    }
}
