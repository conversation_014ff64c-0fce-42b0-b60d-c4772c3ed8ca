<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branch Management Report - UAE English Sports Academy</title>
    <style>
        @page {
            margin: 10mm;
            size: A4 landscape;
        }

        body {
            font-family: 'Deja<PERSON>u Sans', Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #2d3748;
            line-height: 1.3;
            font-size: 10px;
        }

        .header {
            text-align: center;
            margin-bottom: 12px;
            padding: 12px 16px;
            border-bottom: 2px solid #E53E3E;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 6px;
        }

        .logo-section h1 {
            color: #E53E3E;
            margin: 0 0 4px 0;
            font-size: 22px;
            font-weight: bold;
        }

        .logo-section .subtitle {
            color: #4a5568;
            font-size: 12px;
            margin: 0 0 6px 0;
            font-weight: 500;
        }

        .report-meta {
            display: flex;
            justify-content: space-between;
            font-size: 8px;
            color: #718096;
            margin-top: 6px;
        }

        .export-summary {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            padding: 10px 12px;
            border-radius: 6px;
            margin-bottom: 12px;
            border-left: 3px solid #E53E3E;
        }

        .export-summary h3 {
            margin: 0 0 6px 0;
            color: #E53E3E;
            font-size: 13px;
            font-weight: bold;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-top: 6px;
        }

        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 3px 0;
            border-bottom: 1px dotted #cbd5e0;
            font-size: 9px;
        }

        .summary-label {
            font-weight: 600;
            color: #4a5568;
        }

        .summary-value {
            color: #E53E3E;
            font-weight: bold;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            margin-bottom: 12px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            padding: 8px 6px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #E53E3E, #fc8181);
        }

        .stat-card h4 {
            margin: 0 0 2px 0;
            color: #E53E3E;
            font-size: 14px;
            font-weight: bold;
        }

        .stat-card p {
            margin: 0;
            color: #4a5568;
            font-size: 8px;
            font-weight: 500;
        }

        .table-container {
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
            margin-bottom: 12px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
        }

        .table th {
            background: linear-gradient(135deg, #E53E3E 0%, #c53030 100%);
            color: white;
            padding: 6px 4px;
            text-align: left;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.3px;
            border: none;
        }

        .table td {
            padding: 5px 4px;
            border-bottom: 1px solid #e2e8f0;
            font-size: 8px;
            vertical-align: top;
            line-height: 1.2;
        }

        .table tr:nth-child(even) {
            background-color: #f8fafc;
        }

        .table .branch-name {
            font-weight: bold;
            color: #2d3748;
        }

        .table .revenue {
            font-weight: bold;
            color: #38a169;
        }

        .badge {
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 7px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .badge-success {
            background: linear-gradient(135deg, #c6f6d5 0%, #9ae6b4 100%);
            color: #22543d;
            border: 1px solid #9ae6b4;
        }

        .badge-danger {
            background: linear-gradient(135deg, #fed7d7 0%, #fc8181 100%);
            color: #742a2a;
            border: 1px solid #fc8181;
        }

        .no-data {
            text-align: center;
            padding: 20px;
            color: #718096;
            background: #f7fafc;
            border-radius: 6px;
            border: 2px dashed #cbd5e0;
        }

        .no-data h3 {
            color: #4a5568;
            margin-bottom: 6px;
            font-size: 12px;
        }

        .footer {
            margin-top: 12px;
            padding: 8px 12px;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            border-radius: 6px;
            border-top: 2px solid #E53E3E;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            text-align: center;
        }

        .footer-section {
            padding: 4px;
        }

        .footer-section h4 {
            color: #E53E3E;
            margin: 0 0 3px 0;
            font-size: 9px;
        }

        .footer-section p {
            color: #4a5568;
            margin: 1px 0;
            font-size: 7px;
            line-height: 1.2;
        }
    </style>
</head>

<body>
    <div class="header">
        <div class="logo-section">
            <h1>🏆 UAE English Sports Academy</h1>
            <p class="subtitle">Branch Management Report</p>
        </div>
        <div class="report-meta">
            <span>Generated: {{ now()->format('F d, Y \a\t H:i') }}</span>
            <span>Report ID: BR-{{ now()->format('YmdHis') }}</span>
            <span>Page 1 of 1</span>
        </div>
    </div>

    <div class="export-summary">
        <h3>📊 Executive Summary</h3>
        <div class="summary-grid">
            <div class="summary-item">
                <span class="summary-label">Total Branches:</span>
                <span class="summary-value">{{ $stats['total_branches'] }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Active Branches:</span>
                <span class="summary-value">{{ $stats['active_branches'] }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Inactive Branches:</span>
                <span class="summary-value">{{ $stats['inactive_branches'] }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Total Revenue:</span>
                <span class="summary-value">AED {{ number_format($stats['total_revenue'], 2) }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Export Date:</span>
                <span class="summary-value">{{ now()->format('F d, Y H:i:s') }}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Report Type:</span>
                <span class="summary-value">Branch Management Export</span>
            </div>
        </div>
    </div>

    <div class="stats-grid">
        <div class="stat-card">
            <h4>{{ $stats['total_branches'] }}</h4>
            <p>Branches</p>
        </div>
        <div class="stat-card">
            <h4>{{ $stats['active_branches'] }}</h4>
            <p>Active</p>
        </div>
        <div class="stat-card">
            <h4>{{ $stats['total_academies'] }}</h4>
            <p>Academies</p>
        </div>
        <div class="stat-card">
            <h4>{{ $stats['total_students'] }}</h4>
            <p>Students</p>
        </div>
        <div class="stat-card">
            <h4>{{ number_format($stats['total_revenue'], 0) }}</h4>
            <p>Revenue (AED)</p>
        </div>
        <div class="stat-card">
            <h4>{{ $stats['total_branches'] > 0 ? round(($stats['active_branches'] / $stats['total_branches']) * 100) : 0 }}%
            </h4>
            <p>Success Rate</p>
        </div>
    </div>

    <div class="table-container">
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 4%;">ID</th>
                    <th style="width: 18%;">Branch Name</th>
                    <th style="width: 12%;">Location</th>
                    <th style="width: 16%;">Address</th>
                    <th style="width: 12%;">Contact</th>
                    <th style="width: 6%;">Acad.</th>
                    <th style="width: 6%;">Stud.</th>
                    <th style="width: 10%;">Revenue</th>
                    <th style="width: 8%;">Status</th>
                    <th style="width: 8%;">Created</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($branches as $branch)
                    <tr>
                        <td><strong>{{ $branch->id }}</strong></td>
                        <td class="branch-name">{{ $branch->name }}</td>
                        <td>{{ $branch->location ?? '-' }}</td>
                        <td>{{ Str::limit($branch->address ?? '-', 22) }}</td>
                        <td>
                            @if ($branch->phone)
                                {{ $branch->phone }}<br>
                            @endif
                            @if ($branch->email)
                                {{ Str::limit($branch->email, 18) }}
                            @endif
                            @if (!$branch->phone && !$branch->email)
                                -
                            @endif
                        </td>
                        <td style="text-align: center;"><strong>{{ $branch->academies_count ?? 0 }}</strong></td>
                        <td style="text-align: center;"><strong>{{ $branch->students_count ?? 0 }}</strong></td>
                        <td class="revenue" style="text-align: right;">
                            <strong>{{ number_format($branch->payments->where('status', 'completed')->sum('amount'), 0) }}</strong>
                        </td>
                        <td style="text-align: center;">
                            <span class="badge {{ $branch->status ? 'badge-success' : 'badge-danger' }}">
                                {{ $branch->status ? 'Active' : 'Inactive' }}
                            </span>
                        </td>
                        <td style="text-align: center;">{{ $branch->created_at->format('M d') }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>

    @if (count($branches) === 0)
        <div class="no-data">
            <h3>📋 No branches found</h3>
            <p>No branches match the current filter criteria.</p>
        </div>
    @endif

    <div class="footer">
        <div class="footer-content">
            <div class="footer-section">
                <h4>Company Info</h4>
                <p>© {{ date('Y') }} UAE English Sports Academy</p>
                <p>Branch Management System</p>
            </div>
            <div class="footer-section">
                <h4>Report Details</h4>
                <p>{{ now()->format('M d, Y H:i') }}</p>
                <p>Records: {{ count($branches) }}</p>
            </div>
            <div class="footer-section">
                <h4>Summary</h4>
                <p>Revenue: AED {{ number_format($stats['total_revenue'], 0) }}</p>
                <p>Active: {{ $stats['active_branches'] }}/{{ $stats['total_branches'] }}
                    ({{ $stats['total_branches'] > 0 ? round(($stats['active_branches'] / $stats['total_branches']) * 100) : 0 }}%)
                </p>
            </div>
        </div>
    </div>
</body>

</html>
