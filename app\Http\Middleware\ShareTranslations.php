<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\Response;

class ShareTranslations
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get current locale
        $locale = app()->getLocale();
        
        // Load translation files
        $commonTranslations = $this->loadTranslations('common', $locale);
        $dashboardTranslations = $this->loadTranslations('dashboard', $locale);
        
        // Determine text direction
        $textDirection = $locale === 'ar' ? 'rtl' : 'ltr';
        
        // Share with all views
        View::share([
            'commonTranslations' => $commonTranslations,
            'dashboardTranslations' => $dashboardTranslations,
            'currentLocale' => $locale,
            'textDirection' => $textDirection,
        ]);

        return $next($request);
    }

    /**
     * Load translations for a specific file and locale
     */
    private function loadTranslations(string $file, string $locale): array
    {
        $path = resource_path("lang/{$locale}/{$file}.php");
        
        if (file_exists($path)) {
            return include $path;
        }
        
        // Fallback to English if locale file doesn't exist
        $fallbackPath = resource_path("lang/en/{$file}.php");
        if (file_exists($fallbackPath)) {
            return include $fallbackPath;
        }
        
        return [];
    }
}
