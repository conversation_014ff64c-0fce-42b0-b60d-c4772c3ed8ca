<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use App\Models\User;

class TranslationSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create([
            'role' => 'admin',
            'language_preference' => 'en'
        ]);
    }

    /** @test */
    public function it_can_switch_language_via_api()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/language/switch', [
            'locale' => 'ar'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'locale' => 'ar',
                    'direction' => 'rtl'
                ]);

        // Check if user preference was updated
        $this->assertEquals('ar', $this->user->fresh()->language_preference);
    }

    /** @test */
    public function it_can_switch_language_via_web_route()
    {
        $response = $this->get('/language/ar');

        $response->assertStatus(302);
        $this->assertEquals('ar', Session::get('locale'));
    }

    /** @test */
    public function it_validates_locale_parameter()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/language/switch', [
            'locale' => 'invalid'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['locale']);
    }

    /** @test */
    public function it_loads_correct_translations_for_english()
    {
        App::setLocale('en');

        $this->assertEquals('Dashboard', __('dashboard.Dashboard'));
        $this->assertEquals('Branch Management', __('dashboard.Branch Management'));
        $this->assertEquals('Academy Management', __('dashboard.Academy Management'));
    }

    /** @test */
    public function it_loads_correct_translations_for_arabic()
    {
        App::setLocale('ar');

        $this->assertEquals('لوحة التحكم', __('dashboard.Dashboard'));
        $this->assertEquals('إدارة الفروع', __('dashboard.Branch Management'));
        $this->assertEquals('إدارة الأكاديميات', __('dashboard.Academy Management'));
    }

    /** @test */
    public function it_handles_missing_translations_gracefully()
    {
        App::setLocale('en');

        $missingKey = 'non.existent.key';
        $translation = __($missingKey);

        // Should return the key itself when translation is missing
        $this->assertEquals($missingKey, $translation);
    }

    /** @test */
    public function it_applies_localization_middleware()
    {
        // Set Arabic in session
        Session::put('locale', 'ar');

        $response = $this->get('/dashboard');

        $this->assertEquals('ar', App::getLocale());
    }

    /** @test */
    public function it_detects_browser_language()
    {
        $response = $this->withHeaders([
            'Accept-Language' => 'ar-AE,ar;q=0.9,en;q=0.8'
        ])->get('/dashboard');

        $this->assertEquals('ar', App::getLocale());
    }

    /** @test */
    public function it_persists_user_language_preference()
    {
        $this->user->update(['language_preference' => 'ar']);
        
        $this->actingAs($this->user);

        $response = $this->get('/dashboard');

        $this->assertEquals('ar', App::getLocale());
    }

    /** @test */
    public function it_formats_currency_correctly_for_english()
    {
        App::setLocale('en');

        $formatted = app('App\Services\LanguageService')->formatCurrency(1000);

        $this->assertStringContains('AED', $formatted);
        $this->assertStringContains('1,000', $formatted);
    }

    /** @test */
    public function it_formats_currency_correctly_for_arabic()
    {
        App::setLocale('ar');

        $formatted = app('App\Services\LanguageService')->formatCurrency(1000);

        $this->assertStringContains('د.إ', $formatted);
        $this->assertStringContains('1,000', $formatted);
    }

    /** @test */
    public function it_formats_dates_correctly_for_english()
    {
        App::setLocale('en');

        $date = '2024-01-15 14:30:00';
        $formatted = app('App\Services\LanguageService')->formatDate($date);

        $this->assertStringContains('Jan', $formatted);
        $this->assertStringContains('15', $formatted);
        $this->assertStringContains('2024', $formatted);
    }

    /** @test */
    public function it_formats_dates_correctly_for_arabic()
    {
        App::setLocale('ar');

        $date = '2024-01-15 14:30:00';
        $formatted = app('App\Services\LanguageService')->formatDate($date);

        // Should contain Arabic month name or Arabic numerals
        $this->assertNotEmpty($formatted);
    }

    /** @test */
    public function it_formats_phone_numbers_correctly()
    {
        $phone = '501234567';
        $formatted = app('App\Services\LanguageService')->formatPhoneNumber($phone);

        $this->assertEquals('+971 50 123 4567', $formatted);
    }

    /** @test */
    public function it_provides_correct_direction_for_locales()
    {
        $this->assertEquals('ltr', app('App\Services\LanguageService')->getDirection('en'));
        $this->assertEquals('rtl', app('App\Services\LanguageService')->getDirection('ar'));
    }

    /** @test */
    public function it_generates_language_switcher_data()
    {
        $data = app('App\Services\LanguageService')->getLanguageSwitcherData();

        $this->assertArrayHasKey('current', $data);
        $this->assertArrayHasKey('available', $data);
        $this->assertArrayHasKey('direction', $data);
    }

    /** @test */
    public function translation_helper_works_with_context()
    {
        App::setLocale('en');

        $translation = app('App\Helpers\TranslationHelper')
            ->translate('academy', [], 'academies');

        $this->assertEquals('Academy', $translation);
    }

    /** @test */
    public function translation_helper_handles_pluralization()
    {
        App::setLocale('en');

        $singular = app('App\Helpers\TranslationHelper')
            ->translateChoice('academies.academy', 1);
        
        $plural = app('App\Helpers\TranslationHelper')
            ->translateChoice('academies.academies', 2);

        $this->assertEquals('Academy', $singular);
        $this->assertEquals('Academies', $plural);
    }

    /** @test */
    public function it_handles_rtl_text_direction()
    {
        App::setLocale('ar');

        $response = $this->get('/dashboard');

        $response->assertSee('dir="rtl"', false);
    }

    /** @test */
    public function it_loads_correct_font_for_arabic()
    {
        App::setLocale('ar');

        $response = $this->get('/dashboard');

        $response->assertSee('IBM Plex Sans Arabic', false);
    }

    /** @test */
    public function it_maintains_session_across_requests()
    {
        Session::put('locale', 'ar');

        $response1 = $this->get('/dashboard');
        $response2 = $this->get('/branches');

        $this->assertEquals('ar', App::getLocale());
    }

    /** @test */
    public function it_handles_ajax_language_switching()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/language/switch', [
            'locale' => 'ar'
        ], [
            'X-Requested-With' => 'XMLHttpRequest'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'locale' => 'ar',
                    'direction' => 'rtl'
                ]);
    }

    /** @test */
    public function it_validates_supported_locales_only()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/language/switch', [
            'locale' => 'fr' // Unsupported locale
        ]);

        $response->assertStatus(422);
    }

    /** @test */
    public function it_falls_back_to_default_locale()
    {
        // Clear all locale settings
        Session::forget('locale');
        
        $response = $this->get('/dashboard');

        $this->assertEquals(config('app.locale'), App::getLocale());
    }

    /** @test */
    public function it_stores_language_preference_in_cookie()
    {
        $response = $this->get('/language/ar');

        $response->assertCookie('language', 'ar');
    }

    /** @test */
    public function it_reads_language_preference_from_cookie()
    {
        $response = $this->withCookies([
            'language' => 'ar'
        ])->get('/dashboard');

        $this->assertEquals('ar', App::getLocale());
    }
}
