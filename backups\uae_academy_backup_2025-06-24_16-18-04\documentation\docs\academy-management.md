# Academy Management Module Documentation

## Overview

The Academy Management module is a comprehensive system for managing sports academies within the UAE English Sports Academy platform. It provides full CRUD operations, advanced search capabilities, bulk operations, export functionality, and real-time statistics with a focus on student management, program tracking, and financial oversight.

## Features

### Core Functionality
- **Complete CRUD Operations**: Create, Read, Update, Delete academies
- **Advanced Search & Filtering**: Multi-criteria search with real-time results
- **Bulk Operations**: Mass activate, deactivate, or delete academies
- **Export Capabilities**: Excel/CSV and PDF export with filtering
- **Real-time Statistics**: Live dashboard with key metrics
- **Role-based Access Control**: Different permissions for different user roles
- **Mobile-responsive Design**: Optimized for all device sizes
- **Bilingual Support**: English/Arabic language support

### Academy Information Management
- **ID**: Unique identifier for each academy
- **Available Programs**: List of programs offered by the academy
- **Student Lists**: Comprehensive student enrollment tracking
- **Birth Dates**: Student age tracking and demographics
- **UAE Phone Format**: Standardized phone number formatting (+971XXXXXXXXX)
- **Registration Dates**: Student enrollment tracking
- **Start/End Dates**: Program duration management
- **Amount in AED**: Financial tracking in UAE Dirham
- **Status**: Active/Inactive academy management

## Technical Architecture

### Backend Components

#### Models
- **Academy.php**: Enhanced model with relationships and computed properties
  - Relationships: branch, students, programs, payments, uniforms, users
  - Computed Properties: student_count, program_count, total_revenue, pending_payments
  - Scopes: active, inactive, search, byBranch
  - Utility Methods: getStatistics(), getStudentsWithDetails(), getAvailablePrograms()

#### Controllers
- **AcademyController.php**: Main controller with full CRUD operations
  - `index()`: Advanced listing with search/filter/pagination
  - `create()`: Academy creation form
  - `store()`: Save new academy with validation
  - `show()`: Detailed academy view with statistics
  - `edit()`: Academy editing form
  - `update()`: Update academy with validation
  - `destroy()`: Smart delete with data protection
  - `toggleStatus()`: AJAX status toggle
  - `bulkAction()`: Bulk operations handler
  - `exportExcel()`: CSV export functionality
  - `exportPdf()`: PDF export functionality
  - `apiIndex()`: API endpoint for AJAX requests
  - `getStatistics()`: Real-time statistics API

#### Policies
- **AcademyPolicy.php**: Role-based authorization
  - Admin: Full access to all operations
  - Branch Manager: Full access to all academies
  - Academy Manager: Limited access to assigned academy only

#### Validation
- **Form Requests**: Comprehensive validation rules
  - Required fields: name, branch_id
  - Unique constraints: name per branch
  - UAE phone number validation for coach
  - Status boolean validation

### Frontend Components

#### Views
- **index.blade.php**: Main listing page with advanced features
- **create.blade.php**: Academy creation form
- **edit.blade.php**: Academy editing form
- **show.blade.php**: Detailed academy view with student lists
- **_stats.blade.php**: Statistics cards component
- **_filters.blade.php**: Advanced search filters
- **_table.blade.php**: Responsive table component
- **_grid.blade.php**: Grid view component
- **export-pdf.blade.php**: PDF export template

#### JavaScript Features
- **Alpine.js Integration**: Reactive components for bulk actions
- **AJAX Operations**: Real-time updates without page refresh
- **Form Validation**: Client-side validation with visual feedback
- **Notification System**: Toast notifications for user feedback
- **Auto-refresh**: Statistics update capability
- **Mobile Touch Support**: Touch-friendly interface elements

#### CSS Enhancements
- **Mobile-first Design**: Responsive breakpoints for all devices
- **RTL Support**: Complete Arabic language layout support
- **Accessibility**: WCAG compliant with keyboard navigation
- **Print Styles**: Optimized printing layouts
- **Bank-style Design**: Premium aesthetic following design guidelines

## Database Schema

### Academies Table
```sql
CREATE TABLE academies (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    branch_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    coach_name VARCHAR(255) NULL,
    coach_phone VARCHAR(15) NULL,
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    INDEX idx_branch_status (branch_id, status),
    INDEX idx_name (name)
);
```

### Relationships
- **Branch**: belongsTo relationship (academy belongs to one branch)
- **Students**: hasMany relationship (academy has many students)
- **Programs**: hasMany relationship (academy has many programs)
- **Payments**: hasMany relationship (academy has many payments)
- **Uniforms**: hasMany relationship (academy has many uniforms)
- **Users**: hasMany relationship (academy has many users)

## Routing

### Web Routes
```php
// Academy Management (Admin, Branch Manager, and Academy Manager)
Route::middleware('role:admin,branch_manager,academy_manager')->group(function () {
    Route::resource('academies', AcademyController::class);
    Route::post('academies/{academy}/toggle-status', [AcademyController::class, 'toggleStatus']);
    Route::post('academies/bulk-action', [AcademyController::class, 'bulkAction']);
    Route::get('academies/export/excel', [AcademyController::class, 'exportExcel']);
    Route::get('academies/export/pdf', [AcademyController::class, 'exportPdf']);
});
```

### API Routes
```php
Route::prefix('api')->name('api.')->group(function () {
    Route::get('academies', [AcademyController::class, 'apiIndex']);
    Route::get('academies/statistics', [AcademyController::class, 'getStatistics']);
});
```

## Security Features

### Authorization
- **Role-based Access**: Different permissions for different user roles
- **Policy Protection**: All operations protected by Laravel policies
- **CSRF Protection**: All forms protected against CSRF attacks
- **Input Validation**: Comprehensive server-side validation

### Data Protection
- **Smart Delete**: Prevents data loss by soft-deleting academies with associations
- **Audit Trail**: Timestamps for creation and modification tracking
- **Status Management**: Safe deactivation instead of deletion when appropriate

## Performance Optimizations

### Database
- **Eager Loading**: Optimized queries with relationship loading
- **Indexing**: Strategic database indexes for fast searches
- **Pagination**: Efficient data loading with pagination
- **Query Optimization**: Optimized database queries

### Frontend
- **Lazy Loading**: Non-critical components loaded on demand
- **Caching**: Browser caching for static assets
- **Minification**: Compressed CSS and JavaScript
- **Image Optimization**: Optimized images for faster loading

## Usage Examples

### Creating an Academy
```php
$academy = Academy::create([
    'branch_id' => 1,
    'name' => 'Swimming Academy',
    'description' => 'Professional swimming training for all ages',
    'coach_name' => 'Ahmed Al-Rashid',
    'coach_phone' => '+971501234567',
    'status' => true
]);
```

### Searching Academies
```php
$academies = Academy::search('swimming')
    ->byBranch(1)
    ->active()
    ->with(['branch', 'students', 'programs'])
    ->paginate(15);
```

### Getting Academy Statistics
```php
$stats = $academy->getStatistics();
// Returns: total_programs, active_programs, total_students, etc.
```

## API Endpoints

### GET /api/academies
Returns paginated list of academies with filtering support.

**Parameters:**
- `search`: Search term
- `branch_id`: Filter by branch
- `status`: Filter by status (active/inactive)
- `per_page`: Items per page

### GET /api/academies/statistics
Returns academy statistics.

**Response:**
```json
{
    "total_academies": 25,
    "active_academies": 22,
    "total_students": 450,
    "total_programs": 35,
    "total_revenue": 125000.00,
    "pending_payments": 15000.00
}
```

## Testing

### Feature Tests
- Academy CRUD operations
- Search and filtering functionality
- Bulk operations
- Export functionality
- Authorization and permissions
- Student management integration

### Unit Tests
- Model relationships
- Computed properties
- Validation rules
- Policy permissions

## Deployment Considerations

### Requirements
- PHP 8.1+
- Laravel 10+
- MySQL 8.0+
- Node.js 16+ (for asset compilation)

### Configuration
- Database migrations: `php artisan migrate`
- Asset compilation: `npm run build`
- Cache optimization: `php artisan optimize`

## Future Enhancements

### Planned Features
1. **Advanced Analytics**: Detailed reporting and analytics
2. **Integration APIs**: Third-party system integrations
3. **Mobile App**: Dedicated mobile application
4. **Real-time Notifications**: Push notifications for important events
5. **Advanced Scheduling**: Program scheduling and calendar integration

### Performance Improvements
1. **Caching Layer**: Redis caching for frequently accessed data
2. **Search Engine**: Elasticsearch integration for advanced search
3. **CDN Integration**: Content delivery network for static assets
4. **Database Optimization**: Query optimization and indexing improvements

---

*© 2024 UAE English Sports Academy - Academy Management Module*
