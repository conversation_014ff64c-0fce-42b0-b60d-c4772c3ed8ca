<!-- Table View -->
<div class="overflow-x-auto">
    <table class="table-bank min-w-full divide-y divide-light-gray">
        <thead class="bg-dark-gray">
            <tr>
                @can('bulkAction', App\Models\Customer::class)
                    <th class="px-6 py-3 text-left">
                        <input type="checkbox" @change="selectAllCustomers()"
                            :checked="selectedCustomers.length > 0 && selectedCustomers.length === document.querySelectorAll(
                                'input[name=\'customer_ids[]\']').length"
                            class="form-checkbox-bank">
                    </th>
                @endcan
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Customer Info
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Contact Details
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Customer Type
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Reservations
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Status & Activity
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-light-gray">
            @forelse($customers as $customer)
                <tr class="hover:bg-off-white transition-colors duration-200">
                    @can('bulkAction', App\Models\Customer::class)
                        <td class="px-6 py-4">
                            <input type="checkbox" name="customer_ids[]" value="{{ $customer->id }}"
                                @change="toggleCustomerSelection({{ $customer->id }})"
                                :checked="selectedCustomers.includes({{ $customer->id }})" class="form-checkbox-bank">
                        </td>
                    @endcan

                    <!-- Customer Info -->
                    <td class="px-6 py-4">
                        <div class="flex items-center">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-semibold text-sm">
                                {{ substr($customer->full_name, 0, 1) }}
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-charcoal-black">
                                    <a href="{{ route('customers.show', $customer) }}"
                                        class="hover:text-leaders-red transition-colors">
                                        {{ $customer->full_name }}
                                    </a>
                                </div>
                                <div class="text-sm text-dark-gray">
                                    ID: {{ $customer->customer_number }}
                                </div>
                                @if ($customer->full_name_ar)
                                    <div class="text-xs text-gray-500">
                                        {{ $customer->full_name_ar }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </td>

                    <!-- Contact Details -->
                    <td class="px-6 py-4">
                        <div class="text-sm">
                            @if ($customer->email)
                                <div class="flex items-center text-dark-gray mb-1">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                    {{ $customer->email }}
                                </div>
                            @endif

                        </div>
                    </td>

                    <!-- Customer Type -->
                    <td class="px-6 py-4">
                        <div class="text-sm">
                            <div class="font-medium mb-1">
                                <span
                                    class="badge-bank {{ $customer->customer_type === 'individual' ? 'badge-primary' : 'badge-warning' }}">
                                    {{ ucfirst($customer->customer_type) }}
                                </span>
                            </div>
                            @if ($customer->customer_type === 'corporate' && $customer->company_name)
                                <div class="text-gray-600 mb-1">
                                    {{ $customer->company_name }}
                                </div>
                            @endif
                            @if ($customer->nationality)
                                <div class="text-xs text-gray-500">
                                    {{ $customer->nationality }}
                                </div>
                            @endif
                            <div class="text-xs text-gray-500">
                                Joined: {{ $customer->registration_date->format('M d, Y') }}
                            </div>
                        </div>
                    </td>

                    <!-- Reservations -->
                    <td class="px-6 py-4">
                        <div class="text-sm">
                            <div class="flex items-center justify-between mb-1">
                                <span class="text-gray-600">Total:</span>
                                <a href="{{ route('reservations.index', ['customer_id' => $customer->id]) }}"
                                    class="font-medium text-blue-600 hover:text-blue-800 underline transition-colors duration-200">
                                    {{ $customer->reservations_count }}
                                </a>
                            </div>
                            @if ($customer->total_spent > 0)
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-gray-600">Spent:</span>
                                    <span class="font-medium text-green-600">
                                        AED {{ number_format($customer->total_spent, 2) }}
                                    </span>
                                </div>
                            @endif
                            @if ($customer->last_booking_date)
                                <div class="text-xs text-gray-500">
                                    Last: {{ $customer->last_booking_date->format('M d, Y') }}
                                </div>
                            @endif
                        </div>
                    </td>

                    <!-- Status & Activity -->
                    <td class="px-6 py-4">
                        <div class="space-y-2">
                            <span
                                class="badge-bank {{ $customer->status === 'active' ? 'badge-success' : ($customer->status === 'blocked' ? 'badge-danger' : 'badge-secondary') }}">
                                {{ ucfirst($customer->status) }}
                            </span>
                            @if ($customer->vip_status)
                                <div class="text-xs">
                                    <span class="badge-bank badge-warning">VIP</span>
                                </div>
                            @endif
                            @if ($customer->preferred_language === 'ar')
                                <div class="text-xs text-gray-500">
                                    Arabic Speaker
                                </div>
                            @endif
                        </div>
                    </td>

                    <!-- Actions -->
                    <td class="px-6 py-4 text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            @can('view', $customer)
                                <a href="{{ route('customers.show', $customer) }}"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white px-3 py-1 text-xs"
                                    title="View Details">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @can('update', $customer)
                                <a href="{{ route('customers.edit', $customer) }}"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white px-3 py-1 text-xs"
                                    title="Edit Customer">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>

                                <button @click="toggleStatus({{ $customer->id }})"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white px-3 py-1 text-xs"
                                    title="Toggle Status">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                    </svg>
                                </button>
                            @endcan

                            @can('delete', $customer)
                                <button @click="deleteCustomer({{ $customer->id }})"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white px-3 py-1 text-xs"
                                    title="Delete Customer">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                            <h3 class="text-lg font-medium text-dark-gray mb-2">No customers found</h3>
                            <p class="text-gray-500 mb-4">No customers match your current filters.</p>
                            @can('create', App\Models\Customer::class)
                                <a href="{{ route('customers.create') }}"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add First Customer
                                </a>
                            @endcan
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@push('scripts')
    <script>
        // Global notification function
        function showNotification(type, message) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#ef4444'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                font-size: 14px;
                font-weight: 500;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                max-width: 300px;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after delay
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        async function toggleStatus(customerId) {
            try {
                const response = await fetch(`/customers/${customerId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                    window.location.reload();
                } else {
                    showNotification('error', result.message);
                }
            } catch (error) {
                console.error('Toggle status error:', error);
                showNotification('error', 'An error occurred while updating status.');
            }
        }

        async function deleteCustomer(customerId) {
            if (!confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/customers/${customerId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                // Check if response is ok first
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showNotification('error', result.message || 'Failed to delete customer');
                }
            } catch (error) {
                console.error('Delete customer error:', error);
                showNotification('error', 'An error occurred while deleting the customer.');
            }
        }
    </script>
@endpush
