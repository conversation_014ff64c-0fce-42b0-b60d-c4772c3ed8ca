<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('uniform_stock_movements', function (Blueprint $table) {
            $table->id();
            $table->foreignId('uniform_inventory_id')->constrained('uniform_inventory')->onDelete('cascade');
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('academy_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade'); // User who made the movement

            // Movement Details
            $table->string('reference_number')->unique(); // MOV-2025-001
            $table->enum('movement_type', [
                'purchase',         // Stock received from supplier
                'sale',            // Stock sold to student
                'adjustment',      // Manual stock adjustment
                'transfer',        // Transfer between locations
                'return',          // Return from student
                'damage',          // Damaged stock write-off
                'loss',            // Lost/stolen stock
                'reservation',     // Reserve stock for order
                'release'          // Release reserved stock
            ]);

            // Quantity Information
            $table->integer('quantity'); // Positive for IN, Negative for OUT
            $table->integer('stock_before'); // Stock level before movement
            $table->integer('stock_after'); // Stock level after movement

            // Cost Information
            $table->decimal('unit_cost', 10, 2)->nullable(); // Cost per unit
            $table->decimal('total_cost', 12, 2)->nullable(); // Total cost of movement
            $table->string('currency', 3)->default('AED');

            // Related Records
            $table->foreignId('uniform_purchase_order_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('uniform_id')->nullable()->constrained()->onDelete('set null'); // Student order
            $table->string('related_type')->nullable(); // Polymorphic relation type
            $table->unsignedBigInteger('related_id')->nullable(); // Polymorphic relation ID

            // Movement Details
            $table->date('movement_date');
            $table->text('reason')->nullable(); // Reason for movement
            $table->text('notes')->nullable();
            $table->text('notes_ar')->nullable();

            // Location Information
            $table->string('from_location')->nullable();
            $table->string('to_location')->nullable();

            // Approval (for adjustments)
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->boolean('requires_approval')->default(false);
            $table->enum('approval_status', ['pending', 'approved', 'rejected'])->default('approved');

            $table->timestamps();

            // Indexes
            $table->index(['uniform_inventory_id', 'movement_date'], 'stock_mov_inv_date_idx');
            $table->index(['movement_type', 'movement_date'], 'stock_mov_type_date_idx');
            $table->index(['branch_id', 'academy_id', 'movement_date'], 'stock_mov_branch_acad_date_idx');
            $table->index(['reference_number', 'movement_date'], 'stock_mov_ref_date_idx');
            $table->index(['related_type', 'related_id'], 'stock_mov_related_idx');
            $table->index(['user_id', 'movement_date'], 'stock_mov_user_date_idx');
            $table->index(['approval_status', 'requires_approval'], 'stock_mov_approval_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('uniform_stock_movements');
    }
};
