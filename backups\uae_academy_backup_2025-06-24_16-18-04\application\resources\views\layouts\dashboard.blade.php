<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}"
    dir="{{ $textDirection ?? (app()->getLocale() === 'ar' ? 'rtl' : 'ltr') }}">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Sports Academy Dashboard')</title>

    <!-- Favicon -->
    <link rel="icon" type="image/jpeg" href="{{ asset('images/logo.jpg') }}">
    <link rel="apple-touch-icon" href="{{ asset('images/logo.jpg') }}">

    <!-- Fonts -->
    <link rel="preload"
        href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap"
        as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload"
        href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap"
        as="style" onload="this.onload=null;this.rel='stylesheet'">

    <!-- Fallback for non-JS users -->
    <noscript>
        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap">
        <link rel="stylesheet"
            href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap">
    </noscript>

    <!-- Heroicons -->
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>
    <script src="https://unpkg.com/heroicons@2.0.18/24/solid/index.js" type="module"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Emergency Table Header Fix -->
    <style>
        /* Emergency fix for table headers - highest priority */
        table thead th,
        table thead th *,
        .table-bank thead th,
        .table-bank thead th *,
        .table thead th,
        .table thead th *,
        .min-w-full thead th,
        .min-w-full thead th *,
        thead th,
        thead th * {
            color: white !important;
            background: transparent !important;
        }

        table thead,
        .table-bank thead,
        .table thead,
        .min-w-full thead,
        thead,
        .bg-off-white {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
        }

        /* Remove conflicting text colors */
        table thead th.text-dark,
        table thead th.text-black,
        table thead th.text-gray-800,
        table thead th.text-gray-900,
        table thead th.text-dark-gray {
            color: white !important;
        }

        /* Emergency fix for button text colors - highest priority */
        button[class*="red"],
        button[class*="Red"],
        button[class*="primary"],
        button[class*="danger"],
        button[class*="leaders"],
        .btn[class*="red"],
        .btn[class*="Red"],
        .btn[class*="primary"],
        .btn[class*="danger"],
        .btn[class*="leaders"],
        .btn-bank,
        .btn-primary,
        .btn-danger,
        .btn-leaders,
        .btn-red,
        button[style*="background-color: red"],
        button[style*="background: red"],
        .btn[style*="background-color: red"],
        .btn[style*="background: red"] {
            color: white !important;
        }

        /* Button child elements */
        button[class*="red"] *,
        button[class*="Red"] *,
        .btn[class*="red"] *,
        .btn[class*="Red"] *,
        .btn-bank *,
        .btn-primary *,
        .btn-danger *,
        .btn-leaders *,
        .btn-red * {
            color: white !important;
            background: transparent !important;
        }

        /* SVG icons in buttons */
        button[class*="red"] svg,
        button[class*="Red"] svg,
        .btn[class*="red"] svg,
        .btn[class*="Red"] svg,
        .btn-bank svg,
        .btn-primary svg,
        .btn-danger svg,
        .btn-leaders svg,
        .btn-red svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }

        /* Specific fix for Quick Actions section - only text color, preserve background */
        .bg-leaders-red,
        .bg-leaders-deep-red,
        a.bg-leaders-red,
        a.bg-leaders-deep-red,
        .btn-bank.bg-leaders-red,
        .btn-bank.bg-leaders-deep-red {
            color: white !important;
            /* DO NOT override background - keep the red background */
        }

        /* Child elements should have white text but transparent background */
        .bg-leaders-red *:not(svg),
        .bg-leaders-deep-red *:not(svg),
        a.bg-leaders-red *:not(svg),
        a.bg-leaders-deep-red *:not(svg),
        .btn-bank.bg-leaders-red *:not(svg),
        .btn-bank.bg-leaders-deep-red *:not(svg) {
            color: white !important;
            background: transparent !important;
        }

        /* Quick Actions SVG icons */
        .bg-leaders-red svg,
        .bg-leaders-deep-red svg,
        a.bg-leaders-red svg,
        a.bg-leaders-deep-red svg,
        .btn-bank.bg-leaders-red svg,
        .btn-bank.bg-leaders-deep-red svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }

        /* Force white text on hover states */
        .bg-leaders-red:hover,
        .bg-leaders-red:hover *,
        .bg-leaders-deep-red:hover,
        .bg-leaders-deep-red:hover *,
        a.bg-leaders-red:hover,
        a.bg-leaders-red:hover *,
        .btn-bank.bg-leaders-red:hover,
        .btn-bank.bg-leaders-red:hover * {
            color: white !important;
        }
    </style>

    <!-- Additional Styles -->
    @stack('styles')
</head>

<body class="font-sans antialiased" x-data="dashboardLayout()" x-init="init()">
    <div class="dashboard-layout">
        <!-- Mobile Overlay -->
        <div class="mobile-overlay" :class="{ 'active': sidebarOpen }" @click="sidebarOpen = false"></div>

        <!-- Header -->
        <header class="header">
            <!-- Mobile Menu Button -->
            <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden btn-bank-outline p-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16">
                    </path>
                </svg>
            </button>

            <!-- Logo Only -->
            <a href="{{ route('dashboard') }}" class="header-brand">
                <img src="{{ asset('images/logo.jpg') }}" alt="Sports Academy Logo" class="header-logo">
            </a>

            <!-- Header Actions -->
            <div class="header-actions">
                <!-- Premium Language Switcher -->
                <x-language-switcher variant="compact" position="header" />

                <!-- User Profile -->
                <div class="user-profile" x-data="{ open: false }" @click.away="open = false">
                    <div class="user-avatar" @click="open = !open">
                        {{ substr(Auth::user()->name, 0, 1) }}
                    </div>
                    <div class="user-info">
                        <div class="user-name">{{ Auth::user()->name }}</div>
                        <div class="user-role">{{ ucfirst(Auth::user()->role ?? 'User') }}</div>
                    </div>
                    <svg class="w-4 h-4 transition-transform" :class="{ 'rotate-180': open }" fill="none"
                        stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>

                    <!-- Dropdown Menu -->
                    <div x-show="open" x-transition
                        class="absolute top-full right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                        <a href="{{ route('profile.edit') }}"
                            class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Profile
                        </a>
                        <hr class="my-1">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit"
                                class="w-full text-left block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1">
                                    </path>
                                </svg>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </header>

        <!-- Sidebar -->
        <aside class="sidebar" :class="{ 'sidebar-collapsed': sidebarCollapsed, 'mobile-open': sidebarOpen }">
            <!-- Sidebar Toggle -->
            <button @click="sidebarCollapsed = !sidebarCollapsed" class="sidebar-toggle hidden lg:flex">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>

            <!-- Sidebar Header -->
            <div class="sidebar-header" x-show="!sidebarCollapsed || window.innerWidth < 1024">
                <h3 class="text-lg font-semibold text-gray-800">Dashboard</h3>
                <p class="text-sm text-gray-600">{{ ucfirst(Auth::user()->role ?? 'User') }} Panel</p>
            </div>

            <!-- Navigation -->
            <nav class="sidebar-nav">
                @include('layouts.partials.sidebar-nav')
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Page Header -->
            @hasSection('header')
                <div class="bank-card mb-6 fade-in-up">
                    @yield('header')
                </div>
            @endif

            <!-- Page Content -->
            <div class="fade-in-up" style="animation-delay: 0.1s;">
                @yield('content')
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="text-center text-sm text-gray-600">
                <p>&copy; {{ date('Y') }} Sports Academy. All rights reserved.</p>
            </div>
        </footer>
    </div>

    <!-- Additional Scripts -->
    @stack('scripts')

    <!-- Translation Data for JavaScript -->
    <script>
        // Make translations available to JavaScript
        window.commonTranslations = @json($commonTranslations ?? []);
        window.dashboardTranslations = @json($dashboardTranslations ?? []);
        window.currentLocale = '{{ app()->getLocale() }}';
        window.textDirection = '{{ $textDirection ?? (app()->getLocale() === 'ar' ? 'rtl' : 'ltr') }}';
    </script>

    <!-- Enhanced Dashboard Layout Script -->
    <script>
        function dashboardLayout() {
            return {
                sidebarOpen: false,
                sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
                currentLang: '{{ strtoupper(app()->getLocale()) }}',
                currentDirection: '{{ $textDirection ?? (app()->getLocale() === 'ar' ? 'rtl' : 'ltr') }}',

                init() {
                    // Handle window resize
                    window.addEventListener('resize', () => {
                        if (window.innerWidth >= 1024) {
                            this.sidebarOpen = false;
                        }
                    });

                    // Listen for language change events
                    window.addEventListener('languageChanged', (event) => {
                        this.currentLang = event.detail.locale.toUpperCase();
                        this.currentDirection = event.detail.direction;
                        this.applyLanguageChanges(event.detail);
                    });

                    // Apply current language state
                    this.applyLanguageState();

                    // Watch for sidebar collapse changes
                    this.$watch('sidebarCollapsed', (value) => {
                        localStorage.setItem('sidebarCollapsed', value);
                    });
                },

                applyLanguageState() {
                    // Ensure document attributes match current state
                    const html = document.documentElement;
                    html.setAttribute('lang', this.currentLang.toLowerCase());
                    html.setAttribute('dir', this.currentDirection);

                    if (this.currentDirection === 'rtl') {
                        document.body.classList.add('arabic-text');
                    } else {
                        document.body.classList.remove('arabic-text');
                    }

                    // Store in localStorage for consistency
                    localStorage.setItem('language', this.currentLang);
                    localStorage.setItem('direction', this.currentDirection);
                },

                applyLanguageChanges(data) {
                    // Apply immediate UI changes without page reload
                    const html = document.documentElement;
                    html.setAttribute('lang', data.locale);
                    html.setAttribute('dir', data.direction);

                    if (data.direction === 'rtl') {
                        document.body.classList.add('arabic-text');
                    } else {
                        document.body.classList.remove('arabic-text');
                    }

                    // Update any dynamic content that needs immediate updates
                    this.updateDynamicContent(data);
                },

                updateDynamicContent(data) {
                    // Update currency displays
                    const currencyElements = document.querySelectorAll('.currency-amount');
                    currencyElements.forEach(element => {
                        if (data.direction === 'rtl') {
                            element.style.direction = 'ltr';
                            element.style.display = 'inline-block';
                        }
                    });

                    // Update number formatting
                    const numberElements = document.querySelectorAll('.number-display');
                    numberElements.forEach(element => {
                        if (data.direction === 'rtl') {
                            element.style.direction = 'ltr';
                            element.style.display = 'inline-block';
                        }
                    });
                },

                toggleSidebar() {
                    this.sidebarOpen = !this.sidebarOpen;
                },

                collapseSidebar() {
                    this.sidebarCollapsed = !this.sidebarCollapsed;
                }
            }
        }

        // Global language utilities
        window.LanguageUtils = {
            getCurrentLocale() {
                return '{{ app()->getLocale() }}';
            },

            getCurrentDirection() {
                return '{{ $textDirection ?? (app()->getLocale() === 'ar' ? 'rtl' : 'ltr') }}';
            },

            isRTL() {
                return this.getCurrentDirection() === 'rtl';
            },

            formatCurrency(amount, showSymbol = true) {
                const locale = this.getCurrentLocale();
                const options = {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                };

                if (showSymbol) {
                    options.style = 'currency';
                    options.currency = 'AED';
                }

                return new Intl.NumberFormat(locale === 'ar' ? 'ar-AE' : 'en-AE', options).format(amount);
            },

            formatNumber(number) {
                const locale = this.getCurrentLocale();
                return new Intl.NumberFormat(locale === 'ar' ? 'ar-AE' : 'en-AE').format(number);
            },

            formatDate(date, options = {}) {
                const locale = this.getCurrentLocale();
                const defaultOptions = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };

                return new Intl.DateTimeFormat(
                    locale === 'ar' ? 'ar-AE' : 'en-AE', {
                        ...defaultOptions,
                        ...options
                    }
                ).format(new Date(date));
            }
        };
    </script>
</body>

</html>
