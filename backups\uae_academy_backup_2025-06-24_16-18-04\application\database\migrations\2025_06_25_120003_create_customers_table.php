<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('customer_number')->unique(); // Auto-generated customer ID
            $table->string('full_name')->charset('utf8mb4');
            $table->string('full_name_ar')->charset('utf8mb4')->nullable();
            $table->string('first_name')->charset('utf8mb4');
            $table->string('first_name_ar')->charset('utf8mb4')->nullable();
            $table->string('last_name')->charset('utf8mb4');
            $table->string('last_name_ar')->charset('utf8mb4')->nullable();
            $table->string('email')->nullable();
            $table->string('phone', 15); // UAE format: +971XXXXXXXXX
            $table->string('phone_secondary', 15)->nullable();
            $table->string('nationality')->charset('utf8mb4')->nullable();
            $table->string('nationality_ar')->charset('utf8mb4')->nullable();
            $table->text('address')->charset('utf8mb4')->nullable();
            $table->text('address_ar')->charset('utf8mb4')->nullable();
            $table->string('city')->charset('utf8mb4')->nullable();
            $table->string('emirate')->charset('utf8mb4')->nullable(); // UAE specific
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('id_type')->nullable(); // Emirates ID, Passport, etc.
            $table->string('id_number')->nullable();
            $table->date('id_expiry_date')->nullable();
            $table->enum('customer_type', ['individual', 'corporate', 'group'])->default('individual');
            $table->string('company_name')->charset('utf8mb4')->nullable(); // For corporate customers
            $table->string('company_name_ar')->charset('utf8mb4')->nullable();
            $table->string('trade_license')->nullable(); // For corporate customers
            $table->string('tax_number')->nullable(); // For corporate customers
            $table->enum('preferred_language', ['en', 'ar'])->default('en');
            $table->enum('preferred_contact_method', ['phone', 'email', 'whatsapp'])->default('phone');
            $table->json('emergency_contact')->nullable(); // Emergency contact details
            $table->decimal('credit_limit', 10, 2)->default(0); // Credit limit for corporate customers
            $table->enum('payment_terms', ['cash', 'credit_7', 'credit_15', 'credit_30'])->default('cash');
            $table->boolean('vip_status')->default(false);
            $table->date('registration_date')->default(now());
            $table->date('last_booking_date')->nullable();
            $table->integer('total_bookings')->default(0);
            $table->decimal('total_spent', 10, 2)->default(0);
            $table->enum('status', ['active', 'inactive', 'blocked'])->default('active');
            $table->text('notes')->charset('utf8mb4')->nullable();
            $table->text('notes_ar')->charset('utf8mb4')->nullable();
            $table->string('profile_image')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'customer_type']);
            $table->index('phone');
            $table->index('email');
            $table->index('customer_number');
            $table->index(['registration_date', 'status']);
            $table->index('full_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
