/* Settings Management UI Enhancements */

/* Enhanced Card Styles */
.bank-card {
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-card);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.bank-card:hover {
    box-shadow: var(--shadow-hover);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--off-white) 0%, var(--light-gray) 100%);
    padding: var(--space-xl);
    border-bottom: 1px solid var(--medium-gray);
}

.card-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--charcoal-black);
    margin: 0;
    font-family: var(--font-family-primary);
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--dark-gray);
    margin: 0.5rem 0 0 0;
    font-weight: 500;
}

.card-body {
    padding: var(--space-xl);
}

.card-footer {
    background: var(--off-white);
    padding: var(--space-xl);
    border-top: 1px solid var(--medium-gray);
}

/* Enhanced Statistics Cards */
.stats-card {
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-card);
    padding: var(--space-xl);
    box-shadow: var(--shadow-card);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--leaders-red), var(--deep-red));
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-bank);
}

.stats-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-card);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-lg);
}

.stats-icon svg {
    width: 1.5rem;
    height: 1.5rem;
}

.stats-content {
    flex: 1;
}

.stats-value {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--charcoal-black);
    line-height: 1;
    margin-bottom: var(--space-sm);
    font-family: var(--font-family-primary);
}

.stats-label {
    font-size: 0.875rem;
    color: var(--dark-gray);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced Form Styles */
.form-group {
    margin-bottom: var(--space-xl);
}

.form-label {
    display: block;
    font-weight: 600;
    font-size: 0.875rem;
    color: var(--charcoal-black);
    margin-bottom: var(--space-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-label.required::after {
    content: ' *';
    color: var(--error-red);
}

/* Enhanced Input Styles */
.form-control,
.input-bank {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-button);
    font-size: 0.875rem;
    color: var(--charcoal-black);
    background-color: var(--pure-white);
    transition: all var(--transition-fast);
    outline: none;
    font-family: var(--font-family-primary);
    font-weight: 500;
}

.form-control:focus,
.input-bank:focus {
    border-color: var(--leaders-red);
    box-shadow: 0 0 0 4px rgba(229, 62, 62, 0.1);
    background-color: #fef9f9;
}

.form-control.is-invalid {
    border-color: var(--error-red);
    background-color: #fef2f2;
}

.form-control.is-invalid:focus {
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

/* Enhanced Select Styles */
select.form-control,
select.input-bank {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    appearance: none;
}

/* Enhanced Textarea Styles */
textarea.form-control,
textarea.input-bank {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

/* Enhanced Checkbox Styles */
.checkbox-wrapper {
    display: flex;
    align-items: flex-start;
    gap: var(--space-sm);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 0.875rem;
    color: var(--charcoal-black);
    cursor: pointer;
    font-weight: 500;
    line-height: 1.5;
}

.checkbox-input {
    width: 1.25rem;
    height: 1.25rem;
    accent-color: var(--leaders-red);
    border: 2px solid var(--medium-gray);
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.checkbox-input:focus {
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.checkbox-text {
    font-weight: 500;
    color: var(--charcoal-black);
}

/* Enhanced File Upload Styles */
.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.file-upload-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: var(--light-gray);
    border: 2px dashed var(--medium-gray);
    border-radius: var(--radius-button);
    font-size: 0.875rem;
    color: var(--dark-gray);
    transition: all var(--transition-fast);
    min-height: 120px;
    flex-direction: column;
    gap: var(--space-sm);
}

.file-upload-button:hover {
    background: var(--off-white);
    border-color: var(--leaders-red);
    color: var(--leaders-red);
}

.file-upload-text {
    font-weight: 600;
    text-align: center;
}

/* Enhanced Multi-select Styles */
.multi-select {
    border: 2px solid var(--medium-gray);
    border-radius: var(--radius-button);
    padding: var(--space-sm);
    max-height: 200px;
    overflow-y: auto;
    background: var(--pure-white);
}

.multi-select-option {
    display: flex;
    align-items: center;
    padding: var(--space-sm);
    border-radius: var(--radius-button);
    transition: background-color var(--transition-fast);
    cursor: pointer;
}

.multi-select-option:hover {
    background-color: var(--light-gray);
}

.multi-select-checkbox {
    margin-right: var(--space-sm);
    accent-color: var(--leaders-red);
}

/* Enhanced Error Styles */
.setting-error,
.form-error {
    color: var(--error-red);
    font-size: 0.75rem;
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-weight: 500;
}

.setting-error::before,
.form-error::before {
    content: '⚠';
    font-size: 0.875rem;
}

/* Enhanced Help Text */
.form-help {
    font-size: 0.75rem;
    color: var(--dark-gray);
    margin-top: var(--space-xs);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.form-help svg {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
}

/* Enhanced Empty State */
.empty-state {
    text-align: center;
    padding: var(--space-xxl);
    color: var(--dark-gray);
}

.empty-icon {
    width: 5rem;
    height: 5rem;
    margin: 0 auto var(--space-lg);
    color: var(--medium-gray);
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--charcoal-black);
    margin-bottom: var(--space-sm);
    font-family: var(--font-family-primary);
}

.empty-description {
    color: var(--dark-gray);
    margin: 0;
    font-size: 1rem;
    line-height: 1.6;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .stats-card {
        padding: var(--space-lg);
    }
    
    .stats-value {
        font-size: 2rem;
    }
    
    .card-header,
    .card-body,
    .card-footer {
        padding: var(--space-lg);
    }
    
    .form-control,
    .input-bank {
        padding: 0.75rem;
        font-size: 1rem; /* Larger for mobile */
    }
    
    .checkbox-input {
        width: 1.125rem;
        height: 1.125rem;
    }
    
    .file-upload-button {
        min-height: 100px;
        padding: 0.75rem;
    }
}

/* Animation Enhancements */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-card,
.bank-card {
    animation: slideInFromTop 0.6s ease-out;
}

.stats-card:nth-child(1) { animation-delay: 0.1s; }
.stats-card:nth-child(2) { animation-delay: 0.2s; }
.stats-card:nth-child(3) { animation-delay: 0.3s; }
.stats-card:nth-child(4) { animation-delay: 0.4s; }
