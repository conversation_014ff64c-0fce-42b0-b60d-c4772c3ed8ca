@extends('layouts.dashboard')

@section('title', 'Edit Customer - ' . $customer->full_name)

@push('styles')
    <style>
        .customer-edit-content {
            color: black !important;
        }

        .customer-edit-content * {
            color: black !important;
        }

        .customer-edit-content .text-dark-gray,
        .customer-edit-content .text-gray-600,
        .customer-edit-content .text-gray-500 {
            color: #6b7280 !important;
        }

        .customer-edit-content .form-label-bank {
            color: #374151 !important;
            font-weight: 600;
        }

        .customer-edit-content .badge-bank {
            color: white !important;
        }

        .customer-edit-content .btn-bank {
            color: white !important;
        }

        .customer-edit-content .btn-bank-outline {
            color: #dc2626 !important;
        }

        .customer-edit-content .btn-bank-outline:hover {
            color: white !important;
        }

        .customer-edit-content .bank-card-title {
            color: #1f2937 !important;
            font-weight: 600;
        }

        .customer-edit-content .bank-card-subtitle {
            color: #6b7280 !important;
        }

        .customer-edit-content .form-input-bank,
        .customer-edit-content .form-select-bank,
        .customer-edit-content .form-textarea-bank {
            color: black !important;
        }

        .customer-edit-content .form-error {
            color: #dc2626 !important;
        }

        .required::after {
            content: " *";
            color: #dc2626;
        }
    </style>
@endpush

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="relative group">
                @if ($customer->profile_image)
                    <img src="{{ $customer->profile_image_url }}" alt="{{ $customer->full_name }}"
                        class="w-16 h-16 rounded-full object-cover border-2 border-white shadow-lg">
                @else
                    <div
                        class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                        {{ substr($customer->full_name, 0, 1) }}
                    </div>
                @endif
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Edit Customer</h1>
                <p class="text-lg text-dark-gray">Update {{ $customer->full_name }}'s information</p>
                <span class="badge-bank {{ $customer->status_badge_class }}">
                    {{ $customer->status_text }}
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('customers.show', $customer) }}"
                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                    </path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                    </path>
                </svg>
                View Details
            </a>
            <a href="{{ route('customers.index') }}" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Customers
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="customer-edit-content">
        <div class="max-w-6xl mx-auto" x-data="customerEditForm()" x-init="init()">
            <form method="POST" action="{{ route('customers.update', $customer) }}" enctype="multipart/form-data"
                @submit="handleSubmit" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Basic Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Basic Information</h3>
                        <p class="bank-card-subtitle">Update the customer's personal details</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Profile Image -->
                            <div class="md:col-span-2">
                                <label for="profile_image" class="form-label-bank">Profile Image</label>
                                @if ($customer->profile_image)
                                    <div class="mb-4">
                                        <div class="flex items-center space-x-4">
                                            <img src="{{ $customer->profile_image_url }}" alt="{{ $customer->full_name }}"
                                                class="w-20 h-20 rounded-full object-cover border-2 border-gray-200">
                                            <div>
                                                <p class="text-sm text-gray-600">Current profile image</p>
                                                <p class="text-xs text-gray-500">Upload a new image to replace it</p>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                                <input type="file" id="profile_image" name="profile_image" accept="image/*"
                                    class="form-input-bank">
                                <p class="text-sm text-gray-500 mt-1">Maximum file size: 2MB. Supported formats: JPEG, PNG,
                                    JPG, GIF</p>
                                @error('profile_image')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Full Name -->
                            <div>
                                <label for="full_name" class="form-label-bank required">Full Name</label>
                                <input type="text" id="full_name" name="full_name"
                                    value="{{ old('full_name', $customer->full_name) }}" class="form-input-bank"
                                    placeholder="Enter full name" required>
                                @error('full_name')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Full Name Arabic -->
                            <div>
                                <label for="full_name_ar" class="form-label-bank">Full Name (Arabic)</label>
                                <input type="text" id="full_name_ar" name="full_name_ar"
                                    value="{{ old('full_name_ar', $customer->full_name_ar) }}" class="form-input-bank"
                                    placeholder="الاسم الكامل" dir="rtl">
                                @error('full_name_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- First Name -->
                            <div>
                                <label for="first_name" class="form-label-bank required">First Name</label>
                                <input type="text" id="first_name" name="first_name"
                                    value="{{ old('first_name', $customer->first_name) }}" class="form-input-bank"
                                    placeholder="Enter first name" required>
                                @error('first_name')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- First Name Arabic -->
                            <div>
                                <label for="first_name_ar" class="form-label-bank">First Name (Arabic)</label>
                                <input type="text" id="first_name_ar" name="first_name_ar"
                                    value="{{ old('first_name_ar', $customer->first_name_ar) }}" class="form-input-bank"
                                    placeholder="الاسم الأول" dir="rtl">
                                @error('first_name_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Last Name -->
                            <div>
                                <label for="last_name" class="form-label-bank required">Last Name</label>
                                <input type="text" id="last_name" name="last_name"
                                    value="{{ old('last_name', $customer->last_name) }}" class="form-input-bank"
                                    placeholder="Enter last name" required>
                                @error('last_name')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Last Name Arabic -->
                            <div>
                                <label for="last_name_ar" class="form-label-bank">Last Name (Arabic)</label>
                                <input type="text" id="last_name_ar" name="last_name_ar"
                                    value="{{ old('last_name_ar', $customer->last_name_ar) }}" class="form-input-bank"
                                    placeholder="اسم العائلة" dir="rtl">
                                @error('last_name_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="form-label-bank required">Email</label>
                                <input type="email" id="email" name="email"
                                    value="{{ old('email', $customer->email) }}" class="form-input-bank"
                                    placeholder="Enter email address" required>
                                @error('email')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Phone -->
                            <div>
                                <label for="phone" class="form-label-bank required">Phone</label>
                                <input type="tel" id="phone" name="phone"
                                    value="{{ old('phone', $customer->phone) }}" class="form-input-bank"
                                    placeholder="+971 50 123 4567" required>
                                @error('phone')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Secondary Phone -->
                            <div>
                                <label for="phone_secondary" class="form-label-bank">Secondary Phone</label>
                                <input type="tel" id="phone_secondary" name="phone_secondary"
                                    value="{{ old('phone_secondary', $customer->phone_secondary) }}"
                                    class="form-input-bank" placeholder="+971 50 123 4567">
                                @error('phone_secondary')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Gender -->
                            <div>
                                <label for="gender" class="form-label-bank required">Gender</label>
                                <select id="gender" name="gender" class="form-select-bank" required>
                                    <option value="">Select Gender</option>
                                    <option value="male"
                                        {{ old('gender', $customer->gender) === 'male' ? 'selected' : '' }}>Male</option>
                                    <option value="female"
                                        {{ old('gender', $customer->gender) === 'female' ? 'selected' : '' }}>Female
                                    </option>
                                </select>
                                @error('gender')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Birth Date -->
                            <div>
                                <label for="birth_date" class="form-label-bank">Birth Date</label>
                                <input type="date" id="birth_date" name="birth_date"
                                    value="{{ old('birth_date', $customer->birth_date?->format('Y-m-d')) }}"
                                    class="form-input-bank">
                                @error('birth_date')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Nationality -->
                            <div>
                                <label for="nationality" class="form-label-bank required">Nationality</label>
                                <input type="text" id="nationality" name="nationality"
                                    value="{{ old('nationality', $customer->nationality) }}" class="form-input-bank"
                                    placeholder="Enter nationality" required>
                                @error('nationality')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Nationality Arabic -->
                            <div>
                                <label for="nationality_ar" class="form-label-bank">Nationality (Arabic)</label>
                                <input type="text" id="nationality_ar" name="nationality_ar"
                                    value="{{ old('nationality_ar', $customer->nationality_ar) }}"
                                    class="form-input-bank" placeholder="الجنسية" dir="rtl">
                                @error('nationality_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Address Information</h3>
                        <p class="bank-card-subtitle">Customer's location details</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Address -->
                            <div class="md:col-span-2">
                                <label for="address" class="form-label-bank">Address</label>
                                <textarea id="address" name="address" rows="3" class="form-textarea-bank" placeholder="Enter full address">{{ old('address', $customer->address) }}</textarea>
                                @error('address')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Address Arabic -->
                            <div class="md:col-span-2">
                                <label for="address_ar" class="form-label-bank">Address (Arabic)</label>
                                <textarea id="address_ar" name="address_ar" rows="3" class="form-textarea-bank" placeholder="العنوان الكامل"
                                    dir="rtl">{{ old('address_ar', $customer->address_ar) }}</textarea>
                                @error('address_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- City -->
                            <div>
                                <label for="city" class="form-label-bank">City</label>
                                <input type="text" id="city" name="city"
                                    value="{{ old('city', $customer->city) }}" class="form-input-bank"
                                    placeholder="Enter city">
                                @error('city')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Emirate -->
                            <div>
                                <label for="emirate" class="form-label-bank">Emirate</label>
                                <select id="emirate" name="emirate" class="form-select-bank">
                                    <option value="">Select Emirate</option>
                                    <option value="Abu Dhabi"
                                        {{ old('emirate', $customer->emirate) === 'Abu Dhabi' ? 'selected' : '' }}>Abu
                                        Dhabi</option>
                                    <option value="Dubai"
                                        {{ old('emirate', $customer->emirate) === 'Dubai' ? 'selected' : '' }}>Dubai
                                    </option>
                                    <option value="Sharjah"
                                        {{ old('emirate', $customer->emirate) === 'Sharjah' ? 'selected' : '' }}>Sharjah
                                    </option>
                                    <option value="Ajman"
                                        {{ old('emirate', $customer->emirate) === 'Ajman' ? 'selected' : '' }}>Ajman
                                    </option>
                                    <option value="Umm Al Quwain"
                                        {{ old('emirate', $customer->emirate) === 'Umm Al Quwain' ? 'selected' : '' }}>Umm
                                        Al Quwain</option>
                                    <option value="Ras Al Khaimah"
                                        {{ old('emirate', $customer->emirate) === 'Ras Al Khaimah' ? 'selected' : '' }}>Ras
                                        Al Khaimah</option>
                                    <option value="Fujairah"
                                        {{ old('emirate', $customer->emirate) === 'Fujairah' ? 'selected' : '' }}>Fujairah
                                    </option>
                                </select>
                                @error('emirate')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Identification Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Identification Information</h3>
                        <p class="bank-card-subtitle">Customer's identification details</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- ID Type -->
                            <div>
                                <label for="id_type" class="form-label-bank required">ID Type</label>
                                <select id="id_type" name="id_type" class="form-select-bank" required>
                                    <option value="">Select ID Type</option>
                                    <option value="emirates_id"
                                        {{ old('id_type', $customer->id_type) === 'emirates_id' ? 'selected' : '' }}>
                                        Emirates ID</option>
                                    <option value="passport"
                                        {{ old('id_type', $customer->id_type) === 'passport' ? 'selected' : '' }}>Passport
                                    </option>
                                    <option value="visa"
                                        {{ old('id_type', $customer->id_type) === 'visa' ? 'selected' : '' }}>Visa</option>
                                </select>
                                @error('id_type')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- ID Number -->
                            <div>
                                <label for="id_number" class="form-label-bank required">ID Number</label>
                                <input type="text" id="id_number" name="id_number"
                                    value="{{ old('id_number', $customer->id_number) }}" class="form-input-bank"
                                    placeholder="Enter ID number" required>
                                @error('id_number')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- ID Expiry Date -->
                            <div>
                                <label for="id_expiry_date" class="form-label-bank">ID Expiry Date</label>
                                <input type="date" id="id_expiry_date" name="id_expiry_date"
                                    value="{{ old('id_expiry_date', $customer->id_expiry_date?->format('Y-m-d')) }}"
                                    class="form-input-bank">
                                @error('id_expiry_date')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Type & Corporate Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Customer Type & Corporate Information</h3>
                        <p class="bank-card-subtitle">Customer classification and business details</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Customer Type -->
                            <div>
                                <label for="customer_type" class="form-label-bank required">Customer Type</label>
                                <select id="customer_type" name="customer_type" class="form-select-bank" required
                                    x-model="form.customer_type">
                                    <option value="">Select Customer Type</option>
                                    <option value="individual"
                                        {{ old('customer_type', $customer->customer_type) === 'individual' ? 'selected' : '' }}>
                                        Individual</option>
                                    <option value="corporate"
                                        {{ old('customer_type', $customer->customer_type) === 'corporate' ? 'selected' : '' }}>
                                        Corporate</option>
                                </select>
                                @error('customer_type')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- VIP Status -->
                            <div>
                                <label class="form-label-bank">VIP Status</label>
                                <div class="flex items-center space-x-4 mt-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="vip_status" value="0"
                                            {{ old('vip_status', $customer->vip_status ? '1' : '0') === '0' ? 'checked' : '' }}
                                            class="form-radio text-leaders-red">
                                        <span class="ml-2">Regular Customer</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="vip_status" value="1"
                                            {{ old('vip_status', $customer->vip_status ? '1' : '0') === '1' ? 'checked' : '' }}
                                            class="form-radio text-leaders-red">
                                        <span class="ml-2">VIP Customer</span>
                                    </label>
                                </div>
                                @error('vip_status')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Corporate fields (shown when customer_type is corporate) -->
                            <div x-show="form.customer_type === 'corporate'" class="md:col-span-2">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Company Name -->
                                    <div>
                                        <label for="company_name" class="form-label-bank">Company Name</label>
                                        <input type="text" id="company_name" name="company_name"
                                            value="{{ old('company_name', $customer->company_name) }}"
                                            class="form-input-bank" placeholder="Enter company name">
                                        @error('company_name')
                                            <div class="form-error">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Company Name Arabic -->
                                    <div>
                                        <label for="company_name_ar" class="form-label-bank">Company Name (Arabic)</label>
                                        <input type="text" id="company_name_ar" name="company_name_ar"
                                            value="{{ old('company_name_ar', $customer->company_name_ar) }}"
                                            class="form-input-bank" placeholder="اسم الشركة" dir="rtl">
                                        @error('company_name_ar')
                                            <div class="form-error">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Trade License -->
                                    <div>
                                        <label for="trade_license" class="form-label-bank">Trade License</label>
                                        <input type="text" id="trade_license" name="trade_license"
                                            value="{{ old('trade_license', $customer->trade_license) }}"
                                            class="form-input-bank" placeholder="Enter trade license number">
                                        @error('trade_license')
                                            <div class="form-error">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Tax Number -->
                                    <div>
                                        <label for="tax_number" class="form-label-bank">Tax Number</label>
                                        <input type="text" id="tax_number" name="tax_number"
                                            value="{{ old('tax_number', $customer->tax_number) }}"
                                            class="form-input-bank" placeholder="Enter tax number">
                                        @error('tax_number')
                                            <div class="form-error">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences & Settings -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Preferences & Settings</h3>
                        <p class="bank-card-subtitle">Customer preferences and account settings</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Preferred Language -->
                            <div>
                                <label for="preferred_language" class="form-label-bank required">Preferred
                                    Language</label>
                                <select id="preferred_language" name="preferred_language" class="form-select-bank"
                                    required>
                                    <option value="">Select Language</option>
                                    <option value="en"
                                        {{ old('preferred_language', $customer->preferred_language) === 'en' ? 'selected' : '' }}>
                                        English</option>
                                    <option value="ar"
                                        {{ old('preferred_language', $customer->preferred_language) === 'ar' ? 'selected' : '' }}>
                                        Arabic</option>
                                </select>
                                @error('preferred_language')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Preferred Contact Method -->
                            <div>
                                <label for="preferred_contact_method" class="form-label-bank required">Preferred Contact
                                    Method</label>
                                <select id="preferred_contact_method" name="preferred_contact_method"
                                    class="form-select-bank" required>
                                    <option value="">Select Contact Method</option>
                                    <option value="email"
                                        {{ old('preferred_contact_method', $customer->preferred_contact_method) === 'email' ? 'selected' : '' }}>
                                        Email</option>
                                    <option value="phone"
                                        {{ old('preferred_contact_method', $customer->preferred_contact_method) === 'phone' ? 'selected' : '' }}>
                                        Phone</option>
                                    <option value="sms"
                                        {{ old('preferred_contact_method', $customer->preferred_contact_method) === 'sms' ? 'selected' : '' }}>
                                        SMS</option>
                                    <option value="whatsapp"
                                        {{ old('preferred_contact_method', $customer->preferred_contact_method) === 'whatsapp' ? 'selected' : '' }}>
                                        WhatsApp</option>
                                </select>
                                @error('preferred_contact_method')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Payment Terms -->
                            <div>
                                <label for="payment_terms" class="form-label-bank">Payment Terms</label>
                                <select id="payment_terms" name="payment_terms" class="form-select-bank">
                                    <option value="">Select Payment Terms</option>
                                    <option value="immediate"
                                        {{ old('payment_terms', $customer->payment_terms) === 'immediate' ? 'selected' : '' }}>
                                        Immediate</option>
                                    <option value="net_7"
                                        {{ old('payment_terms', $customer->payment_terms) === 'net_7' ? 'selected' : '' }}>
                                        Net 7 Days</option>
                                    <option value="net_15"
                                        {{ old('payment_terms', $customer->payment_terms) === 'net_15' ? 'selected' : '' }}>
                                        Net 15 Days</option>
                                    <option value="net_30"
                                        {{ old('payment_terms', $customer->payment_terms) === 'net_30' ? 'selected' : '' }}>
                                        Net 30 Days</option>
                                </select>
                                @error('payment_terms')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Credit Limit -->
                            <div>
                                <label for="credit_limit" class="form-label-bank">Credit Limit (AED)</label>
                                <input type="number" id="credit_limit" name="credit_limit" step="0.01"
                                    min="0" value="{{ old('credit_limit', $customer->credit_limit) }}"
                                    class="form-input-bank" placeholder="0.00">
                                @error('credit_limit')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Status -->
                            <div>
                                <label for="status" class="form-label-bank required">Status</label>
                                <select id="status" name="status" class="form-select-bank" required>
                                    <option value="">Select Status</option>
                                    <option value="active"
                                        {{ old('status', $customer->status) === 'active' ? 'selected' : '' }}>Active
                                    </option>
                                    <option value="inactive"
                                        {{ old('status', $customer->status) === 'inactive' ? 'selected' : '' }}>Inactive
                                    </option>
                                    <option value="blocked"
                                        {{ old('status', $customer->status) === 'blocked' ? 'selected' : '' }}>Blocked
                                    </option>
                                </select>
                                @error('status')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Emergency Contact</h3>
                        <p class="bank-card-subtitle">Emergency contact information</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Emergency Contact Name -->
                            <div>
                                <label for="emergency_contact_name" class="form-label-bank">Contact Name</label>
                                <input type="text" id="emergency_contact_name" name="emergency_contact[name]"
                                    value="{{ old('emergency_contact.name', $customer->emergency_contact['name'] ?? '') }}"
                                    class="form-input-bank" placeholder="Enter contact name">
                                @error('emergency_contact.name')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Emergency Contact Phone -->
                            <div>
                                <label for="emergency_contact_phone" class="form-label-bank">Contact Phone</label>
                                <input type="tel" id="emergency_contact_phone" name="emergency_contact[phone]"
                                    value="{{ old('emergency_contact.phone', $customer->emergency_contact['phone'] ?? '') }}"
                                    class="form-input-bank" placeholder="+971 50 123 4567">
                                @error('emergency_contact.phone')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Emergency Contact Relationship -->
                            <div>
                                <label for="emergency_contact_relationship" class="form-label-bank">Relationship</label>
                                <input type="text" id="emergency_contact_relationship"
                                    name="emergency_contact[relationship]"
                                    value="{{ old('emergency_contact.relationship', $customer->emergency_contact['relationship'] ?? '') }}"
                                    class="form-input-bank" placeholder="e.g., Spouse, Parent, Sibling">
                                @error('emergency_contact.relationship')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Additional Notes</h3>
                        <p class="bank-card-subtitle">Any additional information about the customer</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Notes -->
                            <div>
                                <label for="notes" class="form-label-bank">Notes</label>
                                <textarea id="notes" name="notes" rows="4" class="form-textarea-bank"
                                    placeholder="Enter any additional notes">{{ old('notes', $customer->notes) }}</textarea>
                                @error('notes')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Notes Arabic -->
                            <div>
                                <label for="notes_ar" class="form-label-bank">Notes (Arabic)</label>
                                <textarea id="notes_ar" name="notes_ar" rows="4" class="form-textarea-bank" placeholder="ملاحظات إضافية"
                                    dir="rtl">{{ old('notes_ar', $customer->notes_ar) }}</textarea>
                                @error('notes_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="bank-card">
                    <div class="bank-card-body">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button type="submit"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white"
                                    :disabled="isSubmitting" x-text="isSubmitting ? 'Updating...' : 'Update Customer'">
                                    Update Customer
                                </button>
                                <a href="{{ route('customers.show', $customer) }}"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                                    Cancel
                                </a>
                            </div>
                            <div class="text-sm text-gray-500">
                                <span class="text-red-500">*</span> Required fields
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function customerEditForm() {
            return {
                isSubmitting: false,
                form: {
                    customer_type: '{{ old('customer_type', $customer->customer_type) }}'
                },

                init() {
                    // Initialize phone number formatting
                    this.initPhoneFormatting();
                },

                initPhoneFormatting() {
                    // Format phone numbers
                    const phoneInputs = ['phone', 'phone_secondary', 'emergency_contact_phone'];
                    phoneInputs.forEach(inputId => {
                        const input = document.getElementById(inputId);
                        if (input) {
                            input.addEventListener('input', (e) => {
                                this.formatPhoneNumber(e.target);
                            });
                        }
                    });
                },

                formatPhoneNumber(input) {
                    let value = input.value.replace(/\D/g, '');
                    if (value.startsWith('971')) {
                        value = '+' + value;
                    } else if (value.startsWith('0')) {
                        value = '+971' + value.substring(1);
                    } else if (!value.startsWith('+971') && value.length > 0) {
                        value = '+971' + value;
                    }
                    input.value = value;
                },

                handleSubmit(event) {
                    this.isSubmitting = true;

                    // Basic validation
                    const requiredFields = ['full_name', 'first_name', 'last_name', 'email', 'phone', 'gender',
                        'nationality', 'id_type', 'id_number', 'customer_type', 'preferred_language',
                        'preferred_contact_method', 'status'
                    ];
                    let hasErrors = false;

                    requiredFields.forEach(field => {
                        const input = document.getElementById(field);
                        if (input && !input.value.trim()) {
                            hasErrors = true;
                        }
                    });

                    if (hasErrors) {
                        event.preventDefault();
                        this.isSubmitting = false;
                        alert('Please fill in all required fields.');
                        return;
                    }
                }
            }
        }

        // Initialize form on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-generate full name from first and last name
            const firstNameInput = document.getElementById('first_name');
            const lastNameInput = document.getElementById('last_name');
            const fullNameInput = document.getElementById('full_name');

            function updateFullName() {
                const firstName = firstNameInput.value.trim();
                const lastName = lastNameInput.value.trim();
                if (firstName && lastName) {
                    fullNameInput.value = firstName + ' ' + lastName;
                }
            }

            if (firstNameInput && lastNameInput && fullNameInput) {
                firstNameInput.addEventListener('input', updateFullName);
                lastNameInput.addEventListener('input', updateFullName);
            }

            // Auto-generate Arabic full name from Arabic first and last name
            const firstNameArInput = document.getElementById('first_name_ar');
            const lastNameArInput = document.getElementById('last_name_ar');
            const fullNameArInput = document.getElementById('full_name_ar');

            function updateFullNameAr() {
                const firstNameAr = firstNameArInput.value.trim();
                const lastNameAr = lastNameArInput.value.trim();
                if (firstNameAr && lastNameAr) {
                    fullNameArInput.value = firstNameAr + ' ' + lastNameAr;
                }
            }

            if (firstNameArInput && lastNameArInput && fullNameArInput) {
                firstNameArInput.addEventListener('input', updateFullNameAr);
                lastNameArInput.addEventListener('input', updateFullNameAr);
            }
        });
    </script>
@endpush
