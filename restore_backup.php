<?php

/**
 * UAE English Sports Academy - System Restoration Script
 * Restores the complete system from backup
 * 
 * Usage: php restore_backup.php [backup_path] [target_path]
 */

echo "🔄 UAE English Sports Academy - System Restoration Script\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Get command line arguments
$backupPath = $argv[1] ?? null;
$targetPath = $argv[2] ?? null;

if (!$backupPath || !$targetPath) {
    echo "❌ Usage: php restore_backup.php [backup_path] [target_path]\n";
    echo "   Example: php restore_backup.php ./backups/uae_academy_backup_2025-06-24_16-18-04 /var/www/academy\n";
    exit(1);
}

if (!is_dir($backupPath)) {
    echo "❌ Backup path does not exist: {$backupPath}\n";
    exit(1);
}

echo "📁 Backup Path: {$backupPath}\n";
echo "🎯 Target Path: {$targetPath}\n\n";

// Create target directory if it doesn't exist
if (!is_dir($targetPath)) {
    mkdir($targetPath, 0755, true);
    echo "✅ Created target directory: {$targetPath}\n";
}

echo "🚀 Starting restoration process...\n\n";

// 1. Restore Application Files
echo "1️⃣ Restoring Application Files...\n";
$appSourcePath = $backupPath . '/application';
if (is_dir($appSourcePath)) {
    $copyCmd = "cp -R '{$appSourcePath}'/* '{$targetPath}'/";
    exec($copyCmd, $output, $returnCode);
    if ($returnCode === 0) {
        echo "   ✅ Application files restored\n";
    } else {
        echo "   ❌ Failed to restore application files\n";
        exit(1);
    }
} else {
    echo "   ❌ Application backup not found\n";
    exit(1);
}

// 2. Restore Configuration Files
echo "\n2️⃣ Restoring Configuration Files...\n";
$configSourcePath = $backupPath . '/configuration';
if (is_dir($configSourcePath)) {
    $configFiles = [
        '.env' => 'Environment configuration',
        'composer.json' => 'PHP dependencies',
        'package.json' => 'Node.js dependencies',
        'vite.config.js' => 'Vite configuration',
        'tailwind.config.js' => 'Tailwind configuration'
    ];
    
    foreach ($configFiles as $file => $description) {
        $sourceFile = $configSourcePath . '/' . $file;
        $targetFile = $targetPath . '/' . $file;
        
        if (file_exists($sourceFile)) {
            copy($sourceFile, $targetFile);
            echo "   ✅ Restored {$file} - {$description}\n";
        }
    }
} else {
    echo "   ❌ Configuration backup not found\n";
}

// 3. Install Dependencies
echo "\n3️⃣ Installing Dependencies...\n";
chdir($targetPath);

// Install Composer dependencies
echo "   📦 Installing PHP dependencies...\n";
exec('composer install --no-dev --optimize-autoloader', $output, $returnCode);
if ($returnCode === 0) {
    echo "   ✅ Composer dependencies installed\n";
} else {
    echo "   ❌ Failed to install Composer dependencies\n";
}

// Install Node.js dependencies
echo "   📦 Installing Node.js dependencies...\n";
exec('npm install', $output, $returnCode);
if ($returnCode === 0) {
    echo "   ✅ Node.js dependencies installed\n";
} else {
    echo "   ❌ Failed to install Node.js dependencies\n";
}

// 4. Generate Application Key
echo "\n4️⃣ Generating Application Key...\n";
exec('php artisan key:generate --force', $output, $returnCode);
if ($returnCode === 0) {
    echo "   ✅ Application key generated\n";
} else {
    echo "   ❌ Failed to generate application key\n";
}

// 5. Database Restoration Instructions
echo "\n5️⃣ Database Restoration Instructions...\n";
$dbBackupFile = $backupPath . '/database/database_backup.sql';
if (file_exists($dbBackupFile)) {
    echo "   📋 To restore the database, run these commands:\n";
    echo "   \n";
    echo "   # Create database:\n";
    echo "   mysql -u root -p -e \"CREATE DATABASE uae_english_sports_academy_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;\"\n";
    echo "   \n";
    echo "   # Import database:\n";
    echo "   mysql -u root -p uae_english_sports_academy_db < {$dbBackupFile}\n";
    echo "   \n";
    echo "   ⚠️  Remember to update your .env file with correct database credentials!\n";
} else {
    echo "   ❌ Database backup file not found\n";
}

// 6. Final Setup Steps
echo "\n6️⃣ Final Setup Steps...\n";

// Create storage link
exec('php artisan storage:link', $output, $returnCode);
if ($returnCode === 0) {
    echo "   ✅ Storage link created\n";
} else {
    echo "   ❌ Failed to create storage link\n";
}

// Build assets
echo "   🔨 Building assets...\n";
exec('npm run build', $output, $returnCode);
if ($returnCode === 0) {
    echo "   ✅ Assets built successfully\n";
} else {
    echo "   ❌ Failed to build assets\n";
}

// Clear and cache configurations
echo "   🧹 Optimizing application...\n";
exec('php artisan optimize:clear', $output, $returnCode);
exec('php artisan config:cache', $output, $returnCode);
exec('php artisan route:cache', $output, $returnCode);
exec('php artisan view:cache', $output, $returnCode);
echo "   ✅ Application optimized\n";

// 7. Restore Documentation
echo "\n7️⃣ Restoring Documentation...\n";
$docsSourcePath = $backupPath . '/documentation';
if (is_dir($docsSourcePath)) {
    $copyCmd = "cp -R '{$docsSourcePath}'/* '{$targetPath}'/";
    exec($copyCmd);
    echo "   ✅ Documentation restored\n";
}

echo "\n🎉 Restoration completed successfully!\n\n";

echo "📋 Next Steps:\n";
echo "1. Update .env file with your environment settings\n";
echo "2. Restore the database using the commands above\n";
echo "3. Set proper file permissions (chmod 755 for directories, 644 for files)\n";
echo "4. Configure your web server to point to the public/ directory\n";
echo "5. Test the application functionality\n";

echo "\n🔗 Access your application:\n";
echo "   Local: http://localhost/public\n";
echo "   Valet: Configure with 'valet link academy' in the project directory\n";

echo "\n✨ Restoration completed at " . date('Y-m-d H:i:s') . "\n";

// Create restoration log
$logContent = [
    'restoration_info' => [
        'completed_at' => date('Y-m-d H:i:s'),
        'backup_source' => $backupPath,
        'target_location' => $targetPath,
        'status' => 'completed'
    ],
    'steps_completed' => [
        'application_files' => true,
        'configuration_files' => true,
        'dependencies_installed' => true,
        'application_key_generated' => true,
        'storage_link_created' => true,
        'assets_built' => true,
        'application_optimized' => true,
        'documentation_restored' => true
    ],
    'manual_steps_required' => [
        'update_env_file' => 'Update .env with environment-specific settings',
        'restore_database' => 'Import database using provided SQL file',
        'set_permissions' => 'Set proper file and directory permissions',
        'configure_webserver' => 'Point web server to public/ directory'
    ]
];

file_put_contents($targetPath . '/RESTORATION_LOG.json', json_encode($logContent, JSON_PRETTY_PRINT));
echo "\n📄 Restoration log saved to: RESTORATION_LOG.json\n";
