<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if ($this->command) {
            $this->command->info('Creating system users...');
        }

        // Create system admin user (production)
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => Hash::make('admin123'),
                'role' => 'admin',
                'status' => true,
            ]
        );

        if ($this->command) {
            $this->command->info('System admin user created/verified.');
        }
    }

    /**
     * Create test users for development/testing
     */
    public function createTestUsers(): void
    {
        if ($this->command) {
            $this->command->info('Creating test users...');
        }

        $testUsers = [
            [
                'name' => 'Test Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'status' => true,
            ],
            [
                'name' => 'Test Branch Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'branch_manager',
                'status' => true,
            ],
            [
                'name' => 'Test Academy Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'academy_manager',
                'status' => true,
            ],
        ];

        foreach ($testUsers as $userData) {
            User::firstOrCreate(['email' => $userData['email']], $userData);
        }

        if ($this->command) {
            $this->command->info('Test users created/verified.');
        }
    }

    /**
     * Create branch and academy specific users
     */
    public function createBranchUsers($branches, $academies): void
    {
        if ($this->command) {
            $this->command->info('Creating branch and academy users...');
        }

        // Create branch managers for each branch
        foreach ($branches as $branch) {
            $email = strtolower(str_replace(' ', '.', $branch->name)) . '.<EMAIL>';

            User::firstOrCreate(
                ['email' => $email],
                [
                    'name' => $branch->name . ' Branch Manager',
                    'password' => Hash::make('manager123'),
                    'role' => 'branch_manager',
                    'branch_id' => $branch->id,
                    'status' => true,
                ]
            );
        }

        // Create academy managers for each academy
        foreach ($academies as $academy) {
            $email = strtolower(str_replace(' ', '.', $academy->name)) . '.<EMAIL>';

            User::firstOrCreate(
                ['email' => $email],
                [
                    'name' => $academy->name . ' Academy Manager',
                    'password' => Hash::make('academy123'),
                    'role' => 'academy_manager',
                    'branch_id' => $academy->branch_id,
                    'academy_id' => $academy->id,
                    'status' => true,
                ]
            );
        }

        if ($this->command) {
            $this->command->info('Branch and academy users created/verified.');
        }
    }
}
