<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Student;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Program;

class StudentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if ($this->command) {
            $this->command->info('Creating realistic students for all programs...');
        }

        // Get existing data
        $branches = Branch::all();
        $academies = Academy::all();
        $programs = Program::where('status', true)->get(); // Only active programs

        if ($branches->isEmpty() || $academies->isEmpty() || $programs->isEmpty()) {
            throw new \Exception('Required data not found. Please run previous seeders first.');
        }

        // Realistic student data for UAE/Middle East region
        $maleFirstNames = [
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
            '<PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>d',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>l',
            '<PERSON>i',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>'
        ];

        $female<PERSON><PERSON>t<PERSON><PERSON>s = [
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>ra',
            '<PERSON><PERSON>',
            '<PERSON>',
            '<PERSON>ur',
            '<PERSON>',
            'Hala',
            'Reem',
            'Dina',
            'Lina',
            'Maya',
            'Rana',
            'Salma',
            'Yasmin',
            'Zeinab',
            'Noura',
            'Huda',
            'Mariam',
            'Rania',
            'Shaimaa',
            'Dalal',
            'Ghada',
            'Iman',
            'Jihan',
            'Karima',
            'Latifa',
            'Mona',
            'Nadine',
            'Ola',
            'Rasha',
            'Samira',
            'Wafa',
            'Yara',
            'Zara',
            'Asma',
            'Bushra',
            'Farah'
        ];

        $lastNames = [
            'Al Mansouri',
            'Al Zahra',
            'Hassan',
            'Mohammed',
            'Al Rashid',
            'Al Maktoum',
            'Al Nahyan',
            'Al Qasimi',
            'Al Sharqi',
            'Al Nuaimi',
            'Al Dhaheri',
            'Al Kaabi',
            'Al Shamsi',
            'Al Mazrouei',
            'Al Marzouqi',
            'Al Suwaidi',
            'Al Blooshi',
            'Al Mansoori',
            'Al Ketbi',
            'Al Ahbabi',
            'Al Shehhi',
            'Al Marri',
            'Al Falasi',
            'Al Hosani',
            'Al Ameri',
            'Al Bastaki',
            'Al Ghurair',
            'Al Habtoor',
            'Al Jaber',
            'Al Otaiba',
            'Al Tayer',
            'Al Futtaim',
            'Al Rostamani',
            'Al Serkal',
            'Al Gurg',
            'Al Majid',
            'Khan',
            'Malik',
            'Sheikh',
            'Ahmad',
            'Ibrahim',
            'Yusuf',
            'Ismail',
            'Abdel Rahman',
            'Farouk',
            'Nader'
        ];

        $nationalities = [
            'UAE',
            'UAE',
            'UAE',
            'UAE',
            'UAE', // Higher probability for UAE nationals
            'Egypt',
            'Jordan',
            'Lebanon',
            'Syria',
            'Palestine',
            'Pakistan',
            'India',
            'Bangladesh',
            'Philippines',
            'Sri Lanka',
            'Sudan',
            'Morocco',
            'Tunisia',
            'Algeria',
            'Yemen'
        ];

        $areas = [
            'Dubai Marina',
            'Downtown Dubai',
            'Jumeirah',
            'Al Barsha',
            'Deira',
            'Bur Dubai',
            'Al Qusais',
            'International City',
            'Discovery Gardens',
            'JLT',
            'Business Bay',
            'Al Karama',
            'Satwa',
            'Ajman City Center',
            'Al Nuaimiya',
            'Al Rashidiya',
            'Al Hamidiya',
            'Al Jurf',
            'Al Tallah',
            'Sharjah City Center',
            'Al Majaz',
            'Al Nahda',
            'Muwaileh',
            'Al Qasimia',
            'Al Taawun',
            'Abu Dhabi Marina',
            'Al Reem Island',
            'Khalifa City',
            'Al Raha',
            'Yas Island',
            'Saadiyat Island'
        ];

        // Generate students for each program to ensure good UX
        $allStudents = [];
        $phoneCounter = 501000000;
        $emailCounter = 1;

        foreach ($programs as $program) {
            // Calculate number of students per program (60-90% of max capacity)
            $maxStudents = $program->max_students ?? 15;
            $minStudents = max(3, (int)($maxStudents * 0.6)); // At least 3 students, or 60% of capacity
            $maxStudentsToCreate = (int)($maxStudents * 0.9); // Up to 90% of capacity
            $studentsToCreate = rand($minStudents, $maxStudentsToCreate);

            for ($i = 0; $i < $studentsToCreate; $i++) {
                $gender = rand(0, 1) ? 'male' : 'female';
                $firstName = $gender === 'male'
                    ? $maleFirstNames[array_rand($maleFirstNames)]
                    : $femaleFirstNames[array_rand($femaleFirstNames)];
                $lastName = $lastNames[array_rand($lastNames)];
                $nationality = $nationalities[array_rand($nationalities)];
                $area = $areas[array_rand($areas)];

                // Generate realistic birth dates (ages 5-17 for youth sports)
                $birthYear = rand(2007, 2019);
                $birthMonth = rand(1, 12);
                $birthDay = rand(1, 28);
                $birthDate = sprintf('%04d-%02d-%02d', $birthYear, $birthMonth, $birthDay);

                // Generate unique phone and email
                $phone = '+971' . $phoneCounter++;
                $email = strtolower(str_replace(' ', '.', $firstName . '.' . str_replace(' ', '', $lastName))) . $emailCounter++ . '@example.com';

                // Generate Arabic names for better localization
                $firstNameAr = $this->generateArabicName($firstName, 'first');
                $lastNameAr = $this->generateArabicName($lastName, 'last');
                $fullNameAr = $firstNameAr . ' ' . $lastNameAr;
                $nationalityAr = $this->getNationalityInArabic($nationality);
                $addressAr = $this->getAreaInArabic($area);

                $allStudents[] = [
                    'branch_id' => $program->academy->branch_id,
                    'academy_id' => $program->academy_id,
                    'program_id' => $program->id,
                    'first_name' => $firstName,
                    'first_name_ar' => $firstNameAr,
                    'last_name' => $lastName,
                    'last_name_ar' => $lastNameAr,
                    'full_name' => $firstName . ' ' . $lastName,
                    'full_name_ar' => $fullNameAr,
                    'phone' => $phone,
                    'email' => $email,
                    'nationality' => $nationality,
                    'nationality_ar' => $nationalityAr,
                    'birth_date' => $birthDate,
                    'address' => $area . ', UAE',
                    'address_ar' => $addressAr . '، الإمارات العربية المتحدة',
                    'join_date' => now()->subDays(rand(1, 180))->format('Y-m-d'),
                    'status' => rand(0, 10) > 0 ? 'active' : 'inactive', // 90% active, 10% inactive
                    'notes' => $this->generateRandomNote(),
                    'notes_ar' => $this->generateRandomNoteArabic(),
                ];
            }
        }

        // Batch insert students for better performance
        $chunks = array_chunk($allStudents, 50);
        foreach ($chunks as $chunk) {
            Student::insert($chunk);
        }

        if ($this->command) {
            $this->command->info('Created ' . count($allStudents) . ' realistic students across ' . $programs->count() . ' programs.');
            $this->command->info('Each program now has 60-90% capacity filled for optimal UX.');
        }
    }

    /**
     * Generate random notes for students
     */
    private function generateRandomNote(): ?string
    {
        $notes = [
            null,
            null,
            null, // 60% chance of no notes
            'Excellent student with great potential',
            'Needs extra attention in basic skills',
            'Very enthusiastic and motivated',
            'Parent requested additional practice sessions',
            'Shows natural talent in the sport',
            'Requires encouragement to build confidence',
            'Advanced student, ready for next level',
            'Good team player and leader',
            'Needs to work on attendance',
            'Exceptional performance in competitions',
        ];

        return $notes[array_rand($notes)];
    }

    /**
     * Generate random Arabic notes for students
     */
    private function generateRandomNoteArabic(): ?string
    {
        $notes = [
            null,
            null,
            null, // 60% chance of no notes
            'طالب ممتاز ولديه إمكانيات كبيرة',
            'يحتاج إلى اهتمام إضافي في المهارات الأساسية',
            'متحمس جداً ومتحفز',
            'طلب الوالدان جلسات تدريب إضافية',
            'يظهر موهبة طبيعية في الرياضة',
            'يحتاج إلى تشجيع لبناء الثقة',
            'طالب متقدم، جاهز للمستوى التالي',
            'لاعب فريق جيد وقائد',
            'يحتاج إلى العمل على الحضور',
            'أداء استثنائي في المسابقات',
        ];

        return $notes[array_rand($notes)];
    }

    /**
     * Generate Arabic name based on English name
     */
    private function generateArabicName(string $englishName, string $type): string
    {
        $arabicNames = [
            // First names
            'first' => [
                'Ahmed' => 'أحمد',
                'Mohammed' => 'محمد',
                'Omar' => 'عمر',
                'Khalid' => 'خالد',
                'Ali' => 'علي',
                'Hassan' => 'حسن',
                'Youssef' => 'يوسف',
                'Saeed' => 'سعيد',
                'Rashid' => 'راشد',
                'Hamad' => 'حمد',
                'Abdullah' => 'عبدالله',
                'Majid' => 'ماجد',
                'Faisal' => 'فيصل',
                'Sultan' => 'سلطان',
                'Nasser' => 'ناصر',
                'Tariq' => 'طارق',
                'Zayed' => 'زايد',
                'Mansour' => 'منصور',
                'Fahad' => 'فهد',
                'Saud' => 'سعود',
                'Fatima' => 'فاطمة',
                'Aisha' => 'عائشة',
                'Maryam' => 'مريم',
                'Khadija' => 'خديجة',
                'Zahra' => 'زهراء',
                'Amina' => 'أمينة',
                'Layla' => 'ليلى',
                'Nour' => 'نور',
                'Sara' => 'سارة',
                'Hala' => 'هالة',
                'Reem' => 'ريم',
                'Dina' => 'دينا',
                'Lina' => 'لينا',
                'Maya' => 'مايا',
                'Rana' => 'رنا',
                'Salma' => 'سلمى',
                'Yasmin' => 'ياسمين',
                'Zeinab' => 'زينب',
                'Noura' => 'نورا',
                'Huda' => 'هدى',
            ],
            // Last names
            'last' => [
                'Al Mansouri' => 'المنصوري',
                'Al Zahra' => 'الزهراء',
                'Hassan' => 'حسن',
                'Mohammed' => 'محمد',
                'Al Rashid' => 'الراشد',
                'Al Maktoum' => 'آل مكتوم',
                'Al Nahyan' => 'آل نهيان',
                'Al Qasimi' => 'القاسمي',
                'Al Sharqi' => 'الشرقي',
                'Al Nuaimi' => 'النعيمي',
                'Al Dhaheri' => 'الظاهري',
                'Al Kaabi' => 'الكعبي',
                'Al Shamsi' => 'الشامسي',
                'Al Mazrouei' => 'المزروعي',
                'Al Marzouqi' => 'المرزوقي',
                'Al Suwaidi' => 'السويدي',
                'Al Blooshi' => 'البلوشي',
                'Al Mansoori' => 'المنصوري',
                'Al Ketbi' => 'الكتبي',
                'Al Ahbabi' => 'الأحبابي',
                'Khan' => 'خان',
                'Malik' => 'مالك',
                'Sheikh' => 'شيخ',
                'Ahmad' => 'أحمد',
                'Ibrahim' => 'إبراهيم',
            ]
        ];

        return $arabicNames[$type][$englishName] ?? $englishName;
    }

    /**
     * Get nationality in Arabic
     */
    private function getNationalityInArabic(string $nationality): string
    {
        $nationalities = [
            'UAE' => 'إماراتي',
            'Egypt' => 'مصري',
            'Jordan' => 'أردني',
            'Lebanon' => 'لبناني',
            'Syria' => 'سوري',
            'Palestine' => 'فلسطيني',
            'Pakistan' => 'باكستاني',
            'India' => 'هندي',
            'Bangladesh' => 'بنغلاديشي',
            'Philippines' => 'فلبيني',
            'Sri Lanka' => 'سريلانكي',
            'Sudan' => 'سوداني',
            'Morocco' => 'مغربي',
            'Tunisia' => 'تونسي',
            'Algeria' => 'جزائري',
            'Yemen' => 'يمني',
        ];

        return $nationalities[$nationality] ?? $nationality;
    }

    /**
     * Get area name in Arabic
     */
    private function getAreaInArabic(string $area): string
    {
        $areas = [
            'Dubai Marina' => 'مارينا دبي',
            'Downtown Dubai' => 'وسط مدينة دبي',
            'Jumeirah' => 'جميرا',
            'Al Barsha' => 'البرشاء',
            'Deira' => 'ديرة',
            'Bur Dubai' => 'بر دبي',
            'Al Qusais' => 'القصيص',
            'International City' => 'المدينة العالمية',
            'Discovery Gardens' => 'حدائق الاكتشاف',
            'JLT' => 'أبراج بحيرة جميرا',
            'Business Bay' => 'الخليج التجاري',
            'Al Karama' => 'الكرامة',
            'Satwa' => 'السطوة',
            'Ajman City Center' => 'مركز مدينة عجمان',
            'Al Nuaimiya' => 'النعيمية',
            'Al Rashidiya' => 'الراشدية',
            'Al Hamidiya' => 'الحميدية',
            'Al Jurf' => 'الجرف',
            'Al Tallah' => 'التلة',
            'Sharjah City Center' => 'مركز مدينة الشارقة',
            'Al Majaz' => 'الماجز',
            'Al Nahda' => 'النهدة',
            'Muwaileh' => 'مويلح',
            'Al Qasimia' => 'القاسمية',
            'Al Taawun' => 'التعاون',
            'Abu Dhabi Marina' => 'مارينا أبوظبي',
            'Al Reem Island' => 'جزيرة الريم',
            'Khalifa City' => 'مدينة خليفة',
            'Al Raha' => 'الراحة',
            'Yas Island' => 'جزيرة ياس',
            'Saadiyat Island' => 'جزيرة السعديات',
        ];

        return $areas[$area] ?? $area;
    }
}
