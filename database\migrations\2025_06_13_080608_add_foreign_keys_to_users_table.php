<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add the foreign key columns first
            $table->unsignedBigInteger('branch_id')->nullable()->after('role');
            $table->unsignedBigInteger('academy_id')->nullable()->after('branch_id');

            // Add indexes
            $table->index(['branch_id', 'academy_id']);

            // Add foreign key constraints
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('set null');
            $table->foreign('academy_id')->references('id')->on('academies')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Drop foreign key constraints first
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['academy_id']);

            // Drop indexes
            $table->dropIndex(['branch_id', 'academy_id']);

            // Drop the columns
            $table->dropColumn(['branch_id', 'academy_id']);
        });
    }
};
