@extends('layouts.dashboard')

@section('title', $field->localized_name . ' - Field Details')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-success-green to-green-600 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $field->localized_name }}</h1>
                <p class="text-lg text-dark-gray">{{ $field->venue->localized_name }} - {{ ucfirst($field->type) }} Field</p>
                <div class="flex items-center space-x-3 mt-2">
                    <span class="badge-bank {{ $field->status_badge_class }}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="{{ $field->status ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                            </path>
                        </svg>
                        {{ $field->status_text }}
                    </span>
                    <span class="badge-bank badge-info">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                            </path>
                        </svg>
                        {{ $field->code }}
                    </span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('update', $field)
                <a href="{{ route('fields.edit', $field) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Field
                </a>
            @endcan
            <a href="{{ route('fields.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Fields
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="fieldDetails()" x-init="init()">
        <!-- Field Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <!-- Total Reservations -->
            <div class="stats-card scale-in" style="animation-delay: 0.1s;">
                <div class="stats-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $field->reservations_count ?? 0 }}</div>
                <div class="stats-label">Total Reservations</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    {{ $todayReservations->count() ?? 0 }} Today
                </div>
            </div>

            <!-- Hourly Rate -->
            <div class="stats-card scale-in" style="animation-delay: 0.2s;">
                <div class="stats-icon bg-gradient-to-br from-gold-yellow to-yellow-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $field->formatted_hourly_rate }}</div>
                <div class="stats-label">Hourly Rate</div>
                <div class="stats-change {{ $field->peak_hour_rate ? 'positive' : 'neutral' }}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="{{ $field->peak_hour_rate ? 'M13 7h8m0 0v8m0-8l-8 8-4-4-6 6' : 'M5 12h14' }}">
                        </path>
                    </svg>
                    {{ $field->peak_hour_rate ? number_format($field->peak_hour_rate, 0) . ' AED Peak' : 'No Peak Rate' }}
                </div>
            </div>

            <!-- Capacity -->
            <div class="stats-card scale-in" style="animation-delay: 0.3s;">
                <div class="stats-icon bg-gradient-to-br from-info-blue to-blue-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $field->capacity ?? 'N/A' }}</div>
                <div class="stats-label">Capacity</div>
                <div class="stats-change neutral">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                        </path>
                    </svg>
                    Players
                </div>
            </div>

            <!-- Surface Type -->
            <div class="stats-card scale-in" style="animation-delay: 0.4s;">
                <div class="stats-icon bg-gradient-to-br from-purple-500 to-purple-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                        </path>
                    </svg>
                </div>
                <div class="stats-value text-sm">{{ ucfirst(str_replace('_', ' ', $field->surface_type ?? 'Standard')) }}</div>
                <div class="stats-label">Surface Type</div>
                <div class="stats-change neutral">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z">
                        </path>
                    </svg>
                    {{ ucfirst($field->type) }}
                </div>
            </div>
        </div>

        <!-- Field Information & Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Field Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Field Information</h3>
                        <p class="bank-card-subtitle">Details and specifications</p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-success-green to-green-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                    </path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black">Venue</h4>
                                <p class="text-dark-gray">{{ $field->venue->localized_name }}</p>
                                <p class="text-sm text-medium-gray">{{ $field->venue->city }}, {{ $field->venue->country }}</p>
                            </div>
                        </div>

                        @if ($field->dimensions)
                            <div class="flex items-start space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-info-blue to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">Dimensions</h4>
                                    <p class="text-dark-gray">{{ $field->dimensions }}</p>
                                </div>
                            </div>
                        @endif

                        <div class="flex items-start space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                    </path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black">Pricing</h4>
                                <p class="text-dark-gray">Regular: {{ $field->formatted_hourly_rate }}</p>
                                @if ($field->peak_hour_rate)
                                    <p class="text-sm text-medium-gray">Peak: {{ number_format($field->peak_hour_rate, 0) }} AED/hour</p>
                                @endif
                                @if ($field->weekend_rate)
                                    <p class="text-sm text-medium-gray">Weekend: {{ number_format($field->weekend_rate, 0) }} AED/hour</p>
                                @endif
                            </div>
                        </div>

                        <div class="flex items-start space-x-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black">Booking Limits</h4>
                                <p class="text-dark-gray">Min: {{ $field->minimum_booking_hours ?? 1 }} hours</p>
                                <p class="text-sm text-medium-gray">Max: {{ $field->maximum_booking_hours ?? 8 }} hours</p>
                                <p class="text-sm text-medium-gray">Advance: {{ $field->advance_booking_days ?? 30 }} days</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Quick Actions</h3>
                        <p class="bank-card-subtitle">Manage field operations</p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 gap-3">
                        @can('update', $field)
                            <a href="{{ route('fields.edit', $field) }}" class="btn-bank w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                                Edit Field Details
                            </a>
                        @endcan

                        <button @click="toggleFieldStatus()" class="btn-bank btn-bank-secondary w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="{{ $field->status ? 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                                </path>
                            </svg>
                            {{ $field->status ? 'Deactivate' : 'Activate' }} Field
                        </button>

                        @can('create', App\Models\Reservation::class)
                            <a href="{{ route('reservations.create', ['venue_id' => $field->venue_id, 'field_id' => $field->id]) }}" class="btn-bank btn-bank-outline w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                                New Booking
                            </a>
                        @endcan

                        <a href="{{ route('reservations.index', ['field_id' => $field->id]) }}" class="btn-bank btn-bank-outline w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                                </path>
                            </svg>
                            View Reservations
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function fieldDetails() {
            return {
                init() {
                    // Initialize any field details functionality
                },

                async toggleFieldStatus() {
                    const currentStatus = {{ $field->status ? 'true' : 'false' }};
                    const newStatus = currentStatus ? 'deactivate' : 'activate';
                    const fieldName = '{{ $field->localized_name }}';

                    if (!confirm(`Are you sure you want to ${newStatus} "${fieldName}"?`)) {
                        return;
                    }

                    try {
                        const response = await fetch('{{ route('fields.toggle-status', $field) }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while updating the field status.');
                    }
                }
            }
        }

        function showNotification(type, message) {
            if (type === 'success') {
                alert('Success: ' + message);
            } else {
                alert('Error: ' + message);
            }
        }
    </script>
@endpush
