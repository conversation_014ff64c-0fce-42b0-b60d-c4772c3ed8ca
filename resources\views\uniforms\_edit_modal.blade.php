<!-- Edit Uniform Modals (Dynamic) -->
@foreach($uniforms as $uniform)
<x-modal name="edit-uniform-{{ $uniform->id }}" maxWidth="4xl">
    <div class="p-6">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-charcoal-black">Edit Uniform Order #{{ str_pad($uniform->id, 4, '0', STR_PAD_LEFT) }}</h2>
            <button @click="$dispatch('close-modal', 'edit-uniform-{{ $uniform->id }}')" class="text-medium-gray hover:text-charcoal-black">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="edit-uniform-form-{{ $uniform->id }}" method="POST" action="{{ route('uniforms.update', $uniform) }}" class="space-y-6">
            @csrf
            @method('PUT')
            
            <!-- Student Selection -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="form-label-bank">Student <span class="text-error-red">*</span></label>
                    <select name="student_id" required class="form-select-bank">
                        <option value="">Select Student</option>
                        @foreach($students as $student)
                            <option value="{{ $student->id }}" {{ $uniform->student_id == $student->id ? 'selected' : '' }}>
                                {{ $student->full_name }} - {{ $student->email }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Branch <span class="text-error-red">*</span></label>
                    <select name="branch_id" required class="form-select-bank">
                        <option value="">Select Branch</option>
                        @foreach($branches as $branch)
                            <option value="{{ $branch->id }}" {{ $uniform->branch_id == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Academy <span class="text-error-red">*</span></label>
                    <select name="academy_id" required class="form-select-bank">
                        <option value="">Select Academy</option>
                        @foreach($academies as $academy)
                            <option value="{{ $academy->id }}" {{ $uniform->academy_id == $academy->id ? 'selected' : '' }}>
                                {{ $academy->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Item Details -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                    <label class="form-label-bank">Item Type <span class="text-error-red">*</span></label>
                    <select name="item" required class="form-select-bank">
                        <option value="">Select Item</option>
                        <option value="jersey" {{ $uniform->item == 'jersey' ? 'selected' : '' }}>Jersey</option>
                        <option value="shorts" {{ $uniform->item == 'shorts' ? 'selected' : '' }}>Shorts</option>
                        <option value="socks" {{ $uniform->item == 'socks' ? 'selected' : '' }}>Socks</option>
                        <option value="tracksuit" {{ $uniform->item == 'tracksuit' ? 'selected' : '' }}>Tracksuit</option>
                        <option value="jacket" {{ $uniform->item == 'jacket' ? 'selected' : '' }}>Jacket</option>
                        <option value="cap" {{ $uniform->item == 'cap' ? 'selected' : '' }}>Cap</option>
                        <option value="bag" {{ $uniform->item == 'bag' ? 'selected' : '' }}>Bag</option>
                        <option value="complete_set" {{ $uniform->item == 'complete_set' ? 'selected' : '' }}>Complete Set</option>
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Size <span class="text-error-red">*</span></label>
                    <select name="size" required class="form-select-bank">
                        <option value="">Select Size</option>
                        <optgroup label="Kids Sizes">
                            <option value="6xs-24-4" {{ $uniform->size == '6xs-24-4' ? 'selected' : '' }}>6XS (24-4)</option>
                            <option value="5xs-26-5" {{ $uniform->size == '5xs-26-5' ? 'selected' : '' }}>5XS (26-5)</option>
                            <option value="4xs-28-6" {{ $uniform->size == '4xs-28-6' ? 'selected' : '' }}>4XS (28-6)</option>
                            <option value="3xs-30-7" {{ $uniform->size == '3xs-30-7' ? 'selected' : '' }}>3XS (30-7)</option>
                            <option value="2xs-32-8" {{ $uniform->size == '2xs-32-8' ? 'selected' : '' }}>2XS (32-8)</option>
                        </optgroup>
                        <optgroup label="Standard Sizes">
                            <option value="xs-34-9" {{ $uniform->size == 'xs-34-9' ? 'selected' : '' }}>XS (34-9)</option>
                            <option value="s-36-10" {{ $uniform->size == 's-36-10' ? 'selected' : '' }}>S (36-10)</option>
                            <option value="m-38-11" {{ $uniform->size == 'm-38-11' ? 'selected' : '' }}>M (38-11)</option>
                            <option value="l-40-12" {{ $uniform->size == 'l-40-12' ? 'selected' : '' }}>L (40-12)</option>
                            <option value="xl-42-13" {{ $uniform->size == 'xl-42-13' ? 'selected' : '' }}>XL (42-13)</option>
                            <option value="xxl-44-14" {{ $uniform->size == 'xxl-44-14' ? 'selected' : '' }}>XXL (44-14)</option>
                        </optgroup>
                        <optgroup label="Large Sizes">
                            <option value="3xl-46-15" {{ $uniform->size == '3xl-46-15' ? 'selected' : '' }}>3XL (46-15)</option>
                            <option value="4xl-48-16" {{ $uniform->size == '4xl-48-16' ? 'selected' : '' }}>4XL (48-16)</option>
                        </optgroup>
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Quantity <span class="text-error-red">*</span></label>
                    <input type="number" name="quantity" min="1" max="50" value="{{ $uniform->quantity }}" required class="form-input-bank quantity-input" data-uniform-id="{{ $uniform->id }}">
                </div>

                <div>
                    <label class="form-label-bank">Unit Price (AED) <span class="text-error-red">*</span></label>
                    <input type="number" name="amount" step="0.01" min="0" max="999999.99" value="{{ $uniform->amount }}" required class="form-input-bank amount-input" data-uniform-id="{{ $uniform->id }}">
                </div>
            </div>

            <!-- Total Amount Display -->
            <div class="bg-light-gray p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <span class="text-lg font-medium text-charcoal-black">Total Amount:</span>
                    <span class="text-2xl font-bold text-success-green total-amount" data-uniform-id="{{ $uniform->id }}">{{ $uniform->formatted_total_amount }}</span>
                </div>
            </div>

            <!-- Payment & Status -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="form-label-bank">Payment Method <span class="text-error-red">*</span></label>
                    <select name="payment_method" required class="form-select-bank">
                        <option value="">Select Method</option>
                        <option value="cash" {{ $uniform->payment_method == 'cash' ? 'selected' : '' }}>Cash</option>
                        <option value="card" {{ $uniform->payment_method == 'card' ? 'selected' : '' }}>Credit/Debit Card</option>
                        <option value="bank_transfer" {{ $uniform->payment_method == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Status <span class="text-error-red">*</span></label>
                    <select name="status" required class="form-select-bank">
                        <option value="ordered" {{ $uniform->status == 'ordered' ? 'selected' : '' }}>Ordered</option>
                        <option value="processing" {{ $uniform->status == 'processing' ? 'selected' : '' }}>Processing</option>
                        <option value="ready" {{ $uniform->status == 'ready' ? 'selected' : '' }}>Ready for Pickup</option>
                        <option value="delivered" {{ $uniform->status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                        <option value="cancelled" {{ $uniform->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Order Date <span class="text-error-red">*</span></label>
                    <input type="date" name="order_date" value="{{ $uniform->order_date->format('Y-m-d') }}" required class="form-input-bank">
                </div>
            </div>

            <!-- Tracking Status -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="form-label-bank">Branch Status <span class="text-error-red">*</span></label>
                    <select name="branch_status" required class="form-select-bank">
                        <option value="pending" {{ $uniform->branch_status == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="received" {{ $uniform->branch_status == 'received' ? 'selected' : '' }}>Received</option>
                        <option value="delivered" {{ $uniform->branch_status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Office Status <span class="text-error-red">*</span></label>
                    <select name="office_status" required class="form-select-bank">
                        <option value="pending" {{ $uniform->office_status == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="received" {{ $uniform->office_status == 'received' ? 'selected' : '' }}>Received</option>
                        <option value="delivered" {{ $uniform->office_status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                    </select>
                </div>

                <div>
                    <label class="form-label-bank">Delivery Date</label>
                    <input type="date" name="delivery_date" value="{{ $uniform->delivery_date ? $uniform->delivery_date->format('Y-m-d') : '' }}" class="form-input-bank">
                </div>
            </div>

            <!-- Additional Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="form-label-bank">Reference Number</label>
                    <input type="text" name="reference_number" maxlength="50" value="{{ $uniform->reference_number }}" class="form-input-bank">
                </div>

                <div>
                    <label class="form-label-bank">Description</label>
                    <input type="text" name="description" maxlength="1000" value="{{ $uniform->description }}" class="form-input-bank">
                </div>
            </div>

            <!-- Notes -->
            <div>
                <label class="form-label-bank">Notes</label>
                <textarea name="note" rows="3" maxlength="1000" class="form-textarea-bank">{{ $uniform->note }}</textarea>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-light-gray">
                <button type="button" @click="$dispatch('close-modal', 'edit-uniform-{{ $uniform->id }}')" class="btn-bank-outline">
                    Cancel
                </button>
                <button type="submit" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Update Order
                </button>
            </div>
        </form>
    </div>
</x-modal>
@endforeach

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate total amount for edit forms
    function updateEditTotalAmount(uniformId) {
        const quantityInput = document.querySelector(`.quantity-input[data-uniform-id="${uniformId}"]`);
        const amountInput = document.querySelector(`.amount-input[data-uniform-id="${uniformId}"]`);
        const totalAmountDisplay = document.querySelector(`.total-amount[data-uniform-id="${uniformId}"]`);

        if (quantityInput && amountInput && totalAmountDisplay) {
            const quantity = parseInt(quantityInput.value) || 0;
            const amount = parseFloat(amountInput.value) || 0;
            const total = quantity * amount;
            totalAmountDisplay.textContent = 'AED ' + total.toFixed(2);
        }
    }

    // Add event listeners for all edit forms
    document.querySelectorAll('.quantity-input, .amount-input').forEach(input => {
        input.addEventListener('input', function() {
            const uniformId = this.dataset.uniformId;
            updateEditTotalAmount(uniformId);
        });
    });

    // Handle edit form submissions
    document.querySelectorAll('[id^="edit-uniform-form-"]').forEach(form => {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            try {
                const response = await fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    window.location.reload();
                } else {
                    alert('Error: ' + result.message);
                }
            } catch (error) {
                alert('An error occurred while updating the uniform order');
            }
        });
    });
});
</script>
