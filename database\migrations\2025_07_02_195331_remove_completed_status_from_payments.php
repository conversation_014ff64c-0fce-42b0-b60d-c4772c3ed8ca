<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, update any remaining 'completed' payments to 'active'
        DB::table('payments')->where('status', 'completed')->update(['status' => 'active']);

        // Then update the enum to remove 'completed' status
        Schema::table('payments', function (Blueprint $table) {
            $table->enum('status', ['active', 'expired', 'pending', 'failed', 'refunded', 'cancelled'])->default('active')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Restore the enum with 'completed' status
            $table->enum('status', ['completed', 'pending', 'failed', 'refunded', 'cancelled', 'active', 'expired'])->default('pending')->change();
        });
    }
};
