<?php

namespace App\Http\Controllers;

use App\Models\Setting;
use App\Models\Translation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class SettingController extends Controller
{
    /**
     * Display the settings dashboard with all categories.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Setting::class);

        // Get all settings grouped by category
        $settingsGrouped = Setting::getAllGrouped();

        // Get categories for navigation
        $categories = Setting::getCategories();

        // Get current category (default to 'general')
        $currentCategory = $request->get('category', 'general');

        // Get settings for current category
        $currentSettings = Setting::getByCategory($currentCategory);

        // Get statistics
        $stats = [
            'total_settings' => Setting::count(),
            'categories_count' => count($categories),
            'public_settings' => Setting::where('is_public', true)->count(),
            'encrypted_settings' => Setting::where('is_encrypted', true)->count(),
        ];

        if ($request->ajax()) {
            return response()->json([
                'settings' => $currentSettings,
                'stats' => $stats,
                'categories' => $categories,
                'current_category' => $currentCategory,
            ]);
        }

        return view('settings.index', compact(
            'settingsGrouped',
            'categories',
            'currentCategory',
            'currentSettings',
            'stats'
        ));
    }

    /**
     * Show the form for editing settings by category.
     */
    public function edit(Request $request, string $category = 'general'): View|JsonResponse
    {
        Gate::authorize('viewAny', Setting::class);

        // Validate category
        $categories = Setting::getCategories();
        if (!array_key_exists($category, $categories)) {
            abort(404, 'Category not found');
        }

        // Handle translation category specially
        if ($category === 'translation') {
            return $this->editTranslations($request);
        }

        // Get settings for the category
        $settings = Setting::getByCategory($category);

        // Get all categories for navigation
        $allCategories = $categories;

        if ($request->ajax()) {
            return response()->json([
                'settings' => $settings,
                'category' => $category,
                'category_name' => $categories[$category],
                'categories' => $allCategories,
            ]);
        }

        return view('settings.edit', compact(
            'settings',
            'category',
            'allCategories'
        ));
    }

    /**
     * Update settings for a specific category.
     */
    public function update(Request $request, string $category = 'general'): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', Setting::class);

        // Validate category
        $categories = Setting::getCategories();
        if (!array_key_exists($category, $categories)) {
            abort(404, 'Category not found');
        }

        try {
            DB::beginTransaction();

            $settings = Setting::getByCategory($category);
            $errors = [];

            foreach ($settings as $setting) {
                $value = $request->input($setting->key);

                // Skip if no value provided and not required
                if ($value === null && !in_array('required', $setting->validation_rules ?? [])) {
                    continue;
                }

                // Handle file uploads
                if ($setting->type === 'file' && $request->hasFile($setting->key)) {
                    $file = $request->file($setting->key);
                    $path = $file->store('settings', 'public');
                    $value = $path;
                }

                // Validate the value
                if (!$setting->validateValue($value)) {
                    $validator = Validator::make(
                        [$setting->key => $value],
                        [$setting->key => $setting->validation_rules]
                    );
                    $errors[$setting->key] = $validator->errors()->first($setting->key);
                    continue;
                }

                // Update the setting
                $setting->setCastedValue($value);
                $setting->save();
            }

            if (!empty($errors)) {
                DB::rollBack();

                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Validation failed',
                        'errors' => $errors,
                    ], 422);
                }

                return redirect()->back()
                    ->withErrors($errors)
                    ->withInput();
            }

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => ucfirst($category) . ' settings updated successfully.',
                ]);
            }

            return redirect()->route('settings.edit', $category)
                ->with('success', ucfirst($category) . ' settings updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update settings. Please try again.',
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to update settings. Please try again.')
                ->withInput();
        }
    }

    /**
     * Store a new setting.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Setting::class);

        $validated = $request->validate([
            'key' => ['required', 'string', 'unique:settings,key', 'max:255'],
            'value' => ['nullable'],
            'type' => ['required', 'string', 'in:string,textarea,integer,decimal,boolean,select,json,file'],
            'category' => ['required', 'string', 'in:' . implode(',', array_keys(Setting::getCategories()))],
            'label' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'validation_rules' => ['nullable', 'array'],
            'options' => ['nullable', 'array'],
            'is_public' => ['boolean'],
            'is_encrypted' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        try {
            $setting = Setting::create($validated);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Setting created successfully.',
                    'setting' => $setting,
                ]);
            }

            return redirect()->route('settings.edit', $validated['category'])
                ->with('success', 'Setting created successfully.');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create setting. Please try again.',
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to create setting. Please try again.')
                ->withInput();
        }
    }

    /**
     * Remove the specified setting.
     */
    public function destroy(Setting $setting): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $setting);

        try {
            $category = $setting->category;
            $setting->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Setting deleted successfully.',
                ]);
            }

            return redirect()->route('settings.edit', $category)
                ->with('success', 'Setting deleted successfully.');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete setting. Please try again.',
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to delete setting. Please try again.');
        }
    }

    /**
     * Export settings as JSON.
     */
    public function export(Request $request): JsonResponse
    {
        Gate::authorize('export', Setting::class);

        $category = $request->get('category');

        if ($category) {
            $settings = Setting::getByCategory($category);
        } else {
            $settings = Setting::all();
        }

        $exportData = [
            'exported_at' => now()->toISOString(),
            'category' => $category,
            'settings' => $settings->toArray(),
        ];

        return response()->json($exportData)
            ->header('Content-Disposition', 'attachment; filename="settings-export-' . now()->format('Y-m-d-H-i-s') . '.json"');
    }

    /**
     * Import settings from JSON.
     */
    public function import(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('import', Setting::class);

        $request->validate([
            'import_file' => ['required', 'file', 'mimes:json', 'max:2048'],
            'overwrite' => ['boolean'],
        ]);

        try {
            $file = $request->file('import_file');
            $content = file_get_contents($file->getRealPath());
            $data = json_decode($content, true);

            if (!$data || !isset($data['settings'])) {
                throw new \Exception('Invalid import file format');
            }

            DB::beginTransaction();

            $imported = 0;
            $skipped = 0;

            foreach ($data['settings'] as $settingData) {
                $exists = Setting::where('key', $settingData['key'])->exists();

                if ($exists && !$request->boolean('overwrite')) {
                    $skipped++;
                    continue;
                }

                Setting::updateOrCreate(
                    ['key' => $settingData['key']],
                    $settingData
                );
                $imported++;
            }

            DB::commit();

            $message = "Import completed. {$imported} settings imported, {$skipped} skipped.";

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'imported' => $imported,
                    'skipped' => $skipped,
                ]);
            }

            return redirect()->route('settings.index')
                ->with('success', $message);
        } catch (\Exception $e) {
            DB::rollBack();

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Import failed: ' . $e->getMessage(),
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Import failed: ' . $e->getMessage());
        }
    }

    /**
     * Show translation management interface.
     */
    private function editTranslations(Request $request): View|JsonResponse
    {
        $group = $request->get('group', 'common');
        $search = $request->get('search');

        // Get available groups
        $groups = Translation::getGroups();
        if (empty($groups)) {
            $groups = ['common', 'dashboard', 'branches', 'academies'];
        }

        // Get translations for the group
        if ($search) {
            $translations = Translation::search($search, $group);
        } else {
            $translations = Translation::getByGroup($group);
        }

        // Get statistics
        $stats = Translation::getStatistics();

        // Get all categories for navigation
        $allCategories = Setting::getCategories();

        if ($request->ajax()) {
            return response()->json([
                'translations' => $translations,
                'groups' => $groups,
                'current_group' => $group,
                'stats' => $stats,
                'search' => $search,
            ]);
        }

        return view('settings.translations', compact(
            'translations',
            'groups',
            'group',
            'stats',
            'search',
            'allCategories'
        ));
    }

    /**
     * Store or update a translation.
     */
    public function storeTranslation(Request $request): JsonResponse
    {
        Gate::authorize('update', Setting::class);

        $validated = $request->validate([
            'key' => ['required', 'string', 'max:255'],
            'group' => ['required', 'string', 'max:100'],
            'text_en' => ['required', 'string'],
            'text_ar' => ['required', 'string'],
        ]);

        try {
            $success = Translation::setTranslation(
                $validated['key'],
                $validated['group'],
                $validated['text_en'],
                $validated['text_ar'],
                Auth::id()
            );

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Translation saved successfully.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save translation.',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a translation.
     */
    public function deleteTranslation(Request $request): JsonResponse
    {
        Gate::authorize('delete', Setting::class);

        $validated = $request->validate([
            'id' => ['required', 'integer', 'exists:translations,id'],
        ]);

        try {
            $translation = Translation::findOrFail($validated['id']);
            $translation->delete();

            return response()->json([
                'success' => true,
                'message' => 'Translation deleted successfully.',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete translation.',
            ], 500);
        }
    }

    /**
     * Export translations to language files.
     */
    public function exportTranslations(Request $request): JsonResponse
    {
        Gate::authorize('export', Setting::class);

        $group = $request->get('group');

        try {
            $success = Translation::exportToFiles($group);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => $group ? "Group '{$group}' exported successfully." : 'All translations exported successfully.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Export failed.',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Export error: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import translations from language files.
     */
    public function importTranslations(Request $request): JsonResponse
    {
        Gate::authorize('import', Setting::class);

        $group = $request->get('group');

        try {
            $success = Translation::importFromFiles($group);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => $group ? "Group '{$group}' imported successfully." : 'All translations imported successfully.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Import failed.',
                ], 500);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Import error: ' . $e->getMessage(),
            ], 500);
        }
    }
}
