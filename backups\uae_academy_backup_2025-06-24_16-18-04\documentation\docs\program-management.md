# Program Management Module Documentation

## Overview

The Program Management module is a comprehensive system for managing sports programs within the UAE English Sports Academy platform. It provides full CRUD operations, advanced search capabilities, bulk operations, export functionality, and real-time statistics with a focus on program scheduling, pricing, and enrollment management.

## Features

### Core Functionality
- **Complete CRUD Operations**: Create, read, update, and delete programs
- **Advanced Search & Filtering**: Multi-criteria search with real-time filtering
- **Bulk Operations**: Mass activate, deactivate, or delete programs
- **Export Capabilities**: Excel and PDF export with custom formatting
- **Real-time Statistics**: Live program metrics and analytics
- **Role-based Access Control**: Different permissions for different user roles
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

### Program Information Management
- **ID**: Unique identifier for each program
- **Program Name**: Descriptive name for the program
- **Academy Name**: Associated academy for the program
- **Days**: Weekly schedule (SUN-SAT) with multi-day selection
- **Classes**: Number of sessions in the program
- **Price in AED**: Program cost in UAE Dirham currency
- **Schedule**: Start and end times for program sessions
- **Capacity**: Maximum number of students (optional)
- **Status**: Active/Inactive program management
- **Description**: Detailed program information

## Technical Architecture

### Backend Components

#### Models
- **Program.php**: Enhanced model with relationships and computed properties
  - Relationships: academy, students, payments
  - Computed Properties: formatted_days, formatted_price, status_text, academy_name, branch_name, duration_hours, student_count, enrollment_percentage
  - Scopes: active, inactive, search, byAcademy, byBranch, priceRange, byDays
  - Utility Methods: getStatistics(), isFull(), hasAvailableSpots(), getAvailableSpots(), runsOnDay(), getNextClassDate(), getScheduleDisplay()

#### Controllers
- **ProgramController.php**: Main controller with comprehensive functionality
  - CRUD Operations: index, create, store, show, edit, update, destroy
  - Status Management: toggleStatus
  - Bulk Operations: bulkAction (activate, deactivate, delete)
  - Export Functions: exportExcel, exportPdf
  - API Endpoints: apiIndex, getStatistics
  - Helper Methods: buildExportQuery, getStatisticsData, calculateGrowthRate

#### Form Requests
- **StoreProgramRequest.php**: Validation for program creation
- **UpdateProgramRequest.php**: Validation for program updates
  - Comprehensive validation rules for all fields
  - Custom validation messages and attributes
  - Data preparation and transformation
  - Advanced validation logic for time ranges and constraints

#### Policies
- **ProgramPolicy.php**: Role-based authorization
  - Admin: Full access to all operations
  - Branch Manager: Full access to all programs
  - Academy Manager: Access to assigned academy programs only
  - Granular permissions for different operations

#### Export Classes
- **ProgramsExport.php**: Excel export with advanced formatting
- **export-pdf.blade.php**: PDF export template with statistics

### Frontend Components

#### Views
- **index.blade.php**: Main listing page with advanced features
- **create.blade.php**: Program creation form
- **edit.blade.php**: Program editing form
- **show.blade.php**: Detailed program view
- **_stats.blade.php**: Statistics cards component
- **_filters.blade.php**: Advanced search filters
- **_table.blade.php**: Responsive table component
- **_grid.blade.php**: Grid view component
- **export-pdf.blade.php**: PDF export template

#### JavaScript Components
- **programManagement()**: Main listing controller with Alpine.js
- **programFilters()**: Advanced search functionality
- **programForm()**: Form validation and submission
- **programEditForm()**: Edit form with change tracking

#### Features Implementation
- **Dual View Modes**: Table and grid views with localStorage persistence
- **Advanced Filtering**: By academy, branch, status, price range, days
- **Bulk Selection**: Checkbox-based selection with select all functionality
- **Real-time Search**: Instant filtering and search capabilities
- **Export Integration**: Excel and PDF export with current filters
- **Status Toggle**: Quick status change with AJAX
- **Responsive Design**: Mobile-first approach with breakpoint optimization

## Database Schema

### Programs Table Structure
```sql
CREATE TABLE programs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    academy_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    days JSON NOT NULL,
    classes INTEGER NOT NULL DEFAULT 1,
    price DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'AED',
    start_time TIME NULL,
    end_time TIME NULL,
    max_students INTEGER NULL,
    status BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (academy_id) REFERENCES academies(id) ON DELETE CASCADE,
    INDEX idx_academy_status (academy_id, status),
    INDEX idx_name (name),
    INDEX idx_price (price)
);
```

### Relationships
- **Academy**: belongsTo relationship (academy_id)
- **Students**: hasMany relationship (for enrollment tracking)
- **Payments**: hasMany relationship (for financial tracking)

## API Endpoints

### REST Routes
- `GET /programs` - List programs with filtering
- `POST /programs` - Create new program
- `GET /programs/{id}` - Show program details
- `PUT /programs/{id}` - Update program
- `DELETE /programs/{id}` - Delete program

### Additional Routes
- `POST /programs/{id}/toggle-status` - Toggle program status
- `POST /programs/bulk-action` - Bulk operations
- `GET /programs/export/excel` - Excel export
- `GET /programs/export/pdf` - PDF export

### API Endpoints
- `GET /api/programs` - JSON API for programs
- `GET /api/programs/statistics` - Statistics API

## Security & Authorization

### Role-based Access Control
- **Admin**: Full access to all program operations
- **Branch Manager**: Full access to all programs
- **Academy Manager**: Access to programs in assigned academies only

### Permission Matrix
| Operation | Admin | Branch Manager | Academy Manager |
|-----------|-------|----------------|-----------------|
| View All | ✅ | ✅ | ✅ (filtered) |
| Create | ✅ | ✅ | ✅ |
| Update | ✅ | ✅ | ✅ (own academy) |
| Delete | ✅ | ✅ | ❌ |
| Bulk Actions | ✅ | ✅ | ❌ |
| Export | ✅ | ✅ | ✅ |
| Statistics | ✅ | ✅ | ✅ |

## Statistics & Analytics

### Real-time Metrics
- **Total Programs**: Count of all programs
- **Active Programs**: Count of active programs
- **Average Price**: Mean price of active programs
- **Growth Rate**: Month-over-month program growth
- **Total Revenue**: Sum of all program prices
- **Programs by Academy**: Top academies by program count

### Export Features
- **Excel Export**: Formatted spreadsheet with styling
- **PDF Export**: Professional report with statistics
- **Filtered Export**: Export respects current search filters
- **Custom Formatting**: Bank-style design with branding

## UI/UX Design

### Design System
- **Bank-style Aesthetics**: Professional financial institution design
- **Consistent Branding**: UAE English Sports Academy colors and fonts
- **Color Scheme**: Red (#E53E3E) primary with complementary colors
- **Typography**: Century Gothic for English, IBM Plex Sans Arabic
- **Icons**: Heroicons for consistency

### Responsive Breakpoints
- **Desktop (1024px+)**: Full table view with all columns
- **Tablet (768px-1023px)**: Condensed table or grid view
- **Mobile (<768px)**: Card-based grid view

### Accessibility
- **WCAG 2.1 Compliance**: Level AA accessibility standards
- **Keyboard Navigation**: Full keyboard support
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **Color Contrast**: Meets accessibility contrast requirements

## Performance Optimization

### Database Optimization
- **Indexed Queries**: Strategic indexing for common queries
- **Eager Loading**: Optimized relationship loading
- **Query Scopes**: Reusable query logic
- **Pagination**: Efficient data loading

### Frontend Optimization
- **Lazy Loading**: Progressive content loading
- **Caching**: LocalStorage for user preferences
- **Minification**: Optimized CSS and JavaScript
- **CDN Integration**: Fast asset delivery

## Testing Strategy

### Backend Testing
- **Unit Tests**: Model and controller testing
- **Feature Tests**: End-to-end functionality testing
- **Policy Tests**: Authorization testing
- **Validation Tests**: Form request testing

### Frontend Testing
- **Component Tests**: Alpine.js component testing
- **Integration Tests**: User interaction testing
- **Accessibility Tests**: WCAG compliance testing
- **Cross-browser Tests**: Multi-browser compatibility

## Deployment & Maintenance

### Environment Requirements
- **PHP 8.1+**: Modern PHP version
- **Laravel 10+**: Latest Laravel framework
- **MySQL 8.0+**: Database with JSON support
- **Node.js 18+**: For asset compilation

### Monitoring
- **Error Tracking**: Comprehensive error logging
- **Performance Monitoring**: Query and response time tracking
- **User Analytics**: Usage pattern analysis
- **Security Monitoring**: Access and permission tracking

## Future Enhancements

### Planned Features
- **Program Templates**: Reusable program configurations
- **Recurring Programs**: Automated program scheduling
- **Waitlist Management**: Student queue management
- **Integration APIs**: Third-party system integration
- **Mobile App**: Native mobile application
- **Advanced Analytics**: Detailed reporting and insights

## Support & Documentation

### User Guides
- **Administrator Guide**: Complete system administration
- **Manager Guide**: Day-to-day operations
- **Quick Start Guide**: Getting started tutorial
- **Troubleshooting Guide**: Common issues and solutions

### Technical Documentation
- **API Documentation**: Complete API reference
- **Database Schema**: Detailed table structures
- **Deployment Guide**: Installation and configuration
- **Customization Guide**: Extending functionality

The Program Management module provides a robust, scalable, and user-friendly solution for managing sports programs. With its comprehensive feature set, mobile-responsive design, and multilingual support, it serves as a core component of the UAE English Sports Academy management system.
