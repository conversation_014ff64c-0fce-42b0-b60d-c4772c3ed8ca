<?php

namespace App\Exports;

use App\Models\Program;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use Illuminate\Support\Collection;

class ProgramsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths, WithTitle
{
    protected $programs;

    public function __construct(Collection $programs)
    {
        $this->programs = $programs;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->programs;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'ID',
            'Program Name',
            'Academy',
            'Branch',
            'Days',
            'Start Time',
            'End Time',
            'Duration (Hours)',
            'Classes',
            'Price (AED)',
            'Max Students',
            'Enrolled Students',
            'Available Spots',
            'Enrollment %',
            'Status',
            'Description',
            'Created Date',
            'Updated Date',
        ];
    }

    /**
     * @param Program $program
     * @return array
     */
    public function map($program): array
    {
        return [
            $program->id,
            $program->name,
            $program->academy->name ?? 'N/A',
            $program->academy->branch->name ?? 'N/A',
            $program->formatted_days,
            $program->start_time ? \Carbon\Carbon::parse($program->start_time)->format('g:i A') : 'N/A',
            $program->end_time ? \Carbon\Carbon::parse($program->end_time)->format('g:i A') : 'N/A',
            $program->duration_hours,
            $program->classes,
            number_format($program->price, 2),
            $program->max_students ?? 'Unlimited',
            $program->student_count,
            $program->max_students ? $program->getAvailableSpots() : 'Unlimited',
            $program->max_students ? $program->enrollment_percentage . '%' : 'N/A',
            $program->status ? 'Active' : 'Inactive',
            $program->description ?? '',
            $program->created_at->format('Y-m-d H:i:s'),
            $program->updated_at->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 8,   // ID
            'B' => 25,  // Program Name
            'C' => 20,  // Academy
            'D' => 20,  // Branch
            'E' => 20,  // Days
            'F' => 12,  // Start Time
            'G' => 12,  // End Time
            'H' => 15,  // Duration
            'I' => 10,  // Classes
            'J' => 12,  // Price
            'K' => 15,  // Max Students
            'L' => 15,  // Enrolled Students
            'M' => 15,  // Available Spots
            'N' => 12,  // Enrollment %
            'O' => 10,  // Status
            'P' => 30,  // Description
            'Q' => 18,  // Created Date
            'R' => 18,  // Updated Date
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        $lastRow = $this->programs->count() + 1;
        $lastColumn = 'R';

        return [
            // Header row styling
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                    'size' => 12,
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E53E3E'], // Leaders Red
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER,
                ],
            ],
            
            // Data rows styling
            "A2:{$lastColumn}{$lastRow}" => [
                'font' => [
                    'size' => 10,
                ],
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true,
                ],
            ],

            // ID column center alignment
            "A2:A{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],

            // Price column right alignment
            "J2:J{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_RIGHT,
                ],
                'numberFormat' => [
                    'formatCode' => '#,##0.00',
                ],
            ],

            // Numeric columns center alignment
            "H2:H{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            "I2:I{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            "L2:L{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            "M2:M{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            "N2:N{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],

            // Status column center alignment
            "O2:O{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],

            // Time columns center alignment
            "F2:F{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
            "G2:G{$lastRow}" => [
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
            ],
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Programs Export';
    }
}
