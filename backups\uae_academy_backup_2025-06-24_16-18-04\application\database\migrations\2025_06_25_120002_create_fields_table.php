<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fields', function (Blueprint $table) {
            $table->id();
            $table->foreignId('venue_id')->constrained()->onDelete('cascade');
            $table->string('name')->charset('utf8mb4'); // e.g., "Field X", "Field Y", "Field Z"
            $table->string('name_ar')->charset('utf8mb4')->nullable(); // Arabic name
            $table->string('code')->unique(); // e.g., "LEADERS_FIELD_X"
            $table->enum('type', ['football', 'basketball', 'tennis', 'volleyball', 'multipurpose'])->default('football');
            $table->text('description')->charset('utf8mb4')->nullable();
            $table->text('description_ar')->charset('utf8mb4')->nullable();
            $table->string('surface_type')->nullable(); // grass, artificial, concrete, etc.
            $table->json('dimensions')->nullable(); // length, width, area in JSON
            $table->integer('capacity')->nullable(); // maximum players/people
            $table->decimal('hourly_rate', 8, 2); // Hourly rate in AED
            $table->decimal('peak_hour_rate', 8, 2)->nullable(); // Peak hours rate
            $table->json('peak_hours')->nullable(); // Peak hours definition
            $table->decimal('weekend_rate', 8, 2)->nullable(); // Weekend rate
            $table->json('equipment_included')->nullable(); // Available equipment
            $table->json('amenities')->nullable(); // Changing rooms, showers, etc.
            $table->boolean('lighting_available')->default(true);
            $table->boolean('air_conditioning')->default(false);
            $table->boolean('covered')->default(false); // Indoor/outdoor
            $table->time('available_from')->default('06:00:00'); // Daily availability start
            $table->time('available_to')->default('23:00:00'); // Daily availability end
            $table->json('unavailable_days')->nullable(); // Days when field is not available
            $table->integer('minimum_booking_hours')->default(1);
            $table->integer('maximum_booking_hours')->default(8);
            $table->integer('advance_booking_days')->default(30); // How many days in advance can book
            $table->boolean('requires_deposit')->default(false);
            $table->decimal('deposit_percentage', 5, 2)->default(0); // Deposit percentage
            $table->string('currency', 3)->default('AED');
            $table->boolean('status')->default(true); // active/inactive
            $table->text('maintenance_notes')->charset('utf8mb4')->nullable();
            $table->date('last_maintenance_date')->nullable();
            $table->date('next_maintenance_date')->nullable();
            $table->text('notes')->charset('utf8mb4')->nullable();
            $table->text('notes_ar')->charset('utf8mb4')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['venue_id', 'status']);
            $table->index(['type', 'status']);
            $table->index('name');
            $table->index('code');
            $table->index(['available_from', 'available_to']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fields');
    }
};
