<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('students', function (Blueprint $table) {
            // Add Arabic name fields
            $table->string('full_name_ar')->charset('utf8mb4')->nullable()->after('full_name');
            $table->string('first_name')->charset('utf8mb4')->nullable()->after('full_name_ar');
            $table->string('first_name_ar')->charset('utf8mb4')->nullable()->after('first_name');
            $table->string('last_name')->charset('utf8mb4')->nullable()->after('first_name_ar');
            $table->string('last_name_ar')->charset('utf8mb4')->nullable()->after('last_name');
            
            // Add nationality in Arabic
            $table->string('nationality_ar')->charset('utf8mb4')->nullable()->after('nationality');
            
            // Add address in Arabic
            $table->text('address_ar')->charset('utf8mb4')->nullable()->after('address');
            
            // Add notes in Arabic
            $table->text('notes_ar')->charset('utf8mb4')->nullable()->after('notes');
            
            // Add indexes for Arabic fields
            $table->index('full_name_ar');
            $table->index(['first_name_ar', 'last_name_ar']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('students', function (Blueprint $table) {
            $table->dropIndex(['students_full_name_ar_index']);
            $table->dropIndex(['students_first_name_ar_last_name_ar_index']);
            
            $table->dropColumn([
                'full_name_ar',
                'first_name',
                'first_name_ar', 
                'last_name',
                'last_name_ar',
                'nationality_ar',
                'address_ar',
                'notes_ar'
            ]);
        });
    }
};
