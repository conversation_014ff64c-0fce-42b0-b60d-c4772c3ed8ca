<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Student;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Program;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Student>
 */
class StudentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $firstName = fake()->firstName();
        $lastName = fake()->lastName();
        $nationalities = ['UAE', 'Saudi Arabia', 'Egypt', 'Jordan', 'Lebanon', 'Syria', 'India', 'Pakistan'];

        return [
            'branch_id' => 1, // Will be overridden when specific ID is provided
            'academy_id' => 1, // Will be overridden when specific ID is provided
            'full_name' => $firstName . ' ' . $lastName,
            'phone' => '+971' . fake()->numerify('5########'),
            'email' => fake()->unique()->safeEmail(),
            'nationality' => fake()->randomElement($nationalities),
            'address' => fake()->address(),
            'birth_date' => fake()->dateTimeBetween('-25 years', '-5 years')->format('Y-m-d'),
            'join_date' => fake()->dateTimeBetween('-2 years', 'now')->format('Y-m-d'),
            'status' => 'active',
        ];
    }

    /**
     * Indicate that the student is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the student is graduated.
     */
    public function graduated(): static
    {
        return $this->state(fn(array $attributes) => [
            'status' => 'graduated',
        ]);
    }
}
