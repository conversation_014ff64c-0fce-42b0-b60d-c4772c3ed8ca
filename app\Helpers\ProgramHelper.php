<?php

if (!function_exists('getProgramBackgroundColor')) {
    /**
     * Get a light background color for program cards based on index
     * Limited to first 10 programs only
     *
     * @param int $index The loop index or program position
     * @return string The Tailwind CSS background color class or 'bg-white' for programs beyond 10
     */
    function getProgramBackgroundColor($index)
    {
        // Only apply colors to first 10 programs
        if ($index >= 10) {
            return 'bg-white';
        }

        $backgroundColors = [
            'bg-stone-50',      // Light warm grey
            'bg-amber-50',      // Light warm coffee
            'bg-slate-50',      // Light cool grey
            'bg-zinc-50',       // Light neutral grey
            'bg-neutral-50',    // Light neutral
            'bg-gray-50',       // Light grey
            'bg-orange-50',     // Light peach
            'bg-yellow-50',     // Light cream
            'bg-indigo-50',     // Light indigo
            'bg-purple-50',     // Light purple
        ];

        return $backgroundColors[$index];
    }
}
