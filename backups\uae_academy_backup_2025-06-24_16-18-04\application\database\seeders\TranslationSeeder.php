<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Translation;
use Illuminate\Support\Facades\File;

class TranslationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Importing translations from language files...');

        // Import from existing language files
        $success = Translation::importFromFiles();

        if ($success) {
            $this->command->info('✅ Translations imported successfully!');
        } else {
            $this->command->error('❌ Failed to import translations');
        }

        // Add some additional sample translations
        $this->addSampleTranslations();

        $stats = Translation::getStatistics();
        $this->command->info("📊 Translation Statistics:");
        $this->command->info("   Total: {$stats['total']}");
        $this->command->info("   Complete: {$stats['complete']}");
        $this->command->info("   Groups: {$stats['groups']}");
        $this->command->info("   Completion: {$stats['completion_percentage']}%");
    }

    /**
     * Add sample translations for demonstration
     */
    private function addSampleTranslations(): void
    {
        $sampleTranslations = [
            [
                'key' => 'welcome_message',
                'group' => 'common',
                'text_en' => 'Welcome to UAE English Sports Academy',
                'text_ar' => 'مرحباً بكم في أكاديمية الإمارات الإنجليزية الرياضية',
            ],
            [
                'key' => 'translation_management',
                'group' => 'settings',
                'text_en' => 'Translation Management',
                'text_ar' => 'إدارة الترجمة',
            ],
            [
                'key' => 'add_translation',
                'group' => 'settings',
                'text_en' => 'Add Translation',
                'text_ar' => 'إضافة ترجمة',
            ],
            [
                'key' => 'edit_translation',
                'group' => 'settings',
                'text_en' => 'Edit Translation',
                'text_ar' => 'تعديل الترجمة',
            ],
            [
                'key' => 'delete_translation',
                'group' => 'settings',
                'text_en' => 'Delete Translation',
                'text_ar' => 'حذف الترجمة',
            ],
            [
                'key' => 'export_translations',
                'group' => 'settings',
                'text_en' => 'Export Translations',
                'text_ar' => 'تصدير الترجمات',
            ],
            [
                'key' => 'import_translations',
                'group' => 'settings',
                'text_en' => 'Import Translations',
                'text_ar' => 'استيراد الترجمات',
            ],
            [
                'key' => 'translation_key',
                'group' => 'settings',
                'text_en' => 'Translation Key',
                'text_ar' => 'مفتاح الترجمة',
            ],
            [
                'key' => 'english_text',
                'group' => 'settings',
                'text_en' => 'English Text',
                'text_ar' => 'النص الإنجليزي',
            ],
            [
                'key' => 'arabic_text',
                'group' => 'settings',
                'text_en' => 'Arabic Text',
                'text_ar' => 'النص العربي',
            ],
            [
                'key' => 'translation_group',
                'group' => 'settings',
                'text_en' => 'Translation Group',
                'text_ar' => 'مجموعة الترجمة',
            ],
            [
                'key' => 'save_translation',
                'group' => 'settings',
                'text_en' => 'Save Translation',
                'text_ar' => 'حفظ الترجمة',
            ],
            [
                'key' => 'translation_saved',
                'group' => 'settings',
                'text_en' => 'Translation saved successfully',
                'text_ar' => 'تم حفظ الترجمة بنجاح',
            ],
            [
                'key' => 'translation_deleted',
                'group' => 'settings',
                'text_en' => 'Translation deleted successfully',
                'text_ar' => 'تم حذف الترجمة بنجاح',
            ],
            [
                'key' => 'search_translations',
                'group' => 'settings',
                'text_en' => 'Search Translations',
                'text_ar' => 'البحث في الترجمات',
            ],
        ];

        foreach ($sampleTranslations as $translation) {
            Translation::setTranslation(
                $translation['key'],
                $translation['group'],
                $translation['text_en'],
                $translation['text_ar'],
                1 // Admin user ID
            );
        }

        $this->command->info('✅ Sample translations added!');
    }
}
