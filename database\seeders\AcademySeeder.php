<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Academy;
use App\Models\Branch;

class AcademySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if ($this->command) {
            $this->command->info('Creating academies...');
        }

        // Get existing branches
        $ajmanBranch = Branch::where('name', 'AJMAN HAMEDYA')->first();
        $dubaiBranch = Branch::where('name', 'DUBAI AL QUSAIS')->first();
        $sharjahBranch = Branch::where('name', 'SHARJAH CITY CENTER')->first();

        if (!$ajmanBranch || !$dubaiBranch || !$sharjahBranch) {
            throw new \Exception('Required branches not found. Please run BranchSeeder first.');
        }

        $academies = [
            [
                'branch_id' => $ajmanBranch->id,
                'name' => 'Swimming Academy',
                'description' => 'Professional swimming training for all ages',
                'coach_name' => 'Coach <PERSON>',
                'coach_phone' => '+971501111111',
                'status' => true,
            ],
            [
                'branch_id' => $dubaiBranch->id,
                'name' => 'FB AL QUSAIS',
                'description' => 'Football training academy',
                'coach_name' => 'Coach Mohammed',
                'coach_phone' => '+971502222222',
                'status' => true,
            ],
            [
                'branch_id' => $sharjahBranch->id,
                'name' => 'Basketball Academy',
                'description' => 'Basketball training for youth and adults',
                'coach_name' => 'Coach Omar',
                'coach_phone' => '+971503333333',
                'status' => true,
            ],
            [
                'branch_id' => $ajmanBranch->id,
                'name' => 'Tennis Academy',
                'description' => 'Professional tennis coaching',
                'coach_name' => 'Coach Sara',
                'coach_phone' => '+971504444444',
                'status' => true,
            ],
        ];

        foreach ($academies as $academyData) {
            Academy::firstOrCreate(
                [
                    'name' => $academyData['name'],
                    'branch_id' => $academyData['branch_id']
                ],
                $academyData
            );
        }

        if ($this->command) {
            $this->command->info('Academies created successfully.');
        }
    }
}
