{{-- UAE English Sports Academy - Premium Language Switcher Component --}}
{{-- Commercial-grade bilingual toggle with smooth transitions --}}

@props([
    'variant' => 'default', // default, compact, large, outline
    'position' => 'header', // header, sidebar, footer
    'showDropdown' => false,
])

@php
    $currentLocale = app()->getLocale();
    $isRtl = $currentLocale === 'ar';
    $languages = [
        'en' => [
            'code' => 'en',
            'name' => 'English',
            'native' => 'English',
            'flag' => '🇺🇸',
            'direction' => 'ltr'
        ],
        'ar' => [
            'code' => 'ar', 
            'name' => 'Arabic',
            'native' => 'العربية',
            'flag' => '🇦🇪',
            'direction' => 'rtl'
        ]
    ];
    
    $currentLang = $languages[$currentLocale];
    $otherLang = $languages[$currentLocale === 'en' ? 'ar' : 'en'];
    
    $classes = [
        'language-switcher',
        $variant !== 'default' ? "language-switcher-{$variant}" : '',
        $position !== 'header' ? "language-switcher-{$position}" : '',
    ];
@endphp

@if($showDropdown)
    {{-- Dropdown Version --}}
    <div class="language-switcher-dropdown" x-data="languageSwitcher()" x-init="init()">
        <button 
            @click="toggleDropdown()"
            @click.away="closeDropdown()"
            class="{{ implode(' ', array_filter($classes)) }}"
            :class="{ 'loading': isLoading }"
            type="button"
            aria-label="{{ __('Switch Language') }}"
            aria-expanded="false"
            x-ref="trigger"
        >
            <svg class="language-switcher-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                    d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
                </path>
            </svg>
            <span class="language-switcher-text">
                {{ $currentLang['flag'] }} {{ $currentLang['native'] }}
            </span>
            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </button>

        <div class="language-switcher-menu" x-show="isOpen" x-transition>
            @foreach($languages as $code => $language)
                <a 
                    href="{{ route('language.switch', $code) }}"
                    @click.prevent="switchLanguage('{{ $code }}')"
                    class="language-switcher-menu-item {{ $code === $currentLocale ? 'active' : '' }}"
                    data-locale="{{ $code }}"
                >
                    <span class="mr-2">{{ $language['flag'] }}</span>
                    <span>{{ $language['native'] }}</span>
                    @if($code === $currentLocale)
                        <svg class="w-4 h-4 ml-auto" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </a>
            @endforeach
        </div>
    </div>
@else
    {{-- Toggle Version --}}
    <button 
        @click="switchLanguage('{{ $otherLang['code'] }}')"
        class="{{ implode(' ', array_filter($classes)) }}"
        :class="{ 'loading': isLoading }"
        type="button"
        aria-label="{{ __('Switch to :language', ['language' => $otherLang['native']]) }}"
        x-data="languageSwitcher()"
        x-init="init()"
    >
        <svg class="language-switcher-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
            </path>
        </svg>
        <span class="language-switcher-text">
            {{ $otherLang['flag'] }} {{ $otherLang['code'] === 'ar' ? 'عربي' : 'EN' }}
        </span>
    </button>
@endif

{{-- Language Switcher JavaScript --}}
@push('scripts')
<script>
function languageSwitcher() {
    return {
        isLoading: false,
        isOpen: false,
        currentLocale: '{{ $currentLocale }}',

        init() {
            // Listen for language change events
            window.addEventListener('languageChanged', (event) => {
                this.currentLocale = event.detail.locale;
                this.updateUI(event.detail);
            });
        },

        toggleDropdown() {
            this.isOpen = !this.isOpen;
        },

        closeDropdown() {
            this.isOpen = false;
        },

        async switchLanguage(locale) {
            if (this.isLoading || locale === this.currentLocale) {
                return;
            }

            this.isLoading = true;
            this.closeDropdown();

            try {
                // Call the API endpoint
                const response = await fetch('{{ route("api.language.switch") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({ locale: locale })
                });

                const data = await response.json();

                if (data.success) {
                    // Update current locale
                    this.currentLocale = locale;
                    
                    // Update document attributes
                    document.documentElement.setAttribute('lang', locale);
                    document.documentElement.setAttribute('dir', data.direction);
                    
                    // Update body classes
                    if (data.direction === 'rtl') {
                        document.body.classList.add('arabic-text');
                    } else {
                        document.body.classList.remove('arabic-text');
                    }

                    // Store preference
                    localStorage.setItem('language', locale.toUpperCase());
                    localStorage.setItem('direction', data.direction);

                    // Dispatch custom event
                    window.dispatchEvent(new CustomEvent('languageChanged', {
                        detail: {
                            locale: locale,
                            direction: data.direction,
                            message: data.message
                        }
                    }));

                    // Show success message
                    this.showNotification(data.message, 'success');

                    // Reload page to apply translations
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);

                } else {
                    this.showNotification(data.message || 'Failed to switch language', 'error');
                }
            } catch (error) {
                console.error('Language switch error:', error);
                this.showNotification('Network error occurred', 'error');
            } finally {
                this.isLoading = false;
            }
        },

        updateUI(data) {
            // Update any UI elements that need immediate updates
            const switchers = document.querySelectorAll('.language-switcher-text');
            switchers.forEach(switcher => {
                if (data.locale === 'ar') {
                    switcher.textContent = '🇦🇪 عربي';
                } else {
                    switcher.textContent = '🇺🇸 EN';
                }
            });
        },

        showNotification(message, type = 'info') {
            // Create a simple notification
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'var(--success-green)' : 'var(--error-red)'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
                font-size: 14px;
                font-weight: 500;
                transform: translateX(100%);
                transition: transform 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    }
}
</script>
@endpush

{{-- Component Styles --}}
@push('styles')
<style>
/* Component-specific enhancements */
.language-switcher-dropdown {
    position: relative;
}

.language-switcher-menu {
    animation: fadeInDown 0.2s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Notification styles */
.notification {
    font-family: var(--font-family-primary);
}

[dir="rtl"] .notification {
    right: auto;
    left: 20px;
    transform: translateX(-100%);
}

[dir="rtl"] .notification.show {
    transform: translateX(0);
}
</style>
@endpush
