<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'System Administrator',
                'password' => bcrypt('admin123'),
                'role' => 'admin',
                'status' => true,
            ]
        );

        // Create sample branches
        $ajmanBranch = \App\Models\Branch::firstOrCreate(
            ['name' => 'AJMAN HAMEDYA'],
            [
                'location' => 'Ajman, UAE',
                'phone' => '+971501234567',
                'email' => '<EMAIL>',
                'address' => 'Hamedya Area, Ajman, UAE',
                'status' => true,
            ]
        );

        $dubaiBranch = \App\Models\Branch::firstOrCreate(
            ['name' => 'AL QUSAIS'],
            [
                'location' => 'Dubai, UAE',
                'phone' => '+971507654321',
                'email' => '<EMAIL>',
                'address' => 'Al Qusais Area, Dubai, UAE',
                'status' => true,
            ]
        );

        // Create sample academies
        $swimmingAcademy = \App\Models\Academy::firstOrCreate(
            ['name' => 'AJMAN SWIMMING ACADEMY', 'branch_id' => $ajmanBranch->id],
            [
                'description' => 'Professional swimming training for all ages',
                'coach_name' => 'Coach Ahmed',
                'coach_phone' => '+971501111111',
                'status' => true,
            ]
        );

        $footballAcademy = \App\Models\Academy::firstOrCreate(
            ['name' => 'FB AL QUSAIS', 'branch_id' => $dubaiBranch->id],
            [
                'description' => 'Football training academy',
                'coach_name' => 'Coach Mohammed',
                'coach_phone' => '+971502222222',
                'status' => true,
            ]
        );

        // Create sample programs
        \App\Models\Program::firstOrCreate(
            ['academy_id' => $swimmingAcademy->id, 'name' => 'Beginner Swimming'],
            [
                'days' => json_encode(['SAT', 'MON', 'WED']),
                'classes' => 3,
                'price' => 315.00,
                'start_time' => '16:00:00',
                'end_time' => '17:00:00',
                'max_students' => 15,
                'status' => true,
            ]
        );

        \App\Models\Program::firstOrCreate(
            ['academy_id' => $footballAcademy->id, 'name' => 'Youth Football Training'],
            [
                'days' => json_encode(['FRI', 'SAT']),
                'classes' => 2,
                'price' => 450.00,
                'start_time' => '17:00:00',
                'end_time' => '18:30:00',
                'max_students' => 20,
                'status' => true,
            ]
        );

        // Create branch manager
        \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Ajman Branch Manager',
                'password' => bcrypt('manager123'),
                'role' => 'branch_manager',
                'branch_id' => $ajmanBranch->id,
                'status' => true,
            ]
        );

        // Create academy manager
        \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Swimming Academy Manager',
                'password' => bcrypt('academy123'),
                'role' => 'academy_manager',
                'branch_id' => $ajmanBranch->id,
                'academy_id' => $swimmingAcademy->id,
                'status' => true,
            ]
        );

        // Create sample students
        $student1 = \App\Models\Student::firstOrCreate(
            ['phone' => '+971585382697'],
            [
                'branch_id' => $ajmanBranch->id,
                'academy_id' => $swimmingAcademy->id,
                'full_name' => 'Abdul Karim Jarkas',
                'email' => '<EMAIL>',
                'nationality' => 'UAE',
                'address' => 'Ajman, UAE',
                'birth_date' => '2010-05-15',
                'join_date' => now()->subDays(30),
                'status' => 'active',
            ]
        );

        $student2 = \App\Models\Student::firstOrCreate(
            ['phone' => '+971501234567'],
            [
                'branch_id' => $dubaiBranch->id,
                'academy_id' => $footballAcademy->id,
                'full_name' => 'AZEEM SHAKH',
                'email' => '<EMAIL>',
                'nationality' => 'UAE',
                'address' => 'Dubai, UAE',
                'birth_date' => '2012-08-20',
                'join_date' => now()->subDays(60),
                'status' => 'active',
            ]
        );

        // Create sample payments
        \App\Models\Payment::create([
            'student_id' => $student1->id,
            'branch_id' => $ajmanBranch->id,
            'academy_id' => $swimmingAcademy->id,
            'amount' => 315.00,
            'discount' => 0.00,
            'payment_method' => 'card',
            'payment_date' => now()->subDays(5),
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(25),
            'status' => 'active',
            'reset_num' => 'RST001',
            'class_time_from' => '16:00:00',
            'class_time_to' => '17:00:00',
            'renewal' => false,
            'note' => 'Initial payment for swimming lessons',
        ]);

        \App\Models\Payment::create([
            'student_id' => $student2->id,
            'branch_id' => $dubaiBranch->id,
            'academy_id' => $footballAcademy->id,
            'amount' => 734.00,
            'discount' => 50.00,
            'payment_method' => 'card',
            'payment_date' => now()->subDays(45),
            'start_date' => now()->subDays(45),
            'end_date' => now()->subDays(15),
            'status' => 'expired',
            'reset_num' => 'RST002',
            'class_time_from' => '17:00:00',
            'class_time_to' => '18:30:00',
            'renewal' => false,
            'note' => 'Football training payment',
        ]);

        // Create sample uniforms
        \App\Models\Uniform::create([
            'student_id' => $student1->id,
            'branch_id' => $ajmanBranch->id,
            'academy_id' => $swimmingAcademy->id,
            'order_date' => now()->subDays(10),
            'size' => 'M',
            'amount' => 85.00,
            'branch_status' => 'received',
            'office_status' => 'delivered',
            'payment_method' => 'cash',
            'note' => 'Swimming uniform - Medium size',
        ]);

        \App\Models\Uniform::create([
            'student_id' => $student2->id,
            'branch_id' => $dubaiBranch->id,
            'academy_id' => $footballAcademy->id,
            'order_date' => now()->subDays(20),
            'size' => 'L',
            'amount' => 95.00,
            'branch_status' => 'pending',
            'office_status' => 'pending',
            'payment_method' => 'card',
            'note' => 'Football uniform - Large size',
        ]);

        // Seed VAT settings
        $this->call(VatSettingsSeeder::class);
    }
}
