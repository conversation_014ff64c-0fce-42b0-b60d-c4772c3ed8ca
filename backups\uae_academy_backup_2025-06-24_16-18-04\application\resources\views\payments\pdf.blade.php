<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payments Report - UAE English Sports Academy</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #e5e5e5;
            padding-bottom: 20px;
        }
        
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #d32f2f;
            margin-bottom: 5px;
        }
        
        .subtitle {
            color: #666;
            font-size: 14px;
        }
        
        .report-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-size: 12px;
            color: #666;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #d32f2f;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
        
        .payments-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }
        
        .payments-table th {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 6px;
            text-align: left;
            font-weight: bold;
            color: #495057;
        }
        
        .payments-table td {
            border: 1px solid #dee2e6;
            padding: 6px;
            vertical-align: top;
        }
        
        .payments-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-failed {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .status-cancelled {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .status-refunded {
            background-color: #cce7ff;
            color: #004085;
        }
        
        .amount {
            font-weight: bold;
            color: #d32f2f;
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e5e5;
            text-align: center;
            font-size: 11px;
            color: #666;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .payments-table {
                font-size: 10px;
            }
            
            .payments-table th,
            .payments-table td {
                padding: 4px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="logo">UAE English Sports Academy</div>
        <div class="subtitle">Payments Report</div>
    </div>

    <!-- Report Information -->
    <div class="report-info">
        <div>
            <strong>Generated:</strong> {{ now()->format('M d, Y H:i') }}
        </div>
        <div>
            <strong>Total Records:</strong> {{ $payments->count() }}
        </div>
    </div>

    <!-- Statistics Summary -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-value">{{ $stats['total_payments'] }}</div>
            <div class="stat-label">Total Payments</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">AED {{ number_format($stats['total_amount'], 2) }}</div>
            <div class="stat-label">Total Amount</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">AED {{ number_format($stats['completed_amount'], 2) }}</div>
            <div class="stat-label">Completed Amount</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">AED {{ number_format($stats['pending_amount'], 2) }}</div>
            <div class="stat-label">Pending Amount</div>
        </div>
    </div>

    <!-- Payments Table -->
    <table class="payments-table">
        <thead>
            <tr>
                <th>Ref. Number</th>
                <th>Student</th>
                <th>Branch</th>
                <th>Academy</th>
                <th>Amount</th>
                <th>Discount</th>
                <th>Net Amount</th>
                <th>Method</th>
                <th>Status</th>
                <th>Payment Date</th>
                <th>Period</th>
            </tr>
        </thead>
        <tbody>
            @foreach($payments as $payment)
                <tr>
                    <td>{{ $payment->reference_number ?? 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}</td>
                    <td>{{ $payment->student->full_name ?? 'N/A' }}</td>
                    <td>{{ $payment->branch->name ?? 'N/A' }}</td>
                    <td>{{ $payment->academy->name ?? 'N/A' }}</td>
                    <td class="amount">AED {{ number_format($payment->amount, 2) }}</td>
                    <td>{{ $payment->discount > 0 ? 'AED ' . number_format($payment->discount, 2) : '-' }}</td>
                    <td class="amount">AED {{ number_format($payment->net_amount, 2) }}</td>
                    <td>{{ $payment->method_text }}</td>
                    <td>
                        <span class="status-badge status-{{ $payment->status }}">
                            {{ $payment->status_text }}
                        </span>
                    </td>
                    <td>{{ $payment->formatted_payment_date ?: 'Not paid' }}</td>
                    <td>{{ $payment->formatted_start_date }} - {{ $payment->formatted_end_date }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Summary by Status -->
    @php
        $statusSummary = $payments->groupBy('status')->map(function ($group) {
            return [
                'count' => $group->count(),
                'amount' => $group->sum('amount')
            ];
        });
    @endphp

    @if($statusSummary->count() > 1)
        <div style="margin-top: 30px;">
            <h3 style="margin-bottom: 15px; color: #333;">Summary by Status</h3>
            <table class="payments-table" style="width: 50%;">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Count</th>
                        <th>Total Amount</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($statusSummary as $status => $data)
                        <tr>
                            <td>
                                <span class="status-badge status-{{ $status }}">
                                    {{ ucfirst(str_replace('_', ' ', $status)) }}
                                </span>
                            </td>
                            <td>{{ $data['count'] }}</td>
                            <td class="amount">AED {{ number_format($data['amount'], 2) }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>This report was generated automatically by the UAE English Sports Academy Management System.</p>
        <p>For questions or support, please contact the system administrator.</p>
    </div>
</body>
</html>
