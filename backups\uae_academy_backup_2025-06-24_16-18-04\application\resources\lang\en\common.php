<?php

// UAE English Sports Academy - Common Translations (English)
// Shared translations used across all modules

return [
    // ===== GENERAL TERMS =====
    'dashboard' => 'Dashboard',
    'home' => 'Home',
    'welcome' => 'Welcome',
    'hello' => 'Hello',
    'goodbye' => 'Goodbye',
    'thank_you' => 'Thank You',
    'please' => 'Please',
    'yes' => 'Yes',
    'no' => 'No',
    'ok' => 'OK',
    'cancel' => 'Cancel',
    'close' => 'Close',
    'back' => 'Back',
    'next' => 'Next',
    'previous' => 'Previous',
    'continue' => 'Continue',
    'finish' => 'Finish',
    'done' => 'Done',
    'loading' => 'Loading...',
    'processing' => 'Processing...',
    'please_wait' => 'Please wait...',

    // ===== NAVIGATION =====
    'navigation' => 'Navigation',
    'menu' => 'Menu',
    'main_menu' => 'Main Menu',
    'sidebar' => 'Sidebar',
    'header' => 'Header',
    'footer' => 'Footer',
    'breadcrumb' => 'Breadcrumb',
    'go_to' => 'Go to',
    'visit' => 'Visit',
    'link' => 'Link',

    // ===== ACTIONS =====
    'actions' => 'Actions',
    'action' => 'Action',
    'add' => 'Add',
    'create' => 'Create',
    'new' => 'New',
    'edit' => 'Edit',
    'update' => 'Update',
    'save' => 'Save',
    'delete' => 'Delete',
    'remove' => 'Remove',
    'view' => 'View',
    'show' => 'Show',
    'hide' => 'Hide',
    'search' => 'Search',
    'filter' => 'Filter',
    'sort' => 'Sort',
    'export' => 'Export',
    'import' => 'Import',
    'download' => 'Download',
    'upload' => 'Upload',
    'print' => 'Print',
    'copy' => 'Copy',
    'duplicate' => 'Duplicate',
    'move' => 'Move',
    'transfer' => 'Transfer',
    'activate' => 'Activate',
    'deactivate' => 'Deactivate',
    'enable' => 'Enable',
    'disable' => 'Disable',
    'approve' => 'Approve',
    'reject' => 'Reject',
    'confirm' => 'Confirm',
    'verify' => 'Verify',
    'validate' => 'Validate',
    'submit' => 'Submit',
    'send' => 'Send',
    'receive' => 'Receive',
    'reset' => 'Reset',
    'clear' => 'Clear',
    'refresh' => 'Refresh',
    'reload' => 'Reload',

    // ===== STATUS =====
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'enabled' => 'Enabled',
    'disabled' => 'Disabled',
    'online' => 'Online',
    'offline' => 'Offline',
    'available' => 'Available',
    'unavailable' => 'Unavailable',
    'pending' => 'Pending',
    'approved' => 'Approved',
    'rejected' => 'Rejected',
    'completed' => 'Completed',
    'cancelled' => 'Cancelled',
    'draft' => 'Draft',
    'published' => 'Published',
    'archived' => 'Archived',
    'deleted' => 'Deleted',
    'suspended' => 'Suspended',
    'expired' => 'Expired',
    'valid' => 'Valid',
    'invalid' => 'Invalid',

    // ===== DATA & INFORMATION =====
    'data' => 'Data',
    'information' => 'Information',
    'details' => 'Details',
    'description' => 'Description',
    'summary' => 'Summary',
    'overview' => 'Overview',
    'profile' => 'Profile',
    'settings' => 'Settings',
    'preferences' => 'Preferences',
    'configuration' => 'Configuration',
    'options' => 'Options',
    'properties' => 'Properties',
    'attributes' => 'Attributes',
    'metadata' => 'Metadata',
    'statistics' => 'Statistics',
    'analytics' => 'Analytics',
    'reports' => 'Reports',
    'logs' => 'Logs',
    'history' => 'History',
    'timeline' => 'Timeline',
    'activity' => 'Activity',
    'events' => 'Events',
    'notifications' => 'Notifications',
    'alerts' => 'Alerts',
    'messages' => 'Messages',
    'comments' => 'Comments',
    'notes' => 'Notes',
    'tags' => 'Tags',
    'categories' => 'Categories',
    'groups' => 'Groups',
    'collections' => 'Collections',

    // ===== FORMS =====
    'form' => 'Form',
    'field' => 'Field',
    'label' => 'Label',
    'value' => 'Value',
    'input' => 'Input',
    'output' => 'Output',
    'required' => 'Required',
    'optional' => 'Optional',
    'placeholder' => 'Placeholder',
    'example' => 'Example',
    'format' => 'Format',
    'validation' => 'Validation',
    'error' => 'Error',
    'warning' => 'Warning',
    'success' => 'Success',
    'info' => 'Information',
    'help' => 'Help',
    'hint' => 'Hint',
    'tip' => 'Tip',
    'guide' => 'Guide',
    'instructions' => 'Instructions',

    // ===== DATES & TIME =====
    'date' => 'Date',
    'time' => 'Time',
    'datetime' => 'Date & Time',
    'today' => 'Today',
    'yesterday' => 'Yesterday',
    'tomorrow' => 'Tomorrow',
    'now' => 'Now',
    'current' => 'Current',
    'recent' => 'Recent',
    'latest' => 'Latest',
    'oldest' => 'Oldest',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'deleted_at' => 'Deleted At',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'due_date' => 'Due Date',
    'expiry_date' => 'Expiry Date',
    'birth_date' => 'Birth Date',
    'join_date' => 'Join Date',
    'last_login' => 'Last Login',
    'duration' => 'Duration',
    'period' => 'Period',
    'schedule' => 'Schedule',
    'calendar' => 'Calendar',

    // ===== NUMBERS & QUANTITIES =====
    'number' => 'Number',
    'count' => 'Count',
    'total' => 'Total',
    'subtotal' => 'Subtotal',
    'amount' => 'Amount',
    'quantity' => 'Quantity',
    'price' => 'Price',
    'cost' => 'Cost',
    'fee' => 'Fee',
    'discount' => 'Discount',
    'tax' => 'Tax',
    'percentage' => 'Percentage',
    'rate' => 'Rate',
    'ratio' => 'Ratio',
    'average' => 'Average',
    'minimum' => 'Minimum',
    'maximum' => 'Maximum',
    'limit' => 'Limit',
    'balance' => 'Balance',
    'credit' => 'Credit',
    'debit' => 'Debit',

    // ===== CONTACT & LOCATION =====
    'contact' => 'Contact',
    'address' => 'Address',
    'location' => 'Location',
    'phone' => 'Phone',
    'mobile' => 'Mobile',
    'email' => 'Email',
    'website' => 'Website',
    'fax' => 'Fax',
    'city' => 'City',
    'state' => 'State',
    'country' => 'Country',
    'postal_code' => 'Postal Code',
    'zip_code' => 'ZIP Code',
    'coordinates' => 'Coordinates',
    'latitude' => 'Latitude',
    'longitude' => 'Longitude',

    // ===== USERS & ROLES =====
    'user' => 'User',
    'users' => 'Users',
    'account' => 'Account',
    'profile' => 'Profile',
    'role' => 'Role',
    'permission' => 'Permission',
    'access' => 'Access',
    'login' => 'Login',
    'logout' => 'Logout',
    'register' => 'Register',
    'signup' => 'Sign Up',
    'signin' => 'Sign In',
    'password' => 'Password',
    'username' => 'Username',
    'email_address' => 'Email Address',
    'full_name' => 'Full Name',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'middle_name' => 'Middle Name',
    'display_name' => 'Display Name',
    'nickname' => 'Nickname',

    // ===== SYSTEM & TECHNICAL =====
    'system' => 'System',
    'application' => 'Application',
    'platform' => 'Platform',
    'service' => 'Service',
    'api' => 'API',
    'database' => 'Database',
    'server' => 'Server',
    'client' => 'Client',
    'browser' => 'Browser',
    'device' => 'Device',
    'version' => 'Version',
    'build' => 'Build',
    'release' => 'Release',
    'update' => 'Update',
    'upgrade' => 'Upgrade',
    'maintenance' => 'Maintenance',
    'backup' => 'Backup',
    'restore' => 'Restore',
    'sync' => 'Sync',
    'cache' => 'Cache',
    'session' => 'Session',
    'cookie' => 'Cookie',
    'token' => 'Token',
    'key' => 'Key',
    'secret' => 'Secret',
    'hash' => 'Hash',
    'encryption' => 'Encryption',
    'security' => 'Security',

    // ===== MESSAGES =====
    'message' => 'Message',
    'notification' => 'Notification',
    'alert' => 'Alert',
    'announcement' => 'Announcement',
    'news' => 'News',
    'update_message' => 'Update',
    'error_message' => 'Error',
    'warning_message' => 'Warning',
    'success_message' => 'Success',
    'info_message' => 'Information',

    // ===== LANGUAGE & LOCALIZATION =====
    'language' => 'Language',
    'locale' => 'Locale',
    'translation' => 'Translation',
    'english' => 'English',
    'arabic' => 'Arabic',
    'switch_language' => 'Switch Language',
    'change_language' => 'Change Language',
    'select_language' => 'Select Language',
    'current_language' => 'Current Language',
    'default_language' => 'Default Language',
    'language_switched' => 'Language switched successfully',
    'unsupported_language' => 'Unsupported language',

    // ===== CURRENCY =====
    'currency' => 'Currency',
    'aed' => 'AED',
    'dirham' => 'Dirham',
    'dirhams' => 'Dirhams',
    'fils' => 'Fils',
    'free' => 'Free',
    'paid' => 'Paid',
    'unpaid' => 'Unpaid',
    'payment' => 'Payment',
    'invoice' => 'Invoice',
    'receipt' => 'Receipt',
    'transaction' => 'Transaction',

    // ===== COMMON PHRASES =====
    'no_data' => 'No data available',
    'no_results' => 'No results found',
    'no_items' => 'No items found',
    'empty_list' => 'List is empty',
    'coming_soon' => 'Coming Soon',
    'under_construction' => 'Under Construction',
    'maintenance_mode' => 'Maintenance Mode',
    'page_not_found' => 'Page Not Found',
    'access_denied' => 'Access Denied',
    'unauthorized' => 'Unauthorized',
    'forbidden' => 'Forbidden',
    'something_went_wrong' => 'Something went wrong',
    'try_again' => 'Try again',
    'contact_support' => 'Contact support',
    'get_help' => 'Get help',

    // ===== MISSING TRANSLATIONS FOR AUTO-TRANSLATION SYSTEM =====

    // Table headers and form fields
    'id' => 'ID',
    'name' => 'Name',
    'student' => 'Student',
    'academy' => 'Academy',
    'branch' => 'Branch',
    'status' => 'Status',
    'actions' => 'Actions',
    'date' => 'Date',
    'amount' => 'Amount',
    'amount_aed' => 'Amount (AED)',
    'student_info' => 'Student Info',
    'contact_location' => 'Contact & Location',
    'academy_name' => 'Academy Name',
    'branch_name' => 'Branch Name',
    'coach' => 'Coach',
    'programs' => 'Programs',
    'students' => 'Students',
    'revenue_aed' => 'Revenue (AED)',
    'created' => 'Created',
    'join_date' => 'Join Date',
    'order_date' => 'Order Date',
    'delivery_date' => 'Delivery Date',
    'item' => 'Item',
    'size' => 'Size',
    'quantity' => 'Quantity',

    // Status values
    'active' => 'Active',
    'inactive' => 'Inactive',
    'pending' => 'Pending',
    'expired' => 'Expired',
    'completed' => 'Completed',
    'processing' => 'Processing',
    'cancelled' => 'Cancelled',
    'approved' => 'Approved',
    'rejected' => 'Rejected',

    // System messages
    'no_students_found' => 'No students found.',
    'no_payments_found' => 'No payments found.',
    'no_data_available' => 'No data available',
    'no_results_found' => 'No results found',
    'data_updated_successfully' => 'Data updated successfully',
    'operation_completed_successfully' => 'Operation completed successfully',
    'error_occurred' => 'Error occurred',
    'please_try_again' => 'Please try again',

    // Form labels
    'full_name' => 'Full Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'address' => 'Address',
    'nationality' => 'Nationality',
    'birth_date' => 'Birth Date',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'role' => 'Role',
    'description' => 'Description',
    'notes' => 'Notes',
    'location' => 'Location',
    'manager' => 'Manager',
    'contact_person' => 'Contact Person',
    'price' => 'Price',
    'days' => 'Days',
    'classes' => 'Classes',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'payment_method' => 'Payment Method',
    'discount' => 'Discount',
    'class_time' => 'Class Time',
    'from' => 'From',
    'to' => 'To',

    // Additional actions
    'submit' => 'Submit',
    'reset' => 'Reset',
    'confirm' => 'Confirm',
    'create_order' => 'Create Order',
    'view_all' => 'View All',
    'not_available' => 'N/A',
    'currency_aed' => 'UAE Dirham',
];
