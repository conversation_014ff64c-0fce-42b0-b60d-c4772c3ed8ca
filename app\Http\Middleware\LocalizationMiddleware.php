<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;
use Symfony\Component\HttpFoundation\Response;

class LocalizationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get supported locales from config
        $supportedLocales = config('app.supported_locales', ['en', 'ar']);
        $defaultLocale = config('app.locale', 'en');

        // Determine the locale from various sources (priority order)
        $locale = $this->determineLocale($request, $supportedLocales, $defaultLocale);

        // Validate and set the locale
        if (in_array($locale, $supportedLocales)) {
            App::setLocale($locale);

            // Store locale in session for persistence
            Session::put('locale', $locale);

            // Store locale in cookie for long-term persistence (30 days)
            Cookie::queue('locale', $locale, 60 * 24 * 30);

            // Set direction attribute for RTL/LTR support
            $direction = $this->getTextDirection($locale);
            view()->share('currentLocale', $locale);
            view()->share('textDirection', $direction);
            view()->share('isRtl', $direction === 'rtl');

            // Share common translations for JavaScript access
            view()->share('commonTranslations', $this->getCommonTranslations($locale));
            view()->share('dashboardTranslations', $this->getDashboardTranslations($locale));
        }

        return $next($request);
    }

    /**
     * Determine the locale from various sources
     *
     * @param Request $request
     * @param array $supportedLocales
     * @param string $defaultLocale
     * @return string
     */
    private function determineLocale(Request $request, array $supportedLocales, string $defaultLocale): string
    {
        // 1. Check URL parameter (highest priority)
        if ($request->has('lang') && in_array($request->get('lang'), $supportedLocales)) {
            return $request->get('lang');
        }

        // 2. Check session
        if (Session::has('locale') && in_array(Session::get('locale'), $supportedLocales)) {
            return Session::get('locale');
        }

        // 3. Check cookie
        if ($request->hasCookie('locale') && in_array($request->cookie('locale'), $supportedLocales)) {
            return $request->cookie('locale');
        }

        // 4. Check user preference (if authenticated)
        if (auth()->check() && auth()->user()->language_preference) {
            $userLang = auth()->user()->language_preference;
            if (in_array($userLang, $supportedLocales)) {
                return $userLang;
            }
        }

        // 5. Auto-detect from browser Accept-Language header
        $browserLocale = $this->detectBrowserLocale($request, $supportedLocales);
        if ($browserLocale) {
            return $browserLocale;
        }

        // 6. Fallback to default locale
        return $defaultLocale;
    }

    /**
     * Detect locale from browser Accept-Language header
     *
     * @param Request $request
     * @param array $supportedLocales
     * @return string|null
     */
    private function detectBrowserLocale(Request $request, array $supportedLocales): ?string
    {
        $acceptLanguage = $request->header('Accept-Language');

        if (!$acceptLanguage) {
            return null;
        }

        // Parse Accept-Language header
        $languages = [];
        foreach (explode(',', $acceptLanguage) as $lang) {
            $parts = explode(';', trim($lang));
            $locale = trim($parts[0]);
            $quality = 1.0;

            if (isset($parts[1]) && strpos($parts[1], 'q=') === 0) {
                $quality = (float) substr($parts[1], 2);
            }

            $languages[$locale] = $quality;
        }

        // Sort by quality (preference)
        arsort($languages);

        // Find the best match
        foreach ($languages as $locale => $quality) {
            // Check exact match
            if (in_array($locale, $supportedLocales)) {
                return $locale;
            }

            // Check language part only (e.g., 'ar' from 'ar-SA')
            $langPart = substr($locale, 0, 2);
            if (in_array($langPart, $supportedLocales)) {
                return $langPart;
            }
        }

        return null;
    }

    /**
     * Get text direction for the given locale
     *
     * @param string $locale
     * @return string
     */
    private function getTextDirection(string $locale): string
    {
        // RTL languages
        $rtlLanguages = ['ar', 'he', 'fa', 'ur'];

        return in_array($locale, $rtlLanguages) ? 'rtl' : 'ltr';
    }

    /**
     * Check if the given locale is RTL
     *
     * @param string $locale
     * @return bool
     */
    public static function isRtlLocale(string $locale): bool
    {
        $rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        return in_array($locale, $rtlLanguages);
    }

    /**
     * Get the opposite direction
     *
     * @param string $direction
     * @return string
     */
    public static function getOppositeDirection(string $direction): string
    {
        return $direction === 'rtl' ? 'ltr' : 'rtl';
    }

    /**
     * Get locale-specific number formatting
     *
     * @param float $number
     * @param string $locale
     * @return string
     */
    public static function formatNumber(float $number, string $locale = null): string
    {
        $locale = $locale ?: App::getLocale();

        if ($locale === 'ar') {
            // Arabic-Indic numerals (optional)
            $formatted = number_format($number, 2, '.', ',');
            // Convert to Arabic-Indic numerals if needed
            // $formatted = strtr($formatted, '0123456789', '٠١٢٣٤٥٦٧٨٩');
            return $formatted;
        }

        return number_format($number, 2, '.', ',');
    }

    /**
     * Get locale-specific currency formatting
     *
     * @param float $amount
     * @param string $currency
     * @param string $locale
     * @return string
     */
    public static function formatCurrency(float $amount, string $currency = 'AED', string $locale = null): string
    {
        $locale = $locale ?: App::getLocale();
        $formattedNumber = self::formatNumber($amount, $locale);

        if ($locale === 'ar') {
            return $formattedNumber . ' ' . $currency;
        }

        return $currency . ' ' . $formattedNumber;
    }

    /**
     * Get common translations for JavaScript access
     *
     * @param string $locale
     * @return array
     */
    private function getCommonTranslations(string $locale): array
    {
        try {
            return trans('common', [], $locale);
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get dashboard translations for JavaScript access
     *
     * @param string $locale
     * @return array
     */
    private function getDashboardTranslations(string $locale): array
    {
        try {
            return trans('dashboard', [], $locale);
        } catch (\Exception $e) {
            return [];
        }
    }
}
