@extends('layouts.dashboard')

@section('title', 'Edit User - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-warning-orange to-orange-600 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Edit User</h1>
                <p class="text-lg text-dark-gray">Update {{ $user->name }}'s information</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('users.show', $user) }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                    </path>
                </svg>
                View User
            </a>
            <a href="{{ route('users.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Users
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="userForm()" x-init="init()">
        <form method="POST" action="{{ route('users.update', $user) }}" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                    <p class="bank-card-subtitle">Update the user's basic details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Name -->
                        <div>
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" value="{{ old('name', $user->name) }}" required
                                class="form-control-bank @error('name') border-error-red @enderror"
                                placeholder="Enter full name">
                            @error('name')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}" required
                                class="form-control-bank @error('email') border-error-red @enderror"
                                placeholder="Enter email address">
                            @error('email')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Password -->
                        <div>
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" id="password" name="password"
                                class="form-control-bank @error('password') border-error-red @enderror"
                                placeholder="Leave blank to keep current password">
                            @error('password')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                            <p class="text-sm text-dark-gray mt-1">Leave blank to keep the current password</p>
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <label for="password_confirmation" class="form-label">Confirm New Password</label>
                            <input type="password" id="password_confirmation" name="password_confirmation"
                                class="form-control-bank"
                                placeholder="Confirm new password">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Role & Access Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Role & Access</h3>
                    <p class="bank-card-subtitle">Update user role and permissions</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Role -->
                        <div>
                            <label for="role" class="form-label">User Role *</label>
                            <select id="role" name="role" x-model="selectedRole" @change="handleRoleChange()" required
                                class="form-select-bank @error('role') border-error-red @enderror">
                                <option value="">Select Role</option>
                                <option value="admin" {{ old('role', $user->role) === 'admin' ? 'selected' : '' }}>
                                    System Administrator
                                </option>
                                <option value="branch_manager" {{ old('role', $user->role) === 'branch_manager' ? 'selected' : '' }}>
                                    Branch Manager
                                </option>
                                <option value="academy_manager" {{ old('role', $user->role) === 'academy_manager' ? 'selected' : '' }}>
                                    Academy Manager
                                </option>
                            </select>
                            @error('role')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Branch -->
                        <div x-show="selectedRole === 'branch_manager' || selectedRole === 'academy_manager'">
                            <label for="branch_id" class="form-label">Branch *</label>
                            <select id="branch_id" name="branch_id" x-model="selectedBranch" @change="loadAcademies()"
                                class="form-select-bank @error('branch_id') border-error-red @enderror">
                                <option value="">Select Branch</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}" {{ old('branch_id', $user->branch_id) == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('branch_id')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Academy -->
                        <div x-show="selectedRole === 'academy_manager'">
                            <label for="academy_id" class="form-label">Academy *</label>
                            <select id="academy_id" name="academy_id" x-model="selectedAcademy"
                                class="form-select-bank @error('academy_id') border-error-red @enderror">
                                <option value="">Select Academy</option>
                                <template x-for="academy in academies" :key="academy.id">
                                    <option :value="academy.id" x-text="academy.name" 
                                        :selected="academy.id == selectedAcademy"></option>
                                </template>
                            </select>
                            @error('academy_id')
                                <p class="text-error-red text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Current Assignment Info -->
                    <div class="mt-6 p-4 bg-off-white rounded-lg">
                        <h4 class="font-semibold text-charcoal-black mb-2">Current Assignment</h4>
                        <div class="space-y-1">
                            <p class="text-sm text-dark-gray">
                                <strong>Role:</strong> {{ $user->role_text }}
                            </p>
                            @if($user->branch)
                                <p class="text-sm text-dark-gray">
                                    <strong>Branch:</strong> {{ $user->branch->name }}
                                </p>
                            @endif
                            @if($user->academy)
                                <p class="text-sm text-dark-gray">
                                    <strong>Academy:</strong> {{ $user->academy->name }}
                                </p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Account Status</h3>
                    <p class="bank-card-subtitle">Update the account status</p>
                </div>
                <div class="bank-card-body">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" id="status" name="status" value="1" 
                            {{ old('status', $user->status) ? 'checked' : '' }}
                            class="form-checkbox-bank">
                        <label for="status" class="form-label mb-0">Active Account</label>
                    </div>
                    <p class="text-sm text-dark-gray mt-2">When unchecked, the user will not be able to log in.</p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('users.show', $user) }}" class="btn-bank btn-bank-outline">
                    Cancel
                </a>
                <button type="submit" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Update User
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function userForm() {
            return {
                selectedRole: '{{ old('role', $user->role) }}',
                selectedBranch: '{{ old('branch_id', $user->branch_id) }}',
                selectedAcademy: '{{ old('academy_id', $user->academy_id) }}',
                academies: [],

                init() {
                    if (this.selectedBranch) {
                        this.loadAcademies();
                    }
                },

                handleRoleChange() {
                    if (this.selectedRole === 'admin') {
                        this.selectedBranch = '';
                        this.selectedAcademy = '';
                        this.academies = [];
                    }
                },

                async loadAcademies() {
                    if (!this.selectedBranch) {
                        this.academies = [];
                        this.selectedAcademy = '';
                        return;
                    }

                    try {
                        const response = await fetch(`{{ route('users.academies-by-branch') }}?branch_id=${this.selectedBranch}`);
                        const data = await response.json();
                        this.academies = data.academies;
                        
                        // Keep current academy selection if it's in the new list
                        if (this.selectedAcademy && !this.academies.find(a => a.id == this.selectedAcademy)) {
                            this.selectedAcademy = '';
                        }
                    } catch (error) {
                        console.error('Error loading academies:', error);
                        this.academies = [];
                    }
                }
            }
        }
    </script>
@endpush
