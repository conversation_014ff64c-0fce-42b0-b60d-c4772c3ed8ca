/* UAE English Sports Academy - Language Switcher Component */
/* Premium Language Toggle with Smooth Transitions */

/* ===== LANGUAGE SWITCHER BASE ===== */

.language-switcher {
    position: relative;
    display: inline-flex;
    align-items: center;
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-button);
    padding: var(--space-xs) var(--space-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-card);
    min-width: 80px;
    justify-content: center;
}

.language-switcher:hover {
    background: var(--off-white);
    border-color: var(--leaders-red);
    box-shadow: var(--shadow-hover);
    transform: translateY(-1px);
}

.language-switcher:active {
    transform: translateY(0);
    box-shadow: var(--shadow-card);
}

/* ===== LANGUAGE SWITCHER CONTENT ===== */

.language-switcher-icon {
    width: 16px;
    height: 16px;
    margin-right: var(--space-xs);
    color: var(--dark-gray);
    transition: color var(--transition-fast);
}

.language-switcher:hover .language-switcher-icon {
    color: var(--leaders-red);
}

.language-switcher-text {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--charcoal-black);
    transition: color var(--transition-fast);
    letter-spacing: 0.025em;
}

.language-switcher:hover .language-switcher-text {
    color: var(--leaders-red);
}

/* ===== LANGUAGE SWITCHER STATES ===== */

/* Active Language State */
.language-switcher.active {
    background: var(--leaders-red);
    border-color: var(--leaders-red);
    color: var(--pure-white);
}

.language-switcher.active .language-switcher-icon,
.language-switcher.active .language-switcher-text {
    color: var(--pure-white);
}

.language-switcher.active:hover {
    background: var(--deep-red);
    border-color: var(--deep-red);
}

/* Loading State */
.language-switcher.loading {
    pointer-events: none;
    opacity: 0.7;
}

.language-switcher.loading .language-switcher-icon {
    animation: spin 1s linear infinite;
}

/* ===== LANGUAGE SWITCHER DROPDOWN ===== */

.language-switcher-dropdown {
    position: relative;
    display: inline-block;
}

.language-switcher-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--pure-white);
    border: 1px solid var(--medium-gray);
    border-radius: var(--radius-card);
    box-shadow: var(--shadow-bank);
    min-width: 160px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    margin-top: var(--space-xs);
}

.language-switcher-dropdown.open .language-switcher-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-switcher-menu-item {
    display: flex;
    align-items: center;
    padding: var(--space-sm) var(--space-md);
    color: var(--charcoal-black);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-bottom: 1px solid var(--light-gray);
}

.language-switcher-menu-item:last-child {
    border-bottom: none;
}

.language-switcher-menu-item:hover {
    background: var(--off-white);
    color: var(--leaders-red);
}

.language-switcher-menu-item.active {
    background: var(--leaders-red);
    color: var(--pure-white);
}

.language-switcher-menu-item.active:hover {
    background: var(--deep-red);
}

/* ===== LANGUAGE SWITCHER VARIANTS ===== */

/* Compact Variant */
.language-switcher.compact {
    padding: var(--space-xs);
    min-width: 60px;
}

.language-switcher.compact .language-switcher-icon {
    margin-right: 0;
}

.language-switcher.compact .language-switcher-text {
    display: none;
}

/* Large Variant */
.language-switcher.large {
    padding: var(--space-sm) var(--space-lg);
    font-size: 1rem;
    min-width: 120px;
}

/* Outline Variant */
.language-switcher.outline {
    background: transparent;
    border: 2px solid var(--leaders-red);
    color: var(--leaders-red);
}

.language-switcher.outline:hover {
    background: var(--leaders-red);
    color: var(--pure-white);
}

.language-switcher.outline .language-switcher-icon,
.language-switcher.outline .language-switcher-text {
    color: inherit;
}

/* ===== RTL SUPPORT ===== */

/* RTL Language Switcher */
[dir="rtl"] .language-switcher {
    direction: ltr; /* Keep switcher LTR for consistency */
}

[dir="rtl"] .language-switcher-icon {
    margin-right: 0;
    margin-left: var(--space-xs);
    order: 2;
}

[dir="rtl"] .language-switcher-text {
    order: 1;
}

[dir="rtl"] .language-switcher-menu {
    right: auto;
    left: 0;
}

[dir="rtl"] .language-switcher-menu-item {
    text-align: right;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Language Switcher */
@media (max-width: 768px) {
    .language-switcher {
        min-width: 60px;
        padding: var(--space-xs);
    }

    .language-switcher-text {
        display: none;
    }

    .language-switcher-icon {
        margin-right: 0;
    }

    .language-switcher-menu {
        min-width: 140px;
    }
}

/* Tablet Adjustments */
@media (max-width: 1024px) {
    .language-switcher-menu {
        min-width: 150px;
    }
}

/* ===== ANIMATIONS ===== */

/* Spin Animation for Loading */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Pulse Animation for Active State */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(229, 62, 62, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(229, 62, 62, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(229, 62, 62, 0);
    }
}

.language-switcher.pulse {
    animation: pulse 2s infinite;
}

/* ===== ACCESSIBILITY ===== */

/* Focus States */
.language-switcher:focus {
    outline: 2px solid var(--leaders-red);
    outline-offset: 2px;
}

.language-switcher-menu-item:focus {
    background: var(--off-white);
    outline: 2px solid var(--leaders-red);
    outline-offset: -2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .language-switcher {
        border-width: 2px;
    }

    .language-switcher:hover {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .language-switcher,
    .language-switcher-icon,
    .language-switcher-text,
    .language-switcher-menu {
        transition: none;
    }

    .language-switcher.loading .language-switcher-icon {
        animation: none;
    }
}

/* ===== INTEGRATION STYLES ===== */

/* Header Integration */
.header .language-switcher {
    margin-left: var(--space-md);
}

[dir="rtl"] .header .language-switcher {
    margin-left: 0;
    margin-right: var(--space-md);
}

/* Sidebar Integration */
.sidebar .language-switcher {
    width: 100%;
    justify-content: flex-start;
    margin-bottom: var(--space-md);
}

.sidebar .language-switcher-text {
    display: block;
}

/* Footer Integration */
.footer .language-switcher {
    margin-left: auto;
}

[dir="rtl"] .footer .language-switcher {
    margin-left: 0;
    margin-right: auto;
}

/* ===== THEME VARIANTS ===== */

/* Dark Theme Support */
.dark .language-switcher {
    background: var(--charcoal-black);
    border-color: var(--dark-gray);
    color: var(--pure-white);
}

.dark .language-switcher:hover {
    background: var(--dark-gray);
    border-color: var(--leaders-red);
}

.dark .language-switcher-menu {
    background: var(--charcoal-black);
    border-color: var(--dark-gray);
}

.dark .language-switcher-menu-item {
    color: var(--pure-white);
    border-color: var(--dark-gray);
}

.dark .language-switcher-menu-item:hover {
    background: var(--dark-gray);
}
