<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attendances', function (Blueprint $table) {
            // Add new columns for enhanced functionality
            $table->foreignId('branch_id')->nullable()->constrained()->onDelete('cascade')->after('student_id');
            $table->foreignId('academy_id')->nullable()->constrained()->onDelete('cascade')->after('branch_id');
            $table->foreignId('program_id')->nullable()->constrained()->onDelete('set null')->after('academy_id');
            $table->date('session_date')->nullable()->after('program_id'); // Alternative to 'date' for clarity
            $table->text('notes')->nullable()->after('note'); // Additional notes field
            $table->foreignId('marked_by')->nullable()->constrained('users')->onDelete('set null')->after('notes');

            // Modify status enum to include more attendance statuses
            $table->enum('status', ['present', 'absent', 'late', 'excused'])->default('present')->change();

            // Add indexes for new columns
            $table->index(['branch_id', 'academy_id']);
            $table->index('program_id');
            $table->index('session_date');
            $table->index('marked_by');

            // Drop the unique constraint to allow multiple sessions per day
            $table->dropUnique(['student_id', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attendances', function (Blueprint $table) {
            // Drop indexes
            $table->dropIndex(['branch_id', 'academy_id']);
            $table->dropIndex(['program_id']);
            $table->dropIndex(['session_date']);
            $table->dropIndex(['marked_by']);

            // Drop new columns
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['academy_id']);
            $table->dropForeign(['program_id']);
            $table->dropForeign(['marked_by']);

            $table->dropColumn([
                'branch_id',
                'academy_id',
                'program_id',
                'session_date',
                'notes',
                'marked_by'
            ]);

            // Revert status enum to original values
            $table->enum('status', ['present', 'absent', 'late'])->default('present')->change();

            // Restore the unique constraint
            $table->unique(['student_id', 'date']);
        });
    }
};
