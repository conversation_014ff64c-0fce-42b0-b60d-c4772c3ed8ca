@extends('layouts.dashboard')

@section('title', 'Reservations Calendar - LEADERS SPORTS SERVICES')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-8 0H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Reservations Calendar</h1>
                <p class="text-lg text-dark-gray">View and manage field reservations in calendar format</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('reservations.index') }}"
                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 6h18m-9 10h9">
                    </path>
                </svg>
                List View
            </a>
            @can('create', App\Models\Reservation::class)
                <a href="{{ route('reservations.create') }}"
                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                        </path>
                    </svg>
                    New Reservation
                </a>
            @endcan
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="calendarManager()">
        <!-- Filters -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="bank-card-title">Filters</h3>
                <p class="bank-card-subtitle">Filter reservations by venue and field</p>
            </div>
            <div class="bank-card-body">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Venue Filter -->
                    <div>
                        <label for="venue_filter" class="form-label-bank">Venue</label>
                        <select id="venue_filter" class="form-select-bank" @change="onVenueFilterChange($event)">
                            <option value="">All Venues</option>
                            @foreach ($venues as $venue)
                                <option value="{{ $venue->id }}">{{ $venue->localized_name }}</option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Field Filter -->
                    <div>
                        <label for="field_filter" class="form-label-bank">Field</label>
                        <select id="field_filter" class="form-select-bank" @change="onFieldFilterChange($event)">
                            <option value="">All Fields</option>
                            @foreach ($fields as $field)
                                <option value="{{ $field->id }}" data-venue="{{ $field->venue_id }}"
                                    style="display: none;">
                                    {{ $field->localized_name }} ({{ $field->venue->localized_name }})
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- View Options -->
                    <div>
                        <label class="form-label-bank">Calendar View</label>
                        <div class="flex items-center space-x-2">
                            <button @click="changeView('dayGridMonth')"
                                :class="currentView === 'dayGridMonth' ? 'bg-leaders-red text-white' :
                                    'bg-gray-200 text-gray-700'"
                                class="px-3 py-2 rounded text-sm font-medium transition-colors">
                                Month
                            </button>
                            <button @click="changeView('timeGridWeek')"
                                :class="currentView === 'timeGridWeek' ? 'bg-leaders-red text-white' :
                                    'bg-gray-200 text-gray-700'"
                                class="px-3 py-2 rounded text-sm font-medium transition-colors">
                                Week
                            </button>
                            <button @click="changeView('timeGridDay')"
                                :class="currentView === 'timeGridDay' ? 'bg-leaders-red text-white' :
                                    'bg-gray-200 text-gray-700'"
                                class="px-3 py-2 rounded text-sm font-medium transition-colors">
                                Day
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Calendar -->
        <div class="bank-card">
            <div class="bank-card-body p-0">
                <div id="calendar" class="p-4"></div>
            </div>
        </div>

        <!-- Legend -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="bank-card-title">Status Legend</h3>
            </div>
            <div class="bank-card-body">
                <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded" style="background-color: #fbbf24;"></div>
                        <span class="text-sm text-charcoal-black">Pending</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded" style="background-color: #10b981;"></div>
                        <span class="text-sm text-charcoal-black">Confirmed</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded" style="background-color: #3b82f6;"></div>
                        <span class="text-sm text-charcoal-black">In Progress</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded" style="background-color: #6b7280;"></div>
                        <span class="text-sm text-charcoal-black">Completed</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded" style="background-color: #ef4444;"></div>
                        <span class="text-sm text-charcoal-black">Cancelled</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="w-4 h-4 rounded" style="background-color: #f59e0b;"></div>
                        <span class="text-sm text-charcoal-black">No Show</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reservation Details Modal -->
    <div x-show="showModal" x-transition class="fixed inset-0 z-50 overflow-y-auto" style="display: none;">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="showModal" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
                x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
                class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75" @click="closeModal()"></div>
            </div>

            <div x-show="showModal" x-transition:enter="ease-out duration-300"
                x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200"
                x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">

                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4" x-show="selectedReservation">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                                Reservation Details
                            </h3>

                            <div class="space-y-3" x-show="selectedReservation">
                                <div>
                                    <span class="font-medium text-gray-700">Reservation #:</span>
                                    <span x-text="selectedReservation?.extendedProps?.reservation_number"></span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Customer:</span>
                                    <span x-text="selectedReservation?.extendedProps?.customer"></span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Venue:</span>
                                    <span x-text="selectedReservation?.extendedProps?.venue"></span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Field:</span>
                                    <span x-text="selectedReservation?.extendedProps?.field"></span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Time:</span>
                                    <span x-text="selectedReservation?.extendedProps?.time_slot"></span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Status:</span>
                                    <span x-text="selectedReservation?.extendedProps?.status"></span>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">Total Amount:</span>
                                    <span x-text="selectedReservation?.extendedProps?.total_amount"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <a :href="selectedReservation ? `/reservations/${selectedReservation.id}` : '#'"
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-leaders-red text-base font-medium text-white hover:bg-leaders-deep-red focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-leaders-red sm:ml-3 sm:w-auto sm:text-sm">
                        View Details
                    </a>
                    <button type="button" @click="closeModal()"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">
@endpush

@push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    <script>
        function calendarManager() {
            return {
                calendar: null,
                currentView: 'dayGridMonth',
                selectedVenue: '',
                selectedField: '',
                showModal: false,
                selectedReservation: null,

                init() {
                    this.$nextTick(() => {
                        this.initializeCalendar();
                    });
                },

                initializeCalendar() {
                    const calendarEl = document.getElementById('calendar');

                    this.calendar = new FullCalendar.Calendar(calendarEl, {
                        initialView: 'dayGridMonth',
                        headerToolbar: {
                            left: 'prev,next today',
                            center: 'title',
                            right: 'dayGridMonth,timeGridWeek,timeGridDay'
                        },
                        height: 'auto',
                        events: this.fetchEvents.bind(this),
                        eventClick: this.handleEventClick.bind(this),
                        eventDidMount: this.handleEventMount.bind(this),
                        loading: this.handleLoading.bind(this),
                        viewDidMount: this.handleViewChange.bind(this),
                        eventTimeFormat: {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        },
                        slotLabelFormat: {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        },
                        businessHours: {
                            daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // Sunday - Saturday
                            startTime: '06:00',
                            endTime: '23:00'
                        },
                        slotMinTime: '06:00:00',
                        slotMaxTime: '23:00:00',
                        allDaySlot: false,
                        nowIndicator: true,
                        eventDisplay: 'block',
                        dayMaxEvents: 3,
                        moreLinkClick: 'popover'
                    });

                    this.calendar.render();
                },

                fetchEvents(info, successCallback, failureCallback) {
                    const params = new URLSearchParams({
                        start: info.startStr,
                        end: info.endStr
                    });

                    if (this.selectedVenue) {
                        params.append('venue_id', this.selectedVenue);
                    }

                    if (this.selectedField) {
                        params.append('field_id', this.selectedField);
                    }

                    fetch(`{{ route('api.reservations.calendar') }}?${params.toString()}`)
                        .then(response => response.json())
                        .then(data => {
                            successCallback(data);
                        })
                        .catch(error => {
                            console.error('Error fetching events:', error);
                            failureCallback(error);
                        });
                },

                handleEventClick(info) {
                    this.selectedReservation = info.event;
                    this.showModal = true;
                },

                handleEventMount(info) {
                    // Add tooltip
                    info.el.title =
                        `${info.event.extendedProps.customer} - ${info.event.extendedProps.field}\n${info.event.extendedProps.time_slot}`;
                },

                handleLoading(isLoading) {
                    // You can add loading indicator here
                    if (isLoading) {
                        console.log('Loading calendar events...');
                    }
                },

                handleViewChange(info) {
                    this.currentView = info.view.type;
                },

                changeView(viewName) {
                    this.calendar.changeView(viewName);
                    this.currentView = viewName;
                },

                onVenueFilterChange(event) {
                    this.selectedVenue = event.target.value;
                    this.updateFieldOptions();
                    this.refreshCalendar();
                },

                onFieldFilterChange(event) {
                    this.selectedField = event.target.value;
                    this.refreshCalendar();
                },

                updateFieldOptions() {
                    const fieldSelect = document.getElementById('field_filter');
                    const fieldOptions = fieldSelect.querySelectorAll('option[data-venue]');

                    // Reset field selection
                    fieldSelect.value = '';
                    this.selectedField = '';

                    // Show/hide field options based on selected venue
                    fieldOptions.forEach(option => {
                        if (!this.selectedVenue || option.dataset.venue === this.selectedVenue) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                        }
                    });
                },

                refreshCalendar() {
                    if (this.calendar) {
                        this.calendar.refetchEvents();
                    }
                },

                closeModal() {
                    this.showModal = false;
                    this.selectedReservation = null;
                }
            }
        }

        // Additional utility functions
        document.addEventListener('DOMContentLoaded', function() {
            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    // Close modal if open
                    const calendarData = document.querySelector('[x-data*="calendarManager"]')?.__x?.$data;
                    if (calendarData?.showModal) {
                        calendarData.closeModal();
                    }
                }
            });
        });
    </script>
@endpush
