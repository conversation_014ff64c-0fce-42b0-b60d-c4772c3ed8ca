@extends('layouts.dashboard')

@section('title', 'Daily Reports - UAE English Sports Academy')

@section('header')
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Daily Reports</h1>
            <p class="text-gray-600 mt-1">Daily activity summaries and operational metrics for {{ \Carbon\Carbon::parse($dailyData['date'])->format('M d, Y') }}</p>
        </div>
        <div class="flex items-center gap-3">
            <button onclick="exportToPDF()" class="btn-bank-outline">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export PDF
            </button>
            <button onclick="exportToExcel()" class="btn-bank-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export Excel
            </button>
        </div>
    </div>
@endsection

@section('content')
    <!-- Filters -->
    @include('reports._filters')

    <!-- Daily Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
        <!-- Total Registrations -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">New Registrations</p>
                    <p class="text-2xl font-bold text-blue-600">{{ number_format($dailyData['summary']['total_registrations']) }}</p>
                </div>
                <div class="p-3 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Total Payments -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Payments Made</p>
                    <p class="text-2xl font-bold text-green-600">{{ number_format($dailyData['summary']['total_payments']) }}</p>
                </div>
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Payment Amount -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Payment Amount</p>
                    <p class="text-2xl font-bold text-green-600">AED {{ number_format($dailyData['summary']['total_payment_amount'], 2) }}</p>
                </div>
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Uniform Orders -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Uniform Orders</p>
                    <p class="text-2xl font-bold text-purple-600">{{ number_format($dailyData['summary']['total_uniforms']) }}</p>
                </div>
                <div class="p-3 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Uniform Amount -->
        <div class="bank-card">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-600">Uniform Amount</p>
                    <p class="text-2xl font-bold text-purple-600">AED {{ number_format($dailyData['summary']['total_uniform_amount'], 2) }}</p>
                </div>
                <div class="p-3 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Activities -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- New Registrations -->
        <div class="bank-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">New Registrations</h3>
                <span class="text-sm text-gray-500">{{ $dailyData['registrations']->count() }} students</span>
            </div>
            <div class="space-y-3 max-h-96 overflow-y-auto">
                @forelse($dailyData['registrations'] as $student)
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-sm">{{ $student->full_name }}</p>
                            <p class="text-xs text-gray-600">{{ $student->academy->name }}</p>
                            <p class="text-xs text-gray-500">{{ $student->branch->name }}</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                {{ ucfirst($student->status) }}
                            </span>
                            <p class="text-xs text-gray-500 mt-1">{{ $student->created_at->format('H:i') }}</p>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        <p class="text-gray-500 text-sm">No new registrations today</p>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Daily Payments -->
        <div class="bank-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Daily Payments</h3>
                <span class="text-sm text-gray-500">{{ $dailyData['payments']->count() }} payments</span>
            </div>
            <div class="space-y-3 max-h-96 overflow-y-auto">
                @forelse($dailyData['payments'] as $payment)
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-sm">{{ $payment->student->full_name }}</p>
                            <p class="text-xs text-gray-600">{{ $payment->academy->name }}</p>
                            <p class="text-xs text-gray-500">{{ ucfirst(str_replace('_', ' ', $payment->payment_method)) }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-green-600">AED {{ number_format($payment->amount, 2) }}</p>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                {{ $payment->status === 'completed' ? 'bg-green-100 text-green-800' : '' }}
                                {{ $payment->status === 'pending' ? 'bg-yellow-100 text-yellow-800' : '' }}
                                {{ $payment->status === 'failed' ? 'bg-red-100 text-red-800' : '' }}">
                                {{ ucfirst($payment->status) }}
                            </span>
                            <p class="text-xs text-gray-500 mt-1">{{ $payment->payment_date->format('H:i') }}</p>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                        <p class="text-gray-500 text-sm">No payments made today</p>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Daily Uniform Orders -->
        <div class="bank-card">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Uniform Orders</h3>
                <span class="text-sm text-gray-500">{{ $dailyData['uniforms']->count() }} orders</span>
            </div>
            <div class="space-y-3 max-h-96 overflow-y-auto">
                @forelse($dailyData['uniforms'] as $uniform)
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                            <p class="font-medium text-sm">{{ $uniform->student->full_name }}</p>
                            <p class="text-xs text-gray-600">{{ $uniform->academy->name }}</p>
                            <p class="text-xs text-gray-500">Size: {{ $uniform->size }}</p>
                        </div>
                        <div class="text-right">
                            <p class="font-medium text-purple-600">AED {{ number_format($uniform->amount, 2) }}</p>
                            <div class="flex flex-col gap-1">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $uniform->branch_status === 'pending' ? 'bg-orange-100 text-orange-800' : '' }}
                                    {{ $uniform->branch_status === 'received' ? 'bg-blue-100 text-blue-800' : '' }}
                                    {{ $uniform->branch_status === 'delivered' ? 'bg-green-100 text-green-800' : '' }}">
                                    B: {{ ucfirst($uniform->branch_status) }}
                                </span>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {{ $uniform->office_status === 'pending' ? 'bg-orange-100 text-orange-800' : '' }}
                                    {{ $uniform->office_status === 'received' ? 'bg-blue-100 text-blue-800' : '' }}
                                    {{ $uniform->office_status === 'delivered' ? 'bg-green-100 text-green-800' : '' }}">
                                    O: {{ ucfirst($uniform->office_status) }}
                                </span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">{{ $uniform->order_date->format('H:i') }}</p>
                        </div>
                    </div>
                @empty
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                        </svg>
                        <p class="text-gray-500 text-sm">No uniform orders today</p>
                    </div>
                @endforelse
            </div>
        </div>
    </div>

    <!-- Daily Summary Table -->
    <div class="bank-card">
        <h3 class="text-lg font-semibold text-gray-900 mb-6">Daily Activity Summary</h3>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount (AED)</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average (AED)</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="p-2 bg-blue-100 rounded-lg mr-3">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                                    </svg>
                                </div>
                                <div class="text-sm font-medium text-gray-900">New Student Registrations</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($dailyData['summary']['total_registrations']) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Student enrollments</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="p-2 bg-green-100 rounded-lg mr-3">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                </div>
                                <div class="text-sm font-medium text-gray-900">Payment Transactions</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($dailyData['summary']['total_payments']) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">{{ number_format($dailyData['summary']['total_payment_amount'], 2) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $dailyData['summary']['total_payments'] > 0 ? number_format($dailyData['summary']['total_payment_amount'] / $dailyData['summary']['total_payments'], 2) : '0.00' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Fee payments</td>
                    </tr>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="p-2 bg-purple-100 rounded-lg mr-3">
                                    <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                    </svg>
                                </div>
                                <div class="text-sm font-medium text-gray-900">Uniform Orders</div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ number_format($dailyData['summary']['total_uniforms']) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-purple-600">{{ number_format($dailyData['summary']['total_uniform_amount'], 2) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ $dailyData['summary']['total_uniforms'] > 0 ? number_format($dailyData['summary']['total_uniform_amount'] / $dailyData['summary']['total_uniforms'], 2) : '0.00' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Uniform purchases</td>
                    </tr>
                    <tr class="bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-bold text-gray-900">Total Daily Revenue</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-green-600">
                            AED {{ number_format($dailyData['summary']['total_payment_amount'] + $dailyData['summary']['total_uniform_amount'], 2) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">-</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Combined revenue</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
@endsection

@push('scripts')
<script>
function exportToPDF() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'pdf');
    window.open(`{{ route('reports.daily') }}?${params.toString()}`, '_blank');
}

function exportToExcel() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', 'excel');
    window.open(`{{ route('reports.daily') }}?${params.toString()}`, '_blank');
}
</script>
@endpush
