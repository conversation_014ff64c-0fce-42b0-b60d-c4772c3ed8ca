<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Add payment type field to distinguish between new entry and renewal payments
            $table->enum('payment_type', ['new_entry', 'renewal', 'regular'])
                ->default('regular')
                ->after('renewal')
                ->comment('Type of payment: new_entry for fresh students, renewal for returning students, regular for ongoing payments');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn('payment_type');
        });
    }
};
