<?php $__env->startSection('title', 'Branch Management - LEADERS SPORTS SERVICES'); ?>

<?php $__env->startSection('header'); ?>
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Branch Management</h1>
                <p class="text-lg text-dark-gray">Manage academy branches and locations</p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?php echo e($branches->total()); ?> Total Branches
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('branches.create')); ?>" class="btn-bank">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                </svg>
                Add New Branch
            </a>
            <div class="flex items-center space-x-2">
                <button onclick="exportData('excel')" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                    Export Excel
                </button>
                <button onclick="exportData('pdf')" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                        </path>
                    </svg>
                    Export PDF
                </button>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <style>
        /* Branch Management Button Text Fix - Apply same rules as Quick Actions */
        .bg-leaders-red,
        .bg-leaders-deep-red,
        a.bg-leaders-red,
        a.bg-leaders-deep-red,
        .btn-bank.bg-leaders-red,
        .btn-bank.bg-leaders-deep-red,
        button.bg-leaders-red,
        button.bg-leaders-deep-red {
            color: white !important;
            /* Keep the red background intact */
        }

        /* Child elements get white text but transparent background */
        .bg-leaders-red *:not(svg),
        .bg-leaders-deep-red *:not(svg),
        a.bg-leaders-red *:not(svg),
        a.bg-leaders-deep-red *:not(svg),
        .btn-bank.bg-leaders-red *:not(svg),
        .btn-bank.bg-leaders-deep-red *:not(svg),
        button.bg-leaders-red *:not(svg),
        button.bg-leaders-deep-red *:not(svg) {
            color: white !important;
            background: transparent !important;
        }

        /* SVG icons should be white */
        .bg-leaders-red svg,
        .bg-leaders-deep-red svg,
        a.bg-leaders-red svg,
        a.bg-leaders-deep-red svg,
        .btn-bank.bg-leaders-red svg,
        .btn-bank.bg-leaders-deep-red svg,
        button.bg-leaders-red svg,
        button.bg-leaders-deep-red svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }

        /* Additional fixes for any red background buttons */
        .btn-bank[class*="red"],
        .btn-bank.bg-red-500,
        .btn-bank.bg-red-600,
        .btn-bank.bg-red-700,
        .btn-bank.bg-red-800,
        .btn-bank.bg-red-900 {
            color: white !important;
        }

        .btn-bank[class*="red"] *,
        .btn-bank.bg-red-500 *,
        .btn-bank.bg-red-600 *,
        .btn-bank.bg-red-700 *,
        .btn-bank.bg-red-800 *,
        .btn-bank.bg-red-900 * {
            color: white !important;
            background: transparent !important;
        }

        .btn-bank[class*="red"] svg,
        .btn-bank.bg-red-500 svg,
        .btn-bank.bg-red-600 svg,
        .btn-bank.bg-red-700 svg,
        .btn-bank.bg-red-800 svg,
        .btn-bank.bg-red-900 svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }

        /* Convert ALL outline buttons to red background buttons */
        .btn-bank-outline {
            background: linear-gradient(135deg, var(--leaders-red) 0%, var(--leaders-deep-red) 100%) !important;
            color: white !important;
            border: 1px solid var(--leaders-deep-red) !important;
        }

        .btn-bank-outline:hover {
            background: linear-gradient(135deg, var(--leaders-deep-red) 0%, #991b1b 100%) !important;
            border-color: #991b1b !important;
            color: white !important;
        }

        .btn-bank-outline * {
            color: white !important;
            background: transparent !important;
        }

        .btn-bank-outline svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }

        /* Ensure proper text visibility for all button variants */
        .btn-bank,
        .btn-bank-sm,
        .btn-bank-outline,
        .btn-bank-secondary {
            text-decoration: none !important;
        }

        .btn-bank * {
            color: white !important;
            background: transparent !important;
        }

        .btn-bank svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }

        /* Force ALL button variants to have red background */
        .btn-bank-sm,
        .btn-bank-outline,
        .btn-bank-secondary,
        button.btn-bank,
        a.btn-bank,
        .btn-bank.btn-bank-sm,
        .btn-bank.btn-bank-outline {
            background: linear-gradient(135deg, var(--leaders-red) 0%, var(--leaders-deep-red) 100%) !important;
            color: white !important;
            border: 1px solid var(--leaders-deep-red) !important;
        }

        .btn-bank-sm:hover,
        .btn-bank-outline:hover,
        .btn-bank-secondary:hover,
        button.btn-bank:hover,
        a.btn-bank:hover,
        .btn-bank.btn-bank-sm:hover,
        .btn-bank.btn-bank-outline:hover {
            background: linear-gradient(135deg, var(--leaders-deep-red) 0%, #991b1b 100%) !important;
            border-color: #991b1b !important;
            color: white !important;
        }

        /* All button children get white text */
        .btn-bank-sm *,
        .btn-bank-outline *,
        .btn-bank-secondary *,
        button.btn-bank *,
        a.btn-bank *,
        .btn-bank.btn-bank-sm *,
        .btn-bank.btn-bank-outline * {
            color: white !important;
            background: transparent !important;
        }

        /* All button SVGs get white styling */
        .btn-bank-sm svg,
        .btn-bank-outline svg,
        .btn-bank-secondary svg,
        button.btn-bank svg,
        a.btn-bank svg,
        .btn-bank.btn-bank-sm svg,
        .btn-bank.btn-bank-outline svg {
            color: white !important;
            fill: white !important;
            stroke: white !important;
        }

        /* Special handling for error/delete buttons - keep red but with white text */
        .text-error-red.btn-bank-outline,
        .border-error-red.btn-bank-outline,
        button[class*="error"],
        button[class*="delete"] {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%) !important;
            color: white !important;
            border: 1px solid #991b1b !important;
        }

        .text-error-red.btn-bank-outline:hover,
        .border-error-red.btn-bank-outline:hover,
        button[class*="error"]:hover,
        button[class*="delete"]:hover {
            background: linear-gradient(135deg, #991b1b 0%, #7f1d1d 100%) !important;
            border-color: #7f1d1d !important;
            color: white !important;
        }
    </style>

    <div class="space-y-6" x-data="branchManagement()" x-init="init()">
        <!-- Advanced Search & Filters -->
        <?php echo $__env->make('branches._filters', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Statistics Cards -->
        <?php echo $__env->make('branches._stats', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Main Content Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">All Branches</h3>
                    <p class="bank-card-subtitle">
                        Showing <?php echo e($branches->firstItem() ?? 0); ?> to <?php echo e($branches->lastItem() ?? 0); ?>

                        of <?php echo e($branches->total()); ?> branches
                    </p>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Bulk Actions -->
                    <div x-show="selectedBranches.length > 0" x-transition class="flex items-center space-x-2">
                        <span class="text-sm text-dark-gray" x-text="`${selectedBranches.length} selected`"></span>
                        <select x-model="bulkAction" class="form-select-bank text-sm">
                            <option value="">Bulk Actions</option>
                            <option value="activate">Activate Selected</option>
                            <option value="deactivate">Deactivate Selected</option>
                            <option value="delete">Delete Selected</option>
                        </select>
                        <button @click="executeBulkAction()" :disabled="!bulkAction" class="btn-bank btn-bank-sm">
                            Apply
                        </button>
                    </div>

                    <!-- View Toggle -->
                    <div class="flex items-center bg-off-white rounded-lg p-1">
                        <button @click="viewMode = 'table'" :class="viewMode === 'table' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M3 6h18m-9 10h9"></path>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded transition-all">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bank-card-body p-0">
                <!-- Table View -->
                <div x-show="viewMode === 'table'" x-transition>
                    <?php echo $__env->make('branches._table', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Grid View -->
                <div x-show="viewMode === 'grid'" x-transition>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                            <?php $__currentLoopData = $branches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $branch): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div
                                    class="bg-white border border-medium-gray rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                                    <!-- Card Header -->
                                    <div class="p-6 pb-4">
                                        <div class="flex items-start justify-between mb-4">
                                            <div class="flex items-center space-x-4">
                                                <div
                                                    class="w-14 h-14 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-xl flex items-center justify-center shadow-lg">
                                                    <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor"
                                                        viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                                        </path>
                                                    </svg>
                                                </div>
                                                <div class="flex-1">
                                                    <h4 class="font-bold text-lg text-charcoal-black mb-1">
                                                        <?php echo e($branch->name); ?></h4>
                                                    <p class="text-sm text-dark-gray flex items-center">
                                                        <svg class="w-4 h-4 mr-1.5 text-medium-gray" fill="none"
                                                            stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                                            </path>
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z">
                                                            </path>
                                                        </svg>
                                                        <?php echo e($branch->location); ?>

                                                    </p>
                                                </div>
                                            </div>
                                            <span
                                                class="badge-bank <?php echo e($branch->status ? 'badge-success' : 'badge-neutral'); ?> px-3 py-1.5 text-sm font-medium">
                                                <?php echo e($branch->status ? 'Active' : 'Inactive'); ?>

                                            </span>
                                        </div>

                                        <!-- Statistics Grid -->
                                        <div class="grid grid-cols-2 gap-4 mb-6">
                                            <div
                                                class="bg-gradient-to-br from-red-50 to-red-100 rounded-lg p-4 text-center border border-red-200">
                                                <div class="text-2xl font-bold text-leaders-red mb-1">
                                                    <?php echo e($branch->academies_count ?? 0); ?>

                                                </div>
                                                <div class="text-xs font-medium text-red-700 uppercase tracking-wide">
                                                    Academies</div>
                                            </div>
                                            <div
                                                class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 text-center border border-green-200">
                                                <div class="text-2xl font-bold text-success-green mb-1">
                                                    <?php echo e($branch->students_count ?? 0); ?>

                                                </div>
                                                <div class="text-xs font-medium text-green-700 uppercase tracking-wide">
                                                    Students</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Card Footer -->
                                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-100">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $branch)): ?>
                                                    <a href="<?php echo e(route('branches.show', $branch)); ?>"
                                                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-900 transition-colors duration-200">
                                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                            </path>
                                                        </svg>
                                                        View
                                                    </a>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $branch)): ?>
                                                    <a href="<?php echo e(route('branches.edit', $branch)); ?>"
                                                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-leaders-red border border-leaders-red rounded-lg hover:bg-leaders-deep-red transition-colors duration-200">
                                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor"
                                                            viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                            </path>
                                                        </svg>
                                                        Edit
                                                    </a>
                                                <?php endif; ?>
                                            </div>
                                            <div class="text-xs text-medium-gray font-medium">
                                                <?php echo e($branch->created_at->format('M d, Y')); ?>

                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>

                <!-- Pagination -->
                <?php if($branches->hasPages()): ?>
                    <div class="px-6 py-4 border-t border-light-gray">
                        <?php echo e($branches->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        function branchManagement() {
            return {
                selectedBranches: [],
                bulkAction: '',
                viewMode: localStorage.getItem('branchViewMode') || 'table',

                init() {
                    this.$watch('viewMode', (value) => {
                        localStorage.setItem('branchViewMode', value);
                    });
                },

                toggleBranchSelection(branchId) {
                    const index = this.selectedBranches.indexOf(branchId);
                    if (index > -1) {
                        this.selectedBranches.splice(index, 1);
                    } else {
                        this.selectedBranches.push(branchId);
                    }
                },

                selectAllBranches() {
                    const checkboxes = document.querySelectorAll('input[name="branch_ids[]"]');
                    const allSelected = this.selectedBranches.length === checkboxes.length;

                    if (allSelected) {
                        this.selectedBranches = [];
                    } else {
                        this.selectedBranches = Array.from(checkboxes).map(cb => parseInt(cb.value));
                    }
                },

                async executeBulkAction() {
                    if (!this.bulkAction || this.selectedBranches.length === 0) return;

                    const confirmed = await this.confirmBulkAction();
                    if (!confirmed) return;

                    try {
                        const response = await fetch('<?php echo e(route('branches.bulk-action')); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                action: this.bulkAction,
                                branch_ids: this.selectedBranches
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while processing the request.');
                    }
                },

                confirmBulkAction() {
                    const actionText = this.bulkAction.charAt(0).toUpperCase() + this.bulkAction.slice(1);
                    return confirm(
                        `Are you sure you want to ${actionText.toLowerCase()} ${this.selectedBranches.length} selected branch(es)?`
                    );
                }
            }
        }

        function exportData(format) {
            const url = format === 'excel' ? '<?php echo e(route('branches.export.excel')); ?>' :
                '<?php echo e(route('branches.export.pdf')); ?>';
            const params = new URLSearchParams(window.location.search);
            window.open(`${url}?${params.toString()}`, '_blank');
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            alert(message);
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xampp\htdocs\erp-login2\resources\views/branches/index.blade.php ENDPATH**/ ?>