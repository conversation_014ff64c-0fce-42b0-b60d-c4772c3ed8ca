@extends('layouts.dashboard')

@section('title', $student->full_name . ' - Student Profile')

@push('styles')
    <style>
        /* Ensure all text in student profile is black and readable */
        .student-profile-content p,
        .student-profile-content span:not(.badge-bank),
        .student-profile-content div:not(.badge-bank) {
            color: #1A202C !important;
            font-weight: 500;
        }

        .form-label-bank {
            color: #4A5568 !important;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
            margin-bottom: 0.25rem;
        }

        .form-label-bank+p {
            color: #1A202C !important;
            font-weight: 500;
            font-size: 0.9rem;
        }

        /* Attendance summary text */
        .bank-card-body .flex span:first-child {
            color: #4A5568 !important;
        }

        .bank-card-body .flex span:last-child {
            color: #1A202C !important;
            font-weight: 600;
        }

        /* Table text */
        .table-bank td {
            color: #1A202C !important;
        }

        .table-bank th {
            color: #4A5568 !important;
        }

        /* Payment History Action Buttons */
        .payment-action-btn {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            border-radius: 0.375rem;
            border: 1px solid;
            transition: all 0.15s ease-in-out;
            text-decoration: none;
        }

        .payment-action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .payment-action-btn svg {
            width: 0.75rem;
            height: 0.75rem;
            margin-right: 0.25rem;
        }

        /* Responsive action buttons */
        @media (max-width: 768px) {
            .payment-action-btn span {
                display: none;
            }

            .payment-action-btn svg {
                margin-right: 0;
            }
        }

        /* Profile Image Upload Styles */
        .profile-image-container {
            position: relative;
            display: inline-block;
        }

        .profile-image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s ease;
            cursor: pointer;
        }

        .profile-image-container:hover .profile-image-overlay {
            opacity: 1;
        }

        .profile-upload-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            padding: 8px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #dc2626;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
@endpush

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="relative group">
                @if ($student->hasProfileImage())
                    <img src="{{ $student->profile_image_url }}" alt="{{ $student->full_name }}"
                        class="w-16 h-16 rounded-full object-cover border-2 border-white shadow-lg">
                @else
                    <div
                        class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                        {{ $student->initials }}
                    </div>
                @endif

                @can('update', $student)
                    <!-- Profile Image Upload Overlay -->
                    <div class="absolute inset-0 bg-black bg-opacity-50 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-pointer"
                        onclick="document.getElementById('profile-image-input').click()">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>

                    <!-- Hidden File Input -->
                    <form id="profile-image-form" action="{{ route('students.update', $student) }}" method="POST"
                        enctype="multipart/form-data" style="display: none;">
                        @csrf
                        @method('PUT')
                        <input type="file" id="profile-image-input" name="profile_image" accept="image/*"
                            onchange="uploadProfileImage()">
                    </form>
                @endcan
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $student->full_name }}</h1>
                <p class="text-lg text-dark-gray">Student Profile • ID: {{ $student->id }}</p>
                <div class="flex items-center space-x-2">
                    <span class="badge-bank {{ $student->status_badge_class }}">
                        {{ $student->status_text }}
                    </span>
                    @if ($student->hasProfileImage())
                        @can('update', $student)
                            <button onclick="deleteProfileImage()" class="text-xs text-red-600 hover:text-red-800 underline"
                                title="Remove Profile Image">
                                Remove Photo
                            </button>
                        @endcan
                    @endif
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <!-- Payment Quick Actions -->
            @if (count($paymentHistory) > 0)
                @php
                    $latestPayment = $paymentHistory->first();
                @endphp
                @can('view', $latestPayment)
                    <div class="flex items-center space-x-2 border-r border-gray-300 pr-3">
                        <!-- View Latest Payment -->
                        <a href="{{ route('payments.show', $latestPayment->id) }}"
                            class="payment-action-btn text-blue-600 bg-blue-50 border-blue-200 hover:bg-blue-100 hover:border-blue-300"
                            title="View Latest Payment">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                </path>
                            </svg>
                            <span>View</span>
                        </a>

                        <!-- Print Latest Payment Invoice -->
                        <a href="{{ route('payments.invoice', $latestPayment->id) }}" target="_blank"
                            class="payment-action-btn text-green-600 bg-green-50 border-green-200 hover:bg-green-100 hover:border-green-300"
                            title="Print Latest Payment Invoice">
                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                                </path>
                            </svg>
                            <span>Print</span>
                        </a>

                        <!-- Edit Latest Payment -->
                        @can('update', $latestPayment)
                            <a href="{{ route('payments.edit', $latestPayment->id) }}"
                                class="payment-action-btn text-orange-600 bg-orange-50 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
                                title="Edit Latest Payment">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                                <span>Edit</span>
                            </a>
                        @endcan
                    </div>
                @endcan
            @endif

            @can('create', App\Models\Payment::class)
                <a href="{{ route('payments.create', ['student_id' => $student->id]) }}" class="btn-bank btn-bank-success">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                        </path>
                    </svg>
                    Add Payment
                </a>
            @endcan
            @can('update', $student)
                <a href="{{ route('students.edit', $student) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Student
                </a>
            @endcan
            <a href="{{ route('students.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Students
            </a>
        </div>
    </div>
@endsection

@section('content')
    <!-- Success Message with Print Options -->
    @if (session('created_records'))
        @php
            $createdRecords = session('created_records');
        @endphp
        <div class="bank-card bg-green-50 border-green-200 mb-6">
            <div class="bank-card-body">
                <div class="flex items-start justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-green-800">Student Created Successfully!</h3>
                            <p class="text-green-700 mt-1">
                                {{ $student->full_name }} has been created with
                                @if (isset($createdRecords['payment']))
                                    <span class="font-medium">payment</span>
                                @endif
                                @if (isset($createdRecords['payment']) && isset($createdRecords['uniform']))
                                    and
                                @endif
                                @if (isset($createdRecords['uniform']))
                                    <span class="font-medium">uniform order</span>
                                @endif
                            </p>
                        </div>
                    </div>
                    <button onclick="this.parentElement.parentElement.parentElement.parentElement.remove()"
                        class="text-green-600 hover:text-green-800">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Quick Print Actions -->
                @if (isset($createdRecords['payment']) || isset($createdRecords['uniform']))
                    <div class="mt-4 pt-4 border-t border-green-200">
                        <h4 class="text-sm font-medium text-green-800 mb-3">Quick Print Options:</h4>
                        <div class="flex flex-wrap gap-3">
                            @if (isset($createdRecords['payment']))
                                <a href="{{ route('payments.invoice', $createdRecords['payment']->id) }}" target="_blank"
                                    class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                                        </path>
                                    </svg>
                                    Print Payment Invoice
                                </a>
                            @endif

                            <button onclick="window.print()"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                                    </path>
                                </svg>
                                Print Student Profile
                            </button>

                            @if (isset($createdRecords['payment']))
                                <a href="{{ route('payments.show', $createdRecords['payment']->id) }}"
                                    class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                    View Payment Details
                                </a>
                            @endif

                            @if (isset($createdRecords['uniform']))
                                <a href="{{ route('uniforms.show', $createdRecords['uniform']->id) }}"
                                    class="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-lg hover:bg-purple-700 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                    </svg>
                                    View Uniform Order
                                </a>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif

    <div class="space-y-6 student-profile-content">
        <!-- Student Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Payments -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-dark-gray">Total Payments</p>
                            <p class="text-2xl font-bold text-success-green">AED
                                {{ number_format($student->total_payments, 2) }}</p>
                        </div>
                        <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-success-green" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pending Payments -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-dark-gray">Pending Payments</p>
                            <p class="text-2xl font-bold text-warning-orange">AED
                                {{ number_format($student->pending_payments, 2) }}</p>
                        </div>
                        <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-warning-orange" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    @can('create', App\Models\Payment::class)
                        <div class="mt-3 pt-3 border-t border-light-gray">
                            <a href="{{ route('payments.create', ['student_id' => $student->id]) }}"
                                class="btn-bank-sm btn-bank-success w-full text-center text-xs">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Add New Payment
                            </a>
                        </div>
                    @endcan
                </div>
            </div>

            <!-- Attendance Rate -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-dark-gray">Attendance Rate</p>
                            <p class="text-2xl font-bold text-blue-600">{{ $student->attendance_rate }}%</p>
                        </div>
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Uniform Orders -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-dark-gray">Uniform Orders</p>
                            <p class="text-2xl font-bold text-purple-600">{{ $student->uniform_orders_count }}</p>
                        </div>
                        <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Student Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Personal Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Personal Information</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="form-label-bank">Full Name</label>
                                <p class="text-charcoal-black font-medium">{{ $student->full_name }}</p>
                            </div>
                            @if ($student->email)
                                <div>
                                    <label class="form-label-bank">Email</label>
                                    <p class="text-charcoal-black">{{ $student->email }}</p>
                                </div>
                            @endif
                            <div>
                                <label class="form-label-bank">Phone</label>
                                <p class="text-charcoal-black">{{ $student->formatted_phone }}</p>
                            </div>
                            @if ($student->nationality)
                                <div>
                                    <label class="form-label-bank">Nationality</label>
                                    <p class="text-charcoal-black">{{ $student->nationality }}</p>
                                </div>
                            @endif
                            @if ($student->birth_date)
                                <div>
                                    <label class="form-label-bank">Birth Date</label>
                                    <p class="text-charcoal-black">{{ $student->formatted_birth_date }}
                                        ({{ $student->age }} years old)</p>
                                </div>
                            @endif
                            <div>
                                <label class="form-label-bank">Join Date</label>
                                <p class="text-charcoal-black">{{ $student->formatted_join_date }}
                                    ({{ $student->days_since_joined }} days ago)</p>
                            </div>
                            @if ($student->address)
                                <div class="md:col-span-2">
                                    <label class="form-label-bank">Address</label>
                                    <p class="text-charcoal-black">{{ $student->address }}</p>
                                </div>
                            @endif
                            @if ($student->notes)
                                <div class="md:col-span-2">
                                    <label class="form-label-bank">Notes</label>
                                    <p class="text-charcoal-black">{{ $student->notes }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Payment History -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <div class="flex items-center justify-between w-full">
                            <div class="flex items-center space-x-3">
                                <h3 class="bank-card-title">Payment History</h3>
                                <span class="badge-bank badge-info">{{ count($paymentHistory) }} payments</span>
                            </div>
                            @can('create', App\Models\Payment::class)
                                <a href="{{ route('payments.create', ['student_id' => $student->id]) }}"
                                    class="btn-bank-sm btn-bank-success text-xs">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add Payment
                                </a>
                            @endcan
                        </div>
                    </div>
                    <div class="bank-card-body p-0">
                        @if (count($paymentHistory) > 0)
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-light-gray">
                                    <thead class="bg-off-white">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">
                                                Date</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">
                                                Amount</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">
                                                Status</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">
                                                Method</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-dark-gray uppercase">
                                                Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-light-gray">
                                        @foreach ($paymentHistory as $payment)
                                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                                <td class="px-6 py-4 text-sm text-charcoal-black">
                                                    {{ $payment->formatted_payment_date ?: $payment->created_at->format('M d, Y') }}
                                                </td>
                                                <td class="px-6 py-4 text-sm font-medium text-success-green">AED
                                                    {{ number_format($payment->amount, 2) }}</td>
                                                <td class="px-6 py-4">
                                                    <span
                                                        class="badge-bank badge-{{ $payment->status === 'completed' ? 'success' : 'warning' }}">
                                                        {{ ucfirst($payment->status) }}
                                                    </span>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-dark-gray">
                                                    {{ $payment->payment_method ?? 'N/A' }}</td>
                                                <td class="px-6 py-4">
                                                    <div class="flex items-center space-x-2">
                                                        <!-- View Payment Button -->
                                                        @can('view', $payment)
                                                            <a href="{{ route('payments.show', $payment->id) }}"
                                                                class="payment-action-btn text-blue-600 bg-blue-50 border-blue-200 hover:bg-blue-100 hover:border-blue-300"
                                                                title="View Payment Details">
                                                                <svg fill="none" stroke="currentColor"
                                                                    viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                                                                    </path>
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        stroke-width="2"
                                                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                                    </path>
                                                                </svg>
                                                                <span>View</span>
                                                            </a>
                                                        @endcan

                                                        <!-- Print Invoice Button -->
                                                        @can('view', $payment)
                                                            <a href="{{ route('payments.invoice', $payment->id) }}"
                                                                target="_blank"
                                                                class="payment-action-btn text-green-600 bg-green-50 border-green-200 hover:bg-green-100 hover:border-green-300"
                                                                title="Print Invoice">
                                                                <svg fill="none" stroke="currentColor"
                                                                    viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        stroke-width="2"
                                                                        d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                                                                    </path>
                                                                </svg>
                                                                <span>Print</span>
                                                            </a>
                                                        @endcan

                                                        <!-- Edit Payment Button -->
                                                        @can('update', $payment)
                                                            <a href="{{ route('payments.edit', $payment->id) }}"
                                                                class="payment-action-btn text-orange-600 bg-orange-50 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
                                                                title="Edit Payment">
                                                                <svg fill="none" stroke="currentColor"
                                                                    viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                                        stroke-width="2"
                                                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                                                    </path>
                                                                </svg>
                                                                <span>Edit</span>
                                                            </a>
                                                        @endcan
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                    </path>
                                </svg>
                                <p class="text-dark-gray">No payment history available</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar Information -->
            <div class="space-y-6">
                <!-- Academy Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Academy Information</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="space-y-4">
                            <div>
                                <label class="form-label-bank">Branch</label>
                                <p class="text-charcoal-black font-medium">{{ $student->branch->name ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="form-label-bank">Academy</label>
                                <p class="text-charcoal-black font-medium">{{ $student->academy->name ?? 'N/A' }}</p>
                            </div>
                            <div>
                                <label class="form-label-bank">Status</label>
                                <span class="badge-bank {{ $student->status_badge_class }}">
                                    {{ $student->status_text }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Attendance Summary -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Attendance Summary</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-dark-gray">Total Sessions:</span>
                                <span class="font-medium">{{ $attendanceSummary['total_sessions'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-dark-gray">Present:</span>
                                <span class="font-medium text-success-green">{{ $attendanceSummary['present'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-dark-gray">Absent:</span>
                                <span class="font-medium text-red-600">{{ $attendanceSummary['absent'] }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-dark-gray">Late:</span>
                                <span class="font-medium text-warning-orange">{{ $attendanceSummary['late'] }}</span>
                            </div>
                            <div class="border-t pt-4">
                                <div class="flex justify-between">
                                    <span class="text-dark-gray font-medium">Attendance Rate:</span>
                                    <span
                                        class="font-bold text-blue-600">{{ $attendanceSummary['attendance_rate'] }}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Uniform Orders -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Uniform Orders</h3>
                        <span class="badge-bank badge-info">{{ count($uniformOrders) }} orders</span>
                    </div>
                    <div class="bank-card-body">
                        @if (count($uniformOrders) > 0)
                            <div class="space-y-3">
                                @foreach ($uniformOrders as $uniform)
                                    <div class="border border-light-gray rounded-lg p-3">
                                        <div class="flex justify-between items-start mb-2">
                                            <span class="font-medium text-charcoal-black">{{ $uniform['item'] }}</span>
                                            <span
                                                class="badge-bank badge-{{ $uniform['status'] === 'delivered' ? 'success' : 'warning' }}">
                                                {{ ucfirst($uniform['status']) }}
                                            </span>
                                        </div>
                                        <div class="text-sm text-dark-gray">
                                            <p>Size: {{ $uniform['size'] }} • Qty: {{ $uniform['quantity'] }}</p>
                                            <p>Amount: AED {{ number_format($uniform['amount'], 2) }}</p>
                                            <p>Ordered: {{ $uniform['order_date'] }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-6">
                                <svg class="w-10 h-10 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                                <p class="text-dark-gray">No uniform orders</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Profile Image Upload Function
        function uploadProfileImage() {
            const form = document.getElementById('profile-image-form');
            const input = document.getElementById('profile-image-input');
            const file = input.files[0];

            if (!file) {
                showErrorMessage('Please select an image file to upload.');
                return;
            }

            // Enhanced file validation
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!validTypes.includes(file.type)) {
                showErrorMessage('Please select a valid image file (JPEG, PNG, JPG, or GIF).');
                return;
            }

            // Validate file size (2MB max)
            const maxSize = 2 * 1024 * 1024; // 2MB
            if (file.size > maxSize) {
                showErrorMessage('Image size must be less than 2MB. Current size: ' + formatFileSize(file.size));
                return;
            }

            // Show loading spinner
            showUploadLoading(true);
            clearErrorMessage();

            // Create FormData and submit
            const formData = new FormData(form);

            fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showSuccessMessage('Profile image uploaded successfully!');
                        // Reload the page to show the new image after a short delay
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        showErrorMessage(data.message || 'Failed to upload image.');
                        showUploadLoading(false);
                    }
                })
                .catch(error => {
                    console.error('Upload error:', error);
                    let errorMessage = 'An error occurred while uploading the image.';

                    if (error.message.includes('413')) {
                        errorMessage = 'Image file is too large. Please select a smaller image.';
                    } else if (error.message.includes('422')) {
                        errorMessage = 'Invalid image file. Please check the file format and size.';
                    } else if (error.message.includes('500')) {
                        errorMessage = 'Server error occurred. Please try again later.';
                    }

                    showErrorMessage(errorMessage);
                    showUploadLoading(false);
                });
        }

        // Profile Image Delete Function
        function deleteProfileImage() {
            if (!confirm('Are you sure you want to remove the profile image?')) {
                return;
            }

            showUploadLoading(true);

            fetch('{{ route('students.delete-profile-image', $student) }}', {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to show the default avatar
                        window.location.reload();
                    } else {
                        alert(data.message || 'Failed to delete image.');
                        showUploadLoading(false);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the image.');
                    showUploadLoading(false);
                });
        }

        // Show/Hide Upload Loading Spinner
        function showUploadLoading(show) {
            const container = document.querySelector('.relative.group');
            let spinner = container.querySelector('.profile-upload-loading');

            if (show) {
                if (!spinner) {
                    spinner = document.createElement('div');
                    spinner.className = 'profile-upload-loading';
                    spinner.innerHTML = '<div class="spinner"></div>';
                    container.appendChild(spinner);
                }
            } else {
                if (spinner) {
                    spinner.remove();
                }
            }
        }

        // Error and Success Message Functions
        function showErrorMessage(message) {
            // Remove existing messages
            clearMessages();

            const errorDiv = document.createElement('div');
            errorDiv.id = 'upload-error-message';
            errorDiv.className = 'mt-2 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm';
            errorDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    ${message}
                </div>
            `;

            const profileSection = document.querySelector('.bank-card-body');
            profileSection.appendChild(errorDiv);
        }

        function showSuccessMessage(message) {
            // Remove existing messages
            clearMessages();

            const successDiv = document.createElement('div');
            successDiv.id = 'upload-success-message';
            successDiv.className = 'mt-2 p-3 bg-green-100 border border-green-400 text-green-700 rounded-md text-sm';
            successDiv.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    ${message}
                </div>
            `;

            const profileSection = document.querySelector('.bank-card-body');
            profileSection.appendChild(successDiv);
        }

        function clearMessages() {
            const errorMsg = document.getElementById('upload-error-message');
            const successMsg = document.getElementById('upload-success-message');
            if (errorMsg) errorMsg.remove();
            if (successMsg) successMsg.remove();
        }

        function clearErrorMessage() {
            const errorMsg = document.getElementById('upload-error-message');
            if (errorMsg) errorMsg.remove();
        }

        // Format file size for display
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
@endpush
