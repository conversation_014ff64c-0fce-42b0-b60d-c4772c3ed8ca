// UAE English Sports Academy - Real-time Name Translation
// Provides automatic Arabic name translation in forms

class NameTranslation {
    constructor() {
        this.apiEndpoint = '/api/students/translate-name';
        this.cache = new Map();
        this.init();
    }

    init() {
        this.setupFormListeners();
        this.setupBulkTranslation();
    }

    setupFormListeners() {
        // Listen for name input changes
        document.addEventListener('input', (event) => {
            const target = event.target;
            
            // Check if it's a name field that needs translation
            if (this.isNameField(target)) {
                this.handleNameInput(target);
            }
        });

        // Listen for blur events to trigger translation
        document.addEventListener('blur', (event) => {
            const target = event.target;
            
            if (this.isNameField(target)) {
                this.translateField(target);
            }
        });
    }

    isNameField(element) {
        const nameFields = [
            'full_name',
            'first_name', 
            'last_name',
            'nationality'
        ];

        return nameFields.some(field => 
            element.name === field || 
            element.id === field ||
            element.classList.contains(`${field}-input`)
        );
    }

    handleNameInput(element) {
        // Clear any existing timeout
        if (element.translationTimeout) {
            clearTimeout(element.translationTimeout);
        }

        // Set a new timeout to translate after user stops typing
        element.translationTimeout = setTimeout(() => {
            this.translateField(element);
        }, 1000);
    }

    async translateField(element) {
        const value = element.value.trim();
        if (!value) return;

        // Check cache first
        const cacheKey = `${element.name}_${value}`;
        if (this.cache.has(cacheKey)) {
            this.fillArabicField(element, this.cache.get(cacheKey));
            return;
        }

        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    name: value,
                    type: element.name === 'nationality' ? 'nationality' : 'name'
                })
            });

            const data = await response.json();

            if (data.success && data.translation) {
                // Cache the translation
                this.cache.set(cacheKey, data.translation);
                
                // Fill the Arabic field
                this.fillArabicField(element, data.translation);
                
                // Show suggestions if available
                if (data.suggestions && data.suggestions.length > 1) {
                    this.showSuggestions(element, data.suggestions);
                }
            }
        } catch (error) {
            console.warn('Translation failed:', error);
        }
    }

    fillArabicField(englishField, arabicText) {
        const arabicFieldName = `${englishField.name}_ar`;
        const arabicField = document.querySelector(`[name="${arabicFieldName}"], #${arabicFieldName}`);
        
        if (arabicField && !arabicField.value) {
            arabicField.value = arabicText;
            
            // Trigger change event
            arabicField.dispatchEvent(new Event('change', { bubbles: true }));
            
            // Add visual feedback
            this.showTranslationFeedback(arabicField);
        }
    }

    showTranslationFeedback(field) {
        // Add a temporary class to show the field was auto-filled
        field.classList.add('auto-translated');
        
        setTimeout(() => {
            field.classList.remove('auto-translated');
        }, 2000);
    }

    showSuggestions(element, suggestions) {
        // Remove existing suggestions
        this.removeSuggestions(element);

        // Create suggestions container
        const suggestionsContainer = document.createElement('div');
        suggestionsContainer.className = 'translation-suggestions';
        suggestionsContainer.innerHTML = `
            <div class="suggestions-header">
                <span>Arabic suggestions:</span>
                <button type="button" class="close-suggestions">&times;</button>
            </div>
            <div class="suggestions-list">
                ${suggestions.map(suggestion => 
                    `<button type="button" class="suggestion-item" data-suggestion="${suggestion}">
                        ${suggestion}
                    </button>`
                ).join('')}
            </div>
        `;

        // Position and show suggestions
        element.parentNode.style.position = 'relative';
        element.parentNode.appendChild(suggestionsContainer);

        // Handle suggestion clicks
        suggestionsContainer.addEventListener('click', (event) => {
            if (event.target.classList.contains('suggestion-item')) {
                const suggestion = event.target.dataset.suggestion;
                this.fillArabicField(element, suggestion);
                this.removeSuggestions(element);
            } else if (event.target.classList.contains('close-suggestions')) {
                this.removeSuggestions(element);
            }
        });

        // Auto-hide after 10 seconds
        setTimeout(() => {
            this.removeSuggestions(element);
        }, 10000);
    }

    removeSuggestions(element) {
        const existing = element.parentNode.querySelector('.translation-suggestions');
        if (existing) {
            existing.remove();
        }
    }

    setupBulkTranslation() {
        // Add bulk translation button to student lists
        const bulkTranslateBtn = document.querySelector('#bulk-translate-names');
        if (bulkTranslateBtn) {
            bulkTranslateBtn.addEventListener('click', () => {
                this.handleBulkTranslation();
            });
        }
    }

    async handleBulkTranslation() {
        // Get selected student IDs
        const selectedStudents = Array.from(document.querySelectorAll('input[name="student_ids[]"]:checked'))
            .map(checkbox => checkbox.value);

        if (selectedStudents.length === 0) {
            alert('Please select students to translate');
            return;
        }

        try {
            const response = await fetch('/api/students/bulk-translate-names', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    student_ids: selectedStudents
                })
            });

            const data = await response.json();

            if (data.success) {
                alert(`${data.translated_count} students translated successfully`);
                // Refresh the page to show updated names
                window.location.reload();
            } else {
                alert('Translation failed: ' + data.message);
            }
        } catch (error) {
            console.error('Bulk translation failed:', error);
            alert('Bulk translation failed. Please try again.');
        }
    }

    // Public method to manually translate a name
    async translateName(name, type = 'name') {
        try {
            const response = await fetch(this.apiEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({ name, type })
            });

            const data = await response.json();
            return data.success ? data.translation : null;
        } catch (error) {
            console.error('Translation failed:', error);
            return null;
        }
    }

    // Public method to clear translation cache
    clearCache() {
        this.cache.clear();
    }
}

// Initialize name translation system
document.addEventListener('DOMContentLoaded', () => {
    window.nameTranslation = new NameTranslation();
});

// Export for use in other modules
export default NameTranslation;
