/* UAE English Sports Academy - Unified Button Text Color Fix */

/*
 * This file ensures all buttons with red backgrounds have white text
 * for optimal readability and consistency across the system.
 */

/* Primary button text styling - highest specificity */
button,
.btn,
.btn-bank,
.btn-primary,
.btn-danger,
.btn-leaders,
.btn-red,
input[type="button"],
input[type="submit"],
input[type="reset"],
a.btn,
a.button,
[role="button"] {
    /* Check for red background and force white text */
}

/* Red background buttons - force white text */
button[class*="red"],
button[class*="Red"],
button[class*="RED"],
.btn[class*="red"],
.btn[class*="Red"],
.btn[class*="RED"],
.btn-primary,
.btn-danger,
.btn-bank,
.btn-leaders,
.btn-red,
button[style*="background-color: red"],
button[style*="background-color: #"],
button[style*="background: red"],
button[style*="background: #"],
.btn[style*="background-color: red"],
.btn[style*="background-color: #"],
.btn[style*="background: red"],
.btn[style*="background: #"] {
    color: white !important;
}

/* Specific red color variations */
button[style*="background-color: #e53e3e"],
button[style*="background-color: #dc2626"],
button[style*="background-color: #b91c1c"],
button[style*="background-color: #991b1b"],
button[style*="background-color: #7f1d1d"],
button[style*="background: #e53e3e"],
button[style*="background: #dc2626"],
button[style*="background: #b91c1c"],
button[style*="background: #991b1b"],
button[style*="background: #7f1d1d"],
.btn[style*="background-color: #e53e3e"],
.btn[style*="background-color: #dc2626"],
.btn[style*="background-color: #b91c1c"],
.btn[style*="background-color: #991b1b"],
.btn[style*="background-color: #7f1d1d"],
.btn[style*="background: #e53e3e"],
.btn[style*="background: #dc2626"],
.btn[style*="background: #b91c1c"],
.btn[style*="background: #991b1b"],
.btn[style*="background: #7f1d1d"] {
    color: white !important;
}

/* Leaders red color variations */
button[class*="leaders"],
button[class*="Leaders"],
.btn[class*="leaders"],
.btn[class*="Leaders"],
.btn-leaders-red,
.bg-leaders-red,
.bg-leaders-deep-red {
    color: white !important;
}

/* CSS custom property based red backgrounds */
button[style*="--leaders-red"],
button[style*="--leaders-deep-red"],
.btn[style*="--leaders-red"],
.btn[style*="--leaders-deep-red"] {
    color: white !important;
}

/* Gradient backgrounds with red */
button[style*="gradient"][style*="red"],
button[style*="linear-gradient"],
.btn[style*="gradient"][style*="red"],
.btn[style*="linear-gradient"] {
    color: white !important;
}

/* Bootstrap and common framework red buttons */
.btn-primary,
.btn-danger,
.btn-error,
.btn-warning,
.button-primary,
.button-danger,
.button-error,
.button-warning {
    color: white !important;
}

/* Ensure child elements also have white text */
button[class*="red"] *,
button[class*="Red"] *,
button[class*="RED"] *,
.btn[class*="red"] *,
.btn[class*="Red"] *,
.btn[class*="RED"] *,
.btn-primary *,
.btn-danger *,
.btn-bank *,
.btn-leaders *,
.btn-red *,
button[style*="background-color: red"] *,
button[style*="background: red"] *,
.btn[style*="background-color: red"] *,
.btn[style*="background: red"] * {
    color: white !important;
    background: transparent !important;
}

/* Specific overrides for common conflicting classes */
button.text-dark,
button.text-black,
button.text-gray-800,
button.text-gray-900,
button.text-dark-gray,
.btn.text-dark,
.btn.text-black,
.btn.text-gray-800,
.btn.text-gray-900,
.btn.text-dark-gray {
    color: white !important;
}

/* SVG icons inside red buttons should be white */
button[class*="red"] svg,
button[class*="Red"] svg,
.btn[class*="red"] svg,
.btn[class*="Red"] svg,
.btn-primary svg,
.btn-danger svg,
.btn-bank svg,
.btn-leaders svg,
.btn-red svg {
    color: white !important;
    fill: white !important;
    stroke: white !important;
}

/* Hover states for red buttons */
button[class*="red"]:hover,
button[class*="Red"]:hover,
.btn[class*="red"]:hover,
.btn[class*="Red"]:hover,
.btn-primary:hover,
.btn-danger:hover,
.btn-bank:hover,
.btn-leaders:hover,
.btn-red:hover {
    color: white !important;
}

/* Focus states for accessibility */
button[class*="red"]:focus,
button[class*="Red"]:focus,
.btn[class*="red"]:focus,
.btn[class*="Red"]:focus,
.btn-primary:focus,
.btn-danger:focus,
.btn-bank:focus,
.btn-leaders:focus,
.btn-red:focus {
    color: white !important;
    outline: 2px solid rgba(255, 255, 255, 0.5) !important;
    outline-offset: 2px !important;
}

/* Active states */
button[class*="red"]:active,
button[class*="Red"]:active,
.btn[class*="red"]:active,
.btn[class*="Red"]:active,
.btn-primary:active,
.btn-danger:active,
.btn-bank:active,
.btn-leaders:active,
.btn-red:active {
    color: white !important;
}

/* Disabled states */
button[class*="red"]:disabled,
button[class*="Red"]:disabled,
.btn[class*="red"]:disabled,
.btn[class*="Red"]:disabled,
.btn-primary:disabled,
.btn-danger:disabled,
.btn-bank:disabled,
.btn-leaders:disabled,
.btn-red:disabled {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    button[class*="red"],
    button[class*="Red"],
    .btn[class*="red"],
    .btn[class*="Red"],
    .btn-primary,
    .btn-danger,
    .btn-bank,
    .btn-leaders,
    .btn-red {
        color: white !important;
    }
}

/* Print styles */
@media print {
    button[class*="red"],
    button[class*="Red"],
    .btn[class*="red"],
    .btn[class*="Red"],
    .btn-primary,
    .btn-danger,
    .btn-bank,
    .btn-leaders,
    .btn-red {
        color: black !important;
        background: #f3f4f6 !important;
    }
}

/* RTL Support */
[dir="rtl"] button[class*="red"],
[dir="rtl"] button[class*="Red"],
[dir="rtl"] .btn[class*="red"],
[dir="rtl"] .btn[class*="Red"],
[dir="rtl"] .btn-primary,
[dir="rtl"] .btn-danger,
[dir="rtl"] .btn-bank,
[dir="rtl"] .btn-leaders,
[dir="rtl"] .btn-red {
    color: white !important;
}

/* Ultra-aggressive override for any remaining cases */
button,
.btn,
input[type="button"],
input[type="submit"],
input[type="reset"] {
    /* This will be handled by JavaScript for dynamic detection */
}

/* Nuclear option - catch everything with red background */
*[style*="background-color: red"],
*[style*="background: red"],
*[style*="background-color: #e53e3e"],
*[style*="background-color: #dc2626"],
*[style*="background-color: #b91c1c"] {
    color: white !important;
}

/* Additional comprehensive button selectors */
button[class*="btn"],
.button,
.Button,
[type="submit"],
[type="button"],
[type="reset"],
a[class*="btn"],
a[class*="button"],
div[class*="btn"],
span[class*="btn"] {
    /* Will be handled by JavaScript for dynamic detection */
}

/* Force white text on any element with red background classes */
.bg-red-500,
.bg-red-600,
.bg-red-700,
.bg-red-800,
.bg-red-900,
.bg-leaders-red,
.bg-leaders-deep-red,
.bg-primary,
.bg-danger,
.bg-error {
    color: white !important;
}

/* Tailwind CSS red background utilities */
.bg-red-50,
.bg-red-100,
.bg-red-200,
.bg-red-300,
.bg-red-400,
.bg-red-500,
.bg-red-600,
.bg-red-700,
.bg-red-800,
.bg-red-900 {
    color: white !important;
}

/* Bootstrap red background utilities */
.bg-primary,
.bg-danger,
.bg-warning,
.bg-error,
.bg-red {
    color: white !important;
}

/* Custom red background classes */
.red-bg,
.red-background,
.primary-bg,
.danger-bg,
.leaders-bg {
    color: white !important;
}

/* GLOBAL RULE: Convert ALL outline buttons to red background buttons */
.btn-bank-outline,
.btn-bank.btn-bank-outline,
button.btn-bank-outline,
a.btn-bank-outline {
    background: linear-gradient(135deg, var(--leaders-red, #E53E3E) 0%, var(--leaders-deep-red, #B91C1C) 100%) !important;
    color: white !important;
    border: 1px solid var(--leaders-deep-red, #B91C1C) !important;
}

.btn-bank-outline:hover,
.btn-bank.btn-bank-outline:hover,
button.btn-bank-outline:hover,
a.btn-bank-outline:hover {
    background: linear-gradient(135deg, var(--leaders-deep-red, #B91C1C) 0%, #991b1b 100%) !important;
    border-color: #991b1b !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* All outline button children get white text */
.btn-bank-outline *,
.btn-bank.btn-bank-outline *,
button.btn-bank-outline *,
a.btn-bank-outline * {
    color: white !important;
    background: transparent !important;
}

/* All outline button SVGs get white styling */
.btn-bank-outline svg,
.btn-bank.btn-bank-outline svg,
button.btn-bank-outline svg,
a.btn-bank-outline svg {
    color: white !important;
    fill: white !important;
    stroke: white !important;
}

/* Force ALL button variants to have red background globally */
.btn-bank-sm,
.btn-bank-secondary,
button.btn-bank,
a.btn-bank,
.btn-bank.btn-bank-sm {
    background: linear-gradient(135deg, var(--leaders-red, #E53E3E) 0%, var(--leaders-deep-red, #B91C1C) 100%) !important;
    color: white !important;
    border: 1px solid var(--leaders-deep-red, #B91C1C) !important;
}

.btn-bank-sm:hover,
.btn-bank-secondary:hover,
button.btn-bank:hover,
a.btn-bank:hover,
.btn-bank.btn-bank-sm:hover {
    background: linear-gradient(135deg, var(--leaders-deep-red, #B91C1C) 0%, #991b1b 100%) !important;
    border-color: #991b1b !important;
    color: white !important;
    transform: translateY(-1px);
}

/* All button children get white text globally */
.btn-bank-sm *,
.btn-bank-secondary *,
button.btn-bank *,
a.btn-bank *,
.btn-bank.btn-bank-sm * {
    color: white !important;
    background: transparent !important;
}

/* All button SVGs get white styling globally */
.btn-bank-sm svg,
.btn-bank-secondary svg,
button.btn-bank svg,
a.btn-bank svg,
.btn-bank.btn-bank-sm svg {
    color: white !important;
    fill: white !important;
    stroke: white !important;
}

/* UNIVERSAL BUTTON RED BACKGROUND RULE - Applies to ALL pages */
button[class*="btn-bank"],
a[class*="btn-bank"],
.button[class*="bank"],
[class*="btn-bank"] {
    background: linear-gradient(135deg, var(--leaders-red, #E53E3E) 0%, var(--leaders-deep-red, #B91C1C) 100%) !important;
    color: white !important;
    border: 1px solid var(--leaders-deep-red, #B91C1C) !important;
    text-decoration: none !important;
}

button[class*="btn-bank"]:hover,
a[class*="btn-bank"]:hover,
.button[class*="bank"]:hover,
[class*="btn-bank"]:hover {
    background: linear-gradient(135deg, var(--leaders-deep-red, #B91C1C) 0%, #991b1b 100%) !important;
    border-color: #991b1b !important;
    color: white !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Universal button children styling */
button[class*="btn-bank"] *,
a[class*="btn-bank"] *,
.button[class*="bank"] *,
[class*="btn-bank"] * {
    color: white !important;
    background: transparent !important;
}

/* Universal button SVG styling */
button[class*="btn-bank"] svg,
a[class*="btn-bank"] svg,
.button[class*="bank"] svg,
[class*="btn-bank"] svg {
    color: white !important;
    fill: white !important;
    stroke: white !important;
}

/* PAGINATION WHITE TEXT STYLING */
/* Target all pagination elements and force white text */
nav[role="navigation"] *,
.pagination *,
span[class*="relative inline-flex"],
a[class*="relative inline-flex"] {
    color: white !important;
    background: var(--leaders-red, #E53E3E) !important;
    border-color: var(--leaders-deep-red, #B91C1C) !important;
}

nav[role="navigation"] *:hover,
.pagination *:hover,
span[class*="relative inline-flex"]:hover,
a[class*="relative inline-flex"]:hover {
    color: white !important;
    background: var(--leaders-deep-red, #B91C1C) !important;
    border-color: var(--leaders-deep-red, #B91C1C) !important;
}

/* Force white text on all pagination numbers and arrows */
nav[role="navigation"] span,
nav[role="navigation"] a,
.pagination span,
.pagination a {
    color: white !important;
}

/* Override any gray text classes in pagination */
nav[role="navigation"] .text-gray-500,
nav[role="navigation"] .text-gray-700,
nav[role="navigation"] .text-gray-400,
.pagination .text-gray-500,
.pagination .text-gray-700,
.pagination .text-gray-400 {
    color: white !important;
}

/* Special handling for specific button combinations */
.btn-bank-outline.btn-bank-sm,
.btn-bank.btn-bank-outline.btn-bank-sm,
button.btn-bank-outline.btn-bank-sm,
a.btn-bank-outline.btn-bank-sm {
    background: linear-gradient(135deg, var(--leaders-red, #E53E3E) 0%, var(--leaders-deep-red, #B91C1C) 100%) !important;
    color: white !important;
    border: 1px solid var(--leaders-deep-red, #B91C1C) !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
}

/* Override any conflicting styles */
.btn-bank-outline.btn-bank-sm:hover,
.btn-bank.btn-bank-outline.btn-bank-sm:hover,
button.btn-bank-outline.btn-bank-sm:hover,
a.btn-bank-outline.btn-bank-sm:hover {
    background: linear-gradient(135deg, var(--leaders-deep-red, #B91C1C) 0%, #991b1b 100%) !important;
    border-color: #991b1b !important;
    color: white !important;
}

/* Specific Quick Actions fixes - CAREFUL with backgrounds */
.bg-leaders-red,
.bg-leaders-deep-red,
a.bg-leaders-red,
a.bg-leaders-deep-red,
.btn-bank.bg-leaders-red,
.btn-bank.bg-leaders-deep-red,
button.bg-leaders-red,
button.bg-leaders-deep-red {
    color: white !important;
    /* DO NOT override background for the button itself */
}

/* Child elements get white text and transparent background */
.bg-leaders-red *:not(svg),
.bg-leaders-deep-red *:not(svg),
a.bg-leaders-red *:not(svg),
a.bg-leaders-deep-red *:not(svg),
.btn-bank.bg-leaders-red *:not(svg),
.btn-bank.bg-leaders-deep-red *:not(svg),
button.bg-leaders-red *:not(svg),
button.bg-leaders-deep-red *:not(svg) {
    color: white !important;
    background: transparent !important;
}

/* Quick Actions SVG icons */
.bg-leaders-red svg,
.bg-leaders-deep-red svg,
a.bg-leaders-red svg,
a.bg-leaders-deep-red svg,
.btn-bank.bg-leaders-red svg,
.btn-bank.bg-leaders-deep-red svg,
button.bg-leaders-red svg,
button.bg-leaders-deep-red svg {
    color: white !important;
    fill: white !important;
    stroke: white !important;
}

/* Quick Actions hover states - only text color */
.bg-leaders-red:hover,
.bg-leaders-deep-red:hover,
a.bg-leaders-red:hover,
.btn-bank.bg-leaders-red:hover {
    color: white !important;
}

/* Hover state child elements */
.bg-leaders-red:hover *:not(svg),
.bg-leaders-deep-red:hover *:not(svg),
a.bg-leaders-red:hover *:not(svg),
.btn-bank.bg-leaders-red:hover *:not(svg) {
    color: white !important;
}

/* Quick Actions focus states - only text color */
.bg-leaders-red:focus,
a.bg-leaders-red:focus,
.btn-bank.bg-leaders-red:focus {
    color: white !important;
}

/* Focus state child elements */
.bg-leaders-red:focus *:not(svg),
a.bg-leaders-red:focus *:not(svg),
.btn-bank.bg-leaders-red:focus *:not(svg) {
    color: white !important;
}
