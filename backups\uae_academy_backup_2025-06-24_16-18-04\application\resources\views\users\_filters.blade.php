<!-- Advanced Search & Filters -->
<div class="bank-card">
    <div class="bank-card-header">
        <h3 class="bank-card-title">Search & Filters</h3>
        <button @click="showFilters = !showFilters"
            class="btn-bank btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                </path>
            </svg>
            <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
        </button>
    </div>

    <div class="bank-card-body" x-show="showFilters" x-transition x-data="{ showFilters: true }">
        <form method="GET" action="{{ route('users.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="form-label">Search</label>
                    <input type="text" id="search" name="search" value="{{ request('search') }}"
                        placeholder="Search by name, email, branch, or academy..." class="form-control-bank">
                </div>

                <!-- Role Filter -->
                <div>
                    <label for="role" class="form-label">Role</label>
                    <select id="role" name="role" class="form-select-bank">
                        <option value="">All Roles</option>
                        <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>
                            System Administrator
                        </option>
                        <option value="branch_manager" {{ request('role') === 'branch_manager' ? 'selected' : '' }}>
                            Branch Manager
                        </option>
                        <option value="academy_manager" {{ request('role') === 'academy_manager' ? 'selected' : '' }}>
                            Academy Manager
                        </option>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <label for="status" class="form-label">Status</label>
                    <select id="status" name="status" class="form-select-bank">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>
                            Active
                        </option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>
                            Inactive
                        </option>
                    </select>
                </div>

                <!-- Branch Filter -->
                <div>
                    <label for="branch_id" class="form-label">Branch</label>
                    <select id="branch_id" name="branch_id" class="form-select-bank">
                        <option value="">All Branches</option>
                        @foreach ($branches as $branch)
                            <option value="{{ $branch->id }}"
                                {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
            </div>

            <!-- Academy Filter (Second Row) -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                    <label for="academy_id" class="form-label">Academy</label>
                    <select id="academy_id" name="academy_id" class="form-select-bank">
                        <option value="">All Academies</option>
                        @foreach ($academies as $academy)
                            <option value="{{ $academy->id }}"
                                {{ request('academy_id') == $academy->id ? 'selected' : '' }}>
                                {{ $academy->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Sort Options -->
                <div>
                    <label for="sort" class="form-label">Sort By</label>
                    <select id="sort" name="sort" class="form-select-bank">
                        <option value="created_at" {{ request('sort') === 'created_at' ? 'selected' : '' }}>
                            Created Date
                        </option>
                        <option value="name" {{ request('sort') === 'name' ? 'selected' : '' }}>
                            Name
                        </option>
                        <option value="email" {{ request('sort') === 'email' ? 'selected' : '' }}>
                            Email
                        </option>
                        <option value="role" {{ request('sort') === 'role' ? 'selected' : '' }}>
                            Role
                        </option>
                        <option value="status" {{ request('sort') === 'status' ? 'selected' : '' }}>
                            Status
                        </option>
                    </select>
                </div>

                <!-- Sort Direction -->
                <div>
                    <label for="direction" class="form-label">Direction</label>
                    <select id="direction" name="direction" class="form-select-bank">
                        <option value="desc" {{ request('direction') === 'desc' ? 'selected' : '' }}>
                            Descending
                        </option>
                        <option value="asc" {{ request('direction') === 'asc' ? 'selected' : '' }}>
                            Ascending
                        </option>
                    </select>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-end space-x-2">
                    <button type="submit" class="btn-bank flex-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Search
                    </button>
                    <a href="{{ route('users.index') }}"
                        class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white flex-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                            </path>
                        </svg>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
