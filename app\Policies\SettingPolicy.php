<?php

namespace App\Policies;

use App\Models\Setting;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SettingPolicy
{
    /**
     * Determine whether the user can view any settings.
     */
    public function viewAny(User $user): bool
    {
        // Only admins can view settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can view the setting.
     */
    public function view(User $user, Setting $setting): bool
    {
        // Only admins can view settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can create settings.
     */
    public function create(User $user): bool
    {
        // Only admins can create settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can update the setting.
     */
    public function update(User $user, Setting $setting): bool
    {
        // Only admins can update settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can delete the setting.
     */
    public function delete(User $user, Setting $setting): bool
    {
        // Only admins can delete settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can restore the setting.
     */
    public function restore(User $user, Setting $setting): bool
    {
        // Only admins can restore settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can permanently delete the setting.
     */
    public function forceDelete(User $user, Setting $setting): bool
    {
        // Only admins can force delete settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can export settings.
     */
    public function export(User $user): bool
    {
        // Only admins can export settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can import settings.
     */
    public function import(User $user): bool
    {
        // Only admins can import settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can backup settings.
     */
    public function backup(User $user): bool
    {
        // Only admins can backup settings
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can restore from backup.
     */
    public function restoreBackup(User $user): bool
    {
        // Only admins can restore from backup
        return $user->role === 'admin';
    }
}
