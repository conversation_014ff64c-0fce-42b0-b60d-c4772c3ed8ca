<div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-light-gray">
        <thead class="bg-off-white">
            <tr>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    <input type="checkbox" @change="selectAllReservations()"
                        :checked="selectedReservations.length === {{ $reservations->count() }}"
                        class="rounded border-gray-300 text-leaders-red focus:ring-leaders-red">
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Reservation #
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Customer
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Venue & Field
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Date & Time
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Duration
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Amount
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Status
                </th>
                <th scope="col"
                    class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Payment
                </th>
                <th scope="col"
                    class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider bg-dark-gray">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-light-gray">
            @forelse($reservations as $reservation)
                <tr class="hover:bg-off-white transition-colors duration-200">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox" name="reservation_ids[]" value="{{ $reservation->id }}"
                            @change="toggleReservationSelection({{ $reservation->id }})"
                            :checked="selectedReservations.includes({{ $reservation->id }})"
                            class="rounded border-gray-300 text-leaders-red focus:ring-leaders-red">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div
                                    class="h-10 w-10 rounded-full bg-gradient-to-br from-leaders-red to-leaders-deep-red flex items-center justify-center">
                                    <span class="text-sm font-medium text-white">
                                        {{ substr($reservation->reservation_number, -3) }}
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-charcoal-black">
                                    <a href="{{ route('reservations.show', $reservation) }}"
                                        class="hover:text-leaders-red">
                                        {{ $reservation->reservation_number }}
                                    </a>
                                </div>
                                <div class="text-sm text-dark-gray">
                                    {{ $reservation->booking_type_text }}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-charcoal-black">
                            <a href="{{ route('customers.show', $reservation->customer) }}"
                                class="hover:text-leaders-red">
                                {{ $reservation->customer->full_name }}
                            </a>
                        </div>
                        <div class="text-sm text-dark-gray">
                            {{ $reservation->customer->email }}
                        </div>
                        <div class="text-sm text-dark-gray">
                            {{ $reservation->customer->phone }}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-charcoal-black">
                            {{ $reservation->venue->name }}
                        </div>
                        <div class="text-sm text-dark-gray">
                            {{ $reservation->field->name }}
                        </div>
                        <div class="text-xs text-dark-gray">
                            {{ $reservation->field->type_text }}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-charcoal-black">
                            {{ $reservation->reservation_date->format('M d, Y') }}
                        </div>
                        <div class="text-sm text-dark-gray">
                            {{ $reservation->time_slot_text }}
                        </div>
                        @if ($reservation->is_today)
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Today
                            </span>
                        @elseif($reservation->is_upcoming)
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                Upcoming
                            </span>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-charcoal-black">
                        {{ $reservation->duration_text }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-charcoal-black">
                            {{ $reservation->formatted_total_amount }}
                        </div>
                        @if ($reservation->discount_percentage > 0)
                            <div class="text-xs text-green-600">
                                {{ $reservation->discount_percentage }}% discount
                            </div>
                        @endif
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $reservation->status_badge_class }}">
                            {{ $reservation->status_text }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $reservation->payment_status_badge_class }}">
                            {{ $reservation->payment_status_text }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            @can('view', $reservation)
                                <a href="{{ route('reservations.show', $reservation) }}"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @if ($reservation->status === 'pending')
                                @can('update', $reservation)
                                    <button onclick="confirmReservation({{ $reservation->id }})"
                                        class="btn-bank bg-green-600 hover:bg-green-700 text-white">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </button>
                                @endcan
                            @endif

                            @if ($reservation->can_modify)
                                @can('update', $reservation)
                                    <a href="{{ route('reservations.edit', $reservation) }}"
                                        class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                            </path>
                                        </svg>
                                    </a>
                                @endcan
                            @endif

                            @if ($reservation->can_cancel)
                                @can('update', $reservation)
                                    <button onclick="cancelReservation({{ $reservation->id }})"
                                        class="btn-bank bg-red-600 hover:bg-red-700 text-white">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                @endcan
                            @endif

                            <!-- Dropdown for more actions -->
                            <div class="relative" x-data="{ open: false }">
                                <button @click="open = !open"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z">
                                        </path>
                                    </svg>
                                </button>
                                <div x-show="open" @click.away="open = false" x-transition
                                    class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 border border-light-gray">
                                    <div class="py-1">
                                        <a href="{{ route('reservations.invoice', $reservation) }}" target="_blank"
                                            class="block px-4 py-2 text-sm text-charcoal-black hover:bg-off-white">
                                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                                </path>
                                            </svg>
                                            View Invoice
                                        </a>
                                        @if ($reservation->payment_status !== 'paid')
                                            <a href="{{ route('reservation-payments.create', ['reservation_id' => $reservation->id]) }}"
                                                class="block px-4 py-2 text-sm text-charcoal-black hover:bg-off-white">
                                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                                    </path>
                                                </svg>
                                                Add Payment
                                            </a>
                                        @endif
                                        @if ($reservation->status === 'confirmed')
                                            <button onclick="markNoShow({{ $reservation->id }})"
                                                class="block w-full text-left px-4 py-2 text-sm text-charcoal-black hover:bg-off-white">
                                                <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor"
                                                    viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                        stroke-width="2"
                                                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z">
                                                    </path>
                                                </svg>
                                                Mark No Show
                                            </button>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center">
                            <svg class="w-12 h-12 text-dark-gray mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-8 0H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2">
                                </path>
                            </svg>
                            <h3 class="text-lg font-medium text-charcoal-black mb-2">No reservations found</h3>
                            <p class="text-dark-gray mb-4">Get started by creating your first reservation.</p>
                            @can('create', App\Models\Reservation::class)
                                <a href="{{ route('reservations.create') }}" class="btn-bank">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Create Reservation
                                </a>
                            @endcan
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<script>
    async function confirmReservation(reservationId) {
        if (!confirm('Are you sure you want to confirm this reservation?')) return;

        try {
            const response = await fetch(`/reservations/${reservationId}/confirm`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while confirming the reservation.');
        }
    }

    async function cancelReservation(reservationId) {
        const reason = prompt('Please provide a reason for cancellation:');
        if (!reason) return;

        try {
            const response = await fetch(`/reservations/${reservationId}/cancel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                },
                body: JSON.stringify({
                    reason
                })
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while cancelling the reservation.');
        }
    }

    async function markNoShow(reservationId) {
        if (!confirm('Are you sure you want to mark this reservation as no-show?')) return;

        try {
            const response = await fetch(`/reservations/${reservationId}/no-show`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while updating the reservation.');
        }
    }
</script>
