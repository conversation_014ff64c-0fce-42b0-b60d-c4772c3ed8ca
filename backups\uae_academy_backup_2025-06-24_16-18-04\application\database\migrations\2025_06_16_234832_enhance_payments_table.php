<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Add new columns for enhanced functionality
            $table->string('reference_number')->nullable()->after('status');
            $table->text('description')->nullable()->after('reference_number');

            // Modify status enum to include more payment statuses
            $table->enum('status', ['completed', 'pending', 'failed', 'refunded', 'cancelled', 'active', 'expired'])->default('pending')->change();

            // Add indexes for new columns
            $table->index('reference_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropIndex(['reference_number']);

            $table->dropColumn([
                'reference_number',
                'description'
            ]);

            // Revert status enum to original values
            $table->enum('status', ['active', 'expired', 'pending', 'cancelled'])->default('active')->change();
        });
    }
};
