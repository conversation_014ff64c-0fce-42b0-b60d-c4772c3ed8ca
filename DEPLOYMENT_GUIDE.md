# UAE English Sports Academy - Production Deployment Guide

## 📋 Pre-Deployment Checklist

### ✅ Backup Created
- **Backup File**: `uae_sports_academy_backup_20250716_190210.tar.gz` (58MB)
- **Location**: `/Users/<USER>/Sites/`
- **Contents**: Full project excluding node_modules, vendor, .git, logs, cache

### 🔍 Project Analysis Summary

**Database**: 35+ migrations, comprehensive seeding system
**Backend**: Laravel 11 with custom middleware, role-based auth
**Frontend**: Vite + TailwindCSS, Arabic/English support
**Features**: Multi-branch academy management, payments, uniforms, reservations

---

## 🚀 DEPLOYMENT STEPS

### PHASE 1: SERVER PREPARATION

#### 1.1 Server Requirements
```bash
# Minimum Requirements
- PHP 8.1+ with extensions: mbstring, xml, ctype, json, bcmath, openssl, pdo, tokenizer
- MySQL 8.0+ or MariaDB 10.3+
- Nginx 1.18+ or Apache 2.4+
- Node.js 16+ and npm
- Composer 2.x
- SSL Certificate
- Minimum 2GB RAM, 20GB storage
```

#### 1.2 Install Dependencies
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-mbstring php8.1-xml php8.1-ctype php8.1-json php8.1-bcmath php8.1-openssl php8.1-tokenizer php8.1-curl php8.1-zip
sudo apt install mysql-server nginx composer nodejs npm

# CentOS/RHEL
sudo yum install php81 php81-fpm php81-mysql php81-mbstring php81-xml php81-json php81-bcmath php81-openssl
sudo yum install mysql-server nginx composer nodejs npm
```

### PHASE 2: DATABASE SETUP

#### 2.1 Create Production Database
```sql
-- Connect to MySQL as root
mysql -u root -p

-- Create database with UTF8MB4 support (Arabic text)
CREATE DATABASE uae_english_sports_academy_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create dedicated user
CREATE USER 'uae_sports_user'@'localhost' IDENTIFIED BY 'STRONG_PASSWORD_HERE';
GRANT ALL PRIVILEGES ON uae_english_sports_academy_prod.* TO 'uae_sports_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### 2.2 Database Configuration Notes
- **Charset**: utf8mb4 (required for Arabic text support)
- **Collation**: utf8mb4_unicode_ci
- **Timezone**: Asia/Dubai (configured in app.php)
- **Foreign Keys**: Comprehensive constraints implemented

### PHASE 3: APPLICATION DEPLOYMENT

#### 3.1 Upload and Extract Files
```bash
# Upload project to server (example path)
cd /var/www/
sudo mkdir uae-sports-academy
sudo chown $USER:$USER uae-sports-academy
cd uae-sports-academy

# Extract your project files here
# Ensure proper ownership
sudo chown -R www-data:www-data /var/www/uae-sports-academy
sudo chmod -R 755 /var/www/uae-sports-academy
sudo chmod -R 775 storage bootstrap/cache
```

#### 3.2 Install PHP Dependencies
```bash
cd /var/www/uae-sports-academy
composer install --optimize-autoloader --no-dev
```

#### 3.3 Environment Configuration
```bash
# Copy and configure environment file
cp .env.example .env
nano .env
```

**Production .env Configuration:**
```env
# Application Settings
APP_NAME="LEADERS SPORTS SERVICES LLC SP"
APP_ENV=production
APP_KEY=base64:GENERATE_NEW_KEY_HERE
APP_DEBUG=false
APP_URL=https://yourdomain.com
APP_TIMEZONE=Asia/Dubai

# Localization
APP_LOCALE=en
APP_FALLBACK_LOCALE=en

# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=uae_english_sports_academy_prod
DB_USERNAME=uae_sports_user
DB_PASSWORD=STRONG_PASSWORD_HERE
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci

# Session & Cache (Database-driven for reliability)
SESSION_DRIVER=database
SESSION_LIFETIME=120
CACHE_STORE=database
QUEUE_CONNECTION=database

# Mail Configuration (Configure with your SMTP)
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# File Storage
FILESYSTEM_DISK=local

# Logging
LOG_CHANNEL=stack
LOG_LEVEL=error
LOG_STACK=single

# Security
BCRYPT_ROUNDS=12
```

#### 3.4 Generate Application Key
```bash
php artisan key:generate
```

### PHASE 4: DATABASE MIGRATION & SEEDING

#### 4.1 Run Migrations
```bash
# Run all migrations (35+ migrations)
php artisan migrate --force

# Verify migration status
php artisan migrate:status
```

#### 4.2 Seed Database
```bash
# Option 1: Basic seeding with admin user
php artisan db:seed --class=DatabaseSeeder

# Option 2: Comprehensive trial data (recommended for testing)
php artisan db:seed --class=WorkingTrialDataSeeder

# Option 3: Full trial data with inventory
php artisan db:seed --class=TrialDataSeeder
```

**Default Admin Credentials:**
- Email: `<EMAIL>`
- Password: `admin123`
- Role: `admin`

### PHASE 5: FRONTEND BUILD

#### 5.1 Install Node Dependencies
```bash
npm install
```

#### 5.2 Build Production Assets
```bash
# Build optimized assets
npm run build

# Verify build output
ls -la public/build/
```

### PHASE 6: WEB SERVER CONFIGURATION

#### 6.1 Nginx Configuration
```nginx
# /etc/nginx/sites-available/uae-sports-academy
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    root /var/www/uae-sports-academy/public;
    index index.php index.html;

    # SSL Configuration
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # File Upload Limits
    client_max_body_size 100M;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Static file caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### 6.2 Enable Site
```bash
sudo ln -s /etc/nginx/sites-available/uae-sports-academy /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### PHASE 7: OPTIMIZATION & CACHING

#### 7.1 Laravel Optimizations
```bash
# Clear all caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# Generate optimized files
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
```

#### 7.2 File Permissions
```bash
# Set proper permissions
sudo chown -R www-data:www-data /var/www/uae-sports-academy
sudo chmod -R 755 /var/www/uae-sports-academy
sudo chmod -R 775 storage bootstrap/cache public/storage
```

### PHASE 8: SCHEDULED TASKS

#### 8.1 Setup Cron Job
```bash
# Edit crontab for www-data user
sudo crontab -u www-data -e

# Add Laravel scheduler
* * * * * cd /var/www/uae-sports-academy && php artisan schedule:run >> /dev/null 2>&1
```

**Scheduled Tasks:**
- Daily payment expiration at midnight
- Cache cleanup
- Log rotation

### PHASE 9: SECURITY HARDENING

#### 9.1 File Security
```bash
# Hide sensitive files
sudo chmod 600 .env
sudo chmod -R 644 config/
sudo chmod -R 644 database/
```

#### 9.2 Additional Security
- Change default admin password immediately
- Enable firewall (UFW/iptables)
- Regular security updates
- Monitor logs in `/var/log/nginx/` and `storage/logs/`

### PHASE 10: TESTING & VERIFICATION

#### 10.1 System Health Checks
```bash
# Test application
curl -I https://yourdomain.com/up

# Check database connection
php artisan tinker
>>> DB::connection()->getPdo();

# Verify scheduled tasks
php artisan schedule:list
```

#### 10.2 Feature Testing
- [ ] Login with admin credentials
- [ ] Create branch and academy
- [ ] Add student and payment
- [ ] Test Arabic/English switching
- [ ] Verify file uploads work
- [ ] Check reports generation
- [ ] Test uniform inventory
- [ ] Verify reservation system

---

## 🔧 MAINTENANCE & MONITORING

### Daily Tasks
- Monitor error logs: `tail -f storage/logs/laravel.log`
- Check disk space and database size
- Verify backup completion

### Weekly Tasks
- Update system packages
- Review application logs
- Performance monitoring

### Monthly Tasks
- Security updates
- Database optimization
- SSL certificate renewal check

---

## 🚨 TROUBLESHOOTING

### Common Issues
1. **500 Error**: Check `storage/logs/laravel.log`
2. **Database Connection**: Verify credentials in `.env`
3. **File Permissions**: Ensure www-data owns storage/
4. **Arabic Text Issues**: Confirm utf8mb4 charset
5. **Asset Loading**: Run `npm run build` and clear cache

### Emergency Contacts
- Server Admin: [Your contact]
- Database Admin: [Your contact]
- Developer: [Your contact]

---

## 📊 DEPLOYMENT SUMMARY

**Total Migrations**: 35+
**Database Tables**: Users, Branches, Academies, Programs, Students, Payments, Uniforms, Reservations, etc.
**Key Features**: Multi-language, Role-based access, VAT calculations, Inventory management
**Security**: CSRF protection, Role middleware, Secure sessions
**Performance**: Database caching, Asset optimization, Gzip compression

**Deployment Status**: ✅ Ready for Production

---

## 📁 PROJECT STRUCTURE OVERVIEW

```
uae_english_sports_academy/
├── app/
│   ├── Http/
│   │   ├── Controllers/          # All application controllers
│   │   ├── Middleware/           # Custom middleware (Role, Localization)
│   │   └── Requests/            # Form request validation
│   ├── Models/                  # Eloquent models with relationships
│   ├── Policies/               # Authorization policies
│   └── Console/Commands/       # Custom artisan commands
├── database/
│   ├── migrations/             # 35+ database migrations
│   └── seeders/               # Comprehensive seeding system
├── resources/
│   ├── views/                 # Blade templates (Arabic/English)
│   ├── css/                   # Custom stylesheets
│   ├── js/                    # Frontend JavaScript
│   └── lang/                  # Translation files
├── public/
│   ├── build/                 # Compiled assets (Vite)
│   ├── images/               # Application images
│   └── storage/              # Symlinked storage
├── storage/
│   ├── app/public/           # File uploads
│   └── logs/                 # Application logs
└── config/                   # Laravel configuration files
```

## 🔄 POST-DEPLOYMENT CONFIGURATION

### SSL Certificate Setup (Let's Encrypt)
```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Generate SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal setup
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Database Backup Strategy
```bash
# Create backup script
sudo nano /usr/local/bin/backup-uae-sports.sh

#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/uae-sports"
DB_NAME="uae_english_sports_academy_prod"
DB_USER="uae_sports_user"
DB_PASS="YOUR_DB_PASSWORD"

mkdir -p $BACKUP_DIR

# Database backup
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Application backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz -C /var/www uae-sports-academy \
    --exclude=node_modules --exclude=vendor --exclude=storage/logs

# Keep only last 7 days
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

# Make executable and schedule
sudo chmod +x /usr/local/bin/backup-uae-sports.sh
sudo crontab -e
# Add: 0 2 * * * /usr/local/bin/backup-uae-sports.sh
```

### Monitoring Setup
```bash
# Install monitoring tools
sudo apt install htop iotop nethogs

# Log monitoring script
sudo nano /usr/local/bin/monitor-uae-sports.sh

#!/bin/bash
LOG_FILE="/var/www/uae-sports-academy/storage/logs/laravel.log"
ERROR_COUNT=$(grep -c "ERROR" $LOG_FILE | tail -100)

if [ $ERROR_COUNT -gt 10 ]; then
    echo "High error count detected: $ERROR_COUNT" | mail -s "UAE Sports Academy Alert" <EMAIL>
fi
```

## 🔐 SECURITY CHECKLIST

### Application Security
- [ ] Change default admin password
- [ ] Remove demo/test accounts
- [ ] Configure CSRF protection
- [ ] Enable rate limiting
- [ ] Set up proper file permissions
- [ ] Configure secure headers
- [ ] Enable HTTPS redirect
- [ ] Set up fail2ban for SSH

### Database Security
- [ ] Use strong database passwords
- [ ] Limit database user privileges
- [ ] Enable MySQL slow query log
- [ ] Configure database firewall rules
- [ ] Regular security updates

### Server Security
- [ ] Configure firewall (UFW)
- [ ] Disable root SSH login
- [ ] Set up SSH key authentication
- [ ] Configure automatic security updates
- [ ] Monitor system logs
- [ ] Set up intrusion detection

## 📈 PERFORMANCE OPTIMIZATION

### Database Optimization
```sql
-- Add indexes for frequently queried columns
ALTER TABLE students ADD INDEX idx_branch_academy (branch_id, academy_id);
ALTER TABLE payments ADD INDEX idx_status_date (status, created_at);
ALTER TABLE attendances ADD INDEX idx_student_date (student_id, attendance_date);

-- Optimize tables
OPTIMIZE TABLE students, payments, attendances, uniforms;
```

### Application Optimization
```bash
# Enable OPcache
sudo nano /etc/php/8.1/fpm/php.ini

# Add/modify:
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1

# Restart PHP-FPM
sudo systemctl restart php8.1-fpm
```

### Nginx Optimization
```nginx
# Add to nginx.conf
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# Enable HTTP/2
listen 443 ssl http2;

# Optimize worker processes
worker_processes auto;
worker_connections 1024;
```

## 🚀 DEPLOYMENT AUTOMATION (Optional)

### GitHub Actions Deployment
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2

    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.KEY }}
        script: |
          cd /var/www/uae-sports-academy
          git pull origin main
          composer install --no-dev --optimize-autoloader
          npm run build
          php artisan migrate --force
          php artisan optimize
          sudo systemctl reload nginx
```

## 📞 SUPPORT & MAINTENANCE

### Regular Maintenance Schedule
- **Daily**: Monitor logs, check system resources
- **Weekly**: Review performance metrics, update packages
- **Monthly**: Security patches, database optimization
- **Quarterly**: Full system backup verification, security audit

### Emergency Procedures
1. **Site Down**: Check nginx status, PHP-FPM, database connection
2. **Database Issues**: Check MySQL logs, verify credentials
3. **High Load**: Monitor with htop, check slow queries
4. **Security Breach**: Isolate system, check logs, change passwords

### Contact Information
- **Technical Support**: [Your contact details]
- **Emergency Hotline**: [24/7 contact]
- **Hosting Provider**: [Provider contact]

---

## ✅ FINAL DEPLOYMENT VERIFICATION

### Pre-Go-Live Checklist
- [ ] All migrations executed successfully
- [ ] Database seeded with initial data
- [ ] Admin user can login
- [ ] All major features tested
- [ ] SSL certificate installed and working
- [ ] Backups configured and tested
- [ ] Monitoring systems active
- [ ] Performance optimizations applied
- [ ] Security measures implemented
- [ ] Documentation updated

### Go-Live Steps
1. Point domain to production server
2. Test all critical functionality
3. Monitor error logs for first 24 hours
4. Verify scheduled tasks are running
5. Confirm backup systems working
6. Update DNS TTL back to normal

**🎉 DEPLOYMENT COMPLETE - UAE English Sports Academy is now live!**
