<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'branch_id',
        'academy_id',
        'status',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be appended to arrays.
     *
     * @var array
     */
    protected $appends = [
        'role_text',
        'status_text',
        'status_badge_class',
        'role_badge_class',
        'formatted_created_at',
        'last_login_text',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'status' => 'boolean',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Get the branch that owns the user.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the user.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the role text attribute.
     */
    public function getRoleTextAttribute(): string
    {
        return match ($this->role) {
            'admin' => 'System Administrator',
            'branch_manager' => 'Branch Manager',
            'academy_manager' => 'Academy Manager',
            default => 'Unknown Role',
        };
    }

    /**
     * Get the status text attribute.
     */
    public function getStatusTextAttribute(): string
    {
        return $this->status ? 'Active' : 'Inactive';
    }

    /**
     * Get the status badge class attribute.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return $this->status ? 'badge-success' : 'badge-danger';
    }

    /**
     * Get the role badge class attribute.
     */
    public function getRoleBadgeClassAttribute(): string
    {
        return match ($this->role) {
            'admin' => 'badge-danger',
            'branch_manager' => 'badge-warning',
            'academy_manager' => 'badge-info',
            default => 'badge-secondary',
        };
    }

    /**
     * Get the formatted created at attribute.
     */
    public function getFormattedCreatedAtAttribute(): string
    {
        return $this->created_at->format('d/m/Y H:i');
    }

    /**
     * Get the last login text attribute.
     */
    public function getLastLoginTextAttribute(): string
    {
        if (!$this->last_login_at) {
            return 'Never logged in';
        }

        return $this->last_login_at->diffForHumans();
    }

    /**
     * Scope a query to only include active users.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', true);
    }

    /**
     * Scope a query to only include inactive users.
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', false);
    }

    /**
     * Scope a query to filter by role.
     */
    public function scopeRole(Builder $query, string $role): Builder
    {
        return $query->where('role', $role);
    }

    /**
     * Scope a query to search users.
     */
    public function scopeSearch(Builder $query, ?string $search): Builder
    {
        if (!$search) {
            return $query;
        }

        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('email', 'like', "%{$search}%")
                ->orWhereHas('branch', function ($branchQuery) use ($search) {
                    $branchQuery->where('name', 'like', "%{$search}%");
                })
                ->orWhereHas('academy', function ($academyQuery) use ($search) {
                    $academyQuery->where('name', 'like', "%{$search}%");
                });
        });
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is branch manager.
     */
    public function isBranchManager(): bool
    {
        return $this->role === 'branch_manager';
    }

    /**
     * Check if user is academy manager.
     */
    public function isAcademyManager(): bool
    {
        return $this->role === 'academy_manager';
    }

    /**
     * Get user statistics.
     */
    public static function getStatistics(): array
    {
        return [
            'total_users' => self::count(),
            'active_users' => self::active()->count(),
            'inactive_users' => self::inactive()->count(),
            'admin_users' => self::role('admin')->count(),
            'branch_managers' => self::role('branch_manager')->count(),
            'academy_managers' => self::role('academy_manager')->count(),
        ];
    }
}
