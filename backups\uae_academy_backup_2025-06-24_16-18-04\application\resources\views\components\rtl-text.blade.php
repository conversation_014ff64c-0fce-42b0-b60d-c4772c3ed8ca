{{-- UAE English Sports Academy - RTL Text Wrapper Component --}}
{{-- Smart RTL text wrapper with automatic direction detection --}}

@props([
    'text' => '',
    'locale' => null,
    'forceDirection' => null,
    'class' => '',
    'tag' => 'span',
    'detectDirection' => true,
])

@php
    $locale = $locale ?? app()->getLocale();
    $isRtl = $locale === 'ar';
    
    // Get the text content
    $content = $text ?: $slot;
    
    // Determine text direction
    if ($forceDirection) {
        $direction = $forceDirection;
    } elseif ($detectDirection && $content) {
        // Auto-detect direction based on content
        $direction = detectTextDirection($content);
    } else {
        $direction = $isRtl ? 'rtl' : 'ltr';
    }
    
    // CSS classes
    $classes = [
        $class,
        'rtl-text-wrapper',
        "dir-{$direction}",
        $direction === 'rtl' ? 'text-right' : 'text-left',
    ];
    
    // Attributes
    $attributes = [
        'dir' => $direction,
        'lang' => $locale,
        'data-direction' => $direction,
        'data-locale' => $locale,
    ];
    
    /**
     * Detect text direction based on content
     */
    function detectTextDirection($text) {
        // Remove HTML tags and get plain text
        $plainText = strip_tags($text);
        
        // Count RTL characters (Arabic, Hebrew, etc.)
        $rtlCount = preg_match_all('/[\x{0590}-\x{05FF}\x{0600}-\x{06FF}\x{0750}-\x{077F}\x{08A0}-\x{08FF}\x{FB50}-\x{FDFF}\x{FE70}-\x{FEFF}]/u', $plainText);
        
        // Count LTR characters (Latin, numbers, etc.)
        $ltrCount = preg_match_all('/[A-Za-z0-9]/u', $plainText);
        
        // Determine direction based on character count
        if ($rtlCount > $ltrCount) {
            return 'rtl';
        } elseif ($ltrCount > $rtlCount) {
            return 'ltr';
        } else {
            // Default to current locale direction
            return app()->getLocale() === 'ar' ? 'rtl' : 'ltr';
        }
    }
@endphp

<{{ $tag }} 
    class="{{ implode(' ', array_filter($classes)) }}"
    @foreach($attributes as $attr => $value)
        {{ $attr }}="{{ $value }}"
    @endforeach
>
    @if($text)
        {{ $text }}
    @else
        {{ $slot }}
    @endif
</{{ $tag }}>

{{-- Component Styles --}}
@once
@push('styles')
<style>
/* RTL Text Wrapper Styles */
.rtl-text-wrapper {
    display: inline-block;
    width: 100%;
}

.dir-rtl {
    direction: rtl;
    text-align: right;
    font-family: var(--font-family-arabic, 'IBM Plex Sans Arabic', sans-serif);
}

.dir-ltr {
    direction: ltr;
    text-align: left;
    font-family: var(--font-family-primary, 'IBM Plex Sans', sans-serif);
}

/* Mixed content handling */
.rtl-text-wrapper.mixed-content {
    unicode-bidi: embed;
}

.rtl-text-wrapper .number,
.rtl-text-wrapper .currency,
.rtl-text-wrapper .date,
.rtl-text-wrapper .email,
.rtl-text-wrapper .url {
    direction: ltr;
    unicode-bidi: embed;
    display: inline-block;
}

/* Text alignment utilities */
.rtl-text-wrapper.text-center {
    text-align: center !important;
}

.rtl-text-wrapper.text-justify {
    text-align: justify !important;
}

/* Block-level wrapper */
.rtl-text-wrapper.block {
    display: block;
    width: 100%;
}

/* Inline wrapper */
.rtl-text-wrapper.inline {
    display: inline;
    width: auto;
}

/* Typography adjustments for RTL */
.dir-rtl {
    line-height: 1.8; /* Better for Arabic text */
    letter-spacing: 0.02em;
}

.dir-ltr {
    line-height: 1.6;
    letter-spacing: normal;
}

/* Paragraph spacing */
.rtl-text-wrapper p {
    margin-bottom: 1rem;
}

.dir-rtl p {
    text-align: right;
}

.dir-ltr p {
    text-align: left;
}

/* List styling */
.dir-rtl ul,
.dir-rtl ol {
    padding-right: 1.5rem;
    padding-left: 0;
}

.dir-ltr ul,
.dir-ltr ol {
    padding-left: 1.5rem;
    padding-right: 0;
}

/* Quote styling */
.dir-rtl blockquote {
    border-right: 4px solid var(--leaders-red, #E53E3E);
    border-left: none;
    padding-right: 1rem;
    padding-left: 0;
    margin-right: 0;
    margin-left: 1rem;
}

.dir-ltr blockquote {
    border-left: 4px solid var(--leaders-red, #E53E3E);
    border-right: none;
    padding-left: 1rem;
    padding-right: 0;
    margin-left: 0;
    margin-right: 1rem;
}

/* Code blocks */
.rtl-text-wrapper code,
.rtl-text-wrapper pre {
    direction: ltr;
    text-align: left;
    font-family: 'Courier New', monospace;
}

/* Tables within RTL text */
.dir-rtl table {
    direction: rtl;
}

.dir-rtl th,
.dir-rtl td {
    text-align: right;
}

/* Form elements */
.dir-rtl input,
.dir-rtl textarea,
.dir-rtl select {
    text-align: right;
}

.dir-ltr input,
.dir-ltr textarea,
.dir-ltr select {
    text-align: left;
}

/* Links */
.rtl-text-wrapper a {
    color: var(--leaders-red, #E53E3E);
    text-decoration: none;
    transition: color 0.2s ease;
}

.rtl-text-wrapper a:hover {
    color: var(--deep-red, #C53030);
    text-decoration: underline;
}

/* Emphasis */
.rtl-text-wrapper strong,
.rtl-text-wrapper b {
    font-weight: 600;
}

.rtl-text-wrapper em,
.rtl-text-wrapper i {
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dir-rtl {
        line-height: 1.7;
    }
    
    .dir-ltr {
        line-height: 1.5;
    }
    
    .rtl-text-wrapper {
        font-size: 0.875rem;
    }
}

/* Print styles */
@media print {
    .rtl-text-wrapper {
        color: black !important;
    }
    
    .rtl-text-wrapper a {
        color: black !important;
        text-decoration: underline !important;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .rtl-text-wrapper {
        font-weight: 500;
    }
    
    .rtl-text-wrapper a {
        font-weight: 600;
        text-decoration: underline;
    }
}

/* Dark theme support */
.dark .rtl-text-wrapper {
    color: var(--pure-white, #FFFFFF);
}

.dark .rtl-text-wrapper a {
    color: var(--leaders-red, #E53E3E);
}

/* Selection styling */
.rtl-text-wrapper::selection {
    background-color: rgba(229, 62, 62, 0.2);
}

.rtl-text-wrapper::-moz-selection {
    background-color: rgba(229, 62, 62, 0.2);
}

/* Animation for dynamic content */
.rtl-text-wrapper.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Accessibility */
.rtl-text-wrapper:focus {
    outline: 2px solid var(--leaders-red, #E53E3E);
    outline-offset: 2px;
    border-radius: 2px;
}

/* Loading state */
.rtl-text-wrapper.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    color: transparent;
    border-radius: 4px;
    min-height: 1em;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Error state */
.rtl-text-wrapper.error {
    color: var(--error-red, #E53E3E);
    border: 1px solid var(--error-red, #E53E3E);
    background-color: rgba(229, 62, 62, 0.05);
    padding: 0.5rem;
    border-radius: 4px;
}

/* Success state */
.rtl-text-wrapper.success {
    color: var(--success-green, #38A169);
    border: 1px solid var(--success-green, #38A169);
    background-color: rgba(56, 161, 105, 0.05);
    padding: 0.5rem;
    border-radius: 4px;
}
</style>
@endpush
@endonce
