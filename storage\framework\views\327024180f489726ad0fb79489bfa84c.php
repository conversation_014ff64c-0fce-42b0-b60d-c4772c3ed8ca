<?php $__env->startSection('title', 'Payment Management'); ?>

<?php $__env->startSection('page-header'); ?>
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-charcoal-black">Payment Management</h1>
            <p class="mt-1 text-sm text-dark-gray">Manage student payments, track transactions, and monitor financial status
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <span
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-leaders-red/10 text-leaders-red">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <?php echo e($payments->total()); ?> Total Payments
            </span>
        </div>
        <div class="flex items-center space-x-3">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Payment::class)): ?>
                <a href="<?php echo e(route('payments.create')); ?>" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                        </path>
                    </svg>
                    Add New Payment
                </a>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('export', App\Models\Payment::class)): ?>
                <div class="flex items-center space-x-2">
                    <a href="<?php echo e(route('payments.export.excel', request()->query())); ?>" class="btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                        Export Excel
                    </a>
                    <a href="<?php echo e(route('payments.export.pdf', request()->query())); ?>" class="btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                            </path>
                        </svg>
                        Export PDF
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="space-y-6" x-data="paymentManagement()" x-init="init()">
        <!-- Advanced Search & Filters -->
        <?php echo $__env->make('payments._filters', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Statistics Cards -->
        <?php echo $__env->make('payments._stats', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Bulk Actions -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bulkAction', App\Models\Payment::class)): ?>
            <div x-show="selectedPayments.length > 0" x-transition class="bank-card bg-blue-50 border-blue-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <span class="text-sm font-medium text-blue-900">
                            <span x-text="selectedPayments.length"></span> payment(s) selected
                        </span>
                        <select x-model="bulkAction" class="form-select-sm border-blue-300 rounded-md">
                            <option value="">Choose action...</option>
                            <option value="complete">Mark as Completed</option>
                            <option value="cancel">Cancel Payments</option>
                            <option value="mark_pending">Mark as Pending</option>
                            <option value="delete">Delete Payments</option>
                        </select>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button @click="executeBulkAction()" :disabled="!bulkAction" class="btn-bank-sm">
                            Execute Action
                        </button>
                        <button @click="clearSelection()" class="btn-bank-outline-sm">
                            Clear Selection
                        </button>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Payments Table/Grid -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">Payments List</h3>
                    <p class="bank-card-subtitle">Manage and track all payment transactions</p>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="flex items-center space-x-1 bg-light-gray rounded-lg p-1">
                        <button @click="viewMode = 'table'" :class="viewMode === 'table' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded-md transition-all duration-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 10h18M3 6h18m-9 8h9m-9 4h9m-9-8h9m-9 4h9"></path>
                            </svg>
                        </button>
                        <button @click="viewMode = 'grid'" :class="viewMode === 'grid' ? 'bg-white shadow-sm' : ''"
                            class="p-2 rounded-md transition-all duration-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z">
                                </path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <div class="bank-card-body p-0">
                <!-- Table View -->
                <div x-show="viewMode === 'table'" x-transition>
                    <?php echo $__env->make('payments._table', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Grid View -->
                <div x-show="viewMode === 'grid'" x-transition>
                    <?php echo $__env->make('payments._grid', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                </div>

                <!-- Pagination -->
                <?php if($payments->hasPages()): ?>
                    <div class="px-6 py-4 border-t border-light-gray">
                        <?php echo e($payments->links()); ?>

                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>
        function paymentManagement() {
            return {
                selectedPayments: [],
                bulkAction: '',
                viewMode: localStorage.getItem('paymentViewMode') || 'table',

                init() {
                    this.$watch('viewMode', (value) => {
                        localStorage.setItem('paymentViewMode', value);
                    });

                    // Check for expired payments on page load
                    setTimeout(() => {
                        if (typeof checkExpiredPayments === 'function') {
                            checkExpiredPayments();
                        }
                    }, 1000);
                },

                togglePaymentSelection(paymentId) {
                    const index = this.selectedPayments.indexOf(paymentId);
                    if (index > -1) {
                        this.selectedPayments.splice(index, 1);
                    } else {
                        this.selectedPayments.push(paymentId);
                    }

                    // Update the select all checkbox state
                    this.updateSelectAllCheckbox();
                },

                updateSelectAllCheckbox() {
                    const selectAllCheckbox = document.getElementById('selectAllPayments');
                    const allCheckboxes = document.querySelectorAll('input[name="payment_ids[]"]');
                    const checkedCheckboxes = document.querySelectorAll('input[name="payment_ids[]"]:checked');

                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length && allCheckboxes.length > 0;
                        selectAllCheckbox.indeterminate = checkedCheckboxes.length > 0 && checkedCheckboxes.length < allCheckboxes.length;
                    }
                },

                toggleAllPayments(isChecked) {
                    const checkboxes = document.querySelectorAll('input[name="payment_ids[]"]');

                    checkboxes.forEach(checkbox => {
                        checkbox.checked = isChecked;
                        const paymentId = parseInt(checkbox.value);

                        if (isChecked) {
                            if (!this.selectedPayments.includes(paymentId)) {
                                this.selectedPayments.push(paymentId);
                            }
                        } else {
                            const index = this.selectedPayments.indexOf(paymentId);
                            if (index > -1) {
                                this.selectedPayments.splice(index, 1);
                            }
                        }
                    });
                },

                clearSelection() {
                    this.selectedPayments = [];
                    this.bulkAction = '';

                    // Uncheck all checkboxes
                    const checkboxes = document.querySelectorAll('input[name="payment_ids[]"]');
                    const selectAllCheckbox = document.getElementById('selectAllPayments');

                    checkboxes.forEach(checkbox => checkbox.checked = false);
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = false;
                        selectAllCheckbox.indeterminate = false;
                    }
                },

                async executeBulkAction() {
                    if (!this.bulkAction || this.selectedPayments.length === 0) return;

                    if (!confirm(
                            `Are you sure you want to ${this.bulkAction} ${this.selectedPayments.length} payment(s)?`
                        )) {
                        return;
                    }

                    try {
                        const response = await fetch('<?php echo e(route('payments.bulk-action')); ?>', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            },
                            body: JSON.stringify({
                                action: this.bulkAction,
                                payment_ids: this.selectedPayments
                            })
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while executing bulk action.');
                    }
                }
            }
        }

        async function toggleStatus(paymentId) {
            try {
                const response = await fetch(`/payments/${paymentId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                    window.location.reload();
                } else {
                    showNotification('error', result.message);
                }
            } catch (error) {
                showNotification('error', 'An error occurred while updating status.');
            }
        }

        async function deletePayment(paymentId) {
            if (!confirm('Are you sure you want to delete this payment? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/payments/${paymentId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const result = await response.json();

                if (result.success) {
                    if (typeof showNotification === 'function') {
                        showNotification('success', result.message);
                    } else {
                        alert(result.message);
                    }
                    window.location.reload();
                } else {
                    if (typeof showNotification === 'function') {
                        showNotification('error', result.message);
                    } else {
                        alert('Error: ' + result.message);
                    }
                }
            } catch (error) {
                if (typeof showNotification === 'function') {
                    showNotification('error', 'An error occurred while deleting the payment.');
                } else {
                    alert('An error occurred while deleting the payment.');
                }
            }
        }
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xampp\htdocs\erp-login2\resources\views/payments/index.blade.php ENDPATH**/ ?>