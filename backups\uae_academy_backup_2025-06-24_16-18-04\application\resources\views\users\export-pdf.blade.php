<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users Export - Sports Academy</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #E53E3E;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #E53E3E;
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0;
            color: #666;
        }
        
        .stats-section {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 10px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: #E53E3E;
        }
        
        .stat-label {
            font-size: 10px;
            color: #666;
            text-transform: uppercase;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .table th,
        .table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .table th {
            background-color: #E53E3E;
            color: white;
            font-weight: bold;
            font-size: 11px;
        }
        
        .table td {
            font-size: 10px;
        }
        
        .table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .badge-success {
            background-color: #38A169;
            color: white;
        }
        
        .badge-danger {
            background-color: #E53E3E;
            color: white;
        }
        
        .badge-warning {
            background-color: #DD6B20;
            color: white;
        }
        
        .badge-info {
            background-color: #3182CE;
            color: white;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .header {
                margin-bottom: 20px;
            }
            
            .table {
                font-size: 9px;
            }
            
            .table th,
            .table td {
                padding: 5px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>UAE English Sports Academy</h1>
        <p>User Management Report</p>
        <p>Generated on {{ date('d/m/Y H:i') }}</p>
    </div>

    <!-- Statistics Section -->
    <div class="stats-section">
        <h3 style="margin: 0 0 10px 0; color: #E53E3E;">User Statistics</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number">{{ number_format($stats['total_users']) }}</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ number_format($stats['active_users']) }}</div>
                <div class="stat-label">Active Users</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ number_format($stats['inactive_users']) }}</div>
                <div class="stat-label">Inactive Users</div>
            </div>
        </div>
        
        <div class="stats-grid" style="margin-top: 15px;">
            <div class="stat-item">
                <div class="stat-number">{{ number_format($stats['admin_users']) }}</div>
                <div class="stat-label">Administrators</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ number_format($stats['branch_managers']) }}</div>
                <div class="stat-label">Branch Managers</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ number_format($stats['academy_managers']) }}</div>
                <div class="stat-label">Academy Managers</div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    @if($users->count() > 0)
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Branch</th>
                    <th>Academy</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Last Login</th>
                </tr>
            </thead>
            <tbody>
                @foreach($users as $user)
                    <tr>
                        <td>{{ $user->id }}</td>
                        <td>{{ $user->name }}</td>
                        <td>{{ $user->email }}</td>
                        <td>
                            <span class="badge {{ $user->role_badge_class === 'badge-danger' ? 'badge-danger' : ($user->role_badge_class === 'badge-warning' ? 'badge-warning' : 'badge-info') }}">
                                {{ $user->role_text }}
                            </span>
                        </td>
                        <td>{{ $user->branch?->name ?? 'N/A' }}</td>
                        <td>{{ $user->academy?->name ?? 'N/A' }}</td>
                        <td>
                            <span class="badge {{ $user->status ? 'badge-success' : 'badge-danger' }}">
                                {{ $user->status_text }}
                            </span>
                        </td>
                        <td>{{ $user->formatted_created_at }}</td>
                        <td>{{ $user->last_login_text }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div class="no-data">
            <h3>No Users Found</h3>
            <p>No users match the current filter criteria.</p>
        </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>
            <strong>UAE English Sports Academy</strong> - User Management System<br>
            Report generated on {{ date('d/m/Y') }} at {{ date('H:i') }}<br>
            Total Records: {{ $users->count() }}
        </p>
    </div>
</body>
</html>
