<?php

namespace App\Helpers;

use Illuminate\Support\Facades\App;
use App\Services\LanguageService;

class TranslationHelper
{
    /**
     * Smart translation with context awareness
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @param string|null $context
     * @return string
     */
    public static function smartTrans(string $key, array $replace = [], string $locale = null, string $context = null): string
    {
        $locale = $locale ?: App::getLocale();
        
        // Try context-specific translation first
        if ($context) {
            $contextKey = "{$context}.{$key}";
            $translation = trans($contextKey, $replace, $locale);
            if ($translation !== $contextKey) {
                return $translation;
            }
        }
        
        // Try regular translation
        $translation = trans($key, $replace, $locale);
        
        // If translation not found and not in fallback locale, try fallback
        if ($translation === $key && $locale !== 'en') {
            $translation = trans($key, $replace, 'en');
        }
        
        return $translation;
    }
    
    /**
     * Pluralization with Arabic support
     *
     * @param string $key
     * @param int $count
     * @param array $replace
     * @param string|null $locale
     * @return string
     */
    public static function transChoice(string $key, int $count, array $replace = [], string $locale = null): string
    {
        $locale = $locale ?: App::getLocale();
        
        // Arabic has complex pluralization rules
        if ($locale === 'ar') {
            return self::arabicPluralization($key, $count, $replace);
        }
        
        // English pluralization
        return trans_choice($key, $count, $replace, $locale);
    }
    
    /**
     * Arabic pluralization rules
     *
     * @param string $key
     * @param int $count
     * @param array $replace
     * @return string
     */
    private static function arabicPluralization(string $key, int $count, array $replace = []): string
    {
        $replace['count'] = $count;
        
        // Arabic pluralization rules:
        // 0: zero form
        // 1: singular form
        // 2: dual form
        // 3-10: few form
        // 11+: many form
        
        if ($count === 0) {
            $pluralKey = $key . '.zero';
        } elseif ($count === 1) {
            $pluralKey = $key . '.one';
        } elseif ($count === 2) {
            $pluralKey = $key . '.two';
        } elseif ($count >= 3 && $count <= 10) {
            $pluralKey = $key . '.few';
        } else {
            $pluralKey = $key . '.many';
        }
        
        $translation = trans($pluralKey, $replace, 'ar');
        
        // Fallback to English if Arabic translation not found
        if ($translation === $pluralKey) {
            return trans_choice($key, $count, $replace, 'en');
        }
        
        return $translation;
    }
    
    /**
     * Get translation with HTML attributes for RTL support
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @return array
     */
    public static function transWithAttributes(string $key, array $replace = [], string $locale = null): array
    {
        $locale = $locale ?: App::getLocale();
        $translation = self::smartTrans($key, $replace, $locale);
        
        return [
            'text' => $translation,
            'dir' => LanguageService::getLocaleConfig($locale)['direction'],
            'lang' => $locale,
        ];
    }
    
    /**
     * Get module-specific translations
     *
     * @param string $module
     * @param string|null $locale
     * @return array
     */
    public static function getModuleTranslations(string $module, string $locale = null): array
    {
        $locale = $locale ?: App::getLocale();
        
        try {
            return trans($module, [], $locale);
        } catch (\Exception $e) {
            // Fallback to English if module translations not found
            if ($locale !== 'en') {
                return trans($module, [], 'en');
            }
            return [];
        }
    }
    
    /**
     * Get common translations used across the application
     *
     * @param string|null $locale
     * @return array
     */
    public static function getCommonTranslations(string $locale = null): array
    {
        $locale = $locale ?: App::getLocale();
        
        return [
            'actions' => [
                'create' => self::smartTrans('common.create', [], $locale),
                'edit' => self::smartTrans('common.edit', [], $locale),
                'delete' => self::smartTrans('common.delete', [], $locale),
                'view' => self::smartTrans('common.view', [], $locale),
                'save' => self::smartTrans('common.save', [], $locale),
                'cancel' => self::smartTrans('common.cancel', [], $locale),
                'back' => self::smartTrans('common.back', [], $locale),
                'next' => self::smartTrans('common.next', [], $locale),
                'previous' => self::smartTrans('common.previous', [], $locale),
                'search' => self::smartTrans('common.search', [], $locale),
                'filter' => self::smartTrans('common.filter', [], $locale),
                'export' => self::smartTrans('common.export', [], $locale),
                'import' => self::smartTrans('common.import', [], $locale),
            ],
            'status' => [
                'active' => self::smartTrans('common.active', [], $locale),
                'inactive' => self::smartTrans('common.inactive', [], $locale),
                'pending' => self::smartTrans('common.pending', [], $locale),
                'approved' => self::smartTrans('common.approved', [], $locale),
                'rejected' => self::smartTrans('common.rejected', [], $locale),
            ],
            'messages' => [
                'success' => self::smartTrans('common.success', [], $locale),
                'error' => self::smartTrans('common.error', [], $locale),
                'warning' => self::smartTrans('common.warning', [], $locale),
                'info' => self::smartTrans('common.info', [], $locale),
                'loading' => self::smartTrans('common.loading', [], $locale),
                'no_data' => self::smartTrans('common.no_data', [], $locale),
            ],
            'time' => [
                'created_at' => self::smartTrans('common.created_at', [], $locale),
                'updated_at' => self::smartTrans('common.updated_at', [], $locale),
                'deleted_at' => self::smartTrans('common.deleted_at', [], $locale),
                'today' => self::smartTrans('common.today', [], $locale),
                'yesterday' => self::smartTrans('common.yesterday', [], $locale),
                'tomorrow' => self::smartTrans('common.tomorrow', [], $locale),
            ],
        ];
    }
    
    /**
     * Get validation messages for current locale
     *
     * @param string|null $locale
     * @return array
     */
    public static function getValidationMessages(string $locale = null): array
    {
        $locale = $locale ?: App::getLocale();
        
        return [
            'required' => self::smartTrans('validation.required', [], $locale),
            'email' => self::smartTrans('validation.email', [], $locale),
            'unique' => self::smartTrans('validation.unique', [], $locale),
            'min' => self::smartTrans('validation.min', [], $locale),
            'max' => self::smartTrans('validation.max', [], $locale),
            'numeric' => self::smartTrans('validation.numeric', [], $locale),
            'string' => self::smartTrans('validation.string', [], $locale),
            'confirmed' => self::smartTrans('validation.confirmed', [], $locale),
        ];
    }
    
    /**
     * Format text for RTL display
     *
     * @param string $text
     * @param string|null $locale
     * @return string
     */
    public static function formatForRtl(string $text, string $locale = null): string
    {
        $locale = $locale ?: App::getLocale();
        
        if (LanguageService::getLocaleConfig($locale)['direction'] === 'rtl') {
            // Add RTL mark for proper text direction
            return "\u{202B}" . $text . "\u{202C}";
        }
        
        return $text;
    }
    
    /**
     * Get breadcrumb translations
     *
     * @param array $breadcrumbs
     * @param string|null $locale
     * @return array
     */
    public static function translateBreadcrumbs(array $breadcrumbs, string $locale = null): array
    {
        $locale = $locale ?: App::getLocale();
        $translated = [];
        
        foreach ($breadcrumbs as $breadcrumb) {
            $translated[] = [
                'title' => self::smartTrans($breadcrumb['title'], [], $locale),
                'url' => $breadcrumb['url'] ?? null,
                'active' => $breadcrumb['active'] ?? false,
            ];
        }
        
        return $translated;
    }
    
    /**
     * Get menu translations
     *
     * @param array $menu
     * @param string|null $locale
     * @return array
     */
    public static function translateMenu(array $menu, string $locale = null): array
    {
        $locale = $locale ?: App::getLocale();
        $translated = [];
        
        foreach ($menu as $item) {
            $translatedItem = [
                'title' => self::smartTrans($item['title'], [], $locale),
                'url' => $item['url'] ?? null,
                'icon' => $item['icon'] ?? null,
                'active' => $item['active'] ?? false,
            ];
            
            if (isset($item['children'])) {
                $translatedItem['children'] = self::translateMenu($item['children'], $locale);
            }
            
            $translated[] = $translatedItem;
        }
        
        return $translated;
    }
    
    /**
     * Get JavaScript translations for frontend
     *
     * @param array $keys
     * @param string|null $locale
     * @return array
     */
    public static function getJsTranslations(array $keys, string $locale = null): array
    {
        $locale = $locale ?: App::getLocale();
        $translations = [];
        
        foreach ($keys as $key) {
            $translations[$key] = self::smartTrans($key, [], $locale);
        }
        
        return $translations;
    }
}
