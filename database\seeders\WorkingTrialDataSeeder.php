<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Program;
use App\Models\Student;
use App\Models\Payment;
use App\Models\Uniform;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class WorkingTrialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating working trial data for UAE English Sports Academy...');

        // Create test users
        $this->createUsers();

        // Create branches
        $branches = $this->createBranches();

        // Create academies
        $academies = $this->createAcademies($branches);

        // Create programs
        $programs = $this->createPrograms($academies);

        // Create students
        $students = $this->createStudents($branches, $academies, $programs);

        // Create payments
        $this->createPayments($students, $programs);

        // Create uniform orders
        $this->createUniformOrders($students);

        $this->command->info('Working trial data created successfully!');
    }

    private function createUsers(): void
    {
        $users = [
            [
                'name' => 'Test Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'status' => true,
            ],
            [
                'name' => 'Test Branch Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'branch_manager',
                'status' => true,
            ],
            [
                'name' => 'Test Academy Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'academy_manager',
                'status' => true,
            ],
        ];

        foreach ($users as $userData) {
            User::firstOrCreate(['email' => $userData['email']], $userData);
        }
    }

    private function createBranches(): array
    {
        $branches = [
            [
                'name' => 'Dubai Main Branch',
                'location' => 'Sheikh Zayed Road, Dubai',
                'address' => 'Sheikh Zayed Road, Dubai, UAE',
                'phone' => '+971501234567',
                'email' => '<EMAIL>',
                'status' => true,
            ],
            [
                'name' => 'Abu Dhabi Branch',
                'location' => 'Corniche Road, Abu Dhabi',
                'address' => 'Corniche Road, Abu Dhabi, UAE',
                'phone' => '+971501234568',
                'email' => '<EMAIL>',
                'status' => true,
            ],
            [
                'name' => 'Sharjah Branch',
                'location' => 'Al Qasba, Sharjah',
                'address' => 'Al Qasba, Sharjah, UAE',
                'phone' => '+971501234569',
                'email' => '<EMAIL>',
                'status' => true,
            ],
        ];

        $createdBranches = [];
        foreach ($branches as $branchData) {
            $createdBranches[] = Branch::firstOrCreate(['name' => $branchData['name']], $branchData);
        }

        return $createdBranches;
    }

    private function createAcademies(array $branches): array
    {
        $academies = [
            [
                'name' => 'Football Academy',
                'description' => 'Professional football training for all ages',
                'address' => 'Sports Complex A',
                'coach_name' => 'Ahmed Al Mansouri',
                'coach_phone' => '+971501234570',
                'status' => true,
            ],
            [
                'name' => 'Basketball Academy',
                'description' => 'Basketball training and development',
                'address' => 'Sports Complex B',
                'coach_name' => 'Sarah Ahmed',
                'coach_phone' => '+971501234571',
                'status' => true,
            ],
            [
                'name' => 'Swimming Academy',
                'description' => 'Swimming lessons and competitive training',
                'address' => 'Aquatic Center',
                'coach_name' => 'Omar Al Shamsi',
                'coach_phone' => '+971501234572',
                'status' => true,
            ],
        ];

        $createdAcademies = [];
        foreach ($branches as $branch) {
            foreach ($academies as $academyData) {
                $academyData['branch_id'] = $branch->id;
                $academyData['name'] = $academyData['name'] . ' - ' . $branch->name;
                $createdAcademies[] = Academy::create($academyData);
            }
        }

        return $createdAcademies;
    }

    private function createPrograms(array $academies): array
    {
        $programs = [
            [
                'name' => 'Beginner Program',
                'days' => json_encode(['SAT', 'MON', 'WED']),
                'classes' => 3,
                'price' => 500.00,
                'currency' => 'AED',
                'start_time' => '16:00:00',
                'end_time' => '17:30:00',
                'max_students' => 20,
                'status' => true,
            ],
            [
                'name' => 'Intermediate Program',
                'days' => json_encode(['SUN', 'TUE', 'THU']),
                'classes' => 3,
                'price' => 800.00,
                'currency' => 'AED',
                'start_time' => '17:30:00',
                'end_time' => '19:00:00',
                'max_students' => 15,
                'status' => true,
            ],
            [
                'name' => 'Advanced Program',
                'days' => json_encode(['FRI', 'SAT', 'SUN']),
                'classes' => 3,
                'price' => 1200.00,
                'currency' => 'AED',
                'start_time' => '19:00:00',
                'end_time' => '20:30:00',
                'max_students' => 10,
                'status' => true,
            ],
        ];

        $createdPrograms = [];
        foreach ($academies as $academy) {
            foreach ($programs as $programData) {
                $programData['academy_id'] = $academy->id;
                $createdPrograms[] = Program::create($programData);
            }
        }

        return $createdPrograms;
    }

    private function createStudents(array $branches, array $academies, array $programs): array
    {
        $students = [
            ['full_name' => 'Ahmed Al Mansouri', 'email' => '<EMAIL>', 'phone' => '+971501234590', 'nationality' => 'UAE', 'birth_date' => '2010-05-15'],
            ['full_name' => 'Fatima Al Zahra', 'email' => '<EMAIL>', 'phone' => '+971501234591', 'nationality' => 'UAE', 'birth_date' => '2012-08-22'],
            ['full_name' => 'Omar Al Shamsi', 'email' => '<EMAIL>', 'phone' => '+971501234592', 'nationality' => 'UAE', 'birth_date' => '2009-12-10'],
            ['full_name' => 'Aisha Al Blooshi', 'email' => '<EMAIL>', 'phone' => '+971501234593', 'nationality' => 'UAE', 'birth_date' => '2011-03-18'],
            ['full_name' => 'Khalid Al Rashid', 'email' => '<EMAIL>', 'phone' => '+971501234594', 'nationality' => 'UAE', 'birth_date' => '2008-07-25'],
        ];

        $createdStudents = [];
        foreach ($programs as $program) {
            $academy = collect($academies)->firstWhere('id', $program->academy_id);
            $branch = collect($branches)->firstWhere('id', $academy->branch_id);

            foreach ($students as $index => $studentData) {
                $studentData['branch_id'] = $branch->id;
                $studentData['academy_id'] = $academy->id;
                $studentData['address'] = 'Dubai, UAE';
                $studentData['join_date'] = Carbon::now()->subDays(rand(30, 365));
                $studentData['status'] = 'active';

                $createdStudents[] = Student::create($studentData);
            }
        }

        return $createdStudents;
    }

    private function createPayments(array $students, array $programs): void
    {
        foreach ($students as $student) {
            $program = collect($programs)->firstWhere('academy_id', $student->academy_id);

            Payment::create([
                'student_id' => $student->id,
                'branch_id' => $student->branch_id,
                'academy_id' => $student->academy_id,
                'amount' => $program->price,
                'discount' => 0,
                'payment_method' => collect(['cash', 'card', 'bank_transfer'])->random(),
                'payment_date' => Carbon::now()->subDays(rand(1, 30)),
                'start_date' => Carbon::now()->subDays(rand(1, 30)),
                'end_date' => Carbon::now()->addDays(rand(30, 90)),
                'status' => collect(['active', 'expired'])->random(),
                'reset_num' => rand(1, 5),
                'class_time_from' => '16:00:00',
                'class_time_to' => '17:30:00',
                'note' => 'Trial payment data',
            ]);
        }
    }

    private function createUniformOrders(array $students): void
    {
        foreach (array_slice($students, 0, 10) as $student) {
            Uniform::create([
                'student_id' => $student->id,
                'branch_id' => $student->branch_id,
                'academy_id' => $student->academy_id,
                'order_date' => Carbon::now()->subDays(rand(1, 60)),
                'size' => collect(['S', 'M', 'L', 'XL'])->random(),
                'amount' => rand(50, 150),
                'currency' => 'AED',
                'branch_status' => collect(['pending', 'received', 'delivered'])->random(),
                'office_status' => collect(['pending', 'received', 'delivered'])->random(),
                'payment_method' => collect(['cash', 'card', 'bank_transfer'])->random(),
                'note' => 'Trial uniform order',
            ]);
        }
    }
}
