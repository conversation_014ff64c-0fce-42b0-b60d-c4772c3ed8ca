{{-- UAE English Sports Academy - Currency Formatting Component --}}
{{-- Premium currency display with RTL support and AED formatting --}}

@props([
    'amount' => 0,
    'currency' => 'AED',
    'showSymbol' => true,
    'showCode' => true,
    'precision' => 2,
    'locale' => null,
    'class' => 'currency-amount',
])

@php
    $locale = $locale ?? app()->getLocale();
    $isRtl = $locale === 'ar';
    $direction = $isRtl ? 'rtl' : 'ltr';
    
    // Format the amount based on locale
    if ($locale === 'ar') {
        // Arabic locale formatting
        $formattedAmount = number_format($amount, $precision, '.', ',');
        $currencySymbol = 'د.إ';
        $currencyCode = 'درهم';
    } else {
        // English locale formatting
        $formattedAmount = number_format($amount, $precision, '.', ',');
        $currencySymbol = 'AED';
        $currencyCode = 'AED';
    }
    
    // Remove unnecessary decimal places for whole numbers
    if ($precision > 0 && $amount == floor($amount)) {
        $formattedAmount = number_format($amount, 0, '.', ',');
    }
    
    // Build the display string
    $displayParts = [];
    
    if ($showSymbol) {
        if ($isRtl) {
            // Arabic: amount + symbol (e.g., "1,000 د.إ")
            $displayParts[] = $formattedAmount;
            $displayParts[] = $currencySymbol;
        } else {
            // English: symbol + amount (e.g., "AED 1,000")
            $displayParts[] = $currencySymbol;
            $displayParts[] = $formattedAmount;
        }
    } else {
        $displayParts[] = $formattedAmount;
        if ($showCode) {
            $displayParts[] = $currencyCode;
        }
    }
    
    $displayText = implode(' ', $displayParts);
    
    // CSS classes
    $classes = [
        $class,
        'currency-display',
        $isRtl ? 'currency-rtl' : 'currency-ltr',
        'font-mono', // Use monospace for better number alignment
    ];
@endphp

<span 
    class="{{ implode(' ', $classes) }}"
    dir="ltr"
    style="display: inline-block; text-align: {{ $isRtl ? 'right' : 'left' }};"
    data-amount="{{ $amount }}"
    data-currency="{{ $currency }}"
    data-locale="{{ $locale }}"
    title="{{ $displayText }}"
>
    {{ $displayText }}
</span>

{{-- Component Styles --}}
@once
@push('styles')
<style>
/* Currency Component Styles */
.currency-display {
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
    white-space: nowrap;
}

.currency-rtl {
    direction: ltr;
    text-align: right;
}

.currency-ltr {
    direction: ltr;
    text-align: left;
}

/* Different currency sizes */
.currency-small {
    font-size: 0.875rem;
}

.currency-large {
    font-size: 1.25rem;
    font-weight: 600;
}

.currency-xl {
    font-size: 1.5rem;
    font-weight: 700;
}

/* Currency colors */
.currency-positive {
    color: var(--success-green, #38A169);
}

.currency-negative {
    color: var(--error-red, #E53E3E);
}

.currency-neutral {
    color: var(--charcoal-black, #1A202C);
}

/* Currency in tables */
.table-bank .currency-display {
    text-align: right;
    font-weight: 500;
}

[dir="rtl"] .table-bank .currency-display {
    text-align: left;
}

/* Currency in cards */
.bank-card .currency-display {
    font-weight: 600;
}

/* Currency in stats */
.stats-card .currency-display {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--leaders-red, #E53E3E);
}

/* Responsive currency display */
@media (max-width: 768px) {
    .currency-display {
        font-size: 0.875rem;
    }
    
    .stats-card .currency-display {
        font-size: 1.25rem;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .currency-display {
        font-weight: 600;
    }
}

/* Print styles */
@media print {
    .currency-display {
        color: black !important;
        font-weight: 600;
    }
}
</style>
@endpush
@endonce
