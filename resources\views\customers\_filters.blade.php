<!-- Advanced Search & Filters -->
<div class="bank-card">
    <div class="bank-card-header">
        <h3 class="bank-card-title">Search & Filters</h3>
        <button @click="showFilters = !showFilters"
            class="btn-bank btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                </path>
            </svg>
            <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
        </button>
    </div>

    <div class="bank-card-body" x-show="showFilters" x-transition x-data="{ showFilters: true }">
        <form method="GET" action="{{ route('customers.index') }}" class="space-y-4">
            <!-- Search Row -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="md:col-span-2">
                    <label class="form-label-bank">Search Customers</label>
                    <div class="relative">
                        <input type="text" name="search" value="{{ request('search') }}"
                            placeholder="Search by name, email, or customer number..." class="form-input-bank pl-10">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="form-label-bank">Status</label>
                    <select name="status" class="form-select-bank">
                        <option value="">All Statuses</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive
                        </option>
                        <option value="blocked" {{ request('status') === 'blocked' ? 'selected' : '' }}>Blocked</option>
                    </select>
                </div>
            </div>

            <!-- Filter Row -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="form-label-bank">Customer Type</label>
                    <select name="customer_type" class="form-select-bank">
                        <option value="">All Types</option>
                        <option value="individual" {{ request('customer_type') === 'individual' ? 'selected' : '' }}>
                            Individual</option>
                        <option value="corporate" {{ request('customer_type') === 'corporate' ? 'selected' : '' }}>
                            Corporate</option>
                    </select>
                </div>
                <div>
                    <label class="form-label-bank">Nationality</label>
                    <input type="text" name="nationality" value="{{ request('nationality') }}"
                        placeholder="e.g., UAE, India, Pakistan..." class="form-input-bank">
                </div>
                <div>
                    <label class="form-label-bank">VIP Status</label>
                    <select name="vip_status" class="form-select-bank">
                        <option value="">All Customers</option>
                        <option value="1" {{ request('vip_status') === '1' ? 'selected' : '' }}>VIP Only</option>
                        <option value="0" {{ request('vip_status') === '0' ? 'selected' : '' }}>Regular Only
                        </option>
                    </select>
                </div>
                <div>
                    <label class="form-label-bank">Per Page</label>
                    <select name="per_page" class="form-select-bank">
                        <option value="15" {{ request('per_page', 15) == 15 ? 'selected' : '' }}>15</option>
                        <option value="25" {{ request('per_page') == 25 ? 'selected' : '' }}>25</option>
                        <option value="50" {{ request('per_page') == 50 ? 'selected' : '' }}>50</option>
                        <option value="100" {{ request('per_page') == 100 ? 'selected' : '' }}>100</option>
                    </select>
                </div>
            </div>

            <!-- Date and Language Filters -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="form-label-bank">Registration From</label>
                    <input type="date" name="registration_start_date"
                        value="{{ request('registration_start_date') }}" class="form-input-bank">
                </div>
                <div>
                    <label class="form-label-bank">Registration To</label>
                    <input type="date" name="registration_end_date" value="{{ request('registration_end_date') }}"
                        class="form-input-bank">
                </div>
                <div>
                    <label class="form-label-bank">Preferred Language</label>
                    <select name="preferred_language" class="form-select-bank">
                        <option value="">All Languages</option>
                        <option value="en" {{ request('preferred_language') === 'en' ? 'selected' : '' }}>English
                        </option>
                        <option value="ar" {{ request('preferred_language') === 'ar' ? 'selected' : '' }}>Arabic
                        </option>
                    </select>
                </div>
                <div>
                    <label class="form-label-bank">Contact Method</label>
                    <select name="preferred_contact_method" class="form-select-bank">
                        <option value="">All Methods</option>
                        <option value="phone" {{ request('preferred_contact_method') === 'phone' ? 'selected' : '' }}>
                            Phone</option>
                        <option value="email" {{ request('preferred_contact_method') === 'email' ? 'selected' : '' }}>
                            Email</option>
                        <option value="whatsapp"
                            {{ request('preferred_contact_method') === 'whatsapp' ? 'selected' : '' }}>WhatsApp
                        </option>
                    </select>
                </div>
            </div>

            <!-- Sorting Options -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <label class="form-label-bank">Sort By</label>
                    <select name="sort_by" class="form-select-bank">
                        <option value="registration_date"
                            {{ request('sort_by', 'registration_date') === 'registration_date' ? 'selected' : '' }}>
                            Registration Date
                        </option>
                        <option value="full_name" {{ request('sort_by') === 'full_name' ? 'selected' : '' }}>Name
                        </option>
                        <option value="customer_number"
                            {{ request('sort_by') === 'customer_number' ? 'selected' : '' }}>Customer Number</option>
                        <option value="total_bookings"
                            {{ request('sort_by') === 'total_bookings' ? 'selected' : '' }}>Total Bookings</option>
                        <option value="total_spent" {{ request('sort_by') === 'total_spent' ? 'selected' : '' }}>Total
                            Spent</option>
                        <option value="last_booking_date"
                            {{ request('sort_by') === 'last_booking_date' ? 'selected' : '' }}>Last Booking</option>
                        <option value="status" {{ request('sort_by') === 'status' ? 'selected' : '' }}>Status</option>
                    </select>
                </div>
                <div>
                    <label class="form-label-bank">Sort Order</label>
                    <select name="sort_order" class="form-select-bank">
                        <option value="desc" {{ request('sort_order', 'desc') === 'desc' ? 'selected' : '' }}>Newest
                            First</option>
                        <option value="asc" {{ request('sort_order') === 'asc' ? 'selected' : '' }}>Oldest First
                        </option>
                    </select>
                </div>
                <div class="flex items-end space-x-2">
                    <button type="submit"
                        class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white flex-1">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Apply Filters
                    </button>
                    <a href="{{ route('customers.index') }}"
                        class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                            </path>
                        </svg>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>
