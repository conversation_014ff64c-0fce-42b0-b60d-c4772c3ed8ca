@extends('layouts.dashboard')

@section('title', 'Create Customer')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Create New Customer</h1>
                <p class="text-lg text-dark-gray">Add a new customer to the system</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('customers.index') }}" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Customers
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="customerCreateForm()" x-init="imageUpload = imageUploadComponent()">

        <div class="max-w-6xl mx-auto">
            <form action="{{ route('customers.store') }}" method="POST" enctype="multipart/form-data"
                @submit="handleSubmit" class="space-y-8">
                @csrf

                <!-- Basic Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Basic Information</h3>
                        <p class="bank-card-subtitle">Customer's personal details and contact information</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Profile Image -->
                            <div class="md:col-span-2" x-data="imageUploadComponent()">
                                <label for="profile_image" class="form-label-bank">Profile Image</label>

                                <!-- Image Preview -->
                                <div x-show="selectedImage" class="mb-4">
                                    <div class="flex items-center space-x-4">
                                        <img :src="selectedImage" alt="Preview"
                                            class="w-20 h-20 rounded-full object-cover border-2 border-gray-200">
                                        <div>
                                            <p class="text-sm text-gray-600" x-text="selectedFileName"></p>
                                            <button type="button" @click="clearImage()"
                                                class="text-xs text-red-600 hover:text-red-800">Remove</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Upload Area -->
                                <div x-show="!selectedImage">
                                    <div
                                        class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-leaders-red transition-colors duration-200">
                                        <div class="space-y-1 text-center">
                                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor"
                                                fill="none" viewBox="0 0 48 48">
                                                <path
                                                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                            </svg>
                                            <div class="flex text-sm text-gray-600">
                                                <label for="profile_image"
                                                    class="relative cursor-pointer bg-white rounded-md font-medium text-leaders-red hover:text-leaders-deep-red focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-leaders-red">
                                                    <span>Upload a photo</span>
                                                    <input id="profile_image" name="profile_image" type="file"
                                                        class="sr-only" accept="image/*" @change="handleFileSelect($event)">
                                                </label>
                                                <p class="pl-1">or drag and drop</p>
                                            </div>
                                            <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                                        </div>
                                    </div>
                                </div>
                                @error('profile_image')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Full Name -->
                            <div>
                                <label for="full_name" class="form-label-bank required">Full Name</label>
                                <input type="text" id="full_name" name="full_name" value="{{ old('full_name') }}"
                                    class="form-input-bank" placeholder="Enter full name" required readonly>
                                @error('full_name')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Full Name Arabic -->
                            <div>
                                <label for="full_name_ar" class="form-label-bank">Full Name (Arabic)</label>
                                <input type="text" id="full_name_ar" name="full_name_ar"
                                    value="{{ old('full_name_ar') }}" class="form-input-bank" placeholder="الاسم الكامل"
                                    dir="rtl" readonly>
                                @error('full_name_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- First Name -->
                            <div>
                                <label for="first_name" class="form-label-bank required">First Name</label>
                                <input type="text" id="first_name" name="first_name" value="{{ old('first_name') }}"
                                    class="form-input-bank" placeholder="Enter first name" required>
                                @error('first_name')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Last Name -->
                            <div>
                                <label for="last_name" class="form-label-bank required">Last Name</label>
                                <input type="text" id="last_name" name="last_name" value="{{ old('last_name') }}"
                                    class="form-input-bank" placeholder="Enter last name" required>
                                @error('last_name')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- First Name Arabic -->
                            <div>
                                <label for="first_name_ar" class="form-label-bank">First Name (Arabic)</label>
                                <input type="text" id="first_name_ar" name="first_name_ar"
                                    value="{{ old('first_name_ar') }}" class="form-input-bank" placeholder="الاسم الأول"
                                    dir="rtl">
                                @error('first_name_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Last Name Arabic -->
                            <div>
                                <label for="last_name_ar" class="form-label-bank">Last Name (Arabic)</label>
                                <input type="text" id="last_name_ar" name="last_name_ar"
                                    value="{{ old('last_name_ar') }}" class="form-input-bank" placeholder="اسم العائلة"
                                    dir="rtl">
                                @error('last_name_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="form-label-bank required">Email Address</label>
                                <input type="email" id="email" name="email" value="{{ old('email') }}"
                                    class="form-input-bank" placeholder="<EMAIL>" required>
                                @error('email')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>



                            <!-- Gender -->
                            <div>
                                <label for="gender" class="form-label-bank required">Gender</label>
                                <select id="gender" name="gender" class="form-select-bank" required>
                                    <option value="">Select Gender</option>
                                    <option value="male" {{ old('gender') === 'male' ? 'selected' : '' }}>Male</option>
                                    <option value="female" {{ old('gender') === 'female' ? 'selected' : '' }}>Female
                                    </option>
                                </select>
                                @error('gender')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Birth Date -->
                            <div>
                                <label for="birth_date" class="form-label-bank">Birth Date</label>
                                <input type="date" id="birth_date" name="birth_date" value="{{ old('birth_date') }}"
                                    class="form-input-bank" max="{{ date('Y-m-d') }}">
                                @error('birth_date')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Nationality -->
                            <div>
                                <label for="nationality" class="form-label-bank required">Nationality</label>
                                <select id="nationality" name="nationality" class="form-select-bank" required>
                                    <option value="">Select Nationality</option>
                                    <option value="UAE" {{ old('nationality', 'UAE') === 'UAE' ? 'selected' : '' }}>UAE
                                    </option>
                                    <option value="Saudi Arabia"
                                        {{ old('nationality') === 'Saudi Arabia' ? 'selected' : '' }}>Saudi Arabia</option>
                                    <option value="Kuwait" {{ old('nationality') === 'Kuwait' ? 'selected' : '' }}>Kuwait
                                    </option>
                                    <option value="Qatar" {{ old('nationality') === 'Qatar' ? 'selected' : '' }}>Qatar
                                    </option>
                                    <option value="Bahrain" {{ old('nationality') === 'Bahrain' ? 'selected' : '' }}>
                                        Bahrain</option>
                                    <option value="Oman" {{ old('nationality') === 'Oman' ? 'selected' : '' }}>Oman
                                    </option>
                                    <option value="Egypt" {{ old('nationality') === 'Egypt' ? 'selected' : '' }}>Egypt
                                    </option>
                                    <option value="Jordan" {{ old('nationality') === 'Jordan' ? 'selected' : '' }}>Jordan
                                    </option>
                                    <option value="Lebanon" {{ old('nationality') === 'Lebanon' ? 'selected' : '' }}>
                                        Lebanon</option>
                                    <option value="Syria" {{ old('nationality') === 'Syria' ? 'selected' : '' }}>Syria
                                    </option>
                                    <option value="Palestine" {{ old('nationality') === 'Palestine' ? 'selected' : '' }}>
                                        Palestine</option>
                                    <option value="Iraq" {{ old('nationality') === 'Iraq' ? 'selected' : '' }}>Iraq
                                    </option>
                                    <option value="Yemen" {{ old('nationality') === 'Yemen' ? 'selected' : '' }}>Yemen
                                    </option>
                                    <option value="India" {{ old('nationality') === 'India' ? 'selected' : '' }}>India
                                    </option>
                                    <option value="Pakistan" {{ old('nationality') === 'Pakistan' ? 'selected' : '' }}>
                                        Pakistan</option>
                                    <option value="Bangladesh"
                                        {{ old('nationality') === 'Bangladesh' ? 'selected' : '' }}>
                                        Bangladesh</option>
                                    <option value="Philippines"
                                        {{ old('nationality') === 'Philippines' ? 'selected' : '' }}>
                                        Philippines</option>
                                    <option value="Other" {{ old('nationality') === 'Other' ? 'selected' : '' }}>Other
                                    </option>
                                </select>
                                @error('nationality')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Address Information</h3>
                        <p class="bank-card-subtitle">Customer's location and address details</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Address -->
                            <div>
                                <label for="address" class="form-label-bank">Address</label>
                                <textarea id="address" name="address" rows="3" class="form-textarea-bank" placeholder="Enter full address">{{ old('address') }}</textarea>
                                @error('address')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Address Arabic -->
                            <div>
                                <label for="address_ar" class="form-label-bank">Address (Arabic)</label>
                                <textarea id="address_ar" name="address_ar" rows="3" class="form-textarea-bank" placeholder="العنوان الكامل"
                                    dir="rtl">{{ old('address_ar') }}</textarea>
                                @error('address_ar')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- City -->
                            <div>
                                <label for="city" class="form-label-bank">City</label>
                                <input type="text" id="city" name="city" value="{{ old('city') }}"
                                    class="form-input-bank" placeholder="Enter city">
                                @error('city')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Emirate -->
                            <div>
                                <label for="emirate" class="form-label-bank">Emirate</label>
                                <select id="emirate" name="emirate" class="form-select-bank">
                                    <option value="">Select Emirate</option>
                                    <option value="abu_dhabi" {{ old('emirate') === 'abu_dhabi' ? 'selected' : '' }}>Abu
                                        Dhabi</option>
                                    <option value="dubai" {{ old('emirate') === 'dubai' ? 'selected' : '' }}>Dubai
                                    </option>
                                    <option value="sharjah" {{ old('emirate') === 'sharjah' ? 'selected' : '' }}>Sharjah
                                    </option>
                                    <option value="ajman" {{ old('emirate') === 'ajman' ? 'selected' : '' }}>Ajman
                                    </option>
                                    <option value="umm_al_quwain"
                                        {{ old('emirate') === 'umm_al_quwain' ? 'selected' : '' }}>
                                        Umm Al Quwain</option>
                                    <option value="ras_al_khaimah"
                                        {{ old('emirate') === 'ras_al_khaimah' ? 'selected' : '' }}>Ras Al Khaimah</option>
                                    <option value="fujairah" {{ old('emirate') === 'fujairah' ? 'selected' : '' }}>
                                        Fujairah</option>
                                </select>
                                @error('emirate')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Customer Type & Corporate Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Customer Type & Corporate Information</h3>
                        <p class="bank-card-subtitle">Customer classification and business details</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Customer Type -->
                            <div>
                                <label for="customer_type" class="form-label-bank required">Customer Type</label>
                                <select id="customer_type" name="customer_type" class="form-select-bank" required
                                    x-model="form.customer_type">
                                    <option value="">Select Customer Type</option>
                                    <option value="individual"
                                        {{ old('customer_type') === 'individual' ? 'selected' : '' }}>
                                        Individual</option>
                                    <option value="corporate"
                                        {{ old('customer_type') === 'corporate' ? 'selected' : '' }}>
                                        Corporate</option>
                                </select>
                                @error('customer_type')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- VIP Status -->
                            <div>
                                <label class="form-label-bank">VIP Status</label>
                                <div class="flex items-center space-x-4 mt-2">
                                    <label class="flex items-center">
                                        <input type="radio" name="vip_status" value="0"
                                            {{ old('vip_status', '0') === '0' ? 'checked' : '' }}
                                            class="form-radio text-leaders-red">
                                        <span class="ml-2">Regular Customer</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="vip_status" value="1"
                                            {{ old('vip_status') === '1' ? 'checked' : '' }}
                                            class="form-radio text-leaders-red">
                                        <span class="ml-2">VIP Customer</span>
                                    </label>
                                </div>
                                @error('vip_status')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Corporate fields (shown when customer_type is corporate) -->
                            <div x-show="form.customer_type === 'corporate'" class="md:col-span-2">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Company Name -->
                                    <div>
                                        <label for="company_name" class="form-label-bank">Company Name</label>
                                        <input type="text" id="company_name" name="company_name"
                                            value="{{ old('company_name') }}" class="form-input-bank"
                                            placeholder="Enter company name">
                                        @error('company_name')
                                            <div class="form-error">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Company Name Arabic -->
                                    <div>
                                        <label for="company_name_ar" class="form-label-bank">Company Name (Arabic)</label>
                                        <input type="text" id="company_name_ar" name="company_name_ar"
                                            value="{{ old('company_name_ar') }}" class="form-input-bank"
                                            placeholder="اسم الشركة" dir="rtl">
                                        @error('company_name_ar')
                                            <div class="form-error">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Trade License -->
                                    <div>
                                        <label for="trade_license" class="form-label-bank">Trade License</label>
                                        <input type="text" id="trade_license" name="trade_license"
                                            value="{{ old('trade_license') }}" class="form-input-bank"
                                            placeholder="Enter trade license number">
                                        @error('trade_license')
                                            <div class="form-error">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Tax Number -->
                                    <div>
                                        <label for="tax_number" class="form-label-bank">Tax Number</label>
                                        <input type="text" id="tax_number" name="tax_number"
                                            value="{{ old('tax_number') }}" class="form-input-bank"
                                            placeholder="Enter tax number">
                                        @error('tax_number')
                                            <div class="form-error">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences & Settings -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Preferences & Settings</h3>
                        <p class="bank-card-subtitle">Customer preferences and account settings</p>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Preferred Language -->
                            <div>
                                <label for="preferred_language" class="form-label-bank required">Preferred
                                    Language</label>
                                <select id="preferred_language" name="preferred_language" class="form-select-bank"
                                    required>
                                    <option value="">Select Language</option>
                                    <option value="en"
                                        {{ old('preferred_language', 'en') === 'en' ? 'selected' : '' }}>
                                        English</option>
                                    <option value="ar" {{ old('preferred_language') === 'ar' ? 'selected' : '' }}>
                                        Arabic</option>
                                </select>
                                @error('preferred_language')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Preferred Contact Method -->
                            <div>
                                <label for="preferred_contact_method" class="form-label-bank required">Preferred Contact
                                    Method</label>
                                <select id="preferred_contact_method" name="preferred_contact_method"
                                    class="form-select-bank" required>
                                    <option value="">Select Contact Method</option>
                                    <option value="email"
                                        {{ old('preferred_contact_method', 'email') === 'email' ? 'selected' : '' }}>Email
                                    </option>
                                    <option value="phone"
                                        {{ old('preferred_contact_method') === 'phone' ? 'selected' : '' }}>Phone</option>
                                    <option value="sms"
                                        {{ old('preferred_contact_method') === 'sms' ? 'selected' : '' }}>
                                        SMS</option>
                                    <option value="whatsapp"
                                        {{ old('preferred_contact_method') === 'whatsapp' ? 'selected' : '' }}>WhatsApp
                                    </option>
                                </select>
                                @error('preferred_contact_method')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Payment Terms -->
                            <div>
                                <label for="payment_terms" class="form-label-bank">Payment Terms</label>
                                <select id="payment_terms" name="payment_terms" class="form-select-bank">
                                    <option value="">Select Payment Terms</option>
                                    <option value="cash"
                                        {{ old('payment_terms', 'cash') === 'cash' ? 'selected' : '' }}>Cash
                                    </option>
                                    <option value="credit_7" {{ old('payment_terms') === 'credit_7' ? 'selected' : '' }}>
                                        Credit 7
                                        Days</option>
                                    <option value="credit_15"
                                        {{ old('payment_terms') === 'credit_15' ? 'selected' : '' }}>Credit
                                        15 Days</option>
                                    <option value="credit_30"
                                        {{ old('payment_terms') === 'credit_30' ? 'selected' : '' }}>Credit
                                        30 Days</option>
                                </select>
                                @error('payment_terms')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Credit Limit -->
                            <div>
                                <label for="credit_limit" class="form-label-bank">Credit Limit (AED)</label>
                                <input type="number" id="credit_limit" name="credit_limit" step="0.01"
                                    min="0" value="{{ old('credit_limit', '0') }}" class="form-input-bank"
                                    placeholder="0.00">
                                @error('credit_limit')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                </div>



                <!-- Form Actions -->
                <div class="bank-card">
                    <div class="bank-card-body">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button type="submit"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white"
                                    :disabled="isSubmitting" x-text="isSubmitting ? 'Creating...' : 'Create Customer'">
                                    Create Customer
                                </button>
                                <a href="{{ route('customers.index') }}"
                                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                                    Cancel
                                </a>
                            </div>
                            <div class="text-sm text-gray-500">
                                <span class="text-red-500">*</span> Required fields
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function customerCreateForm() {
            return {
                isSubmitting: false,
                form: {
                    customer_type: '{{ old('customer_type', 'individual') }}'
                },

                init() {
                    // Initialize phone number formatting
                    this.initPhoneFormatting();
                },

                initPhoneFormatting() {
                    // Phone number formatting is now handled by the UAE phone input component
                },

                handleSubmit(event) {
                    this.isSubmitting = true;
                }
            }
        }

        function imageUploadComponent() {
            return {
                selectedImage: null,
                selectedFile: null,
                selectedFileName: '',

                handleFileSelect(event) {
                    const file = event.target.files[0];
                    if (!file) return;

                    // Validate file type
                    if (!file.type.startsWith('image/')) {
                        alert('Please select an image file.');
                        event.target.value = '';
                        return;
                    }

                    // Validate file size (2MB)
                    if (file.size > 2 * 1024 * 1024) {
                        alert('File size must be less than 2MB.');
                        event.target.value = '';
                        return;
                    }

                    this.selectedFile = file;
                    this.selectedFileName = file.name;

                    // Create preview
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.selectedImage = e.target.result;
                    };
                    reader.readAsDataURL(file);
                },

                clearImage() {
                    this.selectedImage = null;
                    this.selectedFile = null;
                    this.selectedFileName = '';
                    document.getElementById('profile_image').value = '';
                }
            }
        }

        // Initialize form on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-generate full name from first and last name
            const firstNameInput = document.getElementById('first_name');
            const lastNameInput = document.getElementById('last_name');
            const fullNameInput = document.getElementById('full_name');

            function updateFullName() {
                const firstName = firstNameInput.value.trim();
                const lastName = lastNameInput.value.trim();
                if (firstName && lastName) {
                    fullNameInput.value = firstName + ' ' + lastName;
                }
            }

            if (firstNameInput && lastNameInput && fullNameInput) {
                firstNameInput.addEventListener('input', updateFullName);
                lastNameInput.addEventListener('input', updateFullName);
            }

            // Auto-generate Arabic full name from Arabic first and last name
            const firstNameArInput = document.getElementById('first_name_ar');
            const lastNameArInput = document.getElementById('last_name_ar');
            const fullNameArInput = document.getElementById('full_name_ar');

            function updateFullNameAr() {
                const firstNameAr = firstNameArInput.value.trim();
                const lastNameAr = lastNameArInput.value.trim();
                if (firstNameAr && lastNameAr) {
                    fullNameArInput.value = firstNameAr + ' ' + lastNameAr;
                }
            }

            if (firstNameArInput && lastNameArInput && fullNameArInput) {
                firstNameArInput.addEventListener('input', updateFullNameAr);
                lastNameArInput.addEventListener('input', updateFullNameAr);
            }
        });
    </script>
@endpush
