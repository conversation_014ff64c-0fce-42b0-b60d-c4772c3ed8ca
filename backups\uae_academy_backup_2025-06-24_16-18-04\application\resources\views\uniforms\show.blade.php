@extends('layouts.dashboard')

@section('page-header')
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-charcoal-black">
                Uniform Order #{{ str_pad($uniform->id, 4, '0', STR_PAD_LEFT) }}
            </h1>
            <p class="mt-1 text-sm text-dark-gray">
                Detailed information for {{ $uniform->student->full_name }}'s uniform order
            </p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <span class="badge-bank {{ $uniform->status_badge_class }}">
                {{ $uniform->status_text }}
            </span>
            <a href="{{ route('uniforms.index') }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Orders
            </a>
            @can('update', $uniform)
                <a href="{{ route('uniforms.edit', $uniform) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Order
                </a>
            @endcan
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Order Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Student Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="text-lg font-semibold text-charcoal-black">Student Information</h3>
                </div>
                <div class="bank-card-body">
                    <div class="flex items-center space-x-4 mb-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-xl flex items-center justify-center">
                            <span class="text-white font-bold text-xl">
                                {{ strtoupper(substr($uniform->student->full_name, 0, 2)) }}
                            </span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-charcoal-black text-lg">{{ $uniform->student->full_name }}</h4>
                            <p class="text-sm text-dark-gray">{{ $uniform->student->email }}</p>
                            <p class="text-sm text-medium-gray">{{ $uniform->student->formatted_phone }}</p>
                        </div>
                    </div>
                    
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Student ID:</span>
                            <span class="font-mono text-sm text-charcoal-black">#{{ str_pad($uniform->student->id, 4, '0', STR_PAD_LEFT) }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Nationality:</span>
                            <span class="text-sm text-charcoal-black">{{ $uniform->student->nationality ?? 'Not specified' }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Join Date:</span>
                            <span class="text-sm text-charcoal-black">{{ $uniform->student->formatted_join_date }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Status:</span>
                            <span class="badge-bank {{ $uniform->student->status ? 'badge-success' : 'badge-neutral' }}">
                                {{ $uniform->student->status ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Details -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="text-lg font-semibold text-charcoal-black">Order Details</h3>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Order ID:</span>
                            <span class="font-mono text-sm text-charcoal-black">#{{ str_pad($uniform->id, 4, '0', STR_PAD_LEFT) }}</span>
                        </div>
                        
                        @if($uniform->reference_number)
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-medium-gray">Reference:</span>
                                <span class="font-mono text-sm text-charcoal-black">{{ $uniform->reference_number }}</span>
                            </div>
                        @endif

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Item Type:</span>
                            <span class="badge-bank badge-info">{{ ucfirst($uniform->item) }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Size:</span>
                            <span class="badge-bank badge-neutral">{{ $uniform->size }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Quantity:</span>
                            <span class="font-semibold text-charcoal-black">{{ $uniform->quantity }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Unit Price:</span>
                            <span class="text-sm text-charcoal-black">{{ $uniform->formatted_amount }}</span>
                        </div>

                        <div class="flex items-center justify-between pt-3 border-t border-light-gray">
                            <span class="text-lg font-medium text-charcoal-black">Total Amount:</span>
                            <span class="text-xl font-bold text-success-green">{{ $uniform->formatted_total_amount }}</span>
                        </div>

                        <div class="flex items-center justify-between">
                            <span class="text-sm text-medium-gray">Payment Method:</span>
                            <span class="badge-bank badge-warning">{{ $uniform->method_text }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Branch & Academy -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="text-lg font-semibold text-charcoal-black">Location Details</h3>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div>
                            <div class="text-sm text-medium-gray mb-1">Branch</div>
                            <div class="font-semibold text-charcoal-black">{{ $uniform->branch->name }}</div>
                            @if($uniform->branch->location)
                                <div class="text-sm text-dark-gray">{{ $uniform->branch->location }}</div>
                            @endif
                        </div>

                        <div class="border-t border-light-gray pt-4">
                            <div class="text-sm text-medium-gray mb-1">Academy</div>
                            <div class="font-semibold text-charcoal-black">{{ $uniform->academy->name }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Tracking -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="text-lg font-semibold text-charcoal-black">Order Status & Tracking</h3>
            </div>
            <div class="bank-card-body">
                <!-- Progress Timeline -->
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <span class="text-sm font-medium text-charcoal-black">Order Progress</span>
                        <span class="text-sm text-dark-gray">
                            @switch($uniform->status)
                                @case('ordered')
                                    25% Complete
                                    @break
                                @case('processing')
                                    50% Complete
                                    @break
                                @case('ready')
                                    75% Complete
                                    @break
                                @case('delivered')
                                    100% Complete
                                    @break
                                @case('cancelled')
                                    Cancelled
                                    @break
                                @default
                                    0% Complete
                            @endswitch
                        </span>
                    </div>
                    
                    <div class="relative">
                        <div class="flex items-center justify-between">
                            <!-- Ordered -->
                            <div class="flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center {{ in_array($uniform->status, ['ordered', 'processing', 'ready', 'delivered']) ? 'bg-success-green text-white' : 'bg-medium-gray text-white' }}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div class="text-xs text-center mt-2">
                                    <div class="font-medium">Ordered</div>
                                    <div class="text-medium-gray">{{ $uniform->formatted_order_date }}</div>
                                </div>
                            </div>

                            <!-- Processing -->
                            <div class="flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center {{ in_array($uniform->status, ['processing', 'ready', 'delivered']) ? 'bg-success-green text-white' : 'bg-medium-gray text-white' }}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                </div>
                                <div class="text-xs text-center mt-2">
                                    <div class="font-medium">Processing</div>
                                    <div class="text-medium-gray">In production</div>
                                </div>
                            </div>

                            <!-- Ready -->
                            <div class="flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center {{ in_array($uniform->status, ['ready', 'delivered']) ? 'bg-success-green text-white' : 'bg-medium-gray text-white' }}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h1.586a1 1 0 01.707.293l1.414 1.414a1 1 0 00.707.293H15a2 2 0 012 2v2M5 8v10a2 2 0 002 2h10a2 2 0 002-2V10m-9 4h4"></path>
                                    </svg>
                                </div>
                                <div class="text-xs text-center mt-2">
                                    <div class="font-medium">Ready</div>
                                    <div class="text-medium-gray">For pickup</div>
                                </div>
                            </div>

                            <!-- Delivered -->
                            <div class="flex flex-col items-center">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center {{ $uniform->status == 'delivered' ? 'bg-success-green text-white' : 'bg-medium-gray text-white' }}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <div class="text-xs text-center mt-2">
                                    <div class="font-medium">Delivered</div>
                                    <div class="text-medium-gray">
                                        {{ $uniform->delivery_date ? $uniform->formatted_delivery_date : 'Pending' }}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Line -->
                        <div class="absolute top-4 left-4 right-4 h-0.5 bg-medium-gray -z-10">
                            <div class="h-full bg-success-green transition-all duration-300 
                                @switch($uniform->status)
                                    @case('ordered')
                                        w-0
                                        @break
                                    @case('processing')
                                        w-1/3
                                        @break
                                    @case('ready')
                                        w-2/3
                                        @break
                                    @case('delivered')
                                        w-full
                                        @break
                                    @default
                                        w-0
                                @endswitch
                            "></div>
                        </div>
                    </div>
                </div>

                <!-- Status Details -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center p-4 bg-light-gray rounded-lg">
                        <div class="text-sm text-medium-gray mb-1">Current Status</div>
                        <div class="badge-bank {{ $uniform->status_badge_class }} text-lg">{{ $uniform->status_text }}</div>
                    </div>

                    <div class="text-center p-4 bg-light-gray rounded-lg">
                        <div class="text-sm text-medium-gray mb-1">Branch Status</div>
                        <div class="badge-bank {{ $uniform->branch_status === 'delivered' ? 'badge-success' : ($uniform->branch_status === 'received' ? 'badge-warning' : 'badge-neutral') }}">
                            {{ $uniform->branch_status_text }}
                        </div>
                    </div>

                    <div class="text-center p-4 bg-light-gray rounded-lg">
                        <div class="text-sm text-medium-gray mb-1">Office Status</div>
                        <div class="badge-bank {{ $uniform->office_status === 'delivered' ? 'badge-success' : ($uniform->office_status === 'received' ? 'badge-warning' : 'badge-neutral') }}">
                            {{ $uniform->office_status_text }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        @if($uniform->description || $uniform->note)
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="text-lg font-semibold text-charcoal-black">Additional Information</h3>
                </div>
                <div class="bank-card-body">
                    @if($uniform->description)
                        <div class="mb-4">
                            <div class="text-sm text-medium-gray mb-1">Description</div>
                            <div class="text-charcoal-black">{{ $uniform->description }}</div>
                        </div>
                    @endif

                    @if($uniform->note)
                        <div>
                            <div class="text-sm text-medium-gray mb-1">Notes</div>
                            <div class="text-charcoal-black">{{ $uniform->note }}</div>
                        </div>
                    @endif
                </div>
            </div>
        @endif

        <!-- Order History -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="text-lg font-semibold text-charcoal-black">Order History</h3>
            </div>
            <div class="bank-card-body">
                <div class="space-y-3">
                    <div class="flex items-center justify-between py-2 border-b border-light-gray">
                        <div>
                            <div class="font-medium text-charcoal-black">Order Created</div>
                            <div class="text-sm text-dark-gray">Initial uniform order placed</div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-charcoal-black">{{ $uniform->created_at->format('M d, Y') }}</div>
                            <div class="text-xs text-medium-gray">{{ $uniform->created_at->format('h:i A') }}</div>
                        </div>
                    </div>

                    @if($uniform->updated_at != $uniform->created_at)
                        <div class="flex items-center justify-between py-2">
                            <div>
                                <div class="font-medium text-charcoal-black">Last Updated</div>
                                <div class="text-sm text-dark-gray">Order information modified</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm text-charcoal-black">{{ $uniform->updated_at->format('M d, Y') }}</div>
                                <div class="text-xs text-medium-gray">{{ $uniform->updated_at->format('h:i A') }}</div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
