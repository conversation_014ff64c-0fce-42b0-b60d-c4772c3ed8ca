<?php

namespace App\Policies;

use App\Models\Academy;
use App\Models\User;

class AcademyPolicy
{
    /**
     * Determine whether the user can view any academies.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the academy.
     */
    public function view(User $user, Academy $academy): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can only view academies in their assigned branch
                return $user->branch_id === $academy->branch_id;
            case 'academy_manager':
                // Academy managers can only view their assigned academy
                return $user->academy_id === $academy->id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can create academies.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can update the academy.
     */
    public function update(User $user, Academy $academy): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can only update academies in their assigned branch
                return $user->branch_id === $academy->branch_id;
            case 'academy_manager':
                // Academy managers can only update their assigned academy
                return $user->academy_id === $academy->id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can delete the academy.
     */
    public function delete(User $user, Academy $academy): bool
    {
        // Admin can delete any academy
        if ($user->role === 'admin') {
            return true;
        }

        // Branch managers can only delete academies in their assigned branch
        if ($user->role === 'branch_manager') {
            return $user->branch_id === $academy->branch_id;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the academy.
     */
    public function restore(User $user, Academy $academy): bool
    {
        // Admin can restore any academy
        if ($user->role === 'admin') {
            return true;
        }

        // Branch managers can only restore academies in their assigned branch
        if ($user->role === 'branch_manager') {
            return $user->branch_id === $academy->branch_id;
        }

        return false;
    }

    /**
     * Determine whether the user can permanently delete the academy.
     */
    public function forceDelete(User $user, Academy $academy): bool
    {
        // Only admin can permanently delete
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can export academy data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }
}
