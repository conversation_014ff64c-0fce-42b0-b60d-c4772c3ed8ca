@extends('layouts.dashboard')

@section('title', $program->name . ' - Program Details')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $program->name }}</h1>
                <p class="text-lg text-dark-gray">{{ $program->academy->name }} - {{ $program->academy->branch->name }}</p>
                <div class="flex items-center space-x-3 mt-2">
                    <span class="badge-bank badge-info">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        ID: #{{ $program->id }}
                    </span>
                    <span class="badge-bank {{ $program->status ? 'badge-success' : 'badge-neutral' }}">
                        {{ $program->status ? 'Active' : 'Inactive' }}
                    </span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('update', $program)
                <a href="{{ route('programs.edit', $program) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Program
                </a>
            @endcan
            <a href="{{ route('programs.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Programs
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Program Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <!-- Classes Card -->
            <div class="bank-card bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-blue-700 uppercase tracking-wide">Classes</p>
                        <p class="text-3xl font-bold text-blue-900">{{ $program->classes }}</p>
                        <p class="text-xs text-blue-600 mt-1">Total Sessions</p>
                    </div>
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Price Card -->
            <div class="bank-card bg-gradient-to-br from-green-50 to-green-100 border-green-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-green-700 uppercase tracking-wide">Price</p>
                        <p class="text-3xl font-bold text-green-900">{{ number_format($program->price, 2) }}</p>
                        <p class="text-xs text-green-600 mt-1">AED</p>
                    </div>
                    <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Students Card -->
            <div class="bank-card bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-purple-700 uppercase tracking-wide">Students</p>
                        <p class="text-3xl font-bold text-purple-900">{{ $program->student_count }}</p>
                        <p class="text-xs text-purple-600 mt-1">
                            @if ($program->max_students)
                                of {{ $program->max_students }} max
                            @else
                                Enrolled
                            @endif
                        </p>
                    </div>
                    <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Duration Card -->
            <div class="bank-card bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-orange-700 uppercase tracking-wide">Duration</p>
                        <p class="text-3xl font-bold text-orange-900">{{ $program->duration_hours }}</p>
                        <p class="text-xs text-orange-600 mt-1">Hours per session</p>
                    </div>
                    <div class="w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Program Details -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Program Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Program Information</h3>
                        <p class="bank-card-subtitle">Detailed program specifications</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Program Name:</span>
                            <span class="text-sm text-charcoal-black font-medium">{{ $program->name }}</span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Academy:</span>
                            <span class="text-sm text-charcoal-black">{{ $program->academy->name }}</span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Branch:</span>
                            <span class="text-sm text-charcoal-black">{{ $program->academy->branch->name }}</span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Status:</span>
                            <span class="badge-bank {{ $program->status ? 'badge-success' : 'badge-neutral' }}">
                                {{ $program->status ? 'Active' : 'Inactive' }}
                            </span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Number of Classes:</span>
                            <span class="text-sm text-charcoal-black font-medium">{{ $program->classes }}</span>
                        </div>

                        <div class="flex justify-between items-start">
                            <span class="text-sm font-medium text-dark-gray">Price:</span>
                            <span
                                class="text-sm text-charcoal-black font-bold text-leaders-red">{{ number_format($program->price, 2) }}
                                AED</span>
                        </div>

                        @if ($program->max_students)
                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">Max Students:</span>
                                <span class="text-sm text-charcoal-black">{{ $program->max_students }}</span>
                            </div>

                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">Available Spots:</span>
                                <span
                                    class="text-sm text-charcoal-black font-medium {{ $program->getAvailableSpots() > 0 ? 'text-success-green' : 'text-error-red' }}">
                                    {{ $program->getAvailableSpots() }}
                                </span>
                            </div>
                        @endif

                        @if ($program->description)
                            <div class="pt-4 border-t border-light-gray">
                                <span class="text-sm font-medium text-dark-gray block mb-2">Description:</span>
                                <p class="text-sm text-charcoal-black">{{ $program->description }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Schedule Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Schedule Information</h3>
                        <p class="bank-card-subtitle">Program timing and schedule details</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div>
                            <span class="text-sm font-medium text-dark-gray block mb-2">Program Days:</span>
                            <div class="flex flex-wrap gap-2">
                                @php $days = $program->getDaysArray(); @endphp
                                @if (!empty($days))
                                    @foreach ($days as $day)
                                        <span
                                            class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                            {{ $day }}
                                        </span>
                                    @endforeach
                                @else
                                    <span class="text-sm text-medium-gray">No schedule set</span>
                                @endif
                            </div>
                        </div>

                        @if ($program->start_time && $program->end_time)
                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">Start Time:</span>
                                <span
                                    class="text-sm text-charcoal-black font-medium">{{ \Carbon\Carbon::parse($program->start_time)->format('g:i A') }}</span>
                            </div>

                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">End Time:</span>
                                <span
                                    class="text-sm text-charcoal-black font-medium">{{ \Carbon\Carbon::parse($program->end_time)->format('g:i A') }}</span>
                            </div>

                            <div class="flex justify-between items-start">
                                <span class="text-sm font-medium text-dark-gray">Duration:</span>
                                <span class="text-sm text-charcoal-black font-medium">{{ $program->duration_hours }}
                                    hours</span>
                            </div>
                        @endif

                        @if ($program->getNextClassDate())
                            <div class="pt-4 border-t border-light-gray">
                                <div class="flex justify-between items-start">
                                    <span class="text-sm font-medium text-dark-gray">Next Class:</span>
                                    <span class="text-sm text-charcoal-black font-medium text-success-green">
                                        {{ $program->getNextClassDate()->format('l, M d, Y') }}
                                    </span>
                                </div>
                            </div>
                        @endif

                        <div class="pt-4 border-t border-light-gray">
                            <span class="text-sm font-medium text-dark-gray block mb-2">Full Schedule:</span>
                            <p class="text-sm text-charcoal-black bg-off-white p-3 rounded-lg">
                                {{ $program->getScheduleDisplay() }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Timestamps -->
        <div class="bank-card">
            <div class="bank-card-header">
                <div>
                    <h3 class="bank-card-title">Record Information</h3>
                    <p class="bank-card-subtitle">Creation and modification timestamps</p>
                </div>
            </div>
            <div class="bank-card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-dark-gray">Created:</span>
                        <span
                            class="text-sm text-charcoal-black">{{ $program->created_at->format('M d, Y \a\t g:i A') }}</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-sm font-medium text-dark-gray">Last Updated:</span>
                        <span
                            class="text-sm text-charcoal-black">{{ $program->updated_at->format('M d, Y \a\t g:i A') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
