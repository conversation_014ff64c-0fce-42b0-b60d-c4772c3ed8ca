<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('uniforms', function (Blueprint $table) {
            // Add new columns for enhanced functionality
            $table->string('item')->default('jersey')->after('academy_id'); // jersey, shorts, socks, etc.
            $table->integer('quantity')->default(1)->after('size');
            $table->date('delivery_date')->nullable()->after('order_date');
            $table->enum('status', ['ordered', 'processing', 'ready', 'delivered', 'cancelled'])->default('ordered')->after('quantity');
            $table->string('reference_number')->nullable()->after('status');
            $table->text('description')->nullable()->after('reference_number');

            // Add indexes for new columns
            $table->index('status');
            $table->index('item');
            $table->index('delivery_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('uniforms', function (Blueprint $table) {
            $table->dropIndex(['status']);
            $table->dropIndex(['item']);
            $table->dropIndex(['delivery_date']);

            $table->dropColumn([
                'item',
                'quantity',
                'delivery_date',
                'status',
                'reference_number',
                'description'
            ]);
        });
    }
};
