@extends('layouts.app')

@section('title', 'Login - LEADERS SPORTS SERVICES')

@section('content')
    <div class="container-fluid min-vh-100 d-flex align-items-center justify-content-center py-4">
        <div class="row w-100 justify-content-center">
            <div class="col-11 col-sm-8 col-md-6 col-lg-5 col-xl-4">
                <!-- LEADERS SPORTS SERVICES Login Card (Per UI Guide) -->
                <div class="card">
                    <!-- Clean Header (Per UI Guide) -->
                    <div class="text-center py-4 px-4"
                        style="background-color: white; border-radius: var(--radius-card) var(--radius-card) 0 0;">
                        <div class="mb-3 d-flex justify-content-center">
                            <img src="{{ asset('images/logo.jpg') }}" alt="LEADERS SPORTS SERVICES Logo"
                                style="width: 160px; height: auto; object-fit: contain;">
                        </div>
                        <h1 class="fw-bold mb-2"
                            style="font-size: 1.875rem; font-family: var(--font-family-primary); color: var(--leaders-red);">
                            Welcome Back</h1>
                    </div>
                    <!-- Form Body (Per UI Guide) -->
                    <div class="p-4">
                        <!-- Session Status -->
                        @if (session('status'))
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>{{ session('status') }}
                            </div>
                        @endif

                        <form method="POST" action="{{ route('login') }}">
                            @csrf

                            <!-- Email Address (Per UI Guide) -->
                            <div class="mb-3">
                                <label for="email" class="form-label fw-medium" style="color: var(--charcoal-black);">
                                    Email Address
                                </label>
                                <input id="email" type="email"
                                    class="form-control @error('email') is-invalid @enderror" name="email"
                                    value="{{ old('email') }}" required autofocus autocomplete="username"
                                    placeholder="Enter your email address">
                                @error('email')
                                    <div class="invalid-feedback">
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <!-- Password (Per UI Guide) -->
                            <div class="mb-3">
                                <x-password-input id="password" name="password" placeholder="Enter your password"
                                    label="Password" required autocomplete="current-password" />
                            </div>

                            <!-- Remember Me & Forgot Password -->
                            <div class="mb-3 d-flex align-items-center justify-content-between">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remember_me" name="remember">
                                    <label class="form-check-label" for="remember_me">
                                        Remember me
                                    </label>
                                </div>
                                @if (Route::has('password.request'))
                                    <a class="text-decoration-none" href="{{ route('password.request') }}"
                                        style="color: var(--leaders-red);">
                                        Forgot password?
                                    </a>
                                @endif
                            </div>

                            <!-- Login Button (Per UI Guide) -->
                            <div class="d-grid mb-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Company Information -->
                    <div class="text-center py-3 px-4 border-top"
                        style="background-color: #f8f9fa; border-radius: 0 0 var(--radius-card) var(--radius-card);">
                        <div class="mb-2">
                            <h6 class="fw-bold mb-1" style="color: var(--leaders-red); font-size: 0.875rem;">LEADERS SPORTS
                                SERVICES LLC SP</h6>
                            <p class="mb-1 text-muted" style="font-size: 0.75rem;">SHARJAH AL MAMZAR</p>
                        </div>
                        <div class="d-flex justify-content-center gap-3 text-muted" style="font-size: 0.75rem;">
                            <span><i class="fas fa-envelope me-1"></i><EMAIL></span>
                            <span><i class="fas fa-phone me-1"></i>+************</span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        /* Login Page Styles (Per UI Guide) */
        .card {
            max-width: 500px;
            margin: 0 auto;
        }

        /* Custom Checkbox (Per UI Guide) */
        .form-check-input:checked {
            background-color: var(--leaders-red);
            border-color: var(--leaders-red);
        }

        .form-check-input:focus {
            box-shadow: 0 0 0 0.2rem rgba(229, 62, 62, 0.25);
        }



        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .card {
                margin: 1rem;
            }
        }
    </style>
@endpush
