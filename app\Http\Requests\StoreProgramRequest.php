<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreProgramRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled in the controller
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'academy_id' => [
                'required',
                'integer',
                'exists:academies,id'
            ],
            'name' => [
                'required',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    // If creating new program, check uniqueness
                    if ($value === '__new_program__') {
                        return; // Skip uniqueness check for special value
                    }

                    // For existing program selection, allow any existing program name
                    if ($this->has('student_id') && $value !== '__new_program__') {
                        $existingProgram = \App\Models\Program::where('academy_id', $this->academy_id)
                            ->where('name', $value)
                            ->first();
                        if (!$existingProgram) {
                            $fail('The selected program does not exist in this academy.');
                        }
                        return;
                    }

                    // For new program creation, check uniqueness
                    $exists = \App\Models\Program::where('academy_id', $this->academy_id)
                        ->where('name', $value)
                        ->exists();
                    if ($exists) {
                        $fail('A program with this name already exists in the selected academy.');
                    }
                }
            ],
            'new_program_name' => [
                'required_if:name,__new_program__',
                'string',
                'max:255',
                function ($attribute, $value, $fail) {
                    // Only validate if creating new program
                    if ($this->input('name') === '__new_program__' && $value) {
                        $exists = \App\Models\Program::where('academy_id', $this->academy_id)
                            ->where('name', $value)
                            ->exists();
                        if ($exists) {
                            $fail('A program with this name already exists in the selected academy.');
                        }
                    }
                }
            ],
            'description' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'days' => [
                'required',
                'array',
                'min:1'
            ],
            'days.*' => [
                'required',
                'string',
                'in:SUN,MON,TUE,WED,THU,FRI,SAT'
            ],
            'classes' => [
                'required',
                'integer',
                'min:1',
                'max:50'
            ],
            'price' => [
                'required',
                'numeric',
                'min:0',
                'max:99999.99'
            ],
            'currency' => [
                'nullable',
                'string',
                'size:3',
                'in:AED,USD,EUR,GBP'
            ],
            'start_time' => [
                'nullable',
                'date_format:H:i'
            ],
            'end_time' => [
                'nullable',
                'date_format:H:i',
                'after:start_time'
            ],
            'max_students' => [
                'nullable',
                'integer',
                'min:1',
                'max:1000'
            ],
            'status' => [
                'boolean'
            ]
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function messages(): array
    {
        return [
            'academy_id.required' => 'Please select an academy.',
            'academy_id.exists' => 'The selected academy is invalid.',
            'name.required' => 'Program name is required.',
            'new_program_name.required_if' => 'New program name is required when creating a new program.',
            'days.required' => 'Please select at least one day.',
            'days.min' => 'Please select at least one day.',
            'days.*.in' => 'Invalid day selected.',
            'classes.required' => 'Number of classes is required.',
            'classes.min' => 'Number of classes must be at least 1.',
            'classes.max' => 'Number of classes cannot exceed 50.',
            'price.required' => 'Program price is required.',
            'price.min' => 'Price cannot be negative.',
            'price.max' => 'Price cannot exceed 99,999.99 AED.',
            'currency.in' => 'Invalid currency selected.',
            'start_time.date_format' => 'Start time must be in HH:MM format.',
            'end_time.date_format' => 'End time must be in HH:MM format.',
            'end_time.after' => 'End time must be after start time.',
            'max_students.min' => 'Maximum students must be at least 1.',
            'max_students.max' => 'Maximum students cannot exceed 1000.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'academy_id' => 'academy',
            'name' => 'program name',
            'days' => 'days',
            'classes' => 'number of classes',
            'price' => 'price',
            'currency' => 'currency',
            'start_time' => 'start time',
            'end_time' => 'end time',
            'max_students' => 'maximum students',
            'status' => 'status',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default currency to AED if not provided
        if (!$this->has('currency') || empty($this->currency)) {
            $this->merge(['currency' => 'AED']);
        }

        // Set default status to true if not provided
        if (!$this->has('status')) {
            $this->merge(['status' => true]);
        }

        // Convert status to boolean if it's a string
        if ($this->has('status') && is_string($this->status)) {
            $this->merge(['status' => $this->status === '1' || $this->status === 'true']);
        }

        // Ensure days is an array
        if ($this->has('days') && !is_array($this->days)) {
            $this->merge(['days' => [$this->days]]);
        }

        // Remove empty values
        $this->merge(array_filter($this->all(), function ($value) {
            return $value !== '' && $value !== null;
        }));
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional custom validation logic can be added here

            // Validate time range if both start and end times are provided
            if ($this->start_time && $this->end_time) {
                $start = \Carbon\Carbon::createFromFormat('H:i', $this->start_time);
                $end = \Carbon\Carbon::createFromFormat('H:i', $this->end_time);

                if ($start->gte($end)) {
                    $validator->errors()->add('end_time', 'End time must be after start time.');
                }

                // Check if the duration is reasonable (at least 30 minutes, max 8 hours)
                $duration = $start->diffInMinutes($end);
                if ($duration < 30) {
                    $validator->errors()->add('end_time', 'Program duration must be at least 30 minutes.');
                } elseif ($duration > 480) { // 8 hours
                    $validator->errors()->add('end_time', 'Program duration cannot exceed 8 hours.');
                }
            }
        });
    }
}
