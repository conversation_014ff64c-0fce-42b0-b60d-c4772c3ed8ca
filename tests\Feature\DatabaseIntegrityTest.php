<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Student;
use App\Models\Payment;
use App\Models\Program;

class DatabaseIntegrityTest extends TestCase
{
    use RefreshDatabase;

    /**
     * Test that all required tables exist
     */
    public function test_required_tables_exist(): void
    {
        $requiredTables = [
            'users',
            'branches',
            'academies',
            'programs',
            'students',
            'payments',
            'uniforms',
            'attendances',
        ];

        foreach ($requiredTables as $table) {
            $this->assertTrue(
                Schema::hasTable($table),
                "Required table '{$table}' does not exist"
            );
        }
    }

    /**
     * Test foreign key constraints exist
     */
    public function test_foreign_key_constraints_exist(): void
    {
        // Test users table foreign keys (should exist after our fixes)
        $this->assertTrue(Schema::hasColumn('users', 'branch_id'));
        $this->assertTrue(Schema::hasColumn('users', 'academy_id'));

        // Test students table foreign keys
        $this->assertTrue(Schema::hasColumn('students', 'branch_id'));
        $this->assertTrue(Schema::hasColumn('students', 'academy_id'));
        $this->assertTrue(Schema::hasColumn('students', 'program_id'));

        // Test payments table foreign keys
        $this->assertTrue(Schema::hasColumn('payments', 'student_id'));
        $this->assertTrue(Schema::hasColumn('payments', 'branch_id'));
        $this->assertTrue(Schema::hasColumn('payments', 'academy_id'));

        // Test academies table foreign keys
        $this->assertTrue(Schema::hasColumn('academies', 'branch_id'));

        // Test programs table foreign keys
        $this->assertTrue(Schema::hasColumn('programs', 'academy_id'));
    }

    /**
     * Test data relationship integrity
     */
    public function test_data_relationship_integrity(): void
    {
        // Create test data with proper relationships
        $branch = Branch::factory()->create();
        $academy = Academy::factory()->create(['branch_id' => $branch->id]);
        $program = Program::factory()->create(['academy_id' => $academy->id]);
        $student = Student::factory()->create([
            'branch_id' => $branch->id,
            'academy_id' => $academy->id,
            'program_id' => $program->id,
        ]);

        // Test that relationships work correctly
        $this->assertEquals($branch->id, $student->branch_id);
        $this->assertEquals($academy->id, $student->academy_id);
        $this->assertEquals($program->id, $student->program_id);

        // Test that we can access related models
        $this->assertInstanceOf(Branch::class, $student->branch);
        $this->assertInstanceOf(Academy::class, $student->academy);
        $this->assertInstanceOf(Program::class, $student->program);
    }

    /**
     * Test cascade delete behavior
     */
    public function test_cascade_delete_behavior(): void
    {
        // Create test data
        $branch = Branch::factory()->create();
        $academy = Academy::factory()->create(['branch_id' => $branch->id]);
        $student = Student::factory()->create([
            'branch_id' => $branch->id,
            'academy_id' => $academy->id,
        ]);
        $payment = Payment::factory()->create([
            'student_id' => $student->id,
            'branch_id' => $branch->id,
            'academy_id' => $academy->id,
        ]);

        // Test that deleting a student cascades to payments
        $paymentId = $payment->id;
        $student->delete();
        
        $this->assertDatabaseMissing('payments', ['id' => $paymentId]);
    }

    /**
     * Test that no duplicate users exist
     */
    public function test_no_duplicate_users(): void
    {
        // Create users with different emails
        User::factory()->create(['email' => '<EMAIL>']);
        User::factory()->create(['email' => '<EMAIL>']);

        // Check for duplicates
        $duplicates = User::select('email')
            ->groupBy('email')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        $this->assertCount(0, $duplicates, 'Duplicate users found');
    }

    /**
     * Test seeder data integrity
     */
    public function test_seeder_data_integrity(): void
    {
        // Run the database seeder
        $this->seed();

        // Check that admin user exists
        $admin = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($admin, 'Admin user not created by seeder');
        $this->assertEquals('admin', $admin->role);

        // Check that branches exist
        $this->assertGreaterThan(0, Branch::count(), 'No branches created by seeder');

        // Check that academies exist and are properly linked
        $academies = Academy::with('branch')->get();
        $this->assertGreaterThan(0, $academies->count(), 'No academies created by seeder');
        
        foreach ($academies as $academy) {
            $this->assertNotNull($academy->branch, "Academy {$academy->id} has no valid branch");
        }

        // Check data consistency
        $studentCount = Student::count();
        $paymentCount = Payment::count();
        
        if ($studentCount > 0) {
            // Should have at least some payments for students
            $this->assertGreaterThan(0, $paymentCount, 'No payments created for students');
            
            // Check that payment count is reasonable (not too many more than students)
            $this->assertLessThanOrEqual(
                $studentCount * 2, 
                $paymentCount, 
                'Too many payments compared to students - possible data duplication'
            );
        }
    }

    /**
     * Test migration rollback safety
     */
    public function test_migration_rollback_safety(): void
    {
        // This test ensures our migrations can be rolled back safely
        $this->artisan('migrate:status')->assertExitCode(0);
        
        // Test that we can rollback the last batch
        $this->artisan('migrate:rollback', ['--step' => 1])->assertExitCode(0);
        
        // Test that we can re-run the migration
        $this->artisan('migrate')->assertExitCode(0);
    }

    /**
     * Test data validation command
     */
    public function test_data_validation_command(): void
    {
        // Seed some data first
        $this->seed();
        
        // Run our data validation command
        $this->artisan('db:validate-integrity')->assertExitCode(0);
    }

    /**
     * Test system health check command
     */
    public function test_system_health_check_command(): void
    {
        $this->artisan('system:health-check')->assertExitCode(0);
    }

    /**
     * Test that foreign key violations are prevented
     */
    public function test_foreign_key_violations_prevented(): void
    {
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        // Try to create a student with invalid branch_id
        Student::create([
            'branch_id' => 99999, // Non-existent branch
            'academy_id' => 1,
            'full_name' => 'Test Student',
            'phone' => '+971501234567',
            'email' => '<EMAIL>',
            'nationality' => 'UAE',
            'birth_date' => '2000-01-01',
            'join_date' => now(),
            'status' => 'active',
        ]);
    }

    /**
     * Test performance with large datasets
     */
    public function test_performance_with_large_dataset(): void
    {
        $startTime = microtime(true);
        
        // Create a reasonable amount of test data
        $branch = Branch::factory()->create();
        $academy = Academy::factory()->create(['branch_id' => $branch->id]);
        $program = Program::factory()->create(['academy_id' => $academy->id]);
        
        // Create 100 students with payments
        Student::factory(100)->create([
            'branch_id' => $branch->id,
            'academy_id' => $academy->id,
            'program_id' => $program->id,
        ])->each(function ($student) {
            Payment::factory()->create([
                'student_id' => $student->id,
                'branch_id' => $student->branch_id,
                'academy_id' => $student->academy_id,
            ]);
        });
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        // Should complete within reasonable time (adjust as needed)
        $this->assertLessThan(30, $executionTime, 'Data creation took too long');
        
        // Verify data integrity
        $this->assertEquals(100, Student::count());
        $this->assertEquals(100, Payment::count());
    }
}
