{{-- UAE English Sports Academy - Date Formatting Component --}}
{{-- Premium date display with RTL support and Arabic/English formatting --}}

@props([
    'date' => null,
    'format' => null,
    'locale' => null,
    'timezone' => 'Asia/Dubai',
    'relative' => false,
    'showTime' => false,
    'showTimezone' => false,
    'class' => 'date-display',
])

@php
    $locale = $locale ?? app()->getLocale();
    $isRtl = $locale === 'ar';
    
    // Handle null or empty dates
    if (!$date) {
        $displayText = $isRtl ? 'غير محدد' : 'Not specified';
    } else {
        // Convert to Carbon instance
        $carbonDate = \Carbon\Carbon::parse($date)->setTimezone($timezone);
        
        // Set locale for Carbon
        $carbonDate->locale($locale);
        
        // Determine format
        if (!$format) {
            if ($isRtl) {
                $format = $showTime ? 'd/m/Y H:i' : 'd/m/Y';
            } else {
                $format = $showTime ? 'M d, Y H:i' : 'M d, Y';
            }
        }
        
        // Format the date
        if ($relative) {
            // Show relative time (e.g., "2 hours ago", "منذ ساعتين")
            $displayText = $carbonDate->diffForHumans();
        } else {
            // Show formatted date
            if ($isRtl) {
                // Arabic formatting
                $displayText = $carbonDate->format($format);
                
                // Convert to Arabic-Indic numerals if needed
                $arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
                $englishNumerals = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
                $displayText = str_replace($englishNumerals, $arabicNumerals, $displayText);
            } else {
                // English formatting
                $displayText = $carbonDate->format($format);
            }
        }
        
        // Add timezone if requested
        if ($showTimezone) {
            $timezoneText = $isRtl ? 'توقيت الإمارات' : 'UAE Time';
            $displayText .= ' (' . $timezoneText . ')';
        }
    }
    
    // CSS classes
    $classes = [
        $class,
        'date-component',
        $isRtl ? 'date-rtl' : 'date-ltr',
    ];
    
    // Additional attributes
    $attributes = [
        'dir' => $isRtl ? 'rtl' : 'ltr',
        'data-date' => $date ? $carbonDate->toISOString() : null,
        'data-locale' => $locale,
        'data-timezone' => $timezone,
        'title' => $date ? $carbonDate->format('Y-m-d H:i:s T') : null,
    ];
@endphp

<span 
    class="{{ implode(' ', $classes) }}"
    @foreach($attributes as $key => $value)
        @if($value !== null)
            {{ $key }}="{{ $value }}"
        @endif
    @endforeach
>
    {{ $displayText }}
</span>

{{-- Component Styles --}}
@once
@push('styles')
<style>
/* Date Component Styles */
.date-component {
    white-space: nowrap;
    font-variant-numeric: tabular-nums;
    font-feature-settings: "tnum";
}

.date-rtl {
    direction: rtl;
    text-align: right;
}

.date-ltr {
    direction: ltr;
    text-align: left;
}

/* Date sizes */
.date-small {
    font-size: 0.75rem;
    color: var(--dark-gray, #4A5568);
}

.date-normal {
    font-size: 0.875rem;
}

.date-large {
    font-size: 1rem;
    font-weight: 500;
}

/* Date colors */
.date-muted {
    color: var(--dark-gray, #4A5568);
}

.date-primary {
    color: var(--charcoal-black, #1A202C);
}

.date-accent {
    color: var(--leaders-red, #E53E3E);
}

/* Date in different contexts */
.table-bank .date-component {
    font-size: 0.875rem;
    color: var(--dark-gray, #4A5568);
}

.bank-card .date-component {
    font-size: 0.75rem;
    color: var(--dark-gray, #4A5568);
}

.stats-card .date-component {
    font-size: 0.875rem;
    font-weight: 500;
}

/* Relative dates */
.date-relative {
    font-style: italic;
    color: var(--dark-gray, #4A5568);
}

/* Date with time */
.date-with-time {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

[dir="rtl"] .date-with-time {
    align-items: flex-end;
}

.date-with-time .date-part {
    font-weight: 500;
}

.date-with-time .time-part {
    font-size: 0.75rem;
    color: var(--dark-gray, #4A5568);
    margin-top: 0.125rem;
}

/* Date ranges */
.date-range {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.date-range .date-separator {
    color: var(--dark-gray, #4A5568);
    font-weight: 400;
}

[dir="rtl"] .date-range {
    flex-direction: row-reverse;
}

/* Responsive date display */
@media (max-width: 768px) {
    .date-component {
        font-size: 0.75rem;
    }
    
    .date-large {
        font-size: 0.875rem;
    }
    
    .date-with-time {
        flex-direction: row;
        gap: 0.5rem;
    }
    
    .date-with-time .time-part {
        margin-top: 0;
    }
}

/* Print styles */
@media print {
    .date-component {
        color: black !important;
    }
    
    .date-relative {
        font-style: normal;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .date-component {
        font-weight: 500;
    }
    
    .date-muted {
        color: var(--charcoal-black, #1A202C);
    }
}

/* Dark theme support */
.dark .date-component {
    color: var(--pure-white, #FFFFFF);
}

.dark .date-muted {
    color: var(--medium-gray, #E2E8F0);
}

/* Animation for relative dates */
.date-relative {
    transition: color 0.2s ease;
}

.date-relative:hover {
    color: var(--leaders-red, #E53E3E);
}

/* Accessibility */
.date-component:focus {
    outline: 2px solid var(--leaders-red, #E53E3E);
    outline-offset: 2px;
    border-radius: 2px;
}

/* Loading state */
.date-loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    color: transparent;
    border-radius: 4px;
    min-width: 80px;
    height: 1em;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}
</style>
@endpush
@endonce
