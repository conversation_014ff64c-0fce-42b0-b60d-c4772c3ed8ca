<!-- Payment Form Partial -->
<div class="space-y-6" x-data="paymentForm()" x-init="init()" class="payment-form">
    <!-- Student & Location Selection -->
    <div class="bank-card">
        <div class="bank-card-header">
            <h3 class="bank-card-title">Student & Location Information</h3>
        </div>
        <div class="bank-card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Branch Selection -->
                <div>
                    <label for="branch_id" class="form-label required">Branch</label>
                    <select name="branch_id" id="branch_id" class="form-select" x-model="selectedBranch"
                        @change="loadAcademies()" required>
                        <option value="">Select Branch</option>
                        @foreach ($branches as $branch)
                            <option value="{{ $branch->id }}"
                                {{ old('branch_id', $payment->branch_id ?? '') == $branch->id ? 'selected' : '' }}>
                                {{ $branch->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('branch_id')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Academy Selection -->
                <div>
                    <label for="academy_id" class="form-label required">Academy</label>
                    <div class="relative">
                        <select name="academy_id" id="academy_id" class="form-select" x-model="selectedAcademy"
                            @change="loadStudents()" required :disabled="!selectedBranch || loadingAcademies">
                            <option value="" x-text="getAcademyPlaceholder()"></option>
                            <template x-for="academy in academies" :key="academy.id">
                                <option :value="academy.id" x-text="academy.name"
                                    :selected="academy.id == {{ old('academy_id', $payment->academy_id ?? 'null') }}">
                                </option>
                            </template>
                        </select>
                        <div x-show="loadingAcademies" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <svg class="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg"
                                fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    @error('academy_id')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Student Selection -->
                <div>
                    <label for="student_id" class="form-label required">Student</label>
                    <div class="relative">
                        <select name="student_id" id="student_id" class="form-select" required
                            :disabled="!selectedAcademy || loadingStudents" x-model="selectedStudent">
                            <option value="" x-text="getStudentPlaceholder()"></option>
                            <template x-for="student in students" :key="student.id">
                                <option :value="student.id"
                                    x-text="student.full_name + (student.phone ? ' - ' + student.phone : '')">
                                </option>
                            </template>
                        </select>
                        <div x-show="loadingStudents" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <svg class="animate-spin h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg"
                                fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                        </div>
                    </div>
                    @error('student_id')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Details -->
    <div class="bank-card">
        <div class="bank-card-header">
            <h3 class="bank-card-title">Payment Details</h3>
        </div>
        <div class="bank-card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Amount -->
                <div>
                    <label for="amount" class="form-label required">Amount (AED)</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-dark-gray text-sm">AED</span>
                        </div>
                        <input type="number" name="amount" id="amount" class="form-input pl-12"
                            value="{{ old('amount', $payment->amount ?? '') }}" step="0.01" min="0"
                            max="999999.99" required x-model="amount" @input="calculateNet()">
                    </div>
                    @error('amount')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Discount -->
                <div>
                    <label for="discount" class="form-label">Discount (AED)</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <span class="text-dark-gray text-sm">AED</span>
                        </div>
                        <input type="number" name="discount" id="discount" class="form-input pl-12"
                            value="{{ old('discount', $payment->discount ?? '0') }}" step="0.01" min="0"
                            max="999999.99" x-model="discount" @input="calculateNet()">
                    </div>
                    @error('discount')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- VAT Settings -->
                @if (\App\Models\Setting::get('vat_enabled', true))
                    <!-- VAT Rate -->
                    <div>
                        <label for="vat_rate" class="form-label">VAT Rate (%)</label>
                        <div class="relative">
                            <input type="number" name="vat_rate" id="vat_rate" class="form-input pr-8"
                                value="{{ old('vat_rate', $payment->vat_rate ?? \App\Models\Setting::get('vat_rate', 5.0)) }}"
                                step="0.01" min="0" max="100" x-model="vatRate"
                                @input="calculateVat()">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <span class="text-dark-gray text-sm">%</span>
                            </div>
                        </div>
                        @error('vat_rate')
                            <p class="form-error">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- VAT Inclusive -->
                    <div class="md:col-span-2">
                        <div class="flex items-center space-x-4">
                            <input type="hidden" name="vat_inclusive" value="0">
                            <input type="checkbox" name="vat_inclusive" id="vat_inclusive" class="form-checkbox"
                                value="1"
                                {{ old('vat_inclusive', $payment->vat_inclusive ?? \App\Models\Setting::get('vat_inclusive_by_default', false)) ? 'checked' : '' }}
                                x-model="vatInclusive" @change="calculateVat()">
                            <label for="vat_inclusive" class="text-sm text-charcoal-black">
                                Amount includes VAT (VAT Inclusive)
                            </label>
                            <div class="text-xs text-dark-gray">
                                Check this if the entered amount already includes VAT
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Amount Breakdown Display -->
                <div class="md:col-span-2">
                    <div class="bg-off-white p-4 rounded-lg border border-light-gray">
                        @if (\App\Models\Setting::get('vat_enabled', true))
                            <!-- VAT Breakdown -->
                            <div class="space-y-2">
                                <div class="flex justify-between items-center">
                                    <span class="text-dark-gray">Subtotal:</span>
                                    <span class="font-medium" x-text="'AED ' + subtotal.toFixed(2)"></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-dark-gray">VAT (<span x-text="vatRate"></span>%):</span>
                                    <span class="font-medium" x-text="'AED ' + vatAmount.toFixed(2)"></span>
                                </div>
                                <div class="flex justify-between items-center" x-show="discount > 0">
                                    <span class="text-dark-gray">Discount:</span>
                                    <span class="font-medium text-red-600"
                                        x-text="'- AED ' + parseFloat(discount || 0).toFixed(2)"></span>
                                </div>
                                <hr class="border-light-gray">
                                <div class="flex justify-between items-center">
                                    <span class="text-dark-gray font-medium">Total Amount:</span>
                                    <span class="text-2xl font-bold text-leaders-red"
                                        x-text="'AED ' + finalAmount.toFixed(2)"></span>
                                </div>
                            </div>
                        @else
                            <!-- Simple Net Amount -->
                            <div class="flex justify-between items-center">
                                <span class="text-dark-gray font-medium">Net Amount:</span>
                                <span class="text-2xl font-bold text-leaders-red"
                                    x-text="'AED ' + netAmount.toFixed(2)"></span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Payment Method -->
                <div>
                    <label for="payment_method" class="form-label required">Payment Method</label>
                    <select name="payment_method" id="payment_method" class="form-select" required>
                        <option value="">Select Method</option>
                        <option value="cash"
                            {{ old('payment_method', $payment->payment_method ?? '') == 'cash' ? 'selected' : '' }}>
                            Cash</option>
                        <option value="card"
                            {{ old('payment_method', $payment->payment_method ?? '') == 'card' ? 'selected' : '' }}>
                            Credit/Debit Card</option>
                        <option value="bank_transfer"
                            {{ old('payment_method', $payment->payment_method ?? '') == 'bank_transfer' ? 'selected' : '' }}>
                            Bank Transfer</option>
                        <option value="online"
                            {{ old('payment_method', $payment->payment_method ?? '') == 'online' ? 'selected' : '' }}>
                            Online Payment</option>
                        <option value="cheque"
                            {{ old('payment_method', $payment->payment_method ?? '') == 'cheque' ? 'selected' : '' }}>
                            Cheque</option>
                    </select>
                    @error('payment_method')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="form-label required">Payment Status</label>
                    <select name="status" id="status" class="form-select" required>
                        <option value="">Select Status</option>
                        <option value="completed"
                            {{ old('status', $payment->status ?? '') == 'completed' ? 'selected' : '' }}>Completed
                        </option>
                        <option value="pending"
                            {{ old('status', $payment->status ?? '') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="failed"
                            {{ old('status', $payment->status ?? '') == 'failed' ? 'selected' : '' }}>Failed</option>
                        <option value="cancelled"
                            {{ old('status', $payment->status ?? '') == 'cancelled' ? 'selected' : '' }}>Cancelled
                        </option>
                        <option value="refunded"
                            {{ old('status', $payment->status ?? '') == 'refunded' ? 'selected' : '' }}>Refunded
                        </option>
                    </select>
                    @error('status')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Dates & Schedule -->
    <div class="bank-card">
        <div class="bank-card-header">
            <h3 class="bank-card-title">Dates & Schedule</h3>
        </div>
        <div class="bank-card-body">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Payment Date -->
                <div>
                    <label for="payment_date" class="form-label required">Payment Date</label>
                    <input type="date" name="payment_date" id="payment_date" class="form-input"
                        value="{{ old('payment_date', isset($payment) && $payment->payment_date ? $payment->payment_date->format('Y-m-d') : date('Y-m-d')) }}"
                        required>
                    @error('payment_date')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Start Date -->
                <div>
                    <label for="start_date" class="form-label required">Period Start Date</label>
                    <input type="date" name="start_date" id="start_date" class="form-input"
                        value="{{ old('start_date', isset($payment) && $payment->start_date ? $payment->start_date->format('Y-m-d') : '') }}"
                        required>
                    @error('start_date')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- End Date -->
                <div>
                    <label for="end_date" class="form-label required">Period End Date</label>
                    <input type="date" name="end_date" id="end_date" class="form-input"
                        value="{{ old('end_date', isset($payment) && $payment->end_date ? $payment->end_date->format('Y-m-d') : '') }}"
                        required>
                    @error('end_date')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Class Time From -->
                <div>
                    <label for="class_time_from" class="form-label">Class Time From</label>
                    <input type="time" name="class_time_from" id="class_time_from" class="form-input"
                        value="{{ old('class_time_from', $payment->class_time_from ?? '') }}">
                    @error('class_time_from')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Class Time To -->
                <div>
                    <label for="class_time_to" class="form-label">Class Time To</label>
                    <input type="time" name="class_time_to" id="class_time_to" class="form-input"
                        value="{{ old('class_time_to', $payment->class_time_to ?? '') }}">
                    @error('class_time_to')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Renewal Checkbox -->
                <div class="flex items-center">
                    <input type="hidden" name="renewal" value="0">
                    <input type="checkbox" name="renewal" id="renewal" class="form-checkbox" value="1"
                        {{ old('renewal', $payment->renewal ?? false) ? 'checked' : '' }}>
                    <label for="renewal" class="ml-2 text-sm text-charcoal-black">
                        This is a renewal payment
                    </label>
                </div>

                <!-- Payment Type -->
                <div>
                    <label for="payment_type" class="form-label">Payment Type</label>
                    <select name="payment_type" id="payment_type" class="form-select">
                        <option value="">Auto-detect based on student history</option>
                        <option value="new_entry"
                            {{ old('payment_type', $payment->payment_type ?? '') == 'new_entry' ? 'selected' : '' }}>
                            New Entry (طالب جديد)
                        </option>
                        <option value="renewal"
                            {{ old('payment_type', $payment->payment_type ?? '') == 'renewal' ? 'selected' : '' }}>
                            Renewal (تجديد)
                        </option>
                        <option value="regular"
                            {{ old('payment_type', $payment->payment_type ?? '') == 'regular' ? 'selected' : '' }}>
                            Regular (دفعة عادية)
                        </option>
                    </select>
                    <p class="text-xs text-gray-600 mt-1">
                        Leave empty to automatically determine based on student's payment history
                    </p>
                    @error('payment_type')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Information -->
    <div class="bank-card">
        <div class="bank-card-header">
            <h3 class="bank-card-title">Additional Information</h3>
        </div>
        <div class="bank-card-body">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Reset Number -->
                <div>
                    <label for="reset_num" class="form-label">Reset Number</label>
                    <input type="text" name="reset_num" id="reset_num" class="form-input"
                        value="{{ old('reset_num', $payment->reset_num ?? '') }}" maxlength="50">
                    @error('reset_num')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Reference Number -->
                <div>
                    <label for="reference_number" class="form-label">Reference Number</label>
                    <input type="text" name="reference_number" id="reference_number" class="form-input"
                        value="{{ old('reference_number', $payment->reference_number ?? '') }}"
                        placeholder="Auto-generated if empty">
                    @error('reference_number')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div class="md:col-span-2">
                    <label for="description" class="form-label">Description</label>
                    <textarea name="description" id="description" rows="3" class="form-textarea"
                        placeholder="Payment description...">{{ old('description', $payment->description ?? '') }}</textarea>
                    @error('description')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Note -->
                <div class="md:col-span-2">
                    <label for="note" class="form-label">Note</label>
                    <textarea name="note" id="note" rows="3" class="form-textarea" placeholder="Additional notes...">{{ old('note', $payment->note ?? '') }}</textarea>
                    @error('note')
                        <p class="form-error">{{ $message }}</p>
                    @enderror
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
    <script>
        function paymentForm() {
            return {
                selectedBranch: '{{ old('branch_id', isset($payment) ? $payment->branch_id : (isset($prefilledStudent) ? $prefilledStudent->branch_id : '')) }}',
                selectedAcademy: '{{ old('academy_id', isset($payment) ? $payment->academy_id : (isset($prefilledStudent) ? $prefilledStudent->academy_id : '')) }}',
                selectedStudent: '{{ old('student_id', isset($payment) ? $payment->student_id : (isset($prefilledStudent) ? $prefilledStudent->id : '')) }}',
                academies: @json($academies ?? []),
                students: @json($students ?? []),
                amount: {{ old('amount', isset($payment) ? $payment->amount : 0) }},
                discount: {{ old('discount', isset($payment) ? $payment->discount : 0) }},
                vatRate: {{ old('vat_rate', isset($payment) ? $payment->vat_rate : \App\Models\Setting::get('vat_rate', 5.0)) }},
                vatInclusive: {{ old('vat_inclusive', isset($payment) ? ($payment->vat_inclusive ? 'true' : 'false') : (\App\Models\Setting::get('vat_inclusive_by_default', false) ? 'true' : 'false')) }},
                netAmount: 0,
                subtotal: 0,
                vatAmount: 0,
                finalAmount: 0,
                loadingAcademies: false,
                loadingStudents: false,

                init() {
                    this.calculateVat();

                    // Handle pre-filled data
                    if (this.selectedBranch) {
                        this.loadAcademies().then(() => {
                            if (this.selectedAcademy) {
                                this.loadStudents();
                            }
                        });
                    }
                },

                calculateNet() {
                    this.netAmount = Math.max(0, parseFloat(this.amount || 0) - parseFloat(this.discount || 0));
                    this.calculateVat();
                },

                calculateVat() {
                    const amount = parseFloat(this.amount || 0);
                    const discount = parseFloat(this.discount || 0);
                    const vatRate = parseFloat(this.vatRate || 0);

                    if (this.vatInclusive) {
                        // Amount includes VAT - calculate subtotal and VAT amount
                        this.subtotal = amount / (1 + (vatRate / 100));
                        this.vatAmount = amount - this.subtotal;
                        this.finalAmount = amount - discount;
                    } else {
                        // Amount excludes VAT - calculate VAT amount and total
                        this.subtotal = amount;
                        this.vatAmount = amount * (vatRate / 100);
                        this.finalAmount = amount + this.vatAmount - discount;
                    }

                    // Calculate net amount (for non-VAT display)
                    this.netAmount = Math.max(0, amount - discount);

                    // Round to 2 decimal places
                    this.subtotal = Math.round(this.subtotal * 100) / 100;
                    this.vatAmount = Math.round(this.vatAmount * 100) / 100;
                    this.finalAmount = Math.max(0, Math.round(this.finalAmount * 100) / 100);
                },

                getAcademyPlaceholder() {
                    if (!this.selectedBranch) return 'Select Branch First';
                    if (this.loadingAcademies) return 'Loading Academies...';
                    if (this.academies.length === 0) return 'No Academies Available';
                    return 'Select Academy';
                },

                getStudentPlaceholder() {
                    if (!this.selectedAcademy) return 'Select Academy First';
                    if (this.loadingStudents) return 'Loading Students...';
                    if (this.students.length === 0) return 'No Students Available';
                    return 'Select Student';
                },

                async loadAcademies() {
                    if (!this.selectedBranch) {
                        this.academies = [];
                        this.students = [];
                        this.selectedAcademy = '';
                        return Promise.resolve();
                    }

                    this.loadingAcademies = true;
                    this.academies = [];
                    this.students = [];

                    // Don't reset selectedAcademy if it's pre-filled
                    if (!this.selectedAcademy) {
                        this.selectedAcademy = '';
                    }

                    try {
                        console.log('Loading academies for branch:', this.selectedBranch);
                        const response = await fetch(
                            `{{ route('payments.academies-by-branch') }}?branch_id=${this.selectedBranch}`, {
                                method: 'GET',
                                headers: {
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute(
                                        'content') || '',
                                    'X-Requested-With': 'XMLHttpRequest'
                                },
                                credentials: 'same-origin'
                            });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data = await response.json();
                        console.log('Academies loaded:', data);
                        this.academies = data.academies || [];
                        return Promise.resolve();
                    } catch (error) {
                        console.error('Error loading academies:', error);
                        this.academies = [];
                        alert('Failed to load academies. Please try again.');
                        return Promise.reject(error);
                    } finally {
                        this.loadingAcademies = false;
                    }
                },

                async loadStudents() {
                    if (!this.selectedAcademy) {
                        this.students = [];
                        return;
                    }

                    this.loadingStudents = true;
                    this.students = [];

                    try {
                        console.log('Loading students for academy:', this.selectedAcademy);
                        const response = await fetch(
                            `{{ route('payments.students-by-academy') }}?academy_id=${this.selectedAcademy}`, {
                                method: 'GET',
                                headers: {
                                    'Accept': 'application/json',
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute(
                                        'content') || '',
                                    'X-Requested-With': 'XMLHttpRequest'
                                },
                                credentials: 'same-origin'
                            });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data = await response.json();
                        console.log('Students loaded:', data);
                        this.students = data.students || [];
                    } catch (error) {
                        console.error('Error loading students:', error);
                        this.students = [];
                        alert('Failed to load students. Please try again.');
                    } finally {
                        this.loadingStudents = false;
                    }
                }
            }
        }
    </script>
@endpush
