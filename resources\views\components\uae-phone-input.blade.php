@props([
    'name' => 'phone',
    'id' => null,
    'value' => '',
    'placeholder' => '+************',
    'required' => false,
    'class' => '',
])

@php
    $id = $id ?? $name;
@endphp

<!-- Simple phone input - just one field -->
<input type="tel" id="{{ $id }}" name="{{ $name }}" value="{{ $value }}"
    class="form-input-bank {{ $class }}" placeholder="{{ $placeholder }}"
    @if ($required) required @endif
    {{ $attributes->except(['name', 'id', 'value', 'placeholder', 'required', 'class']) }}>
