@extends('layouts.dashboard')

@section('title', 'Add New Student - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Add New Student</h1>
                <p class="text-lg text-dark-gray">Create a new student profile</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('students.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Students
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto" x-data="integratedStudentForm()">
        <form method="POST" action="{{ route('students.store') }}" enctype="multipart/form-data" class="space-y-6">
            @csrf

            <!-- Basic Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                    <p class="bank-card-subtitle">Enter the student's personal details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Profile Image -->
                        <div class="md:col-span-2">
                            <label for="profile_image" class="form-label-bank">Profile Image</label>
                            <div
                                class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-leaders-red transition-colors duration-200">
                                <div class="space-y-1 text-center">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none"
                                        viewBox="0 0 48 48">
                                        <path
                                            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="profile_image"
                                            class="relative cursor-pointer bg-white rounded-md font-medium text-leaders-red hover:text-leaders-deep-red focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-leaders-red">
                                            <span>Upload a photo</span>
                                            <input id="profile_image" name="profile_image" type="file" class="sr-only"
                                                accept="image/*">
                                        </label>
                                        <p class="pl-1">or drag and drop</p>
                                    </div>
                                    <p class="text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                                </div>
                            </div>
                            @error('profile_image')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Full Name -->
                        <div class="md:col-span-2">
                            <label for="full_name" class="form-label-bank required">Full Name</label>
                            <input type="text" id="full_name" name="full_name" value="{{ old('full_name') }}"
                                class="form-input-bank @error('full_name') border-red-500 @enderror"
                                placeholder="Enter student's full name" required>
                            @error('full_name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="form-label-bank">Email Address</label>
                            <input type="email" id="email" name="email" value="{{ old('email') }}"
                                class="form-input-bank @error('email') border-red-500 @enderror"
                                placeholder="<EMAIL>">
                            @error('email')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Phone -->
                        <div>
                            <label for="phone" class="form-label-bank required">Phone Number</label>
                            <input type="tel" id="phone" name="phone" value="{{ old('phone') }}"
                                class="form-input-bank @error('phone') border-red-500 @enderror" placeholder="+971XXXXXXXXX"
                                pattern="^\+971[0-9]{9}$" required>
                            <p class="form-help-bank">UAE format: +971XXXXXXXXX</p>
                            @error('phone')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Nationality -->
                        <div>
                            <label for="nationality" class="form-label-bank">Nationality</label>
                            <input type="text" id="nationality" name="nationality" value="{{ old('nationality') }}"
                                class="form-input-bank @error('nationality') border-red-500 @enderror"
                                placeholder="e.g., UAE, India, Pakistan">
                            @error('nationality')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Birth Date -->
                        <div>
                            <label for="birth_date" class="form-label-bank">Birth Date</label>
                            <input type="date" id="birth_date" name="birth_date" value="{{ old('birth_date') }}"
                                class="form-input-bank @error('birth_date') border-red-500 @enderror"
                                max="{{ date('Y-m-d') }}">
                            @error('birth_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label for="address" class="form-label-bank">Address</label>
                            <textarea id="address" name="address" rows="3"
                                class="form-textarea-bank @error('address') border-red-500 @enderror" placeholder="Enter student's address">{{ old('address') }}</textarea>
                            @error('address')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Academy Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Academy Information</h3>
                    <p class="bank-card-subtitle">Select the branch and academy for enrollment</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Branch -->
                        <div>
                            <label for="branch_id" class="form-label-bank required">Branch</label>
                            <select id="branch_id" name="branch_id"
                                class="form-select-bank @error('branch_id') border-red-500 @enderror"
                                @change="onBranchChange($event)" required>
                                <option value="">Select Branch</option>
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ old('branch_id') == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('branch_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Academy -->
                        <div>
                            <label for="academy_id" class="form-label-bank required">Academy</label>
                            <select id="academy_id" name="academy_id"
                                class="form-select-bank @error('academy_id') border-red-500 @enderror" required>
                                <option value="">Select Academy</option>
                                @foreach ($academies as $academy)
                                    <option value="{{ $academy->id }}" data-branch="{{ $academy->branch_id }}"
                                        {{ old('academy_id') == $academy->id ? 'selected' : '' }} style="display: none;">
                                        {{ $academy->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('academy_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Join Date -->
                        <div>
                            <label for="join_date" class="form-label-bank required">Join Date</label>
                            <input type="date" id="join_date" name="join_date"
                                value="{{ old('join_date', date('Y-m-d')) }}"
                                class="form-input-bank @error('join_date') border-red-500 @enderror" required>
                            @error('join_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="form-label-bank required">Status</label>
                            <select id="status" name="status"
                                class="form-select-bank @error('status') border-red-500 @enderror" required>
                                <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>Active
                                </option>
                                <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>Inactive
                                </option>
                                <option value="suspended" {{ old('status') === 'suspended' ? 'selected' : '' }}>Suspended
                                </option>
                            </select>
                            @error('status')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="bank-card" x-show="true">
                <div class="bank-card-header">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="bank-card-title">Payment Information</h3>
                            <p class="bank-card-subtitle">Create initial payment for the student</p>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="create_payment" name="create_payment" class="form-checkbox"
                                value="1" x-model="createPayment" checked>
                            <label for="create_payment" class="ml-2 text-sm font-medium text-charcoal-black">
                                Create Payment
                            </label>
                        </div>
                    </div>
                </div>
                <div class="bank-card-body" x-show="createPayment" x-transition>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Payment Amount -->
                        <div>
                            <label for="payment_amount" class="form-label-bank">Amount <span
                                    class="text-error-red">*</span></label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-gray">AED</span>
                                <input type="number" id="payment_amount" name="payment_amount"
                                    value="{{ old('payment_amount') }}" step="0.01" min="0"
                                    class="form-input-bank pl-12 @error('payment_amount') border-red-500 @enderror"
                                    placeholder="0.00" x-model="paymentAmount" @input="calculateVAT()">
                            </div>
                            @error('payment_amount')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- VAT Inclusive Toggle -->
                        <div>
                            <label class="form-label-bank">VAT Settings</label>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="vat_inclusive" name="vat_inclusive" class="form-checkbox"
                                        value="1" x-model="vatInclusive" @change="calculateVAT()">
                                    <label for="vat_inclusive" class="ml-2 text-sm text-charcoal-black">
                                        VAT Inclusive
                                    </label>
                                </div>
                            </div>
                            <div class="mt-2">
                                <input type="number" id="vat_rate" name="vat_rate" value="{{ old('vat_rate', 5) }}"
                                    step="0.01" min="0" max="100"
                                    class="form-input-bank @error('vat_rate') border-red-500 @enderror"
                                    placeholder="5.00" x-model="vatRate" @input="calculateVAT()">
                                <p class="text-xs text-gray-600 mt-1">VAT Rate (%)</p>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label for="payment_method" class="form-label-bank">Payment Method <span
                                    class="text-error-red">*</span></label>
                            <select id="payment_method" name="payment_method"
                                class="form-select-bank @error('payment_method') border-red-500 @enderror">
                                <option value="">Select Method</option>
                                <option value="cash" {{ old('payment_method') === 'cash' ? 'selected' : '' }}>Cash
                                </option>
                                <option value="card" {{ old('payment_method') === 'card' ? 'selected' : '' }}>
                                    Credit/Debit Card</option>
                                <option value="bank_transfer"
                                    {{ old('payment_method') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer
                                </option>
                                <option value="online" {{ old('payment_method') === 'online' ? 'selected' : '' }}>Online
                                    Payment</option>
                                <option value="cheque" {{ old('payment_method') === 'cheque' ? 'selected' : '' }}>Cheque
                                </option>
                            </select>
                            @error('payment_method')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Payment Date -->
                        <div>
                            <label for="payment_date" class="form-label-bank">Payment Date <span
                                    class="text-error-red">*</span></label>
                            <input type="date" id="payment_date" name="payment_date"
                                value="{{ old('payment_date', date('Y-m-d')) }}"
                                class="form-input-bank @error('payment_date') border-red-500 @enderror">
                            @error('payment_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Start Date -->
                        <div>
                            <label for="start_date" class="form-label-bank">Start Date <span
                                    class="text-error-red">*</span></label>
                            <input type="date" id="start_date" name="start_date"
                                value="{{ old('start_date', date('Y-m-d')) }}"
                                class="form-input-bank @error('start_date') border-red-500 @enderror">
                            @error('start_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- End Date -->
                        <div>
                            <label for="end_date" class="form-label-bank">End Date <span
                                    class="text-error-red">*</span></label>
                            <input type="date" id="end_date" name="end_date"
                                value="{{ old('end_date', date('Y-m-d', strtotime('+1 month'))) }}"
                                class="form-input-bank @error('end_date') border-red-500 @enderror">
                            @error('end_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Payment Type -->
                        <div>
                            <label for="payment_type" class="form-label-bank">Payment Type</label>
                            <select id="payment_type" name="payment_type"
                                class="form-select-bank @error('payment_type') border-red-500 @enderror">
                                <option value="">Auto-detect based on student history</option>
                                <option value="new_entry"
                                    {{ old('payment_type', 'new_entry') === 'new_entry' ? 'selected' : '' }}>
                                    New Entry (طالب جديد)
                                </option>
                                <option value="renewal" {{ old('payment_type') === 'renewal' ? 'selected' : '' }}>
                                    Renewal (تجديد)
                                </option>
                                <option value="regular" {{ old('payment_type') === 'regular' ? 'selected' : '' }}>
                                    Regular (دفعة عادية)
                                </option>
                            </select>
                            <p class="text-xs text-gray-600 mt-1">
                                Leave empty to automatically determine based on student's payment history
                            </p>
                            @error('payment_type')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- VAT Summary -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg" x-show="paymentAmount > 0">
                        <h4 class="text-sm font-medium text-charcoal-black mb-3">Payment Summary</h4>
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-dark-gray">Subtotal:</span>
                                <span class="font-medium text-charcoal-black"
                                    x-text="'AED ' + subtotal.toFixed(2)"></span>
                            </div>
                            <div>
                                <span class="text-dark-gray">VAT (<span x-text="vatRate"></span>%):</span>
                                <span class="font-medium text-charcoal-black"
                                    x-text="'AED ' + vatAmount.toFixed(2)"></span>
                            </div>
                            <div>
                                <span class="text-dark-gray font-medium">Total:</span>
                                <span class="font-bold text-leaders-red" x-text="'AED ' + totalAmount.toFixed(2)"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Notes -->
                    <div class="mt-6">
                        <label for="payment_note" class="form-label-bank">Payment Notes</label>
                        <textarea id="payment_note" name="payment_note" rows="2"
                            class="form-textarea-bank @error('payment_note') border-red-500 @enderror"
                            placeholder="Any notes about this payment...">{{ old('payment_note') }}</textarea>
                        @error('payment_note')
                            <p class="form-error-bank">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>



            <!-- Uniform Order -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="bank-card-title">Uniform Order</h3>
                            <p class="bank-card-subtitle">Order uniform for the student (Optional)</p>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="create_uniform" name="create_uniform" class="form-checkbox"
                                value="1" x-model="createUniform">
                            <label for="create_uniform" class="ml-2 text-sm font-medium text-charcoal-black">
                                Order Uniform
                            </label>
                        </div>
                    </div>
                </div>
                <div class="bank-card-body" x-show="createUniform" x-transition>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Uniform Item -->
                        <div>
                            <label for="uniform_item" class="form-label-bank">Item <span
                                    class="text-error-red">*</span></label>
                            <select id="uniform_item" name="uniform_item"
                                class="form-select-bank @error('uniform_item') border-red-500 @enderror">
                                <option value="">Select Item</option>
                                <option value="T-Shirt" {{ old('uniform_item') === 'T-Shirt' ? 'selected' : '' }}>T-Shirt
                                </option>
                                <option value="Shorts" {{ old('uniform_item') === 'Shorts' ? 'selected' : '' }}>Shorts
                                </option>
                                <option value="Jersey" {{ old('uniform_item') === 'Jersey' ? 'selected' : '' }}>Jersey
                                </option>
                                <option value="Track Suit" {{ old('uniform_item') === 'Track Suit' ? 'selected' : '' }}>
                                    Track Suit</option>
                                <option value="Socks" {{ old('uniform_item') === 'Socks' ? 'selected' : '' }}>Socks
                                </option>
                                <option value="Cap" {{ old('uniform_item') === 'Cap' ? 'selected' : '' }}>Cap</option>
                                <option value="Complete Set"
                                    {{ old('uniform_item') === 'Complete Set' ? 'selected' : '' }}>Complete Set</option>
                            </select>
                            @error('uniform_item')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Size -->
                        <div>
                            <label for="uniform_size" class="form-label-bank">Size <span
                                    class="text-error-red">*</span></label>
                            <select id="uniform_size" name="uniform_size"
                                class="form-select-bank @error('uniform_size') border-red-500 @enderror">
                                <option value="">Select Size</option>
                                <option value="XS" {{ old('uniform_size') === 'XS' ? 'selected' : '' }}>XS</option>
                                <option value="S" {{ old('uniform_size') === 'S' ? 'selected' : '' }}>S</option>
                                <option value="M" {{ old('uniform_size') === 'M' ? 'selected' : '' }}>M</option>
                                <option value="L" {{ old('uniform_size') === 'L' ? 'selected' : '' }}>L</option>
                                <option value="XL" {{ old('uniform_size') === 'XL' ? 'selected' : '' }}>XL</option>
                                <option value="XXL" {{ old('uniform_size') === 'XXL' ? 'selected' : '' }}>XXL</option>
                                <option value="XXXL" {{ old('uniform_size') === 'XXXL' ? 'selected' : '' }}>XXXL
                                </option>
                            </select>
                            @error('uniform_size')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Quantity -->
                        <div>
                            <label for="uniform_quantity" class="form-label-bank">Quantity <span
                                    class="text-error-red">*</span></label>
                            <input type="number" id="uniform_quantity" name="uniform_quantity"
                                value="{{ old('uniform_quantity', 1) }}" min="1" max="10"
                                class="form-input-bank @error('uniform_quantity') border-red-500 @enderror"
                                placeholder="1">
                            @error('uniform_quantity')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Uniform Amount -->
                        <div>
                            <label for="uniform_amount" class="form-label-bank">Amount <span
                                    class="text-error-red">*</span></label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-gray">AED</span>
                                <input type="number" id="uniform_amount" name="uniform_amount"
                                    value="{{ old('uniform_amount') }}" step="0.01" min="0"
                                    class="form-input-bank pl-12 @error('uniform_amount') border-red-500 @enderror"
                                    placeholder="0.00">
                            </div>
                            @error('uniform_amount')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Uniform Payment Method -->
                        <div>
                            <label for="uniform_payment_method" class="form-label-bank">Payment Method <span
                                    class="text-error-red">*</span></label>
                            <select id="uniform_payment_method" name="uniform_payment_method"
                                class="form-select-bank @error('uniform_payment_method') border-red-500 @enderror">
                                <option value="">Select Method</option>
                                <option value="cash" {{ old('uniform_payment_method') === 'cash' ? 'selected' : '' }}>
                                    Cash</option>
                                <option value="card" {{ old('uniform_payment_method') === 'card' ? 'selected' : '' }}>
                                    Credit/Debit Card</option>
                                <option value="bank_transfer"
                                    {{ old('uniform_payment_method') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer
                                </option>
                                <option value="online" {{ old('uniform_payment_method') === 'online' ? 'selected' : '' }}>
                                    Online Payment</option>
                                <option value="cheque" {{ old('uniform_payment_method') === 'cheque' ? 'selected' : '' }}>
                                    Cheque</option>
                            </select>
                            @error('uniform_payment_method')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Delivery Date -->
                        <div>
                            <label for="uniform_delivery_date" class="form-label-bank">Expected Delivery</label>
                            <input type="date" id="uniform_delivery_date" name="uniform_delivery_date"
                                value="{{ old('uniform_delivery_date', date('Y-m-d', strtotime('+1 week'))) }}"
                                class="form-input-bank @error('uniform_delivery_date') border-red-500 @enderror">
                            @error('uniform_delivery_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Uniform Notes -->
                    <div class="mt-6">
                        <label for="uniform_note" class="form-label-bank">Uniform Notes</label>
                        <textarea id="uniform_note" name="uniform_note" rows="2"
                            class="form-textarea-bank @error('uniform_note') border-red-500 @enderror"
                            placeholder="Any special requirements or notes...">{{ old('uniform_note') }}</textarea>
                        @error('uniform_note')
                            <p class="form-error-bank">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Additional Notes -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Additional Information</h3>
                    <p class="bank-card-subtitle">Optional notes and comments</p>
                </div>
                <div class="bank-card-body">
                    <div>
                        <label for="notes" class="form-label-bank">Notes</label>
                        <textarea id="notes" name="notes" rows="4"
                            class="form-textarea-bank @error('notes') border-red-500 @enderror"
                            placeholder="Any additional notes about the student...">{{ old('notes') }}</textarea>
                        @error('notes')
                            <p class="form-error-bank">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('students.index') }}" class="btn-bank btn-bank-outline">
                    Cancel
                </a>
                <button type="submit" class="btn-bank">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Student
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function integratedStudentForm() {
            return {
                // Form state
                createPayment: true,
                createUniform: false,

                // Payment data
                paymentAmount: 0,
                vatRate: 5,
                vatInclusive: false,
                subtotal: 0,
                vatAmount: 0,
                totalAmount: 0,

                // Methods
                onBranchChange(event) {
                    const branchId = event.target.value;
                    const academySelect = document.getElementById('academy_id');
                    const academyOptions = academySelect.querySelectorAll('option[data-branch]');

                    // Reset academy selection
                    academySelect.value = '';

                    // Show/hide academy options based on selected branch
                    academyOptions.forEach(option => {
                        if (!branchId || option.dataset.branch === branchId) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                        }
                    });
                },

                calculateVAT() {
                    const amount = parseFloat(this.paymentAmount) || 0;
                    const rate = parseFloat(this.vatRate) || 0;

                    if (this.vatInclusive) {
                        // Amount includes VAT
                        this.totalAmount = amount;
                        this.vatAmount = (amount * rate) / (100 + rate);
                        this.subtotal = amount - this.vatAmount;
                    } else {
                        // Amount excludes VAT
                        this.subtotal = amount;
                        this.vatAmount = (amount * rate) / 100;
                        this.totalAmount = amount + this.vatAmount;
                    }
                }
            }
        }

        // Initialize form on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Trigger branch change to show correct academies if branch is pre-selected
            const branchSelect = document.getElementById('branch_id');
            if (branchSelect.value) {
                branchSelect.dispatchEvent(new Event('change'));
            }

            // Phone number formatting
            const phoneInput = document.getElementById('phone');
            phoneInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.startsWith('971')) {
                    value = '+' + value;
                } else if (value.startsWith('0')) {
                    value = '+971' + value.substring(1);
                } else if (!value.startsWith('+971') && value.length > 0) {
                    value = '+971' + value;
                }
                e.target.value = value;
            });
        });
    </script>
@endpush
