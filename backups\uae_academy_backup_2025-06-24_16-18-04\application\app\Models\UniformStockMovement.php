<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;

class UniformStockMovement extends Model
{
    protected $fillable = [
        'uniform_inventory_id',
        'branch_id',
        'academy_id',
        'user_id',
        'reference_number',
        'movement_type',
        'quantity',
        'stock_before',
        'stock_after',
        'unit_cost',
        'total_cost',
        'currency',
        'uniform_purchase_order_id',
        'uniform_id',
        'related_type',
        'related_id',
        'movement_date',
        'reason',
        'notes',
        'notes_ar',
        'from_location',
        'to_location',
        'approved_by',
        'approved_at',
        'requires_approval',
        'approval_status',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'stock_before' => 'integer',
        'stock_after' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'movement_date' => 'date',
        'approved_at' => 'datetime',
        'requires_approval' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'localized_notes',
        'formatted_unit_cost',
        'formatted_total_cost',
        'movement_type_text',
        'movement_type_badge_class',
        'approval_status_text',
        'approval_status_badge_class',
        'is_inbound',
        'is_outbound',
        'formatted_movement_date',
        'stock_change_text',
        'impact_description',
    ];

    /**
     * Get the inventory item for this movement.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(UniformInventory::class, 'uniform_inventory_id');
    }

    /**
     * Get the branch for this movement.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy for this movement.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the user who made this movement.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who approved this movement.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the purchase order related to this movement.
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(UniformPurchaseOrder::class, 'uniform_purchase_order_id');
    }

    /**
     * Get the uniform order related to this movement.
     */
    public function uniformOrder(): BelongsTo
    {
        return $this->belongsTo(Uniform::class, 'uniform_id');
    }

    /**
     * Get the related model (polymorphic).
     */
    public function related(): MorphTo
    {
        return $this->morphTo();
    }

    // Computed Properties

    /**
     * Get localized notes based on current locale.
     */
    public function getLocalizedNotesAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->notes_ar) ? $this->notes_ar : $this->notes;
    }

    /**
     * Get formatted unit cost in AED.
     */
    public function getFormattedUnitCostAttribute(): string
    {
        return 'AED ' . number_format($this->unit_cost ?? 0, 2);
    }

    /**
     * Get formatted total cost in AED.
     */
    public function getFormattedTotalCostAttribute(): string
    {
        return 'AED ' . number_format($this->total_cost ?? 0, 2);
    }

    /**
     * Get movement type text.
     */
    public function getMovementTypeTextAttribute(): string
    {
        return match ($this->movement_type) {
            'purchase' => 'Purchase Receipt',
            'sale' => 'Sale/Issue',
            'adjustment' => 'Stock Adjustment',
            'transfer' => 'Transfer',
            'return' => 'Return',
            'damage' => 'Damage Write-off',
            'loss' => 'Loss/Theft',
            'reservation' => 'Stock Reservation',
            'release' => 'Reservation Release',
            default => 'Unknown'
        };
    }

    /**
     * Get movement type badge class for UI.
     */
    public function getMovementTypeBadgeClassAttribute(): string
    {
        return match ($this->movement_type) {
            'purchase' => 'bg-green-100 text-green-800',
            'sale' => 'bg-blue-100 text-blue-800',
            'adjustment' => 'bg-yellow-100 text-yellow-800',
            'transfer' => 'bg-purple-100 text-purple-800',
            'return' => 'bg-indigo-100 text-indigo-800',
            'damage' => 'bg-red-100 text-red-800',
            'loss' => 'bg-red-100 text-red-800',
            'reservation' => 'bg-orange-100 text-orange-800',
            'release' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get approval status text.
     */
    public function getApprovalStatusTextAttribute(): string
    {
        if (!$this->requires_approval) {
            return 'No Approval Required';
        }

        return match ($this->approval_status) {
            'pending' => 'Pending Approval',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            default => 'Unknown'
        };
    }

    /**
     * Get approval status badge class for UI.
     */
    public function getApprovalStatusBadgeClassAttribute(): string
    {
        if (!$this->requires_approval) {
            return 'bg-gray-100 text-gray-800';
        }

        return match ($this->approval_status) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'approved' => 'bg-green-100 text-green-800',
            'rejected' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Check if this is an inbound movement (increases stock).
     */
    public function getIsInboundAttribute(): bool
    {
        return in_array($this->movement_type, ['purchase', 'return', 'adjustment']) && $this->quantity > 0;
    }

    /**
     * Check if this is an outbound movement (decreases stock).
     */
    public function getIsOutboundAttribute(): bool
    {
        return in_array($this->movement_type, ['sale', 'damage', 'loss', 'transfer']) || $this->quantity < 0;
    }

    /**
     * Get formatted movement date.
     */
    public function getFormattedMovementDateAttribute(): string
    {
        return $this->movement_date->format('M d, Y');
    }

    /**
     * Get stock change text.
     */
    public function getStockChangeTextAttribute(): string
    {
        $sign = $this->quantity >= 0 ? '+' : '';
        return "{$sign}{$this->quantity} units";
    }

    /**
     * Get impact description.
     */
    public function getImpactDescriptionAttribute(): string
    {
        return "Stock changed from {$this->stock_before} to {$this->stock_after} units";
    }

    // Query Scopes

    /**
     * Scope to filter by movement type.
     */
    public function scopeByMovementType(Builder $query, string $type): Builder
    {
        return $query->where('movement_type', $type);
    }

    /**
     * Scope to get inbound movements.
     */
    public function scopeInbound(Builder $query): Builder
    {
        return $query->whereIn('movement_type', ['purchase', 'return', 'adjustment'])
                    ->where('quantity', '>', 0);
    }

    /**
     * Scope to get outbound movements.
     */
    public function scopeOutbound(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereIn('movement_type', ['sale', 'damage', 'loss', 'transfer'])
              ->orWhere('quantity', '<', 0);
        });
    }

    /**
     * Scope to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope to filter by inventory item.
     */
    public function scopeByInventoryItem(Builder $query, int $inventoryId): Builder
    {
        return $query->where('uniform_inventory_id', $inventoryId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeByDateRange(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('movement_date', [$startDate, $endDate]);
    }

    /**
     * Scope to get movements requiring approval.
     */
    public function scopeRequiresApproval(Builder $query): Builder
    {
        return $query->where('requires_approval', true);
    }

    /**
     * Scope to get pending approval movements.
     */
    public function scopePendingApproval(Builder $query): Builder
    {
        return $query->where('requires_approval', true)
                    ->where('approval_status', 'pending');
    }

    // Utility Methods

    /**
     * Generate next reference number.
     */
    public static function generateReferenceNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $lastMovement = self::where('reference_number', 'like', "MOV-{$year}{$month}-%")
                           ->orderBy('id', 'desc')
                           ->first();
        
        if ($lastMovement) {
            $lastNumber = (int) substr($lastMovement->reference_number, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }
        
        return "MOV-{$year}{$month}-" . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Approve the movement.
     */
    public function approve(User $user): bool
    {
        if (!$this->requires_approval || $this->approval_status !== 'pending') {
            return false;
        }
        
        $this->update([
            'approval_status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);
        
        return true;
    }

    /**
     * Reject the movement.
     */
    public function reject(User $user, string $reason = null): bool
    {
        if (!$this->requires_approval || $this->approval_status !== 'pending') {
            return false;
        }
        
        $this->update([
            'approval_status' => 'rejected',
            'approved_by' => $user->id,
            'approved_at' => now(),
            'notes' => ($this->notes ? $this->notes . "\n" : '') . "Rejected: {$reason}",
        ]);
        
        return true;
    }

    /**
     * Get movement statistics for a given period.
     */
    public static function getMovementStatistics(string $startDate, string $endDate, array $filters = []): array
    {
        $query = self::byDateRange($startDate, $endDate);
        
        // Apply filters
        if (!empty($filters['branch_id'])) {
            $query->byBranch($filters['branch_id']);
        }
        if (!empty($filters['academy_id'])) {
            $query->byAcademy($filters['academy_id']);
        }
        if (!empty($filters['movement_type'])) {
            $query->byMovementType($filters['movement_type']);
        }
        
        $movements = $query->get();
        
        return [
            'total_movements' => $movements->count(),
            'inbound_movements' => $movements->where('quantity', '>', 0)->count(),
            'outbound_movements' => $movements->where('quantity', '<', 0)->count(),
            'total_inbound_quantity' => $movements->where('quantity', '>', 0)->sum('quantity'),
            'total_outbound_quantity' => abs($movements->where('quantity', '<', 0)->sum('quantity')),
            'total_value' => $movements->sum('total_cost'),
            'by_type' => $movements->groupBy('movement_type')->map->count(),
        ];
    }
}
