@extends('layouts.dashboard')

@section('title', $venue->localized_name . ' - Venue Details')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $venue->localized_name }}</h1>
                <p class="text-lg text-dark-gray">{{ $venue->city }}, {{ $venue->country }}</p>
                <div class="flex items-center space-x-3 mt-2">
                    <span class="badge-bank {{ $venue->status_badge_class }}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="{{ $venue->status ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                            </path>
                        </svg>
                        {{ $venue->status_text }}
                    </span>
                    <span class="badge-bank badge-info">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                        Created {{ $venue->created_at->format('M j, Y') }}
                    </span>
                    <span class="badge-bank badge-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z">
                            </path>
                        </svg>
                        {{ $venue->code }}
                    </span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('update', $venue)
                <a href="{{ route('venues.edit', $venue) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Venue
                </a>
            @endcan
            <a href="{{ route('venues.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Venues
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="venueDetails()" x-init="init()">
        <!-- Venue Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <!-- Total Fields -->
            <div class="stats-card scale-in" style="animation-delay: 0.1s;">
                <div class="stats-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_fields'] ?? 0 }}</div>
                <div class="stats-label">Total Fields</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $stats['active_fields'] ?? 0 }} Active
                </div>
            </div>

            <!-- Total Reservations -->
            <div class="stats-card scale-in" style="animation-delay: 0.2s;">
                <div class="stats-icon bg-gradient-to-br from-success-green to-green-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_reservations'] ?? 0 }}</div>
                <div class="stats-label">Total Reservations</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    {{ $stats['today_reservations'] ?? 0 }} Today
                </div>
            </div>

            <!-- Total Revenue -->
            <div class="stats-card scale-in" style="animation-delay: 0.3s;">
                <div class="stats-icon bg-gradient-to-br from-gold-yellow to-yellow-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value text-leaders-red">{{ number_format($stats['total_revenue'] ?? 0, 0) }}</div>
                <div class="stats-label">Total Revenue (AED)</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    {{ number_format($stats['monthly_revenue'] ?? 0, 0) }} This Month
                </div>
            </div>

            <!-- Base Rate -->
            <div class="stats-card scale-in" style="animation-delay: 0.4s;">
                <div class="stats-icon bg-gradient-to-br from-info-blue to-blue-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $venue->formatted_hourly_rate }}</div>
                <div class="stats-label">Base Hourly Rate</div>
                <div class="stats-change {{ $venue->vat_applicable ? 'positive' : 'neutral' }}">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="{{ $venue->vat_applicable ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728' }}">
                        </path>
                    </svg>
                    {{ $venue->vat_applicable ? $venue->vat_rate . '% VAT' : 'No VAT' }}
                </div>
            </div>
        </div>

        <!-- Venue Information & Contact Details -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Venue Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Venue Information</h3>
                        <p class="bank-card-subtitle">Contact details and location</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                    </path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black">Address</h4>
                                <p class="text-dark-gray">{{ $venue->localized_address }}</p>
                                @if ($venue->coordinates_text)
                                    <p class="text-sm text-medium-gray mt-1">GPS: {{ $venue->coordinates_text }}</p>
                                @endif
                            </div>
                        </div>

                        @if ($venue->phone)
                            <div class="flex items-start space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-success-green to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">Phone Number</h4>
                                    <p class="text-dark-gray">{{ $venue->formatted_phone }}</p>
                                    <a href="tel:{{ $venue->phone }}"
                                        class="text-sm text-leaders-red hover:text-leaders-deep-red">
                                        Call Now
                                    </a>
                                </div>
                            </div>
                        @endif

                        @if ($venue->email)
                            <div class="flex items-start space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-info-blue to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">Email Address</h4>
                                    <p class="text-dark-gray">{{ $venue->email }}</p>
                                    <a href="mailto:{{ $venue->email }}"
                                        class="text-sm text-leaders-red hover:text-leaders-deep-red">
                                        Send Email
                                    </a>
                                </div>
                            </div>
                        @endif

                        @if ($venue->manager_name)
                            <div class="flex items-start space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">Manager</h4>
                                    <p class="text-dark-gray">{{ $venue->manager_name }}</p>
                                    @if ($venue->manager_phone)
                                        <p class="text-sm text-medium-gray">{{ $venue->formatted_manager_phone }}</p>
                                    @endif
                                    @if ($venue->manager_email)
                                        <a href="mailto:{{ $venue->manager_email }}"
                                            class="text-sm text-leaders-red hover:text-leaders-deep-red">
                                            {{ $venue->manager_email }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <div class="flex items-start space-x-3">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black">Established</h4>
                                <p class="text-dark-gray">{{ $venue->created_at->format('F j, Y') }}</p>
                                <p class="text-sm text-medium-gray">{{ $venue->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Quick Actions</h3>
                        <p class="bank-card-subtitle">Manage venue operations</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 gap-3">
                        @can('update', $venue)
                            <a href="{{ route('venues.edit', $venue) }}" class="btn-bank w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                                Edit Venue Details
                            </a>
                        @endcan

                        <button @click="toggleVenueStatus()" class="btn-bank btn-bank-secondary w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="{{ $venue->status ? 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                                </path>
                            </svg>
                            {{ $venue->status ? 'Deactivate' : 'Activate' }} Venue
                        </button>

                        <a href="#" class="btn-bank btn-bank-outline w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add New Field
                        </a>

                        <a href="#" class="btn-bank btn-bank-outline w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>
                            View Reservations
                        </a>

                        <a href="#" class="btn-bank btn-bank-outline w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Fields and Recent Reservations -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Fields List -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Fields</h3>
                        <p class="bank-card-subtitle">{{ $venue->fields->count() }} fields in this venue</p>
                    </div>
                    <a href="#" class="btn-bank btn-bank-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Field
                    </a>
                </div>
                <div class="bank-card-body">
                    @forelse($venue->fields->take(5) as $field)
                        <div
                            class="flex items-center justify-between py-3 {{ !$loop->last ? 'border-b border-light-gray' : '' }}">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-success-green to-green-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">{{ $field->localized_name }}</h4>
                                    <p class="text-sm text-dark-gray">{{ $field->type_text ?? 'Sports Field' }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="badge-bank {{ $field->status_badge_class }}">
                                    {{ $field->status_text }}
                                </span>
                                <p class="text-sm text-dark-gray mt-1">{{ $field->formatted_hourly_rate }}</p>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-medium-gray mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2">
                                </path>
                            </svg>
                            <p class="text-dark-gray mb-4">No fields found in this venue.</p>
                            <a href="#" class="btn-bank btn-bank-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create First Field
                            </a>
                        </div>
                    @endforelse

                    @if ($venue->fields->count() > 5)
                        <div class="pt-4 border-t border-light-gray">
                            <a href="#" class="text-leaders-red hover:text-leaders-deep-red text-sm font-medium">
                                View All {{ $venue->fields->count() }} Fields →
                            </a>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Recent Reservations -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Recent Reservations</h3>
                        <p class="bank-card-subtitle">Latest bookings at this venue</p>
                    </div>
                    <a href="#" class="btn-bank btn-bank-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Booking
                    </a>
                </div>
                <div class="bank-card-body">
                    @forelse($recentReservations as $reservation)
                        <div
                            class="flex items-center justify-between py-3 {{ !$loop->last ? 'border-b border-light-gray' : '' }}">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">
                                        {{ $reservation->customer->name ?? 'N/A' }}</h4>
                                    <p class="text-sm text-dark-gray">{{ $reservation->field->localized_name ?? 'N/A' }}
                                    </p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span
                                    class="badge-bank {{ $reservation->status === 'confirmed' ? 'badge-success' : ($reservation->status === 'pending' ? 'badge-warning' : 'badge-neutral') }}">
                                    {{ ucfirst($reservation->status ?? 'Pending') }}
                                </span>
                                <p class="text-sm text-dark-gray mt-1">
                                    {{ $reservation->reservation_date ? \Carbon\Carbon::parse($reservation->reservation_date)->format('M d') : 'N/A' }}
                                </p>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-medium-gray mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>
                            <p class="text-dark-gray mb-4">No reservations found for this venue.</p>
                            <a href="#" class="btn-bank btn-bank-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create First Booking
                            </a>
                        </div>
                    @endforelse

                    @if ($venue->reservations->count() > 10)
                        <div class="pt-4 border-t border-light-gray">
                            <a href="#" class="text-leaders-red hover:text-leaders-deep-red text-sm font-medium">
                                View All Reservations →
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        @if ($venue->localized_description || $venue->facilities || $venue->operating_hours || $venue->localized_notes)
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Additional Information</h3>
                        <p class="bank-card-subtitle">Venue details and facilities</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        @if ($venue->localized_description)
                            <div>
                                <h4 class="font-semibold text-charcoal-black mb-2">Description</h4>
                                <p class="text-dark-gray">{{ $venue->localized_description }}</p>
                            </div>
                        @endif

                        @if ($venue->facilities && is_array($venue->facilities) && count($venue->facilities) > 0)
                            <div>
                                <h4 class="font-semibold text-charcoal-black mb-2">Facilities</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    @foreach ($venue->facilities as $key => $facility)
                                        <div class="flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-success-green" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            <span
                                                class="text-sm text-dark-gray">{{ is_string($facility) ? $facility : $key }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        @if ($venue->operating_hours && is_array($venue->operating_hours))
                            <div>
                                <h4 class="font-semibold text-charcoal-black mb-2">Operating Hours</h4>
                                <div class="space-y-1">
                                    @foreach ($venue->operating_hours as $day => $hours)
                                        @if (is_array($hours) && isset($hours['open']) && isset($hours['close']))
                                            <div class="flex justify-between text-sm">
                                                <span class="text-dark-gray capitalize">{{ $day }}:</span>
                                                <span class="text-charcoal-black">{{ $hours['open'] }} -
                                                    {{ $hours['close'] }}</span>
                                            </div>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                        @endif

                        @if ($venue->localized_notes)
                            <div class="lg:col-span-2">
                                <h4 class="font-semibold text-charcoal-black mb-2">Notes</h4>
                                <p class="text-dark-gray">{{ $venue->localized_notes }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @endif
    </div>
@endsection

@push('scripts')
    <script>
        function venueDetails() {
            return {
                init() {
                    // Initialize any venue details functionality
                },

                async toggleVenueStatus() {
                    const currentStatus = {{ $venue->status ? 'true' : 'false' }};
                    const newStatus = currentStatus ? 'deactivate' : 'activate';
                    const venueName = '{{ $venue->localized_name }}';

                    let confirmMessage = `Are you sure you want to ${newStatus} "${venueName}"?`;

                    @if ($venue->fields->count() > 0 || $venue->reservations->count() > 0)
                        if (currentStatus) {
                            confirmMessage +=
                                '\n\nThis venue has {{ $venue->fields->count() }} fields and {{ $venue->reservations->count() }} reservations. Deactivating will affect operations.';
                        }
                    @endif

                    if (!confirm(confirmMessage)) {
                        return;
                    }

                    try {
                        const response = await fetch('{{ route('venues.toggle-status', $venue) }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while updating the venue status.');
                    }
                }
            }
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            if (type === 'success') {
                alert('Success: ' + message);
            } else {
                alert('Error: ' + message);
            }
        }
    </script>
@endpush
