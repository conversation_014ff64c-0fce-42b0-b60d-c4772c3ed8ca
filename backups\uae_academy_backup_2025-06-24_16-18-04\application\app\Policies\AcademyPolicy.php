<?php

namespace App\Policies;

use App\Models\Academy;
use App\Models\User;

class AcademyPolicy
{
    /**
     * Determine whether the user can view any academies.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the academy.
     */
    public function view(User $user, Academy $academy): bool
    {
        switch ($user->role) {
            case 'admin':
            case 'branch_manager':
                return true;
            case 'academy_manager':
                // Academy managers can only view their assigned academy
                return $user->academy_id === $academy->id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can create academies.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can update the academy.
     */
    public function update(User $user, Academy $academy): bool
    {
        switch ($user->role) {
            case 'admin':
            case 'branch_manager':
                return true;
            case 'academy_manager':
                // Academy managers can only update their assigned academy
                return $user->academy_id === $academy->id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can delete the academy.
     */
    public function delete(User $user, Academy $academy): bool
    {
        // Only admin and branch managers can delete academies
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can restore the academy.
     */
    public function restore(User $user, Academy $academy): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can permanently delete the academy.
     */
    public function forceDelete(User $user, Academy $academy): bool
    {
        // Only admin can permanently delete
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can export academy data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }
}
