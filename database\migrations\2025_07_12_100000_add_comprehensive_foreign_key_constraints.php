<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add missing foreign key constraints with proper cascading rules

        // Students table constraints
        Schema::table('students', function (Blueprint $table) {
            // Check if foreign keys don't already exist
            if (!$this->foreignKeyExists('students', 'students_branch_id_foreign')) {
                $table->foreign('branch_id')->references('id')->on('branches')->onDelete('restrict');
            }
            if (!$this->foreignKeyExists('students', 'students_academy_id_foreign')) {
                $table->foreign('academy_id')->references('id')->on('academies')->onDelete('restrict');
            }
            if (!$this->foreignKeyExists('students', 'students_program_id_foreign')) {
                $table->foreign('program_id')->references('id')->on('programs')->onDelete('set null');
            }
        });

        // Payments table constraints (note: payments table doesn't have program_id)
        Schema::table('payments', function (Blueprint $table) {
            if (!$this->foreignKeyExists('payments', 'payments_student_id_foreign')) {
                $table->foreign('student_id')->references('id')->on('students')->onDelete('cascade');
            }
            if (!$this->foreignKeyExists('payments', 'payments_branch_id_foreign')) {
                $table->foreign('branch_id')->references('id')->on('branches')->onDelete('restrict');
            }
            if (!$this->foreignKeyExists('payments', 'payments_academy_id_foreign')) {
                $table->foreign('academy_id')->references('id')->on('academies')->onDelete('restrict');
            }
        });

        // Uniforms table constraints
        Schema::table('uniforms', function (Blueprint $table) {
            if (!$this->foreignKeyExists('uniforms', 'uniforms_student_id_foreign')) {
                $table->foreign('student_id')->references('id')->on('students')->onDelete('cascade');
            }
            if (!$this->foreignKeyExists('uniforms', 'uniforms_branch_id_foreign')) {
                $table->foreign('branch_id')->references('id')->on('branches')->onDelete('restrict');
            }
            if (!$this->foreignKeyExists('uniforms', 'uniforms_academy_id_foreign')) {
                $table->foreign('academy_id')->references('id')->on('academies')->onDelete('restrict');
            }
        });

        // Attendances table constraints
        Schema::table('attendances', function (Blueprint $table) {
            if (!$this->foreignKeyExists('attendances', 'attendances_student_id_foreign')) {
                $table->foreign('student_id')->references('id')->on('students')->onDelete('cascade');
            }
            if (!$this->foreignKeyExists('attendances', 'attendances_branch_id_foreign')) {
                $table->foreign('branch_id')->references('id')->on('branches')->onDelete('restrict');
            }
            if (!$this->foreignKeyExists('attendances', 'attendances_academy_id_foreign')) {
                $table->foreign('academy_id')->references('id')->on('academies')->onDelete('restrict');
            }
        });

        // Uniform inventory constraints (note: already has foreign keys defined in creation migration)
        // The uniform_inventory table already has proper foreign key constraints defined in its creation migration

        // Reservations table constraints
        if (Schema::hasTable('reservations')) {
            Schema::table('reservations', function (Blueprint $table) {
                if (!$this->foreignKeyExists('reservations', 'reservations_customer_id_foreign')) {
                    $table->foreign('customer_id')->references('id')->on('customers')->onDelete('cascade');
                }
                if (!$this->foreignKeyExists('reservations', 'reservations_venue_id_foreign')) {
                    $table->foreign('venue_id')->references('id')->on('venues')->onDelete('restrict');
                }
                if (!$this->foreignKeyExists('reservations', 'reservations_field_id_foreign')) {
                    $table->foreign('field_id')->references('id')->on('fields')->onDelete('restrict');
                }
            });
        }

        // Fields table constraints
        if (Schema::hasTable('fields')) {
            Schema::table('fields', function (Blueprint $table) {
                if (!$this->foreignKeyExists('fields', 'fields_venue_id_foreign')) {
                    $table->foreign('venue_id')->references('id')->on('venues')->onDelete('cascade');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraints in reverse order

        if (Schema::hasTable('fields')) {
            Schema::table('fields', function (Blueprint $table) {
                $table->dropForeign(['venue_id']);
            });
        }

        if (Schema::hasTable('reservations')) {
            Schema::table('reservations', function (Blueprint $table) {
                $table->dropForeign(['customer_id']);
                $table->dropForeign(['venue_id']);
                $table->dropForeign(['field_id']);
            });
        }

        // Uniform inventory foreign keys are managed by their creation migration

        Schema::table('attendances', function (Blueprint $table) {
            $table->dropForeign(['student_id']);
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['academy_id']);
        });

        Schema::table('uniforms', function (Blueprint $table) {
            $table->dropForeign(['student_id']);
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['academy_id']);
        });

        Schema::table('payments', function (Blueprint $table) {
            $table->dropForeign(['student_id']);
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['academy_id']);
        });

        Schema::table('students', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['academy_id']);
            $table->dropForeign(['program_id']);
        });
    }

    /**
     * Check if a foreign key constraint exists
     */
    private function foreignKeyExists(string $table, string $foreignKey): bool
    {
        try {
            $connection = Schema::getConnection();
            $database = $connection->getDatabaseName();

            $result = $connection->select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = ?
                AND TABLE_NAME = ?
                AND CONSTRAINT_NAME = ?
            ", [$database, $table, $foreignKey]);

            return count($result) > 0;
        } catch (\Exception $e) {
            // If we can't check, assume it doesn't exist to be safe
            return false;
        }
    }
};
