<?php

namespace App\Http\Controllers;

use App\Models\Uniform;
use App\Models\Student;
use App\Models\Branch;
use App\Models\Academy;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Validator;

class UniformController extends Controller
{
    /**
     * Display a listing of uniforms with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Uniform::class);

        $query = Uniform::with(['student', 'branch', 'academy'])
            ->withCount(['student']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('student', function ($sq) use ($search) {
                    $sq->where('full_name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                })
                    ->orWhere('size', 'like', "%{$search}%")
                    ->orWhere('item', 'like', "%{$search}%")
                    ->orWhere('reference_number', 'like', "%{$search}%")
                    ->orWhere('note', 'like', "%{$search}%");
            });
        }

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->get('branch_id'));
        }

        if ($request->filled('academy_id')) {
            $query->where('academy_id', $request->get('academy_id'));
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if (in_array($status, ['ordered', 'processing', 'ready', 'delivered', 'cancelled'])) {
                $query->where('status', $status);
            } elseif ($status === 'pending') {
                $query->where('branch_status', 'pending');
            }
        }

        if ($request->filled('size')) {
            $query->where('size', $request->get('size'));
        }

        if ($request->filled('item')) {
            $query->where('item', $request->get('item'));
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        if ($request->filled('date_from')) {
            $query->where('order_date', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('order_date', '<=', $request->get('date_to'));
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        $allowedSorts = ['created_at', 'order_date', 'delivery_date', 'amount', 'status', 'size'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortDirection);
        }

        // Role-based filtering
        $user = Auth::user();
        if ($user && $user->role === 'branch_manager' && $user->branch_id) {
            $query->where('branch_id', $user->branch_id);
        } elseif ($user && $user->role === 'academy_manager' && $user->academy_id) {
            $query->where('academy_id', $user->academy_id);
        }

        $uniforms = $query->paginate(15)->withQueryString();

        // Get filter options
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();
        $students = Student::active()->with(['branch', 'academy'])->orderBy('full_name')->get();

        // Calculate statistics
        $stats = [
            'total_uniforms' => Uniform::count(),
            'ordered_uniforms' => Uniform::where('status', 'ordered')->count(),
            'processing_uniforms' => Uniform::where('status', 'processing')->count(),
            'ready_uniforms' => Uniform::where('status', 'ready')->count(),
            'delivered_uniforms' => Uniform::where('status', 'delivered')->count(),
            'total_amount' => Uniform::sum('amount'),
            'pending_amount' => Uniform::where('status', '!=', 'delivered')->sum('amount'),
            'average_order' => Uniform::avg('amount'),
            'this_month_orders' => Uniform::whereMonth('order_date', now()->month)
                ->whereYear('order_date', now()->year)
                ->sum('amount'),
        ];

        if ($request->ajax()) {
            return response()->json([
                'uniforms' => $uniforms,
                'stats' => $stats
            ]);
        }

        return view('uniforms.index', compact('uniforms', 'branches', 'academies', 'students', 'stats'));
    }

    /**
     * Show the form for creating a new uniform order.
     */
    public function create(): View
    {
        Gate::authorize('create', Uniform::class);

        $students = Student::active()->with(['branch', 'academy'])->orderBy('full_name')->get();
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        return view('uniforms.create', compact('students', 'branches', 'academies'));
    }

    /**
     * Store a newly created uniform order in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Uniform::class);

        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'item' => 'required|string|max:100',
            'size' => 'required|string|max:20',
            'quantity' => 'required|integer|min:1|max:50',
            'amount' => 'required|numeric|min:0|max:999999.99',
            'payment_method' => 'required|in:cash,card,bank_transfer',
            'order_date' => 'required|date',
            'delivery_date' => 'nullable|date|after:order_date',
            'status' => 'required|in:ordered,processing,ready,delivered,cancelled',
            'branch_status' => 'required|in:pending,received,delivered',
            'office_status' => 'required|in:pending,received,delivered',
            'reference_number' => 'nullable|string|max:50|unique:uniforms,reference_number',
            'description' => 'nullable|string|max:1000',
            'note' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = $validator->validated();

            // Generate reference number if not provided
            if (empty($data['reference_number'])) {
                $data['reference_number'] = $this->generateReferenceNumber();
            }

            // Set currency to AED
            $data['currency'] = 'AED';

            $uniform = Uniform::create($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Uniform order created successfully',
                    'uniform' => $uniform->load(['student', 'branch', 'academy'])
                ]);
            }

            return redirect()->route('uniforms.index')
                ->with('success', 'Uniform order created successfully');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create uniform order: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to create uniform order: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified uniform order with detailed information.
     */
    public function show(Uniform $uniform): View|JsonResponse
    {
        Gate::authorize('view', $uniform);

        $uniform->load(['student', 'branch', 'academy']);

        if (request()->ajax()) {
            return response()->json([
                'uniform' => $uniform
            ]);
        }

        return view('uniforms.show', compact('uniform'));
    }

    /**
     * Show the form for editing the specified uniform order.
     */
    public function edit(Uniform $uniform): View
    {
        Gate::authorize('update', $uniform);

        $students = Student::active()->with(['branch', 'academy'])->orderBy('full_name')->get();
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        return view('uniforms.edit', compact('uniform', 'students', 'branches', 'academies'));
    }

    /**
     * Update the specified uniform order in storage.
     */
    public function update(Request $request, Uniform $uniform): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $uniform);

        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'item' => 'required|string|max:100',
            'size' => 'required|string|max:20',
            'quantity' => 'required|integer|min:1|max:50',
            'amount' => 'required|numeric|min:0|max:999999.99',
            'payment_method' => 'required|in:cash,card,bank_transfer',
            'order_date' => 'required|date',
            'delivery_date' => 'nullable|date|after:order_date',
            'status' => 'required|in:ordered,processing,ready,delivered,cancelled',
            'branch_status' => 'required|in:pending,received,delivered',
            'office_status' => 'required|in:pending,received,delivered',
            'reference_number' => 'nullable|string|max:50|unique:uniforms,reference_number,' . $uniform->id,
            'description' => 'nullable|string|max:1000',
            'note' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        try {
            $data = $validator->validated();

            // Keep currency as AED
            $data['currency'] = 'AED';

            $uniform->update($data);

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Uniform order updated successfully',
                    'uniform' => $uniform->load(['student', 'branch', 'academy'])
                ]);
            }

            return redirect()->route('uniforms.show', $uniform)
                ->with('success', 'Uniform order updated successfully');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update uniform order: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to update uniform order: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified uniform order from storage.
     */
    public function destroy(Uniform $uniform): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $uniform);

        try {
            $uniform->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Uniform order deleted successfully'
                ]);
            }

            return redirect()->route('uniforms.index')
                ->with('success', 'Uniform order deleted successfully');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete uniform order: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to delete uniform order: ' . $e->getMessage());
        }
    }

    /**
     * Toggle uniform order status.
     */
    public function toggleStatus(Uniform $uniform): JsonResponse
    {
        Gate::authorize('update', $uniform);

        try {
            $newStatus = match ($uniform->status) {
                'ordered' => 'processing',
                'processing' => 'ready',
                'ready' => 'delivered',
                'delivered' => 'delivered', // Keep as delivered
                'cancelled' => 'ordered',
                default => 'ordered'
            };

            $uniform->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => 'Status updated successfully',
                'uniform' => $uniform->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle bulk actions on uniform orders.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:update_status,mark_delivered,cancel,delete',
            'uniform_ids' => 'required|array|min:1',
            'uniform_ids.*' => 'exists:uniforms,id',
            'status' => 'required_if:action,update_status|in:ordered,processing,ready,delivered,cancelled'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $uniformIds = $request->get('uniform_ids');
            $action = $request->get('action');
            $count = 0;

            foreach ($uniformIds as $uniformId) {
                $uniform = Uniform::find($uniformId);
                if (!$uniform || !Gate::allows('update', $uniform)) {
                    continue;
                }

                switch ($action) {
                    case 'update_status':
                        $uniform->update(['status' => $request->get('status')]);
                        $count++;
                        break;
                    case 'mark_delivered':
                        $uniform->update([
                            'status' => 'delivered',
                            'branch_status' => 'delivered',
                            'office_status' => 'delivered',
                            'delivery_date' => now()
                        ]);
                        $count++;
                        break;
                    case 'cancel':
                        $uniform->update(['status' => 'cancelled']);
                        $count++;
                        break;
                    case 'delete':
                        if (Gate::allows('delete', $uniform)) {
                            $uniform->delete();
                            $count++;
                        }
                        break;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully processed {$count} uniform orders"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process bulk action: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export uniforms to Excel.
     */
    public function exportExcel(Request $request)
    {
        Gate::authorize('export', Uniform::class);

        // Apply same filters as index method
        $query = Uniform::with(['student', 'branch', 'academy']);

        // Apply filters (same logic as index method)
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('student', function ($sq) use ($search) {
                    $sq->where('full_name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                })
                    ->orWhere('size', 'like', "%{$search}%")
                    ->orWhere('item', 'like', "%{$search}%")
                    ->orWhere('reference_number', 'like', "%{$search}%");
            });
        }

        // Apply other filters...
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->get('branch_id'));
        }
        if ($request->filled('academy_id')) {
            $query->where('academy_id', $request->get('academy_id'));
        }
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        $uniforms = $query->get();

        $filename = 'uniforms_export_' . date('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($uniforms) {
            $file = fopen('php://output', 'w');

            // CSV Headers
            fputcsv($file, [
                'Order ID',
                'Reference Number',
                'Student Name',
                'Student Email',
                'Student Phone',
                'Branch',
                'Academy',
                'Item',
                'Size',
                'Quantity',
                'Unit Price (AED)',
                'Total Amount (AED)',
                'Status',
                'Branch Status',
                'Office Status',
                'Payment Method',
                'Order Date',
                'Delivery Date',
                'Created At'
            ]);

            // Data rows
            foreach ($uniforms as $uniform) {
                fputcsv($file, [
                    str_pad($uniform->id, 4, '0', STR_PAD_LEFT),
                    $uniform->reference_number,
                    $uniform->student->full_name,
                    $uniform->student->email,
                    $uniform->student->phone,
                    $uniform->branch->name,
                    $uniform->academy->name,
                    ucfirst($uniform->item),
                    $uniform->size,
                    $uniform->quantity,
                    number_format($uniform->amount, 2),
                    number_format($uniform->total_amount, 2),
                    $uniform->status_text,
                    $uniform->branch_status_text,
                    $uniform->office_status_text,
                    $uniform->method_text,
                    $uniform->order_date->format('Y-m-d'),
                    $uniform->delivery_date ? $uniform->delivery_date->format('Y-m-d') : '',
                    $uniform->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export uniforms to PDF.
     */
    public function exportPdf()
    {
        Gate::authorize('export', Uniform::class);

        // For now, return a simple response
        // In a real implementation, you would use a PDF library like DomPDF or TCPDF
        return response()->json([
            'message' => 'PDF export functionality will be implemented with a PDF library',
            'note' => 'Use Excel export for now'
        ]);
    }

    /**
     * API endpoint for uniforms (AJAX requests).
     */
    public function apiIndex(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', Uniform::class);

        $query = Uniform::with(['student', 'branch', 'academy']);

        // Apply filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('student', function ($sq) use ($search) {
                    $sq->where('full_name', 'like', "%{$search}%");
                })
                    ->orWhere('item', 'like', "%{$search}%")
                    ->orWhere('size', 'like', "%{$search}%");
            });
        }

        $uniforms = $query->paginate(15);

        return response()->json([
            'uniforms' => $uniforms,
            'success' => true
        ]);
    }

    /**
     * Get uniform statistics for API.
     */
    public function getStatistics(): JsonResponse
    {
        Gate::authorize('viewStatistics', Uniform::class);

        $stats = [
            'total_uniforms' => Uniform::count(),
            'ordered_uniforms' => Uniform::where('status', 'ordered')->count(),
            'processing_uniforms' => Uniform::where('status', 'processing')->count(),
            'ready_uniforms' => Uniform::where('status', 'ready')->count(),
            'delivered_uniforms' => Uniform::where('status', 'delivered')->count(),
            'total_amount' => Uniform::sum('amount'),
            'pending_amount' => Uniform::where('status', '!=', 'delivered')->sum('amount'),
            'average_order' => Uniform::avg('amount'),
            'this_month_orders' => Uniform::whereMonth('order_date', now()->month)
                ->whereYear('order_date', now()->year)
                ->sum('amount'),
        ];

        return response()->json([
            'stats' => $stats,
            'success' => true
        ]);
    }

    /**
     * Generate a unique reference number for uniform orders.
     */
    private function generateReferenceNumber(): string
    {
        do {
            $referenceNumber = 'UNI-' . date('Ymd') . '-' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
        } while (Uniform::where('reference_number', $referenceNumber)->exists());

        return $referenceNumber;
    }
}
