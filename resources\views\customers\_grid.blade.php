<!-- Grid View -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
    @forelse($customers as $customer)
        <div class="bank-card bank-card-hover">
            <div class="bank-card-body">
                <!-- Customer Header -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        @can('bulkAction', App\Models\Customer::class)
                            <input type="checkbox" name="customer_ids[]" value="{{ $customer->id }}"
                                @change="toggleCustomerSelection({{ $customer->id }})"
                                :checked="selectedCustomers.includes({{ $customer->id }})" 
                                class="form-checkbox-bank mr-3">
                        @endcan
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-semibold">
                            {{ substr($customer->full_name, 0, 1) }}
                        </div>
                    </div>
                    <div class="flex items-center space-x-1">
                        <span class="badge-bank {{ $customer->status === 'active' ? 'badge-success' : ($customer->status === 'blocked' ? 'badge-danger' : 'badge-secondary') }}">
                            {{ ucfirst($customer->status) }}
                        </span>
                        @if ($customer->vip_status)
                            <span class="badge-bank badge-warning">VIP</span>
                        @endif
                    </div>
                </div>

                <!-- Customer Info -->
                <div class="mb-4">
                    <h3 class="text-lg font-semibold text-charcoal-black mb-1">
                        <a href="{{ route('customers.show', $customer) }}"
                            class="hover:text-leaders-red transition-colors">
                            {{ $customer->full_name }}
                        </a>
                    </h3>
                    <p class="text-sm text-dark-gray mb-1">ID: {{ $customer->customer_number }}</p>
                    @if ($customer->full_name_ar)
                        <p class="text-sm text-gray-500">{{ $customer->full_name_ar }}</p>
                    @endif
                </div>

                <!-- Customer Type -->
                <div class="mb-4">
                    <span class="badge-bank {{ $customer->customer_type === 'individual' ? 'badge-primary' : 'badge-warning' }}">
                        {{ ucfirst($customer->customer_type) }}
                    </span>
                    @if ($customer->customer_type === 'corporate' && $customer->company_name)
                        <p class="text-sm text-gray-600 mt-1">{{ $customer->company_name }}</p>
                    @endif
                </div>

                <!-- Contact Info -->
                <div class="space-y-2 mb-4">
                    @if ($customer->email)
                        <div class="flex items-center text-sm text-dark-gray">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                </path>
                            </svg>
                            <span class="truncate">{{ $customer->email }}</span>
                        </div>
                    @endif
                    <div class="flex items-center text-sm text-dark-gray">
                        <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                            </path>
                        </svg>
                        <span>{{ $customer->phone }}</span>
                    </div>
                    @if ($customer->nationality)
                        <div class="flex items-center text-sm text-dark-gray">
                            <svg class="w-4 h-4 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                                </path>
                            </svg>
                            <span>{{ $customer->nationality }}</span>
                        </div>
                    @endif
                </div>

                <!-- Statistics -->
                <div class="grid grid-cols-2 gap-4 mb-4 p-3 bg-off-white rounded-lg">
                    <div class="text-center">
                        <p class="text-lg font-semibold text-charcoal-black">{{ $customer->reservations_count }}</p>
                        <p class="text-xs text-dark-gray">Reservations</p>
                    </div>
                    <div class="text-center">
                        <p class="text-lg font-semibold text-success-green">AED {{ number_format($customer->total_spent, 0) }}</p>
                        <p class="text-xs text-dark-gray">Total Spent</p>
                    </div>
                </div>

                <!-- Registration Info -->
                <div class="text-xs text-gray-500 mb-4">
                    <p>Joined: {{ $customer->registration_date->format('M d, Y') }}</p>
                    @if ($customer->last_booking_date)
                        <p>Last Booking: {{ $customer->last_booking_date->format('M d, Y') }}</p>
                    @endif
                    @if ($customer->preferred_language === 'ar')
                        <p>Arabic Speaker</p>
                    @endif
                </div>

                <!-- Actions -->
                <div class="flex items-center justify-between pt-4 border-t border-light-gray">
                    <div class="flex items-center space-x-2">
                        @can('view', $customer)
                            <a href="{{ route('customers.show', $customer) }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white px-3 py-1 text-xs" title="View Details">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                            </a>
                        @endcan

                        @can('update', $customer)
                            <a href="{{ route('customers.edit', $customer) }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white px-3 py-1 text-xs" title="Edit Customer">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                            </a>
                        @endcan
                    </div>

                    <div class="flex items-center space-x-2">
                        @can('update', $customer)
                            <button @click="toggleStatus({{ $customer->id }})"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white px-3 py-1 text-xs" title="Toggle Status">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                </svg>
                            </button>
                        @endcan

                        @can('delete', $customer)
                            <button @click="deleteCustomer({{ $customer->id }})"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white px-3 py-1 text-xs" title="Delete Customer">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                    </path>
                                </svg>
                            </button>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-span-full">
            <div class="bank-card">
                <div class="bank-card-body text-center py-12">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor"
                        viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                        </path>
                    </svg>
                    <h3 class="text-xl font-medium text-dark-gray mb-2">No customers found</h3>
                    <p class="text-gray-500 mb-6">No customers match your current filters.</p>
                    @can('create', App\Models\Customer::class)
                        <a href="{{ route('customers.create') }}" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add First Customer
                        </a>
                    @endcan
                </div>
            </div>
        </div>
    @endforelse
</div>
