<!-- Table View -->
<div class="overflow-x-auto">
    <table class="table-bank">
        <thead>
            <tr>
                @can('bulkAction', App\Models\User::class)
                    <th class="w-12">
                        <input type="checkbox" @change="selectAllUsers()"
                            :checked="selectedUsers.length === {{ $users->count() }} && {{ $users->count() }} > 0"
                            class="form-checkbox-bank">
                    </th>
                @endcan
                <th>User</th>
                <th>Role</th>
                <th>Branch & Academy</th>
                <th>Status</th>
                <th>Last Login</th>
                <th>Created</th>
                <th class="actions-column">Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($users as $user)
                <tr class="table-row-hover">
                    @can('bulkAction', App\Models\User::class)
                        <td>
                            <input type="checkbox" name="user_ids[]" value="{{ $user->id }}"
                                @change="toggleUserSelection({{ $user->id }})"
                                :checked="selectedUsers.includes({{ $user->id }})" class="form-checkbox-bank">
                        </td>
                    @endcan
                    <td>
                        <div class="flex items-center space-x-3">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">
                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                </span>
                            </div>
                            <div>
                                <a href="{{ route('users.show', $user) }}"
                                    class="font-semibold text-red-600 hover:text-red-800 underline transition-colors duration-200">{{ $user->name }}</a>
                                <p class="text-sm text-dark-gray">{{ $user->email }}</p>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge-bank {{ $user->role_badge_class }}">
                            {{ $user->role_text }}
                        </span>
                    </td>
                    <td>
                        <div class="space-y-1">
                            @if ($user->branch)
                                <p class="text-sm font-medium text-charcoal-black">
                                    <svg class="w-4 h-4 inline text-dark-gray" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                        </path>
                                    </svg>
                                    <a href="{{ route('branches.show', $user->branch) }}"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200">{{ $user->branch->name }}</a>
                                </p>
                            @endif
                            @if ($user->academy)
                                <p class="text-sm text-dark-gray">
                                    <svg class="w-4 h-4 inline text-dark-gray" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                        </path>
                                    </svg>
                                    <a href="{{ route('academies.show', $user->academy) }}"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200">{{ $user->academy->name }}</a>
                                </p>
                            @endif
                            @if (!$user->branch && !$user->academy)
                                <p class="text-sm text-medium-gray">No assignment</p>
                            @endif
                        </div>
                    </td>
                    <td>
                        <span class="badge-bank {{ $user->status_badge_class }}">
                            {{ $user->status_text }}
                        </span>
                    </td>
                    <td>
                        <p class="text-sm text-dark-gray">{{ $user->last_login_text }}</p>
                    </td>
                    <td>
                        <p class="text-sm text-dark-gray">{{ $user->formatted_created_at }}</p>
                    </td>
                    <td>
                        <div class="btn-action-group">
                            @can('view', $user)
                                <a href="{{ route('users.show', $user) }}" class="btn-action btn-action-view"
                                    title="View User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @can('update', $user)
                                <a href="{{ route('users.edit', $user) }}" class="btn-action btn-action-edit"
                                    title="Edit User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @can('toggleStatus', $user)
                                <button onclick="toggleUserStatus({{ $user->id }})"
                                    class="btn-action {{ $user->status ? 'btn-action-warning' : 'btn-action-success' }}"
                                    title="{{ $user->status ? 'Deactivate' : 'Activate' }} User">
                                    @if ($user->status)
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728">
                                            </path>
                                        </svg>
                                    @else
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    @endif
                                </button>
                            @endcan

                            @can('delete', $user)
                                <button onclick="deleteUser({{ $user->id }})" class="btn-action btn-action-delete"
                                    title="Delete User">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="8" class="text-center py-12">
                        <div class="flex flex-col items-center space-y-4">
                            <svg class="w-16 h-16 text-medium-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                </path>
                            </svg>
                            <div class="text-center">
                                <h3 class="text-lg font-semibold text-charcoal-black">No users found</h3>
                                <p class="text-dark-gray">Try adjusting your search criteria or create a new user.</p>
                            </div>
                            @can('create', App\Models\User::class)
                                <a href="{{ route('users.create') }}" class="btn-bank">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                                        </path>
                                    </svg>
                                    Add New User
                                </a>
                            @endcan
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@push('scripts')
    <script>
        async function toggleUserStatus(userId) {
            try {
                const response = await fetch(`/users/${userId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('success', result.message);
                    window.location.reload();
                } else {
                    showNotification('error', result.message);
                }
            } catch (error) {
                showNotification('error', 'An error occurred while updating user status.');
            }
        }

        async function deleteUser(userId) {
            if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/users/${userId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                if (response.ok) {
                    showNotification('success', 'User deleted successfully.');
                    window.location.reload();
                } else {
                    showNotification('error', 'Failed to delete user.');
                }
            } catch (error) {
                showNotification('error', 'An error occurred while deleting the user.');
            }
        }
    </script>
@endpush
