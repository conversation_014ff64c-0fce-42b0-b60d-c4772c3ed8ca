<?php

// UAE English Sports Academy - Translation Fix Verification
// This script tests if the translation system is working properly

echo "🔧 Translation Fix Verification\n";
echo "=" . str_repeat("=", 40) . "\n\n";

// Test 1: Check if middleware exists
echo "📋 Test 1: Middleware Check\n";
$middlewareFile = 'app/Http/Middleware/ShareTranslations.php';
if (file_exists($middlewareFile)) {
    echo "  ✅ ShareTranslations middleware exists\n";
    
    // Check if it's properly registered
    $bootstrapFile = 'bootstrap/app.php';
    $bootstrapContent = file_get_contents($bootstrapFile);
    
    if (strpos($bootstrapContent, 'ShareTranslations') !== false) {
        echo "  ✅ Middleware is registered in bootstrap/app.php\n";
    } else {
        echo "  ❌ Middleware is NOT registered in bootstrap/app.php\n";
    }
} else {
    echo "  ❌ ShareTranslations middleware does not exist\n";
}
echo "\n";

// Test 2: Check translation files
echo "📋 Test 2: Translation Files Check\n";
$translationFiles = [
    'resources/lang/en/common.php',
    'resources/lang/en/dashboard.php',
    'resources/lang/ar/common.php',
    'resources/lang/ar/dashboard.php'
];

foreach ($translationFiles as $file) {
    if (file_exists($file)) {
        $translations = include $file;
        $count = is_array($translations) ? count($translations) : 0;
        echo "  ✅ {$file}: {$count} keys\n";
    } else {
        echo "  ❌ {$file}: Missing\n";
    }
}
echo "\n";

// Test 3: Check specific dashboard keys
echo "📋 Test 3: Dashboard Translation Keys\n";
$enDashboard = include 'resources/lang/en/dashboard.php';
$arDashboard = include 'resources/lang/ar/dashboard.php';

$testKeys = [
    'active_growing',
    'excellence_sports', 
    'future_champions',
    'welcome_back',
    'view_all',
    'Quick Actions'
];

foreach ($testKeys as $key) {
    $enExists = isset($enDashboard[$key]);
    $arExists = isset($arDashboard[$key]);
    
    $status = ($enExists && $arExists) ? "✅" : "❌";
    echo "  {$status} {$key}: EN(" . ($enExists ? "✓" : "✗") . ") AR(" . ($arExists ? "✓" : "✗") . ")\n";
}
echo "\n";

// Test 4: Check auto-translation JavaScript
echo "📋 Test 4: Auto-Translation JavaScript\n";
$jsFile = 'resources/js/auto-translation.js';
if (file_exists($jsFile)) {
    echo "  ✅ auto-translation.js exists\n";
    
    $jsContent = file_get_contents($jsFile);
    
    // Check for key mappings
    if (strpos($jsContent, 'active_growing') !== false) {
        echo "  ✅ Contains dashboard key mappings\n";
    } else {
        echo "  ❌ Missing dashboard key mappings\n";
    }
    
    // Check for AutoTranslation class
    if (strpos($jsContent, 'class AutoTranslation') !== false) {
        echo "  ✅ AutoTranslation class defined\n";
    } else {
        echo "  ❌ AutoTranslation class missing\n";
    }
} else {
    echo "  ❌ auto-translation.js does not exist\n";
}
echo "\n";

// Test 5: Check if assets are compiled
echo "📋 Test 5: Compiled Assets Check\n";
$manifestFile = 'public/build/manifest.json';
if (file_exists($manifestFile)) {
    echo "  ✅ Build manifest exists\n";
    
    $manifest = json_decode(file_get_contents($manifestFile), true);
    if (isset($manifest['resources/js/app.js'])) {
        echo "  ✅ JavaScript assets compiled\n";
    } else {
        echo "  ❌ JavaScript assets not found in manifest\n";
    }
    
    if (isset($manifest['resources/css/app.css'])) {
        echo "  ✅ CSS assets compiled\n";
    } else {
        echo "  ❌ CSS assets not found in manifest\n";
    }
} else {
    echo "  ❌ Build manifest does not exist - run 'npm run build'\n";
}
echo "\n";

// Test 6: Check dashboard layout for translation injection
echo "📋 Test 6: Dashboard Layout Translation Injection\n";
$layoutFile = 'resources/views/layouts/dashboard.blade.php';
if (file_exists($layoutFile)) {
    $layoutContent = file_get_contents($layoutFile);
    
    if (strpos($layoutContent, 'window.commonTranslations') !== false) {
        echo "  ✅ commonTranslations injection found\n";
    } else {
        echo "  ❌ commonTranslations injection missing\n";
    }
    
    if (strpos($layoutContent, 'window.dashboardTranslations') !== false) {
        echo "  ✅ dashboardTranslations injection found\n";
    } else {
        echo "  ❌ dashboardTranslations injection missing\n";
    }
    
    if (strpos($layoutContent, '$commonTranslations') !== false) {
        echo "  ✅ Variable references found\n";
    } else {
        echo "  ❌ Variable references missing\n";
    }
} else {
    echo "  ❌ Dashboard layout file does not exist\n";
}
echo "\n";

// Summary
echo "🎯 Summary:\n";
echo "  The translation system should now be working properly.\n";
echo "  If you're still seeing raw translation keys like 'dashboard.active_growing',\n";
echo "  try the following:\n\n";
echo "  1. Clear browser cache and hard refresh (Ctrl+F5)\n";
echo "  2. Check browser console for JavaScript errors\n";
echo "  3. Verify the page is loading the compiled assets\n";
echo "  4. Test language switching functionality\n\n";

echo "🔧 Quick Fix Commands:\n";
echo "  php artisan config:clear\n";
echo "  php artisan view:clear\n";
echo "  npm run build\n";
echo "  # Then refresh browser with Ctrl+F5\n\n";

echo str_repeat("=", 50) . "\n";
echo "Translation fix verification completed!\n";
