<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class UniformCategory extends Model
{
    protected $fillable = [
        'name',
        'name_ar',
        'code',
        'description',
        'description_ar',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'localized_name',
        'localized_description',
        'inventory_count',
        'total_stock',
        'low_stock_count',
    ];

    /**
     * Get the inventory items for this category.
     */
    public function inventoryItems(): HasMany
    {
        return $this->hasMany(UniformInventory::class);
    }

    /**
     * Get active inventory items for this category.
     */
    public function activeInventoryItems(): HasMany
    {
        return $this->inventoryItems()->where('status', 'active');
    }

    // Computed Properties

    /**
     * Get localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->name_ar) ? $this->name_ar : $this->name;
    }

    /**
     * Get localized description based on current locale.
     */
    public function getLocalizedDescriptionAttribute(): ?string
    {
        $locale = app()->getLocale();
        return $locale === 'ar' && !empty($this->description_ar) ? $this->description_ar : $this->description;
    }

    /**
     * Get count of inventory items in this category.
     */
    public function getInventoryCountAttribute(): int
    {
        return $this->inventoryItems()->count();
    }

    /**
     * Get total stock across all items in this category.
     */
    public function getTotalStockAttribute(): int
    {
        return $this->inventoryItems()->sum('current_stock');
    }

    /**
     * Get count of items with low stock in this category.
     */
    public function getLowStockCountAttribute(): int
    {
        return $this->inventoryItems()
            ->whereColumn('current_stock', '<=', 'minimum_stock')
            ->count();
    }

    // Query Scopes

    /**
     * Scope to get only active categories.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get categories ordered by sort order.
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope to search categories by name or code.
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('name_ar', 'like', "%{$search}%")
              ->orWhere('code', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    // Utility Methods

    /**
     * Get available uniform items for this category.
     */
    public static function getAvailableItems(): array
    {
        return [
            'jersey' => 'Jersey',
            'shorts' => 'Shorts',
            'socks' => 'Socks',
            'tracksuit' => 'Tracksuit',
            'jacket' => 'Jacket',
            'cap' => 'Cap',
            'bag' => 'Sports Bag',
            'complete_set' => 'Complete Set',
            'training_shirt' => 'Training Shirt',
            'polo_shirt' => 'Polo Shirt',
            'hoodie' => 'Hoodie',
            'sweatpants' => 'Sweatpants',
            'gloves' => 'Gloves',
            'shin_guards' => 'Shin Guards',
            'water_bottle' => 'Water Bottle',
        ];
    }

    /**
     * Generate next category code.
     */
    public static function generateCode(string $name): string
    {
        $baseCode = strtoupper(substr($name, 0, 3));
        $counter = 1;
        
        do {
            $code = $baseCode . str_pad($counter, 3, '0', STR_PAD_LEFT);
            $counter++;
        } while (self::where('code', $code)->exists());
        
        return $code;
    }

    /**
     * Get category statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_items' => $this->inventoryItems()->count(),
            'active_items' => $this->activeInventoryItems()->count(),
            'total_stock' => $this->inventoryItems()->sum('current_stock'),
            'total_value' => $this->inventoryItems()->sum(\DB::raw('current_stock * cost_price')),
            'low_stock_items' => $this->inventoryItems()
                ->whereColumn('current_stock', '<=', 'minimum_stock')
                ->count(),
            'out_of_stock_items' => $this->inventoryItems()
                ->where('current_stock', 0)
                ->count(),
        ];
    }
}
