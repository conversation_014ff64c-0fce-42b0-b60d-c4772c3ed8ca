<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Make phone field nullable and increase length to match validation rules
            $table->string('phone', 20)->nullable()->change();

            // Fix preferred_contact_method enum to include 'sms'
            $table->enum('preferred_contact_method', ['phone', 'email', 'sms', 'whatsapp'])->default('phone')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customers', function (Blueprint $table) {
            // Revert phone field to not nullable and original length
            $table->string('phone', 15)->nullable(false)->change();

            // Revert preferred_contact_method enum
            $table->enum('preferred_contact_method', ['phone', 'email', 'whatsapp'])->default('phone')->change();

            // Revert payment_terms enum
            $table->enum('payment_terms', ['cash', 'credit_7', 'credit_15', 'credit_30'])->default('cash')->change();
        });
    }
};
