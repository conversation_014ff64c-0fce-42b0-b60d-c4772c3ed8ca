<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <!-- Total Users -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Total Users</p>
                    <p class="text-3xl font-bold text-charcoal-black">{{ number_format($stats['total_users']) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                        All system users
                    </p>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Users -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Active Users</p>
                    <p class="text-3xl font-bold text-success-green">{{ number_format($stats['active_users']) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ $stats['total_users'] > 0 ? round(($stats['active_users'] / $stats['total_users']) * 100, 1) : 0 }}% of total
                    </p>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-success-green to-green-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Inactive Users -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Inactive Users</p>
                    <p class="text-3xl font-bold text-warning-orange">{{ number_format($stats['inactive_users']) }}</p>
                    <p class="text-sm text-warning-orange">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                            </path>
                        </svg>
                        {{ $stats['total_users'] > 0 ? round(($stats['inactive_users'] / $stats['total_users']) * 100, 1) : 0 }}% of total
                    </p>
                </div>
                <div class="w-16 h-16 bg-gradient-to-br from-warning-orange to-orange-600 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Role Distribution Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Admin Users -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">System Administrators</p>
                    <p class="text-2xl font-bold text-leaders-red">{{ number_format($stats['admin_users']) }}</p>
                    <span class="badge-bank badge-danger">Admin</span>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Branch Managers -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Branch Managers</p>
                    <p class="text-2xl font-bold text-warning-orange">{{ number_format($stats['branch_managers']) }}</p>
                    <span class="badge-bank badge-warning">Branch Manager</span>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-warning-orange to-orange-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Academy Managers -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Academy Managers</p>
                    <p class="text-2xl font-bold text-info-blue">{{ number_format($stats['academy_managers']) }}</p>
                    <span class="badge-bank badge-info">Academy Manager</span>
                </div>
                <div class="w-12 h-12 bg-gradient-to-br from-info-blue to-blue-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>
