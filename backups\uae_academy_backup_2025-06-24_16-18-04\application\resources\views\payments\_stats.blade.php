<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Payments -->
    <div class="bank-card stats-card-blue">
        <div class="flex items-center justify-between">
            <div>
                <p class="stats-card-label">Total Payments</p>
                <p class="stats-card-number">{{ number_format($stats['total_payments']) }}</p>
                <p class="stats-card-subtitle">All time transactions</p>
            </div>
            <div class="stats-card-icon">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z">
                    </path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Completed Payments -->
    <div class="bank-card stats-card-green">
        <div class="flex items-center justify-between">
            <div>
                <p class="stats-card-label">Completed</p>
                <p class="stats-card-number">{{ number_format($stats['completed_payments']) }}</p>
                <p class="stats-card-subtitle">
                    {{ $stats['total_payments'] > 0 ? round(($stats['completed_payments'] / $stats['total_payments']) * 100, 1) : 0 }}%
                    of total
                </p>
            </div>
            <div class="stats-card-icon">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Pending Payments -->
    <div class="bank-card stats-card-yellow">
        <div class="flex items-center justify-between">
            <div>
                <p class="stats-card-label">Pending</p>
                <p class="stats-card-number">{{ number_format($stats['pending_payments']) }}</p>
                <p class="stats-card-subtitle">
                    {{ $stats['total_payments'] > 0 ? round(($stats['pending_payments'] / $stats['total_payments']) * 100, 1) : 0 }}%
                    of total
                </p>
            </div>
            <div class="stats-card-icon">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Failed Payments -->
    <div class="bank-card stats-card-red">
        <div class="flex items-center justify-between">
            <div>
                <p class="stats-card-label">Failed</p>
                <p class="stats-card-number">{{ number_format($stats['failed_payments']) }}</p>
                <p class="stats-card-subtitle">
                    {{ $stats['total_payments'] > 0 ? round(($stats['failed_payments'] / $stats['total_payments']) * 100, 1) : 0 }}%
                    of total
                </p>
            </div>
            <div class="stats-card-icon">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z">
                    </path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Financial Summary Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Revenue -->
    <div class="bank-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-dark-gray text-sm font-medium">Total Revenue</p>
                <p class="text-2xl font-bold text-charcoal-black">
                    AED {{ number_format($stats['total_amount'], 2) }}
                </p>
                <p class="text-green-600 text-xs mt-1 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    Completed payments
                </p>
            </div>
            <div class="p-3 bg-green-100 rounded-lg">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                    </path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Pending Amount -->
    <div class="bank-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-dark-gray text-sm font-medium">Pending Amount</p>
                <p class="text-2xl font-bold text-charcoal-black">
                    AED {{ number_format($stats['pending_amount'], 2) }}
                </p>
                <p class="text-yellow-600 text-xs mt-1 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Awaiting collection
                </p>
            </div>
            <div class="p-3 bg-yellow-100 rounded-lg">
                <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Average Payment -->
    <div class="bank-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-dark-gray text-sm font-medium">Average Payment</p>
                <p class="text-2xl font-bold text-charcoal-black">
                    AED {{ number_format($stats['average_payment'] ?? 0, 2) }}
                </p>
                <p class="text-blue-600 text-xs mt-1 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                        </path>
                    </svg>
                    Per transaction
                </p>
            </div>
            <div class="p-3 bg-blue-100 rounded-lg">
                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                    </path>
                </svg>
            </div>
        </div>
    </div>

    <!-- This Month Revenue -->
    <div class="bank-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-dark-gray text-sm font-medium">This Month</p>
                <p class="text-2xl font-bold text-charcoal-black">
                    AED {{ number_format($stats['this_month_payments'], 2) }}
                </p>
                <p class="text-purple-600 text-xs mt-1 flex items-center">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                        </path>
                    </svg>
                    {{ now()->format('F Y') }}
                </p>
            </div>
            <div class="p-3 bg-purple-100 rounded-lg">
                <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                    </path>
                </svg>
            </div>
        </div>
    </div>
</div>

<!-- Payment Type Statistics -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- New Entry Payments -->
    <div class="bank-card stats-card-primary">
        <div class="flex items-center justify-between">
            <div>
                <p class="stats-card-label">New Entry</p>
                <p class="stats-card-number">{{ number_format($stats['new_entry_payments']) }}</p>
                <p class="stats-card-subtitle">
                    AED {{ number_format($stats['new_entry_amount'], 2) }}
                </p>
            </div>
            <div class="stats-card-icon">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Renewal Payments -->
    <div class="bank-card stats-card-info">
        <div class="flex items-center justify-between">
            <div>
                <p class="stats-card-label">Renewal</p>
                <p class="stats-card-number">{{ number_format($stats['renewal_payments']) }}</p>
                <p class="stats-card-subtitle">
                    AED {{ number_format($stats['renewal_amount'], 2) }}
                </p>
            </div>
            <div class="stats-card-icon">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                    </path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Regular Payments -->
    <div class="bank-card stats-card-secondary">
        <div class="flex items-center justify-between">
            <div>
                <p class="stats-card-label">Regular</p>
                <p class="stats-card-number">{{ number_format($stats['regular_payments']) }}</p>
                <p class="stats-card-subtitle">
                    AED {{ number_format($stats['regular_amount'], 2) }}
                </p>
            </div>
            <div class="stats-card-icon">
                <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </div>
</div>

@push('styles')
    <style>
        /* Enhanced Statistics Cards Styling */
        .stats-card-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3) !important;
        }

        .stats-card-green {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3) !important;
        }

        .stats-card-yellow {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3) !important;
        }

        .stats-card-red {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.3) !important;
        }

        .stats-card-primary {
            background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 10px 25px rgba(30, 64, 175, 0.3) !important;
        }

        .stats-card-info {
            background: linear-gradient(135deg, #0369a1 0%, #0284c7 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 10px 25px rgba(3, 105, 161, 0.3) !important;
        }

        .stats-card-secondary {
            background: linear-gradient(135deg, #374151 0%, #4b5563 100%) !important;
            color: white !important;
            border: none !important;
            box-shadow: 0 10px 25px rgba(55, 65, 81, 0.3) !important;
        }

        .stats-card-label {
            color: rgba(255, 255, 255, 0.9) !important;
            font-size: 0.875rem !important;
            font-weight: 500 !important;
            margin-bottom: 0.5rem !important;
            text-transform: uppercase !important;
            letter-spacing: 0.05em !important;
        }

        .stats-card-number {
            color: white !important;
            font-size: 2rem !important;
            font-weight: 700 !important;
            line-height: 1.2 !important;
            margin-bottom: 0.25rem !important;
        }

        .stats-card-subtitle {
            color: rgba(255, 255, 255, 0.8) !important;
            font-size: 0.75rem !important;
            font-weight: 400 !important;
            margin-top: 0.25rem !important;
        }

        .stats-card-icon {
            padding: 0.75rem !important;
            background: rgba(255, 255, 255, 0.2) !important;
            border-radius: 0.5rem !important;
            backdrop-filter: blur(10px) !important;
        }

        .stats-card-icon svg {
            color: white !important;
            width: 2rem !important;
            height: 2rem !important;
        }

        /* Hover effects for stats cards */
        .stats-card-blue:hover,
        .stats-card-green:hover,
        .stats-card-yellow:hover,
        .stats-card-red:hover,
        .stats-card-primary:hover,
        .stats-card-info:hover,
        .stats-card-secondary:hover {
            transform: translateY(-2px) !important;
            transition: all 0.3s ease-in-out !important;
        }

        .stats-card-blue:hover {
            box-shadow: 0 15px 35px rgba(59, 130, 246, 0.4) !important;
        }

        .stats-card-green:hover {
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.4) !important;
        }

        .stats-card-yellow:hover {
            box-shadow: 0 15px 35px rgba(245, 158, 11, 0.4) !important;
        }

        .stats-card-red:hover {
            box-shadow: 0 15px 35px rgba(239, 68, 68, 0.4) !important;
        }

        .stats-card-primary:hover {
            box-shadow: 0 15px 35px rgba(30, 64, 175, 0.4) !important;
        }

        .stats-card-info:hover {
            box-shadow: 0 15px 35px rgba(3, 105, 161, 0.4) !important;
        }

        .stats-card-secondary:hover {
            box-shadow: 0 15px 35px rgba(55, 65, 81, 0.4) !important;
        }

        /* Financial cards styling improvements */
        .bank-card .text-dark-gray {
            color: #374151 !important;
            font-weight: 600 !important;
        }

        .bank-card .text-charcoal-black {
            color: #111827 !important;
            font-weight: 700 !important;
        }

        .bank-card .text-2xl {
            font-size: 1.875rem !important;
            line-height: 2.25rem !important;
        }

        /* Enhanced financial card backgrounds */
        .bank-card:not([class*="stats-card"]) {
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%) !important;
            border: 1px solid #e5e7eb !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        }

        .bank-card:not([class*="stats-card"]):hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
            transition: all 0.3s ease-in-out !important;
        }
    </style>
@endpush
