<?php

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        // Only admins can view user management
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        // Only admins can view users
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        // Only admins can create users
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        // Only admins can update users
        // Users cannot update themselves to prevent lockout
        return $user->isAdmin() && $user->id !== $model->id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        // Only admins can delete users
        // Users cannot delete themselves to prevent lockout
        // Cannot delete the last admin user
        if (!$user->isAdmin() || $user->id === $model->id) {
            return false;
        }

        // Prevent deletion of the last admin
        if ($model->isAdmin()) {
            $adminCount = User::role('admin')->where('status', true)->count();
            return $adminCount > 1;
        }

        return true;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        return $user->isAdmin() && $user->id !== $model->id;
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can export data.
     */
    public function export(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can toggle status.
     */
    public function toggleStatus(User $user, User $model): bool
    {
        // Only admins can toggle status
        // Users cannot toggle their own status to prevent lockout
        if (!$user->isAdmin() || $user->id === $model->id) {
            return false;
        }

        // Prevent deactivating the last admin
        if ($model->isAdmin() && $model->status) {
            $activeAdminCount = User::role('admin')->where('status', true)->count();
            return $activeAdminCount > 1;
        }

        return true;
    }
}
