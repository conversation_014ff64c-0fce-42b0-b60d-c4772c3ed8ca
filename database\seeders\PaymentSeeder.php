<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Payment;
use App\Models\Student;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if ($this->command) {
            $this->command->info('Creating payments...');
        }

        // Get all students
        $students = Student::with(['branch', 'academy', 'program'])->get();

        if ($students->isEmpty()) {
            throw new \Exception('No students found. Please run StudentSeeder first.');
        }

        foreach ($students as $student) {
            // Create 1-2 payments per student
            $paymentCount = rand(1, 2);

            for ($i = 0; $i < $paymentCount; $i++) {
                $amount = $student->program ? $student->program->price : rand(300, 500);
                $paymentDate = now()->subDays(rand(1, 180));
                $isRenewal = $i > 0; // First payment is fresh, subsequent are renewals

                $paymentData = [
                    'student_id' => $student->id,
                    'branch_id' => $student->branch_id,
                    'academy_id' => $student->academy_id,
                    'amount' => $amount,
                    'subtotal' => $amount,
                    'vat_rate' => 5.00,
                    'vat_amount' => $amount * 0.05,
                    'total_amount' => $amount * 1.05,
                    'vat_inclusive' => false,
                    'discount' => rand(0, 1) ? rand(10, 50) : 0,
                    'currency' => 'AED',
                    'payment_method' => ['cash', 'card', 'bank_transfer'][rand(0, 2)],
                    'payment_date' => $paymentDate->format('Y-m-d'),
                    'start_date' => $paymentDate->format('Y-m-d'),
                    'end_date' => $paymentDate->addDays(30)->format('Y-m-d'),
                    'status' => rand(0, 1) ? 'active' : 'expired',
                    'reset_num' => 'RST' . str_pad($student->id * 100 + $i, 6, '0', STR_PAD_LEFT),
                    'class_time_from' => '16:00:00',
                    'class_time_to' => '17:00:00',
                    'renewal' => $isRenewal,
                    'payment_type' => $isRenewal ? 'renewal' : 'new_entry',
                    'reference_number' => 'PAY' . str_pad($student->id * 1000 + $i, 8, '0', STR_PAD_LEFT),
                    'description' => $isRenewal ? 'Renewal payment' : 'Initial enrollment payment',
                    'note' => 'Seeded payment data',
                ];

                // Ensure unique reset_num
                $resetNum = $paymentData['reset_num'];
                $counter = 1;
                while (Payment::where('reset_num', $resetNum)->exists()) {
                    $resetNum = $paymentData['reset_num'] . '_' . $counter;
                    $counter++;
                }
                $paymentData['reset_num'] = $resetNum;

                Payment::create($paymentData);
            }
        }

        if ($this->command) {
            $paymentCount = Payment::count();
            $this->command->info("Payments created successfully. Total: {$paymentCount}");
        }
    }
}
