<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Image Upload Error Handling Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">Image Upload Error Handling Test</h1>
            
            <div class="mb-6">
                <h2 class="text-lg font-semibold text-gray-700 mb-3">Test Scenarios</h2>
                <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                    <li>Upload a file larger than 2MB (should show size error)</li>
                    <li>Upload a non-image file (should show type error)</li>
                    <li>Upload a valid image (should succeed)</li>
                    <li>Try uploading without selecting a file (should show selection error)</li>
                </ul>
            </div>

            <form id="test-upload-form" class="space-y-4">
                @csrf
                <div>
                    <label for="test_image" class="block text-sm font-medium text-gray-700 mb-2">
                        Select Image File
                    </label>
                    <input type="file" id="test_image" name="test_image" accept="image/*"
                           class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                </div>

                <button type="button" onclick="testUpload()" 
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Test Upload
                </button>
            </form>

            <div id="test-results" class="mt-6 space-y-2"></div>
        </div>

        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold text-gray-700 mb-3">Error Handling Features</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                    <h3 class="font-medium text-gray-800">Client-Side Validation</h3>
                    <ul class="list-disc list-inside text-gray-600 mt-1">
                        <li>File type checking</li>
                        <li>File size validation</li>
                        <li>Pre-upload validation</li>
                        <li>User-friendly messages</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-medium text-gray-800">Server-Side Handling</h3>
                    <ul class="list-disc list-inside text-gray-600 mt-1">
                        <li>Comprehensive validation</li>
                        <li>Error logging</li>
                        <li>Storage management</li>
                        <li>Security checks</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testUpload() {
            const fileInput = document.getElementById('test_image');
            const file = fileInput.files[0];
            const resultsDiv = document.getElementById('test-results');

            // Clear previous results
            resultsDiv.innerHTML = '';

            if (!file) {
                showTestResult('error', 'Please select an image file to upload.');
                return;
            }

            // Client-side validation (same as in the actual application)
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!validTypes.includes(file.type)) {
                showTestResult('error', 'Please select a valid image file (JPEG, PNG, JPG, or GIF).');
                return;
            }

            const maxSize = 2 * 1024 * 1024; // 2MB
            if (file.size > maxSize) {
                showTestResult('error', 'Image size must be less than 2MB. Current size: ' + formatFileSize(file.size));
                return;
            }

            showTestResult('info', 'File validation passed! File: ' + file.name + ' (' + formatFileSize(file.size) + ')');
            showTestResult('success', 'This file would be successfully uploaded in the actual application.');
        }

        function showTestResult(type, message) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            
            let bgColor, textColor, icon;
            switch(type) {
                case 'error':
                    bgColor = 'bg-red-100';
                    textColor = 'text-red-700';
                    icon = '❌';
                    break;
                case 'success':
                    bgColor = 'bg-green-100';
                    textColor = 'text-green-700';
                    icon = '✅';
                    break;
                case 'info':
                    bgColor = 'bg-blue-100';
                    textColor = 'text-blue-700';
                    icon = 'ℹ️';
                    break;
                default:
                    bgColor = 'bg-gray-100';
                    textColor = 'text-gray-700';
                    icon = '📝';
            }

            resultDiv.className = `p-3 rounded-md ${bgColor} ${textColor} text-sm`;
            resultDiv.innerHTML = `
                <div class="flex items-center">
                    <span class="mr-2">${icon}</span>
                    ${message}
                </div>
            `;
            
            resultsDiv.appendChild(resultDiv);
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
