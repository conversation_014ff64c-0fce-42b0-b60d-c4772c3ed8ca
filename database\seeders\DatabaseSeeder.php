<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create system users
        $this->call(UserSeeder::class);

        // Create sample branches
        $ajmanBranch = \App\Models\Branch::firstOrCreate(
            ['name' => 'AJMAN HAMEDYA'],
            [
                'location' => 'Ajman, UAE',
                'phone' => '+971501234567',
                'email' => '<EMAIL>',
                'address' => 'Hamedya Area, Ajman, UAE',
                'status' => true,
            ]
        );

        $dubaiBranch = \App\Models\Branch::firstOrCreate(
            ['name' => 'AL QUSAIS'],
            [
                'location' => 'Dubai, UAE',
                'phone' => '+971507654321',
                'email' => '<EMAIL>',
                'address' => 'Al Qusais Area, Dubai, UAE',
                'status' => true,
            ]
        );

        // Create sample academies
        $swimmingAcademy = \App\Models\Academy::firstOrCreate(
            ['name' => 'AJMAN SWIMMING ACADEMY', 'branch_id' => $ajmanBranch->id],
            [
                'description' => 'Professional swimming training for all ages',
                'coach_name' => 'Coach Ahmed',
                'coach_phone' => '+971501111111',
                'status' => true,
            ]
        );

        $footballAcademy = \App\Models\Academy::firstOrCreate(
            ['name' => 'FB AL QUSAIS', 'branch_id' => $dubaiBranch->id],
            [
                'description' => 'Football training academy',
                'coach_name' => 'Coach Mohammed',
                'coach_phone' => '+971502222222',
                'status' => true,
            ]
        );

        // Create sample programs
        \App\Models\Program::firstOrCreate(
            ['academy_id' => $swimmingAcademy->id, 'name' => 'Beginner Swimming'],
            [
                'days' => json_encode(['SAT', 'MON', 'WED']),
                'classes' => 3,
                'price' => 315.00,
                'start_time' => '16:00:00',
                'end_time' => '17:00:00',
                'max_students' => 15,
                'status' => true,
            ]
        );

        \App\Models\Program::firstOrCreate(
            ['academy_id' => $footballAcademy->id, 'name' => 'Youth Football Training'],
            [
                'days' => json_encode(['FRI', 'SAT']),
                'classes' => 2,
                'price' => 450.00,
                'start_time' => '17:00:00',
                'end_time' => '18:30:00',
                'max_students' => 20,
                'status' => true,
            ]
        );

        // Create branch and academy users
        $userSeeder = new UserSeeder();
        $branches = [$ajmanBranch, $dubaiBranch];
        $academies = [$swimmingAcademy, $footballAcademy];
        $userSeeder->createBranchUsers($branches, $academies);

        // Create additional programs using ProgramSeeder for better variety
        $this->call(ProgramSeeder::class);

        // Create realistic students for all programs
        $this->call(StudentSeeder::class);

        // Create sample payments and uniforms using PaymentSeeder and UniformSeeder
        // These will be created for the students generated by StudentSeeder
        $this->call(PaymentSeeder::class);
        $this->call(UniformSeeder::class);

        // Seed VAT settings
        $this->call(VatSettingsSeeder::class);
    }
}
