# Technical Implementation Plan - UAE English Sports Academy

## 🌐 Internationalization & Localization Requirements

### Language Support
- **Primary Language**: English (en-US)
- **Secondary Language**: Arabic (ar-AE) with RTL support
- **Font Family**: IBM Plex Sans (English), IBM Plex Sans Arabic (Arabic text)
- **Text Direction**: Auto-detection with proper RTL/LTR handling
- **Character Encoding**: UTF-8 for proper Arabic character support

### Currency Standards
- **Primary Currency**: UAE Dirham (AED) only
- **Currency Format**: "315 AED" (amount + space + currency code)
- **Currency Symbol**: د.إ (Arabic contexts) / AED (English contexts)
- **Number Format**: English numerals (1,234.56) with comma separators
- **Decimal Precision**: Up to 2 decimal places
- **Input Validation**: Currency fields accept only valid AED amounts

### Regional Settings
- **Timezone**: Asia/Dubai (UTC+4)
- **Date Format**: DD/MM/YYYY (UAE standard)
- **Phone Format**: +971 XX XXX XXXX (UAE format)
- **Address Format**: UAE address standards

## 🖥️ Environment Requirements

### Frontend
- **Laravel Blade** (Simple HTML pages with bilingual support)
- **Bootstrap 5** (Ready-made and easy design with RTL support)
- **jQuery** (Simple interaction with currency formatting utilities)
- **IBM Plex Sans Fonts** (Primary typography for English and Arabic)
- **Font Awesome** (Icons with RTL compatibility)
- **No Vite, Vue, or any complex tools**

### Backend
- **Laravel 10+** (PHP framework with localization support)
- **PHP 8.1+** (With UTF-8 support)
- **MySQL** (Database with UTF8MB4 charset for Arabic text)
- **Composer** (To install Laravel and localization packages)
- **Laravel Localization** (Built-in multi-language support)
- **Carbon** (Date/time handling with Dubai timezone)

### ✅ What you need on your machine:
1. **Laravel Valet** (Already installed and configured)
2. **PHP 8.4.7** (Managed by Homebrew/Valet)
3. **MySQL** (Managed by Homebrew)
4. **Composer** (For Laravel dependencies)
5. **Browser** (Safari or Chrome)
6. **Text Editor** (VS Code or any editor)

### ❌ What you don't need:
- ~~Node.js~~
- ~~npm/yarn~~
- ~~Vite~~
- ~~Vue.js~~
- ~~Docker~~
- ~~Any complex tools~~

## 🏗️ Simplified Project Structure

### Simple Development Principles:
- **Clear and understandable code**
- **Avoid repetition (DRY)**
- **Plan before programming**
- **Continuous code review**

### System Architecture:
- **Admin** (System Administrator)
- **Branch Manager** (Branch Manager)
- **Academy Manager** (Academy Manager)

### Core Modules:
- **Branch Management** (Date, ID, Academy Names, Programs, Location, Prices in AED, Days Selection, Student Count, Coach)
- **Academy Management** (ID, Available Programs, Student Lists, Birth Dates, UAE Phone Format, Registration Dates, Start/End Dates, Amount in AED, Status)
- **Program Management** (ID, Program Name, Academy Name, Days, Classes, Price in AED)
- **Student Management** (Full Profile, Status, Account Information, Payment History in AED, Attendance, Uniform Details)
- **Payment Management** (Payment List, Branch/Academy Filtering, Payment Status, Methods, Dates, AED Currency)
- **Uniform Management** (Student List, Order Details, Size, Status Tracking, Branch/Office Status, Amount in AED)

- **Reports Management** (Financial Reports in AED, Uniform Reports, Program Reports, Status Reports, Daily Reports)
## 🚀 Laravel Valet Development Setup

### Running Steps:
1. **Ensure Valet is running**: `valet status`
2. **Project is already parked**: Available at `https://uae_english_sports_academy.test`
3. **Database is configured**: MySQL running via Homebrew
4. **Open browser at `https://uae_english_sports_academy.test`**

### Valet Configuration:
- **URL**: https://uae_english_sports_academy.test
- **PHP Version**: 8.4.7 (via Homebrew)
- **Web Server**: Nginx (managed by Valet)
- **SSL**: Available via `valet secure uae_english_sports_academy`

### Database Settings:
- **Server**: localhost
- **Username**: root
- **Password**: (empty)
- **Port**: 3306 (MySQL via Homebrew)
- **Database Name**: uae_english_sports_academy_db
- **Charset**: utf8mb4 (for Arabic text support)
- **Collation**: utf8mb4_unicode_ci

### Project Files:
```
/Users/<USER>/Sites/uae_english_sports_academy/
├── app/                 (Laravel files)
├── public/             (Web root - served by Valet)
├── resources/views/    (HTML pages)
├── database/           (Database migrations and seeders)
└── .env               (Project settings)
```

## 🗄️ Database Structure

### Core Tables:

#### Branches Table
- id, name, location, created_at, updated_at

#### Academies Table
- id, branch_id, name, created_at, updated_at

#### Programs Table
- id, academy_id, name, days (JSON: FRI-TUE), classes, price (DECIMAL for AED amounts), created_at, updated_at

#### Students Table (replaces players)
- id, branch_id, academy_id, full_name, email, phone (UAE format: +971XXXXXXXXX), nationality, address, birth_date, join_date, status, created_at, updated_at

#### Payments Table
- id, student_id, branch_id, academy_id, amount (DECIMAL for AED), discount (DECIMAL for AED), payment_method, payment_date, start_date, end_date, status, reset_num, class_time_from, class_time_to, note, created_at, updated_at

#### Uniforms Table
- id, student_id, branch_id, academy_id, order_date, size, amount (DECIMAL for AED), branch_status, office_status, payment_method, note, created_at, updated_at

#### Attendance Table
- id, student_id, date, time, created_at, updated_at

#### Users Table (System Users)
- id, name, email, password, role (admin/branch_manager/academy_manager), branch_id, academy_id, created_at, updated_at

### Simple Security:
- **Strong passwords**
- **Protection from hacking**
- **Regular backups**

### Project Testing:
- **Test pages in browser**
- **Ensure database works**
- **Test login functionality**

## 📝 Simple Code Standards

### Writing Clear Code:
- **Understandable variable names in English**
- **Comments in English**
- **Logical file organization**
- **Currency values stored as DECIMAL(10,2) for AED precision**
- **UTF-8 encoding for all text fields**
- **Proper validation for UAE phone numbers and AED amounts**

### Documentation:
- **Explain every important function**
- **Usage examples**
- **Clear README file**

## 🔒 Simple Security

### Login Protection:
- **Strong passwords**
- **Specific permissions for each user**
- **Automatic logout**

### Data Protection:
- **Daily backups**
- **Protection from hacking**
- **Password encryption**

## ⚡ Performance Optimization

### Website Speed:
- **Optimized and small-sized images**
- **Organized database with proper indexing**
- **Clean and fast code**
- **Font optimization with preloading (IBM Plex Sans)**
- **Client-side currency formatting to reduce server load**

### Ease of Use:
- **Simple and understandable bilingual interface**
- **Fast page loading with font optimization**
- **Works on mobile and computer with RTL support**
- **Consistent AED currency formatting throughout**
- **Auto-detection of text direction for mixed content**

## 🎯 Simplified Implementation Plan

### Phase One (Basics):
1. **Setup Laravel development environment**
2. **Create database**
3. **Login page**
4. **Basic dashboard**

### Phase Two (Core Modules):
1. **Branch Management System**
   - Branch listing with academies, programs, locations
   - Day selection (1, 2, or 3 days)
   - Student count tracking
   - Coach assignment

2. **Academy Management System**
   - Academy profiles with available programs
   - Student enrollment management
   - Registration and renewal tracking
   - Status monitoring (Active/Inactive)

3. **Student Management System**
   - Complete student profiles
   - Payment history tracking
   - Uniform order management
   - Attendance recording

### Phase Three (Advanced Features):
1. **Payment Management System**
   - Payment processing with multiple methods
   - Discount and renewal handling
   - Payment status tracking
   - Branch/Academy filtering

2. **Uniform Management System**
   - Order tracking and status updates
   - Size management
   - Branch and office status tracking
   - Delivery confirmation

3. **Reports Management System**
   - Financial reports
   - Uniform reports
   - Program performance reports
   - Status reports
   - Daily operational reports

## ✅ Advantages of This Approach:

- **🚀 Easy to run**: Laravel Valet with zero configuration
- **🎯 Simple**: No complex tools
- **💻 Works on Mac**: Optimized for macOS
- **📱 Responsive**: Works on mobile and computer
- **🔧 Easy maintenance**: Clear and understandable code

---

## 🌍 Localization Implementation Details

### Laravel Configuration
```php
// config/app.php
'locale' => 'en',
'fallback_locale' => 'en',
'faker_locale' => 'en_US',
'timezone' => 'Asia/Dubai',

// Supported locales
'supported_locales' => ['en', 'ar'],
```

### Database Migrations for Multilingual Support
```php
// Migration example for currency fields
Schema::create('payments', function (Blueprint $table) {
    $table->id();
    $table->decimal('amount', 10, 2); // AED with 2 decimal places
    $table->decimal('discount', 10, 2)->default(0);
    $table->string('currency', 3)->default('AED');
    $table->timestamps();
});

// Migration for UTF8MB4 support
Schema::create('students', function (Blueprint $table) {
    $table->id();
    $table->string('full_name')->charset('utf8mb4');
    $table->string('phone', 15); // UAE format: +971XXXXXXXXX
    $table->timestamps();
});
```

### Frontend Implementation
```html
<!-- Language detection and font loading -->
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() == 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" as="style">
</head>
```

### Currency Formatting Helper
```php
// app/Helpers/CurrencyHelper.php
class CurrencyHelper {
    public static function formatAED($amount) {
        return number_format($amount, 2) . ' AED';
    }

    public static function parseAED($formatted) {
        return (float) str_replace([' AED', ','], '', $formatted);
    }
}
```

---

**Last Updated**: December 2024
**Version**: 3.0 (Bilingual Laravel with AED Currency)
**Developer**: UAE English Sports Academy Team
**Languages**: English (Primary), Arabic (Secondary)
**Currency**: UAE Dirham (AED) Only
