<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Attendance extends Model
{
    protected $fillable = [
        'student_id',
        'branch_id',
        'academy_id',
        'program_id',
        'date',
        'time',
        'status',
        'note',
        'session_date',
        'notes',
        'marked_by',
    ];

    protected $casts = [
        'date' => 'date',
        'session_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'status_text',
        'status_badge_class',
        'formatted_session_date',
        'formatted_date',
    ];

    /**
     * Get the student that owns the attendance.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    /**
     * Get the branch that owns the attendance.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the attendance.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the program that owns the attendance.
     */
    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class);
    }

    /**
     * Get the user who marked the attendance.
     */
    public function markedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'marked_by');
    }

    // Computed Properties

    /**
     * Get human-readable status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'present' => 'Present',
            'absent' => 'Absent',
            'late' => 'Late',
            'excused' => 'Excused',
            default => 'Unknown'
        };
    }

    /**
     * Get status badge CSS class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'present' => 'badge-success',
            'absent' => 'badge-danger',
            'late' => 'badge-warning',
            'excused' => 'badge-info',
            default => 'badge-secondary'
        };
    }

    /**
     * Get formatted session date.
     */
    public function getFormattedSessionDateAttribute(): string
    {
        return $this->session_date ? $this->session_date->format('M d, Y') : '';
    }

    /**
     * Get formatted date.
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->date ? $this->date->format('M d, Y') : '';
    }

    // Query Scopes

    /**
     * Scope a query to only include present attendance.
     */
    public function scopePresent(Builder $query): Builder
    {
        return $query->where('status', 'present');
    }

    /**
     * Scope a query to only include absent attendance.
     */
    public function scopeAbsent(Builder $query): Builder
    {
        return $query->where('status', 'absent');
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope a query to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    // Utility Methods

    /**
     * Check if attendance is present.
     */
    public function isPresent(): bool
    {
        return $this->status === 'present';
    }

    /**
     * Mark attendance as present.
     */
    public function markAsPresent(): bool
    {
        $this->status = 'present';
        return $this->save();
    }

    /**
     * Get available attendance statuses.
     */
    public static function getAvailableStatuses(): array
    {
        return [
            'present' => 'Present',
            'absent' => 'Absent',
            'late' => 'Late',
            'excused' => 'Excused',
        ];
    }
}
