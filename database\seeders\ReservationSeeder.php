<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Venue;
use App\Models\Field;
use App\Models\Customer;

class ReservationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Leaders Venue
        $leadersVenue = Venue::create([
            'name' => 'Leaders Sports Academy',
            'name_ar' => 'أكاديمية ليدرز الرياضية',
            'code' => 'LEADERS_001',
            'description' => 'Premier sports facility with multiple fields for football, basketball, and other sports.',
            'description_ar' => 'منشأة رياضية متميزة مع ملاعب متعددة لكرة القدم وكرة السلة والرياضات الأخرى.',
            'address' => 'Al Wasl Road, Jumeirah, Dubai, UAE',
            'address_ar' => 'شارع الوصل، جميرا، دبي، الإمارات العربية المتحدة',
            'city' => 'Dubai',
            'country' => 'UAE',
            'phone' => '+971501234567',
            'email' => '<EMAIL>',
            'manager_name' => 'Ahmed <PERSON> Mansouri',
            'manager_phone' => '+971507654321',
            'manager_email' => '<EMAIL>',
            'latitude' => 25.2048,
            'longitude' => 55.2708,
            'operating_hours' => [
                'sunday' => ['open' => '06:00', 'close' => '23:00'],
                'monday' => ['open' => '06:00', 'close' => '23:00'],
                'tuesday' => ['open' => '06:00', 'close' => '23:00'],
                'wednesday' => ['open' => '06:00', 'close' => '23:00'],
                'thursday' => ['open' => '06:00', 'close' => '23:00'],
                'friday' => ['open' => '14:00', 'close' => '23:00'], // Friday afternoon only
                'saturday' => ['open' => '06:00', 'close' => '23:00'],
            ],
            'facilities' => [
                'parking' => 'Free parking for 50 cars',
                'cafe' => 'Sports cafe with healthy meals',
                'changing_rooms' => '4 changing rooms with showers',
                'equipment_rental' => 'Football and basketball equipment available',
                'first_aid' => 'First aid station with trained staff',
                'wifi' => 'Free WiFi throughout the facility',
            ],
            'hourly_rate_base' => 150.00, // Base rate in AED
            'currency' => 'AED',
            'vat_applicable' => true,
            'vat_rate' => 5.00,
            'status' => true,
            'notes' => 'Premium sports facility with professional-grade fields and excellent amenities.',
            'notes_ar' => 'منشأة رياضية متميزة مع ملاعب احترافية ووسائل راحة ممتازة.',
        ]);

        // Create Fields for Leaders Venue
        $fields = [
            [
                'name' => 'Field X',
                'name_ar' => 'الملعب إكس',
                'type' => 'football',
                'description' => 'Professional football field with natural grass surface.',
                'description_ar' => 'ملعب كرة قدم احترافي بأرضية عشب طبيعي.',
                'surface_type' => 'natural_grass',
                'dimensions' => ['length' => 105, 'width' => 68, 'area' => 7140],
                'capacity' => 22,
                'hourly_rate' => 200.00,
                'peak_hour_rate' => 250.00,
                'weekend_rate' => 300.00,
                'peak_hours' => ['18:00-22:00'], // Evening peak hours
                'equipment_included' => ['goals', 'corner_flags', 'footballs'],
                'amenities' => ['floodlights', 'dugouts', 'scoreboard'],
                'lighting_available' => true,
                'air_conditioning' => false,
                'covered' => false,
            ],
            [
                'name' => 'Field Y',
                'name_ar' => 'الملعب واي',
                'type' => 'football',
                'description' => 'Artificial turf football field suitable for all weather conditions.',
                'description_ar' => 'ملعب كرة قدم بأرضية صناعية مناسب لجميع الظروف الجوية.',
                'surface_type' => 'artificial_turf',
                'dimensions' => ['length' => 100, 'width' => 64, 'area' => 6400],
                'capacity' => 22,
                'hourly_rate' => 180.00,
                'peak_hour_rate' => 220.00,
                'weekend_rate' => 280.00,
                'peak_hours' => ['18:00-22:00'],
                'equipment_included' => ['goals', 'corner_flags', 'footballs'],
                'amenities' => ['floodlights', 'dugouts'],
                'lighting_available' => true,
                'air_conditioning' => false,
                'covered' => false,
            ],
            [
                'name' => 'Field Z',
                'name_ar' => 'الملعب زد',
                'type' => 'multipurpose',
                'description' => 'Multi-purpose indoor court for basketball, volleyball, and futsal.',
                'description_ar' => 'ملعب داخلي متعدد الأغراض لكرة السلة والكرة الطائرة وكرة القدم الصالات.',
                'surface_type' => 'wooden_floor',
                'dimensions' => ['length' => 40, 'width' => 20, 'area' => 800],
                'capacity' => 20,
                'hourly_rate' => 120.00,
                'peak_hour_rate' => 150.00,
                'weekend_rate' => 180.00,
                'peak_hours' => ['18:00-22:00'],
                'equipment_included' => ['basketball_hoops', 'volleyball_net', 'futsal_goals'],
                'amenities' => ['air_conditioning', 'sound_system', 'scoreboard'],
                'lighting_available' => true,
                'air_conditioning' => true,
                'covered' => true,
            ],
        ];

        foreach ($fields as $fieldData) {
            $fieldData['venue_id'] = $leadersVenue->id;
            $fieldData['code'] = Field::generateCode($leadersVenue->id, $fieldData['name']);
            $fieldData['currency'] = 'AED';
            $fieldData['available_from'] = '06:00:00';
            $fieldData['available_to'] = '23:00:00';
            $fieldData['unavailable_days'] = []; // Available all days
            $fieldData['minimum_booking_hours'] = 1;
            $fieldData['maximum_booking_hours'] = 8;
            $fieldData['advance_booking_days'] = 30;
            $fieldData['requires_deposit'] = false;
            $fieldData['deposit_percentage'] = 0;
            $fieldData['status'] = true;

            Field::create($fieldData);
        }

        // Create sample customers
        $customers = [
            [
                'full_name' => 'Mohammed Ahmed Al Rashid',
                'full_name_ar' => 'محمد أحمد الراشد',
                'first_name' => 'Mohammed',
                'first_name_ar' => 'محمد',
                'last_name' => 'Al Rashid',
                'last_name_ar' => 'الراشد',
                'email' => '<EMAIL>',
                'phone' => '+971501234567',
                'nationality' => 'Emirati',
                'nationality_ar' => 'إماراتي',
                'address' => 'Al Wasl Road, Dubai',
                'address_ar' => 'شارع الوصل، دبي',
                'city' => 'Dubai',
                'emirate' => 'Dubai',
                'birth_date' => '1985-03-15',
                'gender' => 'male',
                'id_type' => 'Emirates ID',
                'id_number' => '784198512345678',
                'id_expiry_date' => '2030-03-15',
                'customer_type' => 'individual',
                'preferred_language' => 'ar',
                'preferred_contact_method' => 'phone',
                'emergency_contact' => [
                    'name' => 'Fatima Al Rashid',
                    'phone' => '+971507654321',
                    'relationship' => 'Wife'
                ],
                'payment_terms' => 'cash',
                'vip_status' => false,
                'registration_date' => now(),
                'status' => 'active',
            ],
            [
                'full_name' => 'Sarah Johnson',
                'first_name' => 'Sarah',
                'last_name' => 'Johnson',
                'email' => '<EMAIL>',
                'phone' => '+971509876543',
                'nationality' => 'British',
                'address' => 'Jumeirah Beach Road, Dubai',
                'city' => 'Dubai',
                'emirate' => 'Dubai',
                'birth_date' => '1990-07-22',
                'gender' => 'female',
                'id_type' => 'Passport',
                'id_number' => 'GB123456789',
                'id_expiry_date' => '2028-07-22',
                'customer_type' => 'individual',
                'preferred_language' => 'en',
                'preferred_contact_method' => 'email',
                'emergency_contact' => [
                    'name' => 'John Johnson',
                    'phone' => '+971505555555',
                    'relationship' => 'Husband'
                ],
                'payment_terms' => 'cash',
                'vip_status' => false,
                'registration_date' => now(),
                'status' => 'active',
            ],
            [
                'full_name' => 'Dubai Sports Club',
                'company_name' => 'Dubai Sports Club',
                'company_name_ar' => 'نادي دبي الرياضي',
                'first_name' => 'Dubai Sports',
                'last_name' => 'Club',
                'email' => '<EMAIL>',
                'phone' => '+971501111111',
                'address' => 'Sheikh Zayed Road, Dubai',
                'city' => 'Dubai',
                'emirate' => 'Dubai',
                'customer_type' => 'corporate',
                'trade_license' => 'DED123456789',
                'tax_number' => 'TRN987654321',
                'preferred_language' => 'en',
                'preferred_contact_method' => 'email',
                'credit_limit' => 10000.00,
                'payment_terms' => 'credit_30',
                'vip_status' => true,
                'registration_date' => now(),
                'status' => 'active',
            ],
        ];

        foreach ($customers as $customerData) {
            $customerData['customer_number'] = Customer::generateCustomerNumber();
            Customer::create($customerData);
        }

        $this->command->info('Reservation system seeded successfully!');
        $this->command->info('Created:');
        $this->command->info('- 1 Venue (Leaders Sports Academy)');
        $this->command->info('- 3 Fields (Field X, Field Y, Field Z)');
        $this->command->info('- 3 Sample Customers');
    }
}
