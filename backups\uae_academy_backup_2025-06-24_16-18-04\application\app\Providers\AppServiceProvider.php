<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Models\Setting;
use App\Models\UniformInventory;
use App\Models\Venue;
use App\Models\Field;
use App\Models\Customer;
use App\Models\Reservation;
use App\Models\ReservationPayment;
use App\Policies\SettingPolicy;
use App\Policies\UniformInventoryPolicy;
use App\Policies\VenuePolicy;
use App\Policies\FieldPolicy;
use App\Policies\CustomerPolicy;
use App\Policies\ReservationPolicy;
use App\Policies\ReservationPaymentPolicy;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register policies
        Gate::policy(Setting::class, SettingPolicy::class);
        Gate::policy(UniformInventory::class, UniformInventoryPolicy::class);

        // Register Reservation module policies
        Gate::policy(Venue::class, VenuePolicy::class);
        Gate::policy(Field::class, FieldPolicy::class);
        Gate::policy(Customer::class, CustomerPolicy::class);
        Gate::policy(Reservation::class, ReservationPolicy::class);
        Gate::policy(ReservationPayment::class, ReservationPaymentPolicy::class);
    }
}
