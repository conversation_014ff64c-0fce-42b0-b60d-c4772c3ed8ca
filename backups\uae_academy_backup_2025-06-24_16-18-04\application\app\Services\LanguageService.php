<?php

namespace App\Services;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cookie;
use Carbon\Carbon;

class LanguageService
{
    /**
     * Supported locales configuration
     */
    private const SUPPORTED_LOCALES = [
        'en' => [
            'name' => 'English',
            'native' => 'English',
            'direction' => 'ltr',
            'flag' => '🇺🇸',
            'date_format' => 'd/m/Y',
            'time_format' => 'H:i',
            'currency_position' => 'before',
            'decimal_separator' => '.',
            'thousands_separator' => ',',
        ],
        'ar' => [
            'name' => 'Arabic',
            'native' => 'العربية',
            'direction' => 'rtl',
            'flag' => '🇦🇪',
            'date_format' => 'd/m/Y',
            'time_format' => 'H:i',
            'currency_position' => 'after',
            'decimal_separator' => '.',
            'thousands_separator' => ',',
        ],
    ];

    /**
     * Get all supported locales
     *
     * @return array
     */
    public static function getSupportedLocales(): array
    {
        return self::SUPPORTED_LOCALES;
    }

    /**
     * Get locale configuration
     *
     * @param string|null $locale
     * @return array
     */
    public static function getLocaleConfig(string $locale = null): array
    {
        $locale = $locale ?: App::getLocale();
        return self::SUPPORTED_LOCALES[$locale] ?? self::SUPPORTED_LOCALES['en'];
    }

    /**
     * Check if locale is supported
     *
     * @param string $locale
     * @return bool
     */
    public static function isSupported(string $locale): bool
    {
        return array_key_exists($locale, self::SUPPORTED_LOCALES);
    }

    /**
     * Get current locale
     *
     * @return string
     */
    public static function getCurrentLocale(): string
    {
        return App::getLocale();
    }

    /**
     * Get current text direction
     *
     * @return string
     */
    public static function getCurrentDirection(): string
    {
        $config = self::getLocaleConfig();
        return $config['direction'];
    }

    /**
     * Get text direction for a specific locale
     *
     * @param string $locale
     * @return string
     */
    public static function getDirection(string $locale): string
    {
        $config = self::getLocaleConfig($locale);
        return $config['direction'];
    }

    /**
     * Check if current locale is RTL
     *
     * @return bool
     */
    public static function isCurrentRtl(): bool
    {
        return self::getCurrentDirection() === 'rtl';
    }

    /**
     * Switch to a different locale
     *
     * @param string $locale
     * @return bool
     */
    public static function switchTo(string $locale): bool
    {
        if (!self::isSupported($locale)) {
            return false;
        }

        App::setLocale($locale);
        Session::put('locale', $locale);
        Cookie::queue('locale', $locale, 60 * 24 * 30); // 30 days

        return true;
    }

    /**
     * Get locale name in English
     *
     * @param string|null $locale
     * @return string
     */
    public static function getLocaleName(string $locale = null): string
    {
        $config = self::getLocaleConfig($locale);
        return $config['name'];
    }

    /**
     * Get locale name in native language
     *
     * @param string|null $locale
     * @return string
     */
    public static function getNativeName(string $locale = null): string
    {
        $config = self::getLocaleConfig($locale);
        return $config['native'];
    }

    /**
     * Get locale flag emoji
     *
     * @param string|null $locale
     * @return string
     */
    public static function getFlag(string $locale = null): string
    {
        $config = self::getLocaleConfig($locale);
        return $config['flag'];
    }

    /**
     * Format date according to locale
     *
     * @param Carbon|string $date
     * @param string|null $locale
     * @param bool $includeTime
     * @return string
     */
    public static function formatDate($date, string $locale = null, bool $includeTime = false): string
    {
        if (!$date instanceof Carbon) {
            $date = Carbon::parse($date);
        }

        $config = self::getLocaleConfig($locale);
        $format = $config['date_format'];

        if ($includeTime) {
            $format .= ' ' . $config['time_format'];
        }

        // Set timezone to Dubai
        $date->setTimezone('Asia/Dubai');

        return $date->format($format);
    }

    /**
     * Format currency according to locale
     *
     * @param float $amount
     * @param string $currency
     * @param string|null $locale
     * @return string
     */
    public static function formatCurrency(float $amount, string $currency = 'AED', string $locale = null): string
    {
        $config = self::getLocaleConfig($locale);

        $formattedAmount = number_format(
            $amount,
            2,
            $config['decimal_separator'],
            $config['thousands_separator']
        );

        if ($config['currency_position'] === 'before') {
            return $currency . ' ' . $formattedAmount;
        } else {
            return $formattedAmount . ' ' . $currency;
        }
    }

    /**
     * Format number according to locale
     *
     * @param float $number
     * @param int $decimals
     * @param string|null $locale
     * @return string
     */
    public static function formatNumber(float $number, int $decimals = 2, string $locale = null): string
    {
        $config = self::getLocaleConfig($locale);

        return number_format(
            $number,
            $decimals,
            $config['decimal_separator'],
            $config['thousands_separator']
        );
    }

    /**
     * Get phone number format for locale
     *
     * @param string|null $locale
     * @return string
     */
    public static function getPhoneFormat(string $locale = null): string
    {
        // UAE phone format for both locales
        return '+971 XX XXX XXXX';
    }

    /**
     * Format phone number according to locale
     *
     * @param string $phone
     * @param string|null $locale
     * @return string
     */
    public static function formatPhone(string $phone, string $locale = null): string
    {
        // Remove all non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Handle UAE phone numbers
        if (strlen($phone) === 9 && !str_starts_with($phone, '971')) {
            $phone = '971' . $phone;
        }

        if (strlen($phone) === 12 && str_starts_with($phone, '971')) {
            return '+971 ' . substr($phone, 3, 2) . ' ' . substr($phone, 5, 3) . ' ' . substr($phone, 8, 4);
        }

        return $phone;
    }

    /**
     * Get available languages for language switcher
     *
     * @return array
     */
    public static function getLanguageSwitcherData(): array
    {
        $languages = [];

        foreach (self::SUPPORTED_LOCALES as $code => $config) {
            $languages[] = [
                'code' => $code,
                'name' => $config['name'],
                'native' => $config['native'],
                'flag' => $config['flag'],
                'direction' => $config['direction'],
                'is_current' => $code === self::getCurrentLocale(),
            ];
        }

        return $languages;
    }

    /**
     * Get CSS classes for current locale
     *
     * @return string
     */
    public static function getCssClasses(): string
    {
        $locale = self::getCurrentLocale();
        $direction = self::getCurrentDirection();

        return "locale-{$locale} dir-{$direction}" . (self::isCurrentRtl() ? ' rtl' : ' ltr');
    }

    /**
     * Get HTML attributes for current locale
     *
     * @return array
     */
    public static function getHtmlAttributes(): array
    {
        return [
            'lang' => self::getCurrentLocale(),
            'dir' => self::getCurrentDirection(),
            'class' => self::getCssClasses(),
        ];
    }

    /**
     * Translate with fallback
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @return string
     */
    public static function trans(string $key, array $replace = [], string $locale = null): string
    {
        $locale = $locale ?: self::getCurrentLocale();

        // Try to get translation in requested locale
        $translation = trans($key, $replace, $locale);

        // If translation is the same as key (not found), try fallback locale
        if ($translation === $key && $locale !== 'en') {
            $translation = trans($key, $replace, 'en');
        }

        return $translation;
    }
}
