<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // Add VAT-related fields
            $table->decimal('subtotal', 10, 2)->nullable()->after('amount')->comment('Amount before VAT');
            $table->decimal('vat_rate', 5, 2)->default(5.00)->after('subtotal')->comment('VAT rate percentage');
            $table->decimal('vat_amount', 10, 2)->nullable()->after('vat_rate')->comment('VAT amount calculated');
            $table->decimal('total_amount', 10, 2)->nullable()->after('vat_amount')->comment('Total amount including VAT');
            $table->boolean('vat_inclusive')->default(false)->after('total_amount')->comment('Whether amount includes VAT');

            // Add payment type field to distinguish between new entry and renewal payments
            $table->enum('payment_type', ['new_entry', 'renewal', 'regular'])
                ->default('regular')
                ->after('renewal')
                ->comment('Type of payment: new_entry for fresh students, renewal for returning students, regular for ongoing payments');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->dropColumn([
                'subtotal',
                'vat_rate',
                'vat_amount',
                'total_amount',
                'vat_inclusive',
                'payment_type'
            ]);
        });
    }
};
