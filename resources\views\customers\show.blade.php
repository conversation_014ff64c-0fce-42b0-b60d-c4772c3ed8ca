@extends('layouts.dashboard')

@section('title', $customer->full_name . ' - Customer Profile')

@push('styles')
    <style>
        .customer-profile-content {
            color: black !important;
        }

        .customer-profile-content * {
            color: black !important;
        }

        .customer-profile-content .text-dark-gray,
        .customer-profile-content .text-gray-600,
        .customer-profile-content .text-gray-500 {
            color: #6b7280 !important;
        }

        .customer-profile-content .form-label-bank {
            color: #374151 !important;
            font-weight: 600;
        }

        .customer-profile-content .badge-bank {
            color: white !important;
        }

        .customer-profile-content .badge-success {
            background-color: #10b981 !important;
            color: white !important;
        }

        .customer-profile-content .badge-warning {
            background-color: #f59e0b !important;
            color: white !important;
        }

        .customer-profile-content .badge-danger {
            background-color: #ef4444 !important;
            color: white !important;
        }

        .customer-profile-content .badge-info {
            background-color: #3b82f6 !important;
            color: white !important;
        }

        .customer-profile-content .badge-secondary {
            background-color: #6b7280 !important;
            color: white !important;
        }

        .customer-profile-content .text-success-green {
            color: #10b981 !important;
        }

        .customer-profile-content .text-leaders-red {
            color: #dc2626 !important;
        }

        .customer-profile-content .text-warning-orange {
            color: #f59e0b !important;
        }

        .customer-profile-content .text-charcoal-black {
            color: #1f2937 !important;
            font-weight: 500;
        }

        .customer-profile-content .bank-card-title {
            color: #1f2937 !important;
            font-weight: 600;
        }

        .customer-profile-content .bank-card-subtitle {
            color: #6b7280 !important;
        }

        .customer-profile-content table th {
            color: white !important;
        }

        .customer-profile-content table td {
            color: black !important;
        }

        .customer-profile-content .btn-bank {
            color: white !important;
        }

        .customer-profile-content .btn-bank-outline {
            color: #dc2626 !important;
        }

        .customer-profile-content .btn-bank-outline:hover {
            color: white !important;
        }

        /* Pagination text colors */
        .customer-profile-content .pagination .page-link {
            color: white !important;
        }

        .customer-profile-content .pagination .page-item.active .page-link {
            background-color: #dc2626 !important;
            border-color: #dc2626 !important;
            color: white !important;
        }

        .customer-profile-content .pagination .page-item.disabled .page-link {
            color: #6b7280 !important;
        }

        /* Stats cards text */
        .customer-profile-content .stats-card .stat-value {
            color: #1f2937 !important;
            font-weight: 700;
        }

        .customer-profile-content .stats-card .stat-label {
            color: #6b7280 !important;
        }

        /* Ensure all text in cards is black */
        .customer-profile-content .bank-card p,
        .customer-profile-content .bank-card span:not(.badge-bank),
        .customer-profile-content .bank-card div:not(.badge-bank) {
            color: black !important;
        }

        /* Profile image styling */
        .profile-image-container {
            position: relative;
            display: inline-block;
        }

        .profile-image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            cursor: pointer;
        }

        .profile-image-container:hover .profile-image-overlay {
            opacity: 1;
        }
    </style>
@endpush

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="relative group">
                @if ($customer->profile_image)
                    <img src="{{ $customer->profile_image_url }}" alt="{{ $customer->full_name }}"
                        class="w-16 h-16 rounded-full object-cover border-2 border-white shadow-lg">
                @else
                    <div
                        class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                        {{ substr($customer->full_name, 0, 1) }}
                    </div>
                @endif

                @can('update', $customer)
                    <div class="profile-image-overlay" onclick="document.getElementById('profile-image-input').click()">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>

                    <!-- Hidden File Input -->
                    <form id="profile-image-form" action="{{ route('customers.update', $customer) }}" method="POST"
                        enctype="multipart/form-data" style="display: none;">
                        @csrf
                        @method('PUT')
                        <input type="file" id="profile-image-input" name="profile_image" accept="image/*"
                            onchange="uploadProfileImage()">
                    </form>
                @endcan
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $customer->full_name }}</h1>
                <p class="text-lg text-dark-gray">Customer Profile • ID: {{ $customer->customer_number }}</p>
                <div class="flex items-center space-x-2">
                    <span class="badge-bank {{ $customer->status_badge_class }}">
                        {{ $customer->status_text }}
                    </span>
                    @if ($customer->vip_status)
                        <span class="badge-bank badge-warning">VIP Customer</span>
                    @endif
                    @if ($customer->customer_type === 'corporate')
                        <span class="badge-bank badge-info">Corporate</span>
                    @endif
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('create', App\Models\Reservation::class)
                <a href="{{ route('reservations.create', ['customer_id' => $customer->id]) }}"
                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                        </path>
                    </svg>
                    New Reservation
                </a>
            @endcan
            @can('update', $customer)
                <a href="{{ route('customers.edit', $customer) }}"
                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Customer
                </a>
            @endcan
            <a href="{{ route('customers.index') }}" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Customers
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="customer-profile-content">
        <!-- Statistics Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bank-card bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                <div class="bank-card-body">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <p class="text-blue-100 text-sm font-medium">Total Reservations</p>
                            <p class="text-3xl font-bold">{{ $stats['total_reservations'] }}</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bank-card bg-gradient-to-r from-green-500 to-green-600 text-white">
                <div class="bank-card-body">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <p class="text-green-100 text-sm font-medium">Active Reservations</p>
                            <p class="text-3xl font-bold">{{ $stats['active_reservations'] }}</p>
                        </div>
                        <div class="w-12 h-12 bg-green-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bank-card bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                <div class="bank-card-body">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <p class="text-purple-100 text-sm font-medium">Completed Reservations</p>
                            <p class="text-3xl font-bold">{{ $stats['completed_reservations'] }}</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bank-card bg-gradient-to-r from-leaders-red to-leaders-deep-red text-white">
                <div class="bank-card-body">
                    <div class="flex items-center">
                        <div class="flex-1">
                            <p class="text-red-100 text-sm font-medium">Total Spent</p>
                            <p class="text-3xl font-bold">AED {{ number_format($stats['total_spent'], 0) }}</p>
                        </div>
                        <div class="w-12 h-12 bg-red-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Customer Information -->
            <div class="lg:col-span-2 space-y-6">
                <!-- Personal Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Personal Information</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="form-label-bank">Full Name</label>
                                <p class="text-charcoal-black font-medium">{{ $customer->full_name }}</p>
                                @if ($customer->full_name_ar)
                                    <p class="text-sm text-gray-600">{{ $customer->full_name_ar }}</p>
                                @endif
                            </div>
                            @if ($customer->email)
                                <div>
                                    <label class="form-label-bank">Email</label>
                                    <p class="text-charcoal-black">{{ $customer->email }}</p>
                                </div>
                            @endif

                            @if ($customer->nationality)
                                <div>
                                    <label class="form-label-bank">Nationality</label>
                                    <p class="text-charcoal-black">{{ $customer->localized_nationality }}</p>
                                </div>
                            @endif
                            @if ($customer->birth_date)
                                <div>
                                    <label class="form-label-bank">Birth Date</label>
                                    <p class="text-charcoal-black">{{ $customer->birth_date->format('M d, Y') }}
                                        ({{ $customer->age }} years old)</p>
                                </div>
                            @endif
                            <div>
                                <label class="form-label-bank">Registration Date</label>
                                <p class="text-charcoal-black">{{ $customer->registration_date->format('M d, Y') }}
                                    ({{ $customer->days_since_registration }} days ago)</p>
                            </div>
                            @if ($customer->address)
                                <div class="md:col-span-2">
                                    <label class="form-label-bank">Address</label>
                                    <p class="text-charcoal-black">{{ $customer->localized_address }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Corporate Information -->
                @if ($customer->customer_type === 'corporate')
                    <div class="bank-card">
                        <div class="bank-card-header">
                            <h3 class="bank-card-title">Corporate Information</h3>
                        </div>
                        <div class="bank-card-body">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                @if ($customer->company_name)
                                    <div>
                                        <label class="form-label-bank">Company Name</label>
                                        <p class="text-charcoal-black font-medium">{{ $customer->localized_company_name }}
                                        </p>
                                    </div>
                                @endif
                                @if ($customer->trade_license)
                                    <div>
                                        <label class="form-label-bank">Trade License</label>
                                        <p class="text-charcoal-black">{{ $customer->trade_license }}</p>
                                    </div>
                                @endif
                                @if ($customer->tax_number)
                                    <div>
                                        <label class="form-label-bank">Tax Number</label>
                                        <p class="text-charcoal-black">{{ $customer->tax_number }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Identification Information -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Identification Information</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @if ($customer->id_type)
                                <div>
                                    <label class="form-label-bank">ID Type</label>
                                    <p class="text-charcoal-black">{{ $customer->id_type }}</p>
                                </div>
                            @endif
                            @if ($customer->id_number)
                                <div>
                                    <label class="form-label-bank">ID Number</label>
                                    <p class="text-charcoal-black">{{ $customer->id_number }}</p>
                                </div>
                            @endif
                            @if ($customer->id_expiry_date)
                                <div>
                                    <label class="form-label-bank">ID Expiry Date</label>
                                    <p class="text-charcoal-black {{ $customer->is_id_expired ? 'text-red-600' : '' }}">
                                        {{ $customer->id_expiry_date->format('M d, Y') }}
                                        @if ($customer->is_id_expired)
                                            <span class="badge-bank badge-danger ml-2">Expired</span>
                                        @endif
                                    </p>
                                </div>
                            @endif
                            <div>
                                <label class="form-label-bank">Gender</label>
                                <p class="text-charcoal-black">{{ ucfirst($customer->gender) }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Reservations -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <div>
                            <h3 class="bank-card-title">Recent Reservations</h3>
                            <p class="bank-card-subtitle">Latest {{ count($recentReservations) }} reservations</p>
                        </div>
                        @if (count($recentReservations) > 0)
                            <a href="{{ route('reservations.index', ['customer_id' => $customer->id]) }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white btn-bank-sm">
                                View All
                            </a>
                        @endif
                    </div>
                    <div class="bank-card-body p-0">
                        @if (count($recentReservations) > 0)
                            <div class="overflow-x-auto">
                                <table class="table-bank min-w-full">
                                    <thead class="bg-dark-gray">
                                        <tr>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase">Date &
                                                Time</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase">Field
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase">
                                                Duration</th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase">Amount
                                            </th>
                                            <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase">Status
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-light-gray">
                                        @foreach ($recentReservations as $reservation)
                                            <tr class="hover:bg-off-white">
                                                <td class="px-6 py-4">
                                                    <div class="text-sm">
                                                        <div class="font-medium text-charcoal-black">
                                                            {{ $reservation->reservation_date->format('M d, Y') }}
                                                        </div>
                                                        <div class="text-gray-600">
                                                            {{ $reservation->start_time }} - {{ $reservation->end_time }}
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <div class="text-sm text-charcoal-black">
                                                        {{ $reservation->field->name ?? 'N/A' }}
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <div class="text-sm text-charcoal-black">
                                                        {{ $reservation->duration }} hours
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <div class="text-sm font-medium text-success-green">
                                                        AED {{ number_format($reservation->total_amount, 2) }}
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4">
                                                    <span
                                                        class="badge-bank badge-{{ $reservation->status === 'completed' ? 'success' : ($reservation->status === 'cancelled' ? 'danger' : 'warning') }}">
                                                        {{ ucfirst($reservation->status) }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-12">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <h3 class="text-lg font-medium text-dark-gray mb-2">No reservations yet</h3>
                                <p class="text-gray-500 mb-4">This customer hasn't made any reservations.</p>
                                @can('create', App\Models\Reservation::class)
                                    <a href="{{ route('reservations.create', ['customer_id' => $customer->id]) }}"
                                        class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                        Create First Reservation
                                    </a>
                                @endcan
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Sidebar Information -->
            <div class="space-y-6">
                <!-- Customer Status & Settings -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Customer Status</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="space-y-4">
                            <div>
                                <label class="form-label-bank">Status</label>
                                <span class="badge-bank {{ $customer->status_badge_class }}">
                                    {{ $customer->status_text }}
                                </span>
                            </div>
                            <div>
                                <label class="form-label-bank">Customer Type</label>
                                <p class="text-charcoal-black font-medium">{{ $customer->customer_type_text }}</p>
                            </div>
                            @if ($customer->vip_status)
                                <div>
                                    <label class="form-label-bank">VIP Status</label>
                                    <span class="badge-bank badge-warning">VIP Customer</span>
                                </div>
                            @endif
                            <div>
                                <label class="form-label-bank">Customer Tier</label>
                                <p class="text-charcoal-black font-medium">{{ $customer->customer_tier }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <h3 class="bank-card-title">Preferences</h3>
                    </div>
                    <div class="bank-card-body">
                        <div class="space-y-4">
                            <div>
                                <label class="form-label-bank">Preferred Language</label>
                                <p class="text-charcoal-black">
                                    {{ $customer->preferred_language === 'ar' ? 'Arabic' : 'English' }}</p>
                            </div>
                            <div>
                                <label class="form-label-bank">Contact Method</label>
                                <p class="text-charcoal-black">{{ ucfirst($customer->preferred_contact_method) }}</p>
                            </div>
                            <div>
                                <label class="form-label-bank">Payment Terms</label>
                                <p class="text-charcoal-black">{{ $customer->payment_terms_text }}</p>
                            </div>
                            @if ($customer->credit_limit > 0)
                                <div>
                                    <label class="form-label-bank">Credit Limit</label>
                                    <p class="text-charcoal-black font-medium">{{ $customer->formatted_credit_limit }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Emergency Contact -->
                @if ($customer->emergency_contact)
                    <div class="bank-card">
                        <div class="bank-card-header">
                            <h3 class="bank-card-title">Emergency Contact</h3>
                        </div>
                        <div class="bank-card-body">
                            <div class="space-y-3">
                                <div>
                                    <label class="form-label-bank">Name</label>
                                    <p class="text-charcoal-black font-medium">
                                        {{ $customer->emergency_contact['name'] ?? 'N/A' }}</p>
                                </div>

                                <div>
                                    <label class="form-label-bank">Relationship</label>
                                    <p class="text-charcoal-black">
                                        {{ $customer->emergency_contact['relationship'] ?? 'N/A' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Notes -->
                @if ($customer->notes)
                    <div class="bank-card">
                        <div class="bank-card-header">
                            <h3 class="bank-card-title">Notes</h3>
                        </div>
                        <div class="bank-card-body">
                            <p class="text-charcoal-black">{{ $customer->localized_notes }}</p>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        // Profile Image Upload Function
        function uploadProfileImage() {
            const form = document.getElementById('profile-image-form');
            const input = document.getElementById('profile-image-input');
            const file = input.files[0];

            if (!file) {
                showErrorMessage('Please select an image file to upload.');
                return;
            }

            // Enhanced file validation
            const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
            if (!validTypes.includes(file.type)) {
                showErrorMessage('Please select a valid image file (JPEG, PNG, JPG, or GIF).');
                return;
            }

            // Validate file size (2MB max)
            const maxSize = 2 * 1024 * 1024; // 2MB
            if (file.size > maxSize) {
                showErrorMessage('Image size must be less than 2MB. Current size: ' + formatFileSize(file.size));
                return;
            }

            // Show loading state
            showLoadingMessage('Uploading profile image...');

            // Submit the form
            form.submit();
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showErrorMessage(message) {
            // This will be implemented with the notification system
            alert('Error: ' + message);
        }

        function showLoadingMessage(message) {
            // This will be implemented with the notification system
            console.log('Loading: ' + message);
        }
    </script>
@endpush
