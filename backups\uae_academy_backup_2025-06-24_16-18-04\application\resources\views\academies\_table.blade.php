<!-- Desktop Table View -->
<div class="hidden lg:block overflow-x-auto">
    <table class="table-bank">
        <thead>
            <tr>
                @can('bulkAction', App\Models\Academy::class)
                    <th class="w-12">
                        <input type="checkbox" @change="selectAllAcademies()"
                            :checked="selectedAcademies.length === document.querySelectorAll('input[name=\'academy_ids[]\']')
                                .length && document.querySelectorAll('input[name=\'academy_ids[]\']').length > 0"
                            class="form-checkbox-bank">
                    </th>
                @endcan
                <th>ID</th>
                <th>Academy Name</th>
                <th>Branch</th>
                <th>Coach</th>
                <th>Programs</th>
                <th>Students</th>
                <th>Revenue (AED)</th>
                <th>Status</th>
                <th>Created</th>
                <th class="actions-column">Actions</th>
            </tr>
        </thead>
        <tbody>
            @forelse($academies as $academy)
                <tr class="table-row-bank">
                    @can('bulkAction', App\Models\Academy::class)
                        <td>
                            <input type="checkbox" name="academy_ids[]" value="{{ $academy->id }}"
                                @change="toggleAcademySelection({{ $academy->id }})"
                                :checked="selectedAcademies.includes({{ $academy->id }})" class="form-checkbox-bank">
                        </td>
                    @endcan
                    <td class="font-mono text-sm text-medium-gray">#{{ $academy->id }}</td>
                    <td>
                        <div class="flex items-center space-x-3">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                    </path>
                                </svg>
                            </div>
                            <div>
                                <a href="{{ route('academies.show', $academy) }}"
                                    class="font-semibold text-red-600 hover:text-red-800 underline transition-colors duration-200">{{ $academy->name }}</a>
                                @if ($academy->description)
                                    <div class="text-sm text-dark-gray">{{ Str::limit($academy->description, 50) }}
                                    </div>
                                @endif
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-medium-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                </path>
                            </svg>
                            @if ($academy->branch)
                                <a href="{{ route('branches.show', $academy->branch) }}"
                                    class="text-sm font-medium text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                    style="color: #dc2626 !important;">
                                    {{ $academy->branch->name }}
                                </a>
                            @else
                                <span class="text-sm font-medium text-gray-500">N/A</span>
                            @endif
                        </div>
                    </td>
                    <td>
                        @if ($academy->coach_name)
                            <div>
                                <div class="font-medium text-charcoal-black">{{ $academy->coach_name }}</div>
                                @if ($academy->formatted_coach_phone)
                                    <div class="text-sm text-dark-gray">{{ $academy->formatted_coach_phone }}</div>
                                @endif
                            </div>
                        @else
                            <span class="text-medium-gray">No coach assigned</span>
                        @endif
                    </td>
                    <td>
                        <a href="{{ route('programs.index', ['academy_id' => $academy->id]) }}"
                            class="flex items-center space-x-2 hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200">
                            <span
                                class="badge-bank badge-info hover:bg-blue-600 transition-colors duration-200">{{ $academy->programs_count ?? 0 }}</span>
                            <span class="text-sm text-dark-gray">programs</span>
                        </a>
                    </td>
                    <td>
                        <a href="{{ route('students.index', ['academy_id' => $academy->id]) }}"
                            class="flex items-center space-x-2 hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200">
                            <span
                                class="badge-bank badge-success hover:bg-green-600 transition-colors duration-200">{{ $academy->students_count ?? 0 }}</span>
                            <span class="text-sm text-dark-gray">students</span>
                        </a>
                    </td>
                    <td>
                        <a href="{{ route('payments.index', ['academy_id' => $academy->id]) }}"
                            class="block text-right hover:bg-gray-50 rounded-lg p-2 transition-colors duration-200">
                            <div class="font-semibold text-green-600 hover:text-green-800 underline">
                                {{ number_format($academy->total_revenue, 2) }}
                            </div>
                            @if ($academy->pending_payments > 0)
                                <div class="text-sm text-orange-600 hover:text-orange-800 underline">
                                    +{{ number_format($academy->pending_payments, 2) }} pending
                                </div>
                            @endif
                        </a>
                    </td>
                    <td>
                        <button @click="toggleStatus({{ $academy->id }})"
                            class="badge-bank {{ $academy->status ? 'badge-success' : 'badge-neutral' }} cursor-pointer hover:opacity-80 transition-opacity">
                            {{ $academy->status ? 'Active' : 'Inactive' }}
                        </button>
                    </td>
                    <td class="text-sm text-medium-gray">
                        {{ $academy->created_at->format('M d, Y') }}
                        <div class="text-xs">{{ $academy->created_at->format('H:i') }}</div>
                    </td>
                    <td class="actions-column">
                        <div class="btn-action-group">
                            @can('view', $academy)
                                <a href="{{ route('academies.show', $academy) }}" class="btn-action btn-action-view"
                                    title="View Academy">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan
                            @can('update', $academy)
                                <a href="{{ route('academies.edit', $academy) }}" class="btn-action btn-action-edit"
                                    title="Edit Academy">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan
                            @can('delete', $academy)
                                <button @click="deleteAcademy({{ $academy->id }})" class="btn-action btn-action-delete"
                                    title="Delete Academy">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="{{ can('bulkAction', App\Models\Academy::class) ? '11' : '10' }}"
                        class="text-center py-12">
                        <div class="flex flex-col items-center justify-center space-y-4">
                            <svg class="w-16 h-16 text-medium-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                </path>
                            </svg>
                            <div class="text-lg font-medium text-dark-gray">No academies found</div>
                            <div class="text-sm text-medium-gray">Try adjusting your search criteria or create a new
                                academy</div>
                            @can('create', App\Models\Academy::class)
                                <a href="{{ route('academies.create') }}" class="btn-bank btn-bank-sm">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Create Academy
                                </a>
                            @endcan
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

<!-- Mobile Card View -->
<div class="lg:hidden space-y-4 p-4">
    @forelse($academies as $academy)
        <div class="bg-white border border-medium-gray rounded-xl shadow-sm overflow-hidden">
            <!-- Card Header -->
            <div class="p-4 border-b border-light-gray">
                <div class="flex items-start justify-between">
                    <div class="flex items-center space-x-3">
                        @can('bulkAction', App\Models\Academy::class)
                            <input type="checkbox" name="academy_ids[]" value="{{ $academy->id }}"
                                @change="toggleAcademySelection({{ $academy->id }})"
                                :checked="selectedAcademies.includes({{ $academy->id }})" class="form-checkbox-bank">
                        @endcan
                        <div
                            class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                </path>
                            </svg>
                        </div>
                        <div>
                            <a href="{{ route('academies.show', $academy) }}"
                                class="font-semibold text-red-600 hover:text-red-800 underline transition-colors duration-200">{{ $academy->name }}</a>
                            <p class="text-sm text-dark-gray">ID: #{{ $academy->id }}</p>
                        </div>
                    </div>
                    <button @click="toggleStatus({{ $academy->id }})"
                        class="badge-bank {{ $academy->status ? 'badge-success' : 'badge-neutral' }} cursor-pointer">
                        {{ $academy->status ? 'Active' : 'Inactive' }}
                    </button>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-4 space-y-3">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <span class="text-sm font-medium text-dark-gray">Branch</span>
                        @if ($academy->branch)
                            <a href="{{ route('branches.show', $academy->branch) }}"
                                class="text-sm text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                style="color: #dc2626 !important;">
                                {{ $academy->branch->name }}
                            </a>
                        @else
                            <p class="text-sm text-gray-500">N/A</p>
                        @endif
                    </div>
                    <div>
                        <span class="text-sm font-medium text-dark-gray">Coach</span>
                        <p class="text-sm text-charcoal-black">{{ $academy->coach_name ?? 'Not assigned' }}</p>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-dark-gray">Programs</span>
                        <a href="{{ route('programs.index', ['academy_id' => $academy->id]) }}"
                            class="text-sm text-blue-600 hover:text-blue-800 underline transition-colors duration-200">{{ $academy->programs_count ?? 0 }}</a>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-dark-gray">Students</span>
                        <a href="{{ route('students.index', ['academy_id' => $academy->id]) }}"
                            class="text-sm text-green-600 hover:text-green-800 underline transition-colors duration-200">{{ $academy->students_count ?? 0 }}</a>
                    </div>
                </div>

                <div>
                    <span class="text-sm font-medium text-dark-gray">Revenue</span>
                    <a href="{{ route('payments.index', ['academy_id' => $academy->id]) }}" class="block">
                        <p
                            class="text-lg font-semibold text-green-600 hover:text-green-800 underline transition-colors duration-200">
                            AED
                            {{ number_format($academy->total_revenue, 2) }}</p>
                        @if ($academy->pending_payments > 0)
                            <p
                                class="text-sm text-orange-600 hover:text-orange-800 underline transition-colors duration-200">
                                +AED {{ number_format($academy->pending_payments, 2) }}
                                pending</p>
                        @endif
                    </a>
                </div>

                @if ($academy->description)
                    <div>
                        <span class="text-sm font-medium text-dark-gray">Description</span>
                        <p class="text-sm text-charcoal-black">{{ Str::limit($academy->description, 100) }}</p>
                    </div>
                @endif
            </div>

            <!-- Card Footer -->
            <div class="px-4 py-3 bg-gray-50 border-t border-gray-100">
                <div class="flex items-center justify-between">
                    <span class="text-xs text-medium-gray">{{ $academy->created_at->format('M d, Y H:i') }}</span>
                    <div class="btn-action-group">
                        @can('view', $academy)
                            <a href="{{ route('academies.show', $academy) }}" class="btn-action btn-action-view">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                            </a>
                        @endcan
                        @can('update', $academy)
                            <a href="{{ route('academies.edit', $academy) }}" class="btn-action btn-action-edit">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                            </a>
                        @endcan
                        @can('delete', $academy)
                            <button @click="deleteAcademy({{ $academy->id }})" class="btn-action btn-action-delete">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                    </path>
                                </svg>
                            </button>
                        @endcan
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="text-center py-12">
            <svg class="w-16 h-16 text-medium-gray mx-auto mb-4" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z">
                </path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                </path>
            </svg>
            <h3 class="text-lg font-medium text-dark-gray mb-2">No academies found</h3>
            <p class="text-sm text-medium-gray mb-4">Try adjusting your search criteria or create a new academy</p>
            @can('create', App\Models\Academy::class)
                <a href="{{ route('academies.create') }}" class="btn-bank btn-bank-sm">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Create Academy
                </a>
            @endcan
        </div>
    @endforelse
</div>

<script>
    async function toggleStatus(academyId) {
        try {
            const response = await fetch(`/academies/${academyId}/toggle-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while updating the status.');
        }
    }

    async function deleteAcademy(academyId) {
        if (!confirm('Are you sure you want to delete this academy? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/academies/${academyId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                }
            });

            const result = await response.json();

            if (result.success) {
                showNotification('success', result.message);
                window.location.reload();
            } else {
                showNotification('error', result.message);
            }
        } catch (error) {
            showNotification('error', 'An error occurred while deleting the academy.');
        }
    }
</script>
