<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class Student extends Model
{
    protected $fillable = [
        'branch_id',
        'academy_id',
        'full_name',
        'full_name_ar',
        'first_name',
        'first_name_ar',
        'last_name',
        'last_name_ar',
        'email',
        'phone',
        'nationality',
        'nationality_ar',
        'address',
        'address_ar',
        'birth_date',
        'join_date',
        'status',
        'notes',
        'notes_ar',
        'profile_image',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'join_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'age',
        'formatted_phone',
        'status_text',
        'status_badge_class',
        'total_payments',
        'pending_payments',
        'attendance_rate',
        'uniform_orders_count',
        'days_since_joined',
        'formatted_join_date',
        'formatted_birth_date',
        'localized_name',
        'localized_first_name',
        'localized_last_name',
        'localized_nationality',
        'localized_address',
        'localized_notes',
        'localized_join_date',
        'localized_birth_date',
        'arabic_join_date',
        'arabic_birth_date',
    ];

    /**
     * Get the branch that owns the student.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns the student.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the payments for the student.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the uniforms for the student.
     */
    public function uniforms(): HasMany
    {
        return $this->hasMany(Uniform::class);
    }

    /**
     * Get the attendance records for the student.
     */
    public function attendances(): HasMany
    {
        return $this->hasMany(Attendance::class);
    }

    /**
     * Get the programs the student is enrolled in.
     */
    public function programs(): HasMany
    {
        return $this->hasMany(Program::class, 'academy_id', 'academy_id');
    }

    // Computed Properties

    /**
     * Get student's age based on birth date.
     */
    public function getAgeAttribute(): ?int
    {
        return $this->birth_date ? $this->birth_date->age : null;
    }

    /**
     * Get formatted phone number.
     */
    public function getFormattedPhoneAttribute(): string
    {
        $phone = $this->phone;
        if (strlen($phone) === 13 && str_starts_with($phone, '+971')) {
            return '+971 ' . substr($phone, 4, 2) . ' ' . substr($phone, 6, 3) . ' ' . substr($phone, 9);
        }
        return $phone;
    }

    /**
     * Get human-readable status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'suspended' => 'Suspended',
            default => 'Unknown'
        };
    }

    /**
     * Get status badge CSS class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'active' => 'badge-success',
            'inactive' => 'badge-secondary',
            'suspended' => 'badge-warning',
            default => 'badge-secondary'
        };
    }

    /**
     * Get total payments amount in AED.
     */
    public function getTotalPaymentsAttribute(): float
    {
        // Handle both new 'completed' status and legacy 'active' status
        return $this->payments()->whereIn('status', ['completed', 'active'])->sum('amount');
    }

    /**
     * Get pending payments amount in AED.
     */
    public function getPendingPaymentsAttribute(): float
    {
        return $this->payments()->where('status', 'pending')->sum('amount');
    }

    /**
     * Get attendance rate percentage.
     */
    public function getAttendanceRateAttribute(): float
    {
        $totalSessions = $this->attendances()->count();
        if ($totalSessions === 0) return 0;

        $attendedSessions = $this->attendances()->where('status', 'present')->count();
        return round(($attendedSessions / $totalSessions) * 100, 1);
    }

    /**
     * Get uniform orders count.
     */
    public function getUniformOrdersCountAttribute(): int
    {
        return $this->uniforms()->count();
    }

    /**
     * Get days since student joined.
     */
    public function getDaysSinceJoinedAttribute(): int
    {
        return $this->join_date ? $this->join_date->diffInDays(now()) : 0;
    }

    /**
     * Get formatted join date.
     */
    public function getFormattedJoinDateAttribute(): string
    {
        return $this->join_date ? $this->join_date->format('M d, Y') : '';
    }

    /**
     * Get formatted birth date.
     */
    public function getFormattedBirthDateAttribute(): string
    {
        return $this->birth_date ? $this->birth_date->format('M d, Y') : '';
    }

    // ===== LOCALIZATION METHODS =====

    /**
     * Get localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        $locale = app()->getLocale();

        if ($locale === 'ar' && !empty($this->full_name_ar)) {
            return $this->full_name_ar;
        }

        return $this->full_name ?? '';
    }

    /**
     * Get localized first name based on current locale.
     */
    public function getLocalizedFirstNameAttribute(): string
    {
        $locale = app()->getLocale();

        if ($locale === 'ar' && !empty($this->first_name_ar)) {
            return $this->first_name_ar;
        }

        return $this->first_name ?? '';
    }

    /**
     * Get localized last name based on current locale.
     */
    public function getLocalizedLastNameAttribute(): string
    {
        $locale = app()->getLocale();

        if ($locale === 'ar' && !empty($this->last_name_ar)) {
            return $this->last_name_ar;
        }

        return $this->last_name ?? '';
    }

    /**
     * Get localized nationality based on current locale.
     */
    public function getLocalizedNationalityAttribute(): string
    {
        $locale = app()->getLocale();

        if ($locale === 'ar' && !empty($this->nationality_ar)) {
            return $this->nationality_ar;
        }

        return $this->nationality ?? '';
    }

    /**
     * Get localized address based on current locale.
     */
    public function getLocalizedAddressAttribute(): string
    {
        $locale = app()->getLocale();

        if ($locale === 'ar' && !empty($this->address_ar)) {
            return $this->address_ar;
        }

        return $this->address ?? '';
    }

    /**
     * Get localized notes based on current locale.
     */
    public function getLocalizedNotesAttribute(): string
    {
        $locale = app()->getLocale();

        if ($locale === 'ar' && !empty($this->notes_ar)) {
            return $this->notes_ar;
        }

        return $this->notes ?? '';
    }

    /**
     * Get localized join date based on current locale.
     */
    public function getLocalizedJoinDateAttribute(): string
    {
        if (!$this->join_date) return '';

        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return $this->join_date->format('d/m/Y');
        }

        return $this->join_date->format('M d, Y');
    }

    /**
     * Get localized birth date based on current locale.
     */
    public function getLocalizedBirthDateAttribute(): string
    {
        if (!$this->birth_date) return '';

        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return $this->birth_date->format('d/m/Y');
        }

        return $this->birth_date->format('M d, Y');
    }

    /**
     * Get Arabic formatted join date.
     */
    public function getArabicJoinDateAttribute(): string
    {
        if (!$this->join_date) return '';

        return $this->join_date->format('d/m/Y');
    }

    /**
     * Get Arabic formatted birth date.
     */
    public function getArabicBirthDateAttribute(): string
    {
        if (!$this->birth_date) return '';

        return $this->birth_date->format('d/m/Y');
    }

    // Query Scopes

    /**
     * Scope a query to only include active students.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive students.
     */
    public function scopeInactive(Builder $query): Builder
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope a query to only include suspended students.
     */
    public function scopeSuspended(Builder $query): Builder
    {
        return $query->where('status', 'suspended');
    }

    /**
     * Scope a query to search students by name, email, or phone.
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('full_name', 'like', "%{$search}%")
                ->orWhere('email', 'like', "%{$search}%")
                ->orWhere('phone', 'like', "%{$search}%")
                ->orWhere('nationality', 'like', "%{$search}%");
        });
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope a query to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope a query to filter by age range.
     */
    public function scopeByAgeRange(Builder $query, int $minAge, int $maxAge): Builder
    {
        $maxDate = now()->subYears($minAge)->format('Y-m-d');
        $minDate = now()->subYears($maxAge + 1)->format('Y-m-d');

        return $query->whereBetween('birth_date', [$minDate, $maxDate]);
    }

    /**
     * Scope a query to filter by join date range.
     */
    public function scopeByJoinDateRange(Builder $query, string $startDate, string $endDate): Builder
    {
        return $query->whereBetween('join_date', [$startDate, $endDate]);
    }

    // Utility Methods

    /**
     * Get comprehensive student statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_payments' => $this->total_payments,
            'pending_payments' => $this->pending_payments,
            'attendance_rate' => $this->attendance_rate,
            'uniform_orders' => $this->uniform_orders_count,
            'days_enrolled' => $this->days_since_joined,
            'age' => $this->age,
            'programs_count' => $this->programs()->count(),
        ];
    }

    /**
     * Get student's payment history with details.
     */
    public function getPaymentHistory()
    {
        return $this->payments()
            ->with(['academy', 'branch'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get student's attendance summary.
     */
    public function getAttendanceSummary()
    {
        $attendances = $this->attendances()->get();

        return [
            'total_sessions' => $attendances->count(),
            'present' => $attendances->where('status', 'present')->count(),
            'absent' => $attendances->where('status', 'absent')->count(),
            'late' => $attendances->where('status', 'late')->count(),
            'attendance_rate' => $this->attendance_rate,
        ];
    }

    /**
     * Get student's uniform orders with details.
     */
    public function getUniformOrders()
    {
        return $this->uniforms()
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($uniform) {
                return [
                    'id' => $uniform->id,
                    'item' => $uniform->item,
                    'size' => $uniform->size,
                    'quantity' => $uniform->quantity,
                    'amount' => $uniform->amount,
                    'status' => $uniform->status,
                    'order_date' => $uniform->created_at->format('M d, Y'),
                ];
            });
    }

    /**
     * Check if student has any pending payments.
     */
    public function hasPendingPayments(): bool
    {
        return $this->pending_payments > 0;
    }

    /**
     * Check if student is active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Toggle student status between active and inactive.
     */
    public function toggleStatus(): bool
    {
        $this->status = $this->status === 'active' ? 'inactive' : 'active';
        return $this->save();
    }

    /**
     * Validate UAE phone number format.
     */
    public static function validateUAEPhone(string $phone): bool
    {
        // UAE phone format: +971XXXXXXXXX (13 characters total)
        return preg_match('/^\+971[0-9]{9}$/', $phone);
    }

    // Profile Image Methods

    /**
     * Get the full URL for the student's profile image
     */
    public function getProfileImageUrlAttribute(): string
    {
        if ($this->profile_image) {
            return asset('storage/' . $this->profile_image);
        }

        // Return default avatar based on first letter of name
        return $this->getDefaultAvatarUrl();
    }

    /**
     * Get default avatar URL with student's initial
     */
    public function getDefaultAvatarUrl(): string
    {
        $initial = strtoupper(substr($this->localized_name, 0, 1));
        return "https://ui-avatars.com/api/?name={$initial}&size=200&background=dc2626&color=ffffff&font-size=0.6";
    }

    /**
     * Get the student's initials for avatar
     */
    public function getInitialsAttribute(): string
    {
        $name = $this->localized_name;
        $words = explode(' ', $name);

        if (count($words) >= 2) {
            return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
        }

        return strtoupper(substr($name, 0, 1));
    }

    /**
     * Check if student has a profile image
     */
    public function hasProfileImage(): bool
    {
        return !empty($this->profile_image) && file_exists(storage_path('app/public/' . $this->profile_image));
    }

    /**
     * Delete the student's profile image file
     */
    public function deleteProfileImage(): bool
    {
        if ($this->profile_image && file_exists(storage_path('app/public/' . $this->profile_image))) {
            unlink(storage_path('app/public/' . $this->profile_image));
            $this->profile_image = null;
            return $this->save();
        }
        return true;
    }
}
