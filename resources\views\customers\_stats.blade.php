<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Customers -->
    <a href="{{ route('customers.index') }}" class="bank-card bank-card-hover cursor-pointer">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Total Customers</p>
                    <p class="text-3xl font-bold text-charcoal-black">{{ number_format($stats['total_customers']) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        All registered customers
                    </p>
                </div>
                <div
                    class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </a>

    <!-- Active Customers -->
    <a href="{{ route('customers.index', ['status' => 'active']) }}" class="bank-card bank-card-hover cursor-pointer">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Active Customers</p>
                    <p class="text-3xl font-bold text-success-green">{{ number_format($stats['active_customers']) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Currently active
                    </p>
                </div>
                <div
                    class="w-12 h-12 bg-gradient-to-br from-success-green to-green-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </a>

    <!-- Total Reservations -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Total Reservations</p>
                    <p class="text-3xl font-bold text-leaders-red">{{ number_format($stats['total_reservations']) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                        All bookings made
                    </p>
                </div>
                <div
                    class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Total Revenue -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Total Revenue</p>
                    <p class="text-3xl font-bold text-warning-orange">AED
                        {{ number_format($stats['total_revenue'], 2) }}</p>
                    <p class="text-sm text-success-green">
                        <svg class="w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                            </path>
                        </svg>
                        From all customers
                    </p>
                </div>
                <div
                    class="w-12 h-12 bg-gradient-to-br from-warning-orange to-orange-600 rounded-full flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics Row -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Individual Customers -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Individual Customers</p>
                    <p class="text-2xl font-bold text-blue-600">{{ number_format($stats['individual_customers']) }}</p>
                    <p class="text-sm text-blue-600">Personal accounts</p>
                </div>
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Corporate Customers -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Corporate Customers</p>
                    <p class="text-2xl font-bold text-purple-600">{{ number_format($stats['corporate_customers']) }}
                    </p>
                    <p class="text-sm text-purple-600">Business accounts</p>
                </div>
                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- VIP Customers -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">VIP Customers</p>
                    <p class="text-2xl font-bold text-yellow-600">{{ number_format($stats['vip_customers']) }}</p>
                    <p class="text-sm text-yellow-600">Premium members</p>
                </div>
                <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Blocked Customers -->
    <div class="bank-card bank-card-hover">
        <div class="bank-card-body">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-dark-gray">Blocked Customers</p>
                    <p class="text-2xl font-bold text-red-600">{{ number_format($stats['blocked_customers']) }}</p>
                    <p class="text-sm text-red-600">Restricted access</p>
                </div>
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                    <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728">
                        </path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>
