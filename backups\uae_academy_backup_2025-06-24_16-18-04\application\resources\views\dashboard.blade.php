@extends('layouts.dashboard')

@section('title', 'Dashboard - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black" data-trans-key="welcome_back" data-context="dashboard">
                    {{ __('dashboard.welcome_back') }}</h1>
                <p class="text-lg text-dark-gray">{{ Auth::user()->name }}</p>
                <span class="badge-bank badge-success">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ ucfirst(Auth::user()->role ?? 'User') }}
                </span>
            </div>
        </div>
        <div class="text-right">
            <div class="premium-date-section"
                style="background: linear-gradient(135deg, var(--leaders-red) 0%, var(--leaders-deep-red) 100%); border-radius: 16px; padding: 1.75rem 2.25rem; margin: 0.75rem; box-shadow: 0 10px 30px rgba(229, 62, 62, 0.2), 0 6px 15px rgba(0, 0, 0, 0.1); border: 1px solid rgba(255, 255, 255, 0.15); backdrop-filter: blur(12px); transform: translateZ(0); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); max-width: 320px; margin-left: auto; position: relative; overflow: hidden;">
                <!-- Premium overlay effect -->
                <div class="absolute inset-0 bg-gradient-to-br from-white/15 to-transparent pointer-events-none"></div>
                <div class="absolute top-0 right-0 w-20 h-20 bg-white/5 rounded-full -translate-y-10 translate-x-10"></div>

                <div class="relative z-10">
                    <div class="text-white font-bold mb-2 flex items-center justify-center">
                        <svg class="w-5 h-5 mr-3 text-white drop-shadow-lg" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                        <span class="text-lg tracking-wide font-semibold">Today</span>
                    </div>
                    <div class="text-white/95 font-medium text-center mb-1 text-base">{{ now()->format('l, F j, Y') }}</div>
                    <div class="text-white/90 text-center font-medium tracking-wider">{{ now()->format('g:i A') }}</div>
                </div>
            </div>
        </div>
    @endsection

    @section('content')
        <style>
            /* Emergency Quick Actions fix - ONLY text color, preserve red background */
            .bg-leaders-red,
            a.bg-leaders-red,
            .btn-bank.bg-leaders-red {
                color: white !important;
                /* Keep the red background intact */
            }

            /* Child elements get white text but transparent background */
            .bg-leaders-red *:not(svg),
            a.bg-leaders-red *:not(svg),
            .btn-bank.bg-leaders-red *:not(svg) {
                color: white !important;
                background: transparent !important;
            }

            .bg-leaders-red svg,
            a.bg-leaders-red svg,
            .btn-bank.bg-leaders-red svg {
                color: white !important;
                fill: white !important;
                stroke: white !important;
            }
        </style>

        <div class="space-y-6">
            <!-- Quick Actions Section -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title" data-trans-key="Quick Actions" data-context="dashboard">
                            {{ __('dashboard.Quick Actions') }}</h3>
                        <p class="bank-card-subtitle" data-trans-key="quick_stats" data-context="dashboard">
                            {{ __('dashboard.quick_stats') }}</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        @can('create', App\Models\Student::class)
                            <a href="{{ route('students.create') }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                <span data-trans-key="Add Student"
                                    data-context="dashboard">{{ __('dashboard.Add Student') }}</span>
                            </a>
                        @endcan

                        @can('create', App\Models\Payment::class)
                            <a href="{{ route('payments.create') }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                                    </path>
                                </svg>
                                <span data-trans-key="Add Payment"
                                    data-context="dashboard">{{ __('dashboard.Add Payment') }}</span>
                            </a>
                        @endcan

                        @if (in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager']))
                            <a href="{{ route('uniforms.create') }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                </svg>
                                <span data-trans-key="Order Uniform"
                                    data-context="dashboard">{{ __('dashboard.Order Uniform') }}</span>
                            </a>
                        @endif

                        @if (in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager']))
                            <a href="{{ route('inventory.index') }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                                <span>{{ __('Manage Inventory') }}</span>
                            </a>
                        @endif

                        @if (in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager']))
                            <a href="{{ route('venues.index') }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                    </path>
                                </svg>
                                <span>{{ __('Manage Venues') }}</span>
                            </a>
                        @endif

                        @if (in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager']))
                            <a href="{{ route('reservations.index') }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                                <span>{{ __('Field Reservations') }}</span>
                            </a>
                        @endif

                        @if (in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager']))
                            <a href="{{ route('customers.index') }}"
                                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z">
                                    </path>
                                </svg>
                                <span>{{ __('Manage Customers') }}</span>
                            </a>
                        @endif

                        <a href="{{ route('reports.index') }}"
                            class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                            <span data-trans-key="view_details"
                                data-context="dashboard">{{ __('dashboard.view_details') }}</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Bank-Style Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-5 gap-6">
                @if (Auth::user()->role === 'admin' || Auth::user()->role === 'branch_manager')
                    <!-- Total Branches -->
                    <a href="{{ route('branches.index') }}"
                        class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                        style="animation-delay: 0.1s;">
                        <div class="stats-icon">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                </path>
                            </svg>
                        </div>
                        <div class="stats-value">{{ \App\Models\Branch::count() ?? 0 }}</div>
                        <div class="stats-label" data-trans-key="total_branches" data-context="dashboard">
                            {{ __('dashboard.total_branches') }}</div>
                        <div class="stats-change positive">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                            <span data-trans-key="active_growing"
                                data-context="dashboard">{{ __('dashboard.active_growing') }}</span>
                        </div>
                    </a>
                @endif

                <!-- Total Academies -->
                <a href="{{ route('academies.index') }}"
                    class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                    style="animation-delay: 0.2s;">
                    <div class="stats-icon bg-gradient-to-br from-success-green to-green-600">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 14l9-5-9-5-9 5 9 5z">
                            </path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value">
                        @if (Auth::user()->role === 'admin')
                            {{ \App\Models\Academy::count() ?? 0 }}
                        @elseif(Auth::user()->role === 'branch_manager')
                            {{ \App\Models\Academy::where('branch_id', Auth::user()->branch_id)->count() ?? 0 }}
                        @else
                            {{ Auth::user()->academy_id ? 1 : 0 }}
                        @endif
                    </div>
                    <div class="stats-label" data-trans-key="total_academies" data-context="dashboard">
                        {{ __('dashboard.total_academies') }}</div>
                    <div class="stats-change positive">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg>
                        <span data-trans-key="excellence_sports"
                            data-context="dashboard">{{ __('dashboard.excellence_sports') }}</span>
                    </div>
                </a>

                <!-- Total Students -->
                <a href="{{ route('students.index') }}"
                    class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                    style="animation-delay: 0.3s;">
                    <div class="stats-icon bg-gradient-to-br from-gold-yellow to-yellow-600">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value">
                        @if (Auth::user()->role === 'admin')
                            {{ \App\Models\Student::count() ?? 0 }}
                        @elseif(Auth::user()->role === 'branch_manager')
                            {{ \App\Models\Student::where('branch_id', Auth::user()->branch_id)->count() ?? 0 }}
                        @else
                            {{ \App\Models\Student::where('academy_id', Auth::user()->academy_id)->count() ?? 0 }}
                        @endif
                    </div>
                    <div class="stats-label" data-trans-key="total_students" data-context="dashboard">
                        {{ __('dashboard.total_students') }}</div>
                    <div class="stats-change positive">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
                            </path>
                        </svg>
                        <span data-trans-key="future_champions"
                            data-context="dashboard">{{ __('dashboard.future_champions') }}</span>
                    </div>
                </a>

                <!-- Monthly Revenue -->
                <a href="{{ route('payments.index') }}"
                    class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                    style="animation-delay: 0.4s;">
                    <div class="stats-icon bg-gradient-to-br from-info-blue to-blue-600">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                            </path>
                        </svg>
                    </div>
                    <div class="stats-value text-leaders-red">
                        @php
                            $query = \App\Models\Payment::whereMonth('payment_date', now()->month)->whereYear(
                                'payment_date',
                                now()->year,
                            );

                            if (Auth::user()->role === 'branch_manager') {
                                $query->where('branch_id', Auth::user()->branch_id);
                            } elseif (Auth::user()->role === 'academy_manager') {
                                $query->where('academy_id', Auth::user()->academy_id);
                            }

                            $monthlyRevenue = $query->sum('amount') ?? 0;
                        @endphp
                        {{ number_format($monthlyRevenue, 0) }}
                    </div>
                    <div class="stats-label" data-trans-key="monthly_revenue" data-context="dashboard">
                        {{ __('dashboard.monthly_revenue') }}</div>
                    <div class="stats-change positive">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                        </svg>
                        {{ app()->getLocale() === 'ar' ? now()->format('m/Y') : now()->format('F Y') }}
                    </div>
                </a>

                <!-- Inventory Status -->
                @if (in_array(Auth::user()->role, ['admin', 'branch_manager', 'academy_manager']))
                    <a href="{{ route('inventory.index') }}"
                        class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                        style="animation-delay: 0.5s;">
                        <div class="stats-icon bg-gradient-to-br from-purple-500 to-purple-600">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                        <div class="stats-value">
                            @php
                                $inventoryQuery = \App\Models\UniformInventory::query();

                                if (Auth::user()->role === 'branch_manager') {
                                    $inventoryQuery->where('branch_id', Auth::user()->branch_id);
                                } elseif (Auth::user()->role === 'academy_manager') {
                                    $inventoryQuery->where('academy_id', Auth::user()->academy_id);
                                }

                                $totalInventoryItems = $inventoryQuery->count() ?? 0;
                            @endphp
                            {{ $totalInventoryItems }}
                        </div>
                        <div class="stats-label">{{ __('Inventory Items') }}</div>
                        <div class="stats-change {{ $totalInventoryItems > 0 ? 'positive' : 'neutral' }}">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ __('Stock Management') }}</span>
                        </div>
                    </a>
                @endif
            </div>

            <!-- Bank-Style Content Cards -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Students -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <div>
                            <h3 class="bank-card-title" data-trans-key="recent_students" data-context="dashboard">
                                {{ __('dashboard.recent_students') }}</h3>
                            <p class="bank-card-subtitle" data-trans-key="new_registrations" data-context="dashboard">
                                {{ __('dashboard.new_registrations') }}</p>
                        </div>
                        <a href="{{ route('students.index') }}" class="btn-bank" data-trans-key="view_all"
                            data-context="dashboard">{{ __('dashboard.view_all') }}</a>
                    </div>
                    <div class="bank-card-body">
                        @php
                            $recentStudentsQuery = \App\Models\Student::with(['branch', 'academy'])
                                ->orderBy('created_at', 'desc')
                                ->limit(5);

                            if (Auth::user()->role === 'branch_manager') {
                                $recentStudentsQuery->where('branch_id', Auth::user()->branch_id);
                            } elseif (Auth::user()->role === 'academy_manager') {
                                $recentStudentsQuery->where('academy_id', Auth::user()->academy_id);
                            }

                            $recentStudents = $recentStudentsQuery->get();
                        @endphp

                        @if ($recentStudents->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="table-bank">
                                    <thead>
                                        <tr>
                                            <th data-trans-key="name" data-context="common">{{ __('common.name') }}</th>
                                            <th data-trans-key="academy" data-context="common">{{ __('common.academy') }}
                                            </th>
                                            <th data-trans-key="join_date" data-context="common">
                                                {{ __('common.join_date') }}
                                            </th>
                                            <th data-trans-key="status" data-context="common">{{ __('common.status') }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($recentStudents as $student)
                                            <tr>
                                                <td class="font-semibold">
                                                    <a href="{{ route('students.show', $student->id) }}"
                                                        class="text-leaders-red hover:text-leaders-deep-red transition-colors duration-200">
                                                        {{ $student->localized_name ?: $student->full_name ?? __('common.not_available') }}
                                                    </a>
                                                </td>
                                                <td>{{ $student->academy->name ?? __('common.not_available') }}</td>
                                                <td>{{ $student->localized_join_date ?: __('common.not_available') }}
                                                </td>
                                                <td>
                                                    <span
                                                        class="badge-bank {{ ($student->status ?? 'inactive') === 'active' ? 'badge-success' : 'badge-neutral' }}"
                                                        data-trans-key="{{ strtolower($student->status ?? 'inactive') }}"
                                                        data-context="common">
                                                        {{ __('common.' . strtolower($student->status ?? 'inactive')) }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 mx-auto text-medium-gray mb-4" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                                    </path>
                                </svg>
                                <p class="text-dark-gray" data-trans-key="no_students_found" data-context="common">
                                    {{ __('common.no_students_found') }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Recent Payments -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <div>
                            <h3 class="bank-card-title" data-trans-key="recent_payments" data-context="dashboard">
                                {{ __('dashboard.recent_payments') }}</h3>
                            <p class="bank-card-subtitle" data-trans-key="recent_payments" data-context="dashboard">
                                {{ __('dashboard.recent_payments') }}</p>
                        </div>
                        <a href="{{ route('payments.index') }}" class="btn-bank" data-trans-key="view_all"
                            data-context="dashboard">{{ __('dashboard.view_all') }}</a>
                    </div>
                    <div class="bank-card-body">
                        @php
                            $recentPaymentsQuery = \App\Models\Payment::with(['student', 'academy'])
                                ->orderBy('created_at', 'desc')
                                ->limit(5);

                            if (Auth::user()->role === 'branch_manager') {
                                $recentPaymentsQuery->where('branch_id', Auth::user()->branch_id);
                            } elseif (Auth::user()->role === 'academy_manager') {
                                $recentPaymentsQuery->where('academy_id', Auth::user()->academy_id);
                            }

                            $recentPayments = $recentPaymentsQuery->get();
                        @endphp

                        @if ($recentPayments->count() > 0)
                            <div class="overflow-x-auto">
                                <table class="table-bank">
                                    <thead>
                                        <tr>
                                            <th data-trans-key="student" data-context="common">{{ __('common.student') }}
                                            </th>
                                            <th data-trans-key="amount" data-context="common">{{ __('common.amount') }}
                                            </th>
                                            <th data-trans-key="date" data-context="common">{{ __('common.date') }}</th>
                                            <th data-trans-key="status" data-context="common">{{ __('common.status') }}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($recentPayments as $payment)
                                            <tr>
                                                <td class="font-semibold">
                                                    <a href="{{ route('students.show', $payment->student->id) }}"
                                                        class="text-leaders-red hover:text-leaders-deep-red transition-colors duration-200">
                                                        {{ $payment->student->localized_name ?: $payment->student->full_name ?? __('common.not_available') }}
                                                    </a>
                                                </td>
                                                <td class="font-bold text-leaders-red">
                                                    <a href="{{ route('payments.show', $payment->id) }}"
                                                        class="hover:text-leaders-deep-red transition-colors duration-200">
                                                        {{ number_format($payment->amount ?? 0, 0) }}
                                                        {{ app()->getLocale() === 'ar' ? __('common.aed') : 'AED' }}
                                                    </a>
                                                </td>
                                                <td>{{ $payment->payment_date ? \App\Services\DateLocalizationService::formatTableDate($payment->payment_date) : __('common.not_available') }}
                                                </td>
                                                <td>
                                                    @php
                                                        $status = $payment->status ?? 'pending';
                                                        $badgeClass = match ($status) {
                                                            'active' => 'badge-success',
                                                            'expired' => 'badge-error',
                                                            'pending' => 'badge-warning',
                                                            default => 'badge-neutral',
                                                        };
                                                    @endphp
                                                    <span class="badge-bank {{ $badgeClass }}"
                                                        data-trans-key="{{ strtolower($status) }}" data-context="common">
                                                        {{ __('common.' . strtolower($status)) }}
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 mx-auto text-medium-gray mb-4" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                                    </path>
                                </svg>
                                <p class="text-dark-gray" data-trans-key="no_payments_found" data-context="common">
                                    {{ __('common.no_payments_found') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>


        </div>
    @endsection

    @push('scripts')
        <script>
            // Modal functionality placeholder
            function openModal(action, type, id = null) {
                console.log(`Opening ${action} modal for ${type}`, id);
                // This will be implemented when modal system is created
                const message = window.currentLocale === 'ar' ?
                    `سيتم تنفيذ نافذة ${type} قريباً!` :
                    `${action.charAt(0).toUpperCase() + action.slice(1)} ${type} modal will be implemented soon!`;
                alert(message);
            }

            // Currency formatting for AED
            function formatCurrency(amount) {
                if (isNaN(amount) || amount === '') return '';
                const formattedAmount = parseFloat(amount).toLocaleString('en-US', {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2
                });
                const currency = window.currentLocale === 'ar' ? 'درهم' : 'AED';
                return `${formattedAmount} ${currency}`;
            }

            // Initialize dashboard
            document.addEventListener('DOMContentLoaded', function() {
                // Add any dashboard-specific initialization here
                const message = window.currentLocale === 'ar' ?
                    'تم تحميل لوحة التحكم بنجاح' :
                    'Bank-style dashboard loaded successfully';
                console.log(message);
            });
        </script>
    @endpush
