<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Program;
use App\Models\Academy;

class ProgramSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all academies
        $academies = Academy::all();

        if ($academies->isEmpty()) {
            $this->command->warn('No academies found. Please run AcademySeeder first.');
            return;
        }

        $programs = [
            [
                'name' => 'Football Training Program',
                'description' => 'Comprehensive football training program for beginners and intermediate players. Focus on basic skills, teamwork, and physical fitness.',
                'days' => ['MON', 'WED', 'FRI'],
                'classes' => 12,
                'price' => 1200.00,
                'start_time' => '16:00',
                'end_time' => '18:00',
                'max_students' => 20,
                'status' => true,
            ],
            [
                'name' => 'Basketball Skills Development',
                'description' => 'Intensive basketball program covering shooting, dribbling, passing, and game strategy. Suitable for all skill levels.',
                'days' => ['TUE', 'THU', 'SAT'],
                'classes' => 10,
                'price' => 950.00,
                'start_time' => '17:00',
                'end_time' => '19:00',
                'max_students' => 15,
                'status' => true,
            ],
            [
                'name' => 'Swimming Lessons',
                'description' => 'Learn to swim or improve your swimming technique with certified instructors. All ages welcome.',
                'days' => ['MON', 'TUE', 'WED', 'THU', 'FRI'],
                'classes' => 16,
                'price' => 1500.00,
                'start_time' => '15:00',
                'end_time' => '16:00',
                'max_students' => 8,
                'status' => true,
            ],
            [
                'name' => 'Tennis Academy',
                'description' => 'Professional tennis coaching for beginners to advanced players. Individual and group sessions available.',
                'days' => ['SAT', 'SUN'],
                'classes' => 8,
                'price' => 800.00,
                'start_time' => '09:00',
                'end_time' => '11:00',
                'max_students' => 12,
                'status' => true,
            ],
            [
                'name' => 'Martial Arts Training',
                'description' => 'Traditional martial arts training focusing on discipline, self-defense, and physical conditioning.',
                'days' => ['TUE', 'THU'],
                'classes' => 15,
                'price' => 1100.00,
                'start_time' => '18:00',
                'end_time' => '19:30',
                'max_students' => 25,
                'status' => true,
            ],
            [
                'name' => 'Volleyball Training',
                'description' => 'Team-based volleyball training program emphasizing coordination, teamwork, and competitive play.',
                'days' => ['WED', 'FRI'],
                'classes' => 10,
                'price' => 750.00,
                'start_time' => '16:30',
                'end_time' => '18:30',
                'max_students' => 18,
                'status' => true,
            ],
            [
                'name' => 'Athletics & Track',
                'description' => 'Track and field athletics program covering running, jumping, and throwing events.',
                'days' => ['MON', 'WED', 'SAT'],
                'classes' => 12,
                'price' => 900.00,
                'start_time' => '17:00',
                'end_time' => '18:30',
                'max_students' => 30,
                'status' => true,
            ],
            [
                'name' => 'Gymnastics Program',
                'description' => 'Artistic gymnastics program for children and teenagers. Focus on flexibility, strength, and coordination.',
                'days' => ['TUE', 'THU', 'SAT'],
                'classes' => 14,
                'price' => 1300.00,
                'start_time' => '15:30',
                'end_time' => '17:00',
                'max_students' => 10,
                'status' => true,
            ],
            [
                'name' => 'Cricket Academy',
                'description' => 'Cricket training program covering batting, bowling, fielding, and match tactics.',
                'days' => ['FRI', 'SAT', 'SUN'],
                'classes' => 12,
                'price' => 1000.00,
                'start_time' => '14:00',
                'end_time' => '16:00',
                'max_students' => 22,
                'status' => true,
            ],
            [
                'name' => 'Badminton Training',
                'description' => 'Badminton skills development program for all ages. Learn proper techniques and game strategies.',
                'days' => ['MON', 'THU'],
                'classes' => 8,
                'price' => 600.00,
                'start_time' => '18:30',
                'end_time' => '20:00',
                'max_students' => 16,
                'status' => false, // Inactive program for testing
            ],
        ];

        foreach ($programs as $programData) {
            // Randomly assign to an academy
            $academy = $academies->random();
            
            Program::create([
                'academy_id' => $academy->id,
                'name' => $programData['name'],
                'description' => $programData['description'],
                'days' => $programData['days'],
                'classes' => $programData['classes'],
                'price' => $programData['price'],
                'currency' => 'AED',
                'start_time' => $programData['start_time'],
                'end_time' => $programData['end_time'],
                'max_students' => $programData['max_students'],
                'status' => $programData['status'],
            ]);
        }

        $this->command->info('Created ' . count($programs) . ' sample programs.');
    }
}
