// UAE English Sports Academy - Advanced Language Switcher
// Premium language switching with smooth transitions and backend integration

/**
 * Language Switcher Class
 * Handles all language switching functionality with backend integration
 */
class LanguageSwitcher {
    constructor() {
        this.currentLocale = this.getCurrentLocale();
        this.currentDirection = this.getCurrentDirection();
        this.isLoading = false;
        this.apiEndpoint = '/api/language/switch';
        this.csrfToken = this.getCSRFToken();
        
        this.languages = {
            'en': {
                code: 'en',
                name: 'English',
                native: 'English',
                flag: '🇺🇸',
                direction: 'ltr'
            },
            'ar': {
                code: 'ar',
                name: 'Arabic', 
                native: 'العربية',
                flag: '🇦🇪',
                direction: 'rtl'
            }
        };

        this.init();
    }

    init() {
        this.bindEvents();
        this.updateUI();
        this.applyStoredPreferences();
    }

    getCurrentLocale() {
        return document.documentElement.getAttribute('lang') || 'en';
    }

    getCurrentDirection() {
        return document.documentElement.getAttribute('dir') || 'ltr';
    }

    getCSRFToken() {
        const metaTag = document.querySelector('meta[name="csrf-token"]');
        return metaTag ? metaTag.getAttribute('content') : '';
    }

    bindEvents() {
        // Listen for manual language switch events
        document.addEventListener('click', (event) => {
            const switcher = event.target.closest('[data-language-switch]');
            if (switcher) {
                event.preventDefault();
                const locale = switcher.getAttribute('data-language-switch');
                this.switchLanguage(locale);
            }
        });

        // Listen for keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            // Alt + L for language switch
            if (event.altKey && event.key === 'l') {
                event.preventDefault();
                this.toggleLanguage();
            }
        });

        // Listen for storage changes (multi-tab sync)
        window.addEventListener('storage', (event) => {
            if (event.key === 'language' || event.key === 'direction') {
                this.syncWithStorage();
            }
        });
    }

    /**
     * Switch to specific language
     */
    async switchLanguage(locale) {
        if (this.isLoading || locale === this.currentLocale) {
            return false;
        }

        if (!this.languages[locale]) {
            console.error(`Unsupported locale: ${locale}`);
            return false;
        }

        this.setLoadingState(true);

        try {
            const response = await this.makeAPIRequest(locale);
            
            if (response.success) {
                await this.handleSuccessfulSwitch(response);
                return true;
            } else {
                this.handleSwitchError(response.message || 'Failed to switch language');
                return false;
            }
        } catch (error) {
            console.error('Language switch error:', error);
            this.handleSwitchError('Network error occurred');
            return false;
        } finally {
            this.setLoadingState(false);
        }
    }

    /**
     * Toggle between languages
     */
    async toggleLanguage() {
        const newLocale = this.currentLocale === 'en' ? 'ar' : 'en';
        return await this.switchLanguage(newLocale);
    }

    /**
     * Make API request to switch language
     */
    async makeAPIRequest(locale) {
        const response = await fetch(this.apiEndpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': this.csrfToken,
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ locale: locale })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * Handle successful language switch
     */
    async handleSuccessfulSwitch(response) {
        // Update internal state
        this.currentLocale = response.locale;
        this.currentDirection = response.direction;

        // Update document attributes
        this.updateDocumentAttributes(response);

        // Store preferences
        this.storePreferences(response);

        // Update UI immediately
        this.updateUI();

        // Dispatch custom event
        this.dispatchLanguageChangeEvent(response);

        // Show success notification
        this.showNotification(response.message || 'Language switched successfully', 'success');

        // Reload page after short delay for translations
        await this.delayedReload();
    }

    /**
     * Update document attributes
     */
    updateDocumentAttributes(data) {
        document.documentElement.setAttribute('lang', data.locale);
        document.documentElement.setAttribute('dir', data.direction);
        
        if (data.direction === 'rtl') {
            document.body.classList.add('arabic-text');
        } else {
            document.body.classList.remove('arabic-text');
        }
    }

    /**
     * Store language preferences
     */
    storePreferences(data) {
        localStorage.setItem('language', data.locale.toUpperCase());
        localStorage.setItem('direction', data.direction);
        localStorage.setItem('locale', data.locale);
        
        // Store timestamp for cache management
        localStorage.setItem('language_switched_at', Date.now().toString());
    }

    /**
     * Apply stored preferences
     */
    applyStoredPreferences() {
        const storedLocale = localStorage.getItem('locale');
        const storedDirection = localStorage.getItem('direction');

        if (storedLocale && storedDirection) {
            this.currentLocale = storedLocale;
            this.currentDirection = storedDirection;
            this.updateDocumentAttributes({
                locale: storedLocale,
                direction: storedDirection
            });
        }
    }

    /**
     * Sync with localStorage changes
     */
    syncWithStorage() {
        const storedLocale = localStorage.getItem('locale');
        const storedDirection = localStorage.getItem('direction');

        if (storedLocale !== this.currentLocale || storedDirection !== this.currentDirection) {
            this.currentLocale = storedLocale;
            this.currentDirection = storedDirection;
            this.updateUI();
            window.location.reload();
        }
    }

    /**
     * Update UI elements
     */
    updateUI() {
        const switchers = document.querySelectorAll('.language-switcher-text');
        const currentLang = this.languages[this.currentLocale];
        const otherLang = this.languages[this.currentLocale === 'en' ? 'ar' : 'en'];

        switchers.forEach(switcher => {
            if (switcher.closest('[data-show-current]')) {
                switcher.textContent = `${currentLang.flag} ${currentLang.native}`;
            } else {
                switcher.textContent = `${otherLang.flag} ${otherLang.code === 'ar' ? 'عربي' : 'EN'}`;
            }
        });

        // Update active states
        const menuItems = document.querySelectorAll('.language-switcher-menu-item');
        menuItems.forEach(item => {
            const locale = item.getAttribute('data-locale');
            if (locale === this.currentLocale) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * Set loading state
     */
    setLoadingState(loading) {
        this.isLoading = loading;
        
        const switchers = document.querySelectorAll('.language-switcher');
        switchers.forEach(switcher => {
            if (loading) {
                switcher.classList.add('loading');
                switcher.style.pointerEvents = 'none';
            } else {
                switcher.classList.remove('loading');
                switcher.style.pointerEvents = '';
            }
        });
    }

    /**
     * Handle switch errors
     */
    handleSwitchError(message) {
        console.error('Language switch failed:', message);
        this.showNotification(message, 'error');
    }

    /**
     * Dispatch language change event
     */
    dispatchLanguageChangeEvent(data) {
        const event = new CustomEvent('languageChanged', {
            detail: {
                locale: data.locale,
                direction: data.direction,
                message: data.message,
                timestamp: Date.now()
            }
        });
        
        window.dispatchEvent(event);
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existing = document.querySelectorAll('.language-notification');
        existing.forEach(notification => notification.remove());

        // Create notification
        const notification = document.createElement('div');
        notification.className = `language-notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            ${this.currentDirection === 'rtl' ? 'left' : 'right'}: 20px;
            background: ${type === 'success' ? '#38A169' : type === 'error' ? '#E53E3E' : '#3182CE'};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            font-size: 14px;
            font-weight: 500;
            transform: translateX(${this.currentDirection === 'rtl' ? '-' : ''}100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            font-family: var(--font-family-primary);
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Remove after delay
        setTimeout(() => {
            notification.style.transform = `translateX(${this.currentDirection === 'rtl' ? '-' : ''}100%)`;
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Delayed page reload
     */
    async delayedReload(delay = 500) {
        return new Promise((resolve) => {
            setTimeout(() => {
                window.location.reload();
                resolve();
            }, delay);
        });
    }

    /**
     * Get language info
     */
    getLanguageInfo(locale = null) {
        locale = locale || this.currentLocale;
        return this.languages[locale] || null;
    }

    /**
     * Check if locale is supported
     */
    isSupported(locale) {
        return !!this.languages[locale];
    }

    /**
     * Get available languages
     */
    getAvailableLanguages() {
        return Object.values(this.languages);
    }
}

// Initialize Language Switcher
window.LanguageSwitcher = new LanguageSwitcher();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LanguageSwitcher;
}
