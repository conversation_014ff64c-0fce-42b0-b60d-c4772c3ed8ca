<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'role' => \App\Http\Middleware\RoleMiddleware::class,
            'localization' => \App\Http\Middleware\LocalizationMiddleware::class,
            'share.translations' => \App\Http\Middleware\ShareTranslations::class,
        ]);

        // Add localization and translation sharing middleware to web group
        $middleware->web(append: [
            \App\Http\Middleware\LocalizationMiddleware::class,
            \App\Http\Middleware\ShareTranslations::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
