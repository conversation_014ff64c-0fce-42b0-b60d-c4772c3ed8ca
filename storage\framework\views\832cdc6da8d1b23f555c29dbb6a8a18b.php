<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 mb-6">
    <!-- Total Academies -->
    <a href="<?php echo e(route('academies.index')); ?>"
        class="bank-card bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-xl transition-all duration-300 cursor-pointer">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-blue-700 uppercase tracking-wide">Total Academies</p>
                <p class="text-3xl font-bold text-blue-900"><?php echo e($stats['total_academies'] ?? 0); ?></p>
                <div class="flex items-center mt-2">
                    <svg class="w-4 h-4 text-blue-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    <span class="text-sm text-blue-600 font-medium">All Academies</span>
                </div>
            </div>
            <div
                class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z">
                    </path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                    </path>
                </svg>
            </div>
        </div>
    </a>

    <!-- Active Academies -->
    <a href="<?php echo e(route('academies.index', ['status' => 'active'])); ?>"
        class="bank-card bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-xl transition-all duration-300 cursor-pointer">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-green-700 uppercase tracking-wide">Active Academies</p>
                <p class="text-3xl font-bold text-green-900"><?php echo e($stats['active_academies'] ?? 0); ?></p>
                <div class="flex items-center mt-2">
                    <svg class="w-4 h-4 text-green-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm text-green-600 font-medium">Currently Active</span>
                </div>
            </div>
            <div
                class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </a>

    <!-- Total Students -->
    <a href="<?php echo e(route('students.index')); ?>"
        class="bank-card bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-xl transition-all duration-300 cursor-pointer">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-purple-700 uppercase tracking-wide">Total Students</p>
                <p class="text-3xl font-bold text-purple-900"><?php echo e($stats['total_students'] ?? 0); ?></p>
                <div class="flex items-center mt-2">
                    <svg class="w-4 h-4 text-purple-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                        </path>
                    </svg>
                    <span class="text-sm text-purple-600 font-medium">Enrolled Students</span>
                </div>
            </div>
            <div
                class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z">
                    </path>
                </svg>
            </div>
        </div>
    </a>

    <!-- Total Programs -->
    <a href="<?php echo e(route('programs.index')); ?>"
        class="bank-card bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-xl transition-all duration-300 cursor-pointer">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-orange-700 uppercase tracking-wide">Total Programs</p>
                <p class="text-3xl font-bold text-orange-900"><?php echo e($stats['total_programs'] ?? 0); ?></p>
                <div class="flex items-center mt-2">
                    <svg class="w-4 h-4 text-orange-600 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                        </path>
                    </svg>
                    <span class="text-sm text-orange-600 font-medium">Available Programs</span>
                </div>
            </div>
            <div
                class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                    </path>
                </svg>
            </div>
        </div>
    </a>
</div>

<!-- Quick Stats Row -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <!-- Revenue Overview -->
    <a href="<?php echo e(route('payments.index')); ?>"
        class="bank-card bg-gradient-to-br from-emerald-50 to-emerald-100 border-emerald-200 cursor-pointer">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-emerald-700 uppercase tracking-wide">Total Revenue</p>
                <p class="text-2xl font-bold text-emerald-900">
                    AED <?php echo e(number_format($stats['total_revenue'] ?? 0, 2)); ?>

                </p>
                <p class="text-sm text-emerald-600 mt-1">From completed payments</p>
            </div>
            <div
                class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                    </path>
                </svg>
            </div>
        </div>
    </a>

    <!-- Pending Payments -->
    <a href="<?php echo e(route('payments.index', ['status' => 'pending'])); ?>"
        class="bank-card bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200 cursor-pointer">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-amber-700 uppercase tracking-wide">Pending Payments</p>
                <p class="text-2xl font-bold text-amber-900">
                    AED <?php echo e(number_format($stats['pending_payments'] ?? 0, 2)); ?>

                </p>
                <p class="text-sm text-amber-600 mt-1">Awaiting collection</p>
            </div>
            <div
                class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
        </div>
    </a>
</div>
<?php /**PATH D:\xampp\htdocs\erp-login2\resources\views/academies/_stats.blade.php ENDPATH**/ ?>