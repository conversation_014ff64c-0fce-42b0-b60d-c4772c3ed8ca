<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Academy Export - UAE English Sports Academy</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #E53E3E;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #E53E3E;
            margin: 0;
            font-size: 24px;
        }
        
        .header p {
            margin: 5px 0;
            color: #666;
        }
        
        .export-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #E53E3E;
        }
        
        .export-info h3 {
            margin: 0 0 10px 0;
            color: #E53E3E;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        
        .stat-card h4 {
            margin: 0 0 5px 0;
            color: #E53E3E;
            font-size: 18px;
        }
        
        .stat-card p {
            margin: 0;
            color: #666;
            font-size: 12px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        
        .table th {
            background-color: #E53E3E;
            color: white;
            font-weight: bold;
        }
        
        .table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            font-weight: bold;
        }
        
        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }
        
        .badge-neutral {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 15px;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 15px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>UAE English Sports Academy</h1>
        <p>Academy Management Report</p>
        <p>Generated on {{ now()->format('F d, Y \a\t H:i') }}</p>
    </div>

    <div class="export-info">
        <h3>Export Summary</h3>
        <p><strong>Total Academies:</strong> {{ count($academies) }}</p>
        <p><strong>Export Date:</strong> {{ now()->format('F d, Y H:i:s') }}</p>
        <p><strong>Report Type:</strong> Academy Management Export</p>
    </div>

    @php
        $totalStudents = $academies->sum(function($academy) { return $academy->students_count ?? 0; });
        $totalPrograms = $academies->sum(function($academy) { return $academy->programs_count ?? 0; });
        $totalRevenue = $academies->sum(function($academy) { 
            return $academy->completedPayments()->sum('amount'); 
        });
        $activeAcademies = $academies->where('status', true)->count();
    @endphp

    <div class="stats-grid">
        <div class="stat-card">
            <h4>{{ count($academies) }}</h4>
            <p>Total Academies</p>
        </div>
        <div class="stat-card">
            <h4>{{ $activeAcademies }}</h4>
            <p>Active Academies</p>
        </div>
        <div class="stat-card">
            <h4>{{ $totalStudents }}</h4>
            <p>Total Students</p>
        </div>
        <div class="stat-card">
            <h4>{{ $totalPrograms }}</h4>
            <p>Total Programs</p>
        </div>
    </div>

    <table class="table">
        <thead>
            <tr>
                <th>ID</th>
                <th>Academy Name</th>
                <th>Branch</th>
                <th>Coach</th>
                <th>Coach Phone</th>
                <th>Programs</th>
                <th>Students</th>
                <th>Revenue (AED)</th>
                <th>Status</th>
                <th>Created</th>
            </tr>
        </thead>
        <tbody>
            @foreach($academies as $academy)
                <tr>
                    <td>#{{ $academy->id }}</td>
                    <td>
                        <strong>{{ $academy->name }}</strong>
                        @if($academy->description)
                            <br><small>{{ Str::limit($academy->description, 50) }}</small>
                        @endif
                    </td>
                    <td>{{ $academy->branch->name ?? 'N/A' }}</td>
                    <td>{{ $academy->coach_name ?? 'Not assigned' }}</td>
                    <td>{{ $academy->formatted_coach_phone ?? 'N/A' }}</td>
                    <td>{{ $academy->programs_count ?? 0 }}</td>
                    <td>{{ $academy->students_count ?? 0 }}</td>
                    <td>{{ number_format($academy->total_revenue, 2) }}</td>
                    <td>
                        <span class="badge {{ $academy->status ? 'badge-success' : 'badge-neutral' }}">
                            {{ $academy->status ? 'Active' : 'Inactive' }}
                        </span>
                    </td>
                    <td>{{ $academy->created_at->format('M d, Y') }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    @if(count($academies) === 0)
        <div style="text-align: center; padding: 40px; color: #666;">
            <h3>No academies found</h3>
            <p>No academies match the current filter criteria.</p>
        </div>
    @endif

    <div class="footer">
        <p>© {{ date('Y') }} UAE English Sports Academy - Academy Management System</p>
        <p>This report was generated automatically on {{ now()->format('F d, Y \a\t H:i:s') }}</p>
        <p>Total Revenue: AED {{ number_format($totalRevenue, 2) }} | Active Academies: {{ $activeAcademies }}/{{ count($academies) }}</p>
    </div>
</body>
</html>
