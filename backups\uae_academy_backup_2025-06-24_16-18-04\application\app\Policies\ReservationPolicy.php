<?php

namespace App\Policies;

use App\Models\Reservation;
use App\Models\User;

class ReservationPolicy
{
    /**
     * Determine whether the user can view any reservations.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the reservation.
     */
    public function view(User $user, Reservation $reservation): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can create reservations.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can update the reservation.
     */
    public function update(User $user, Reservation $reservation): bool
    {
        // Cannot update completed, cancelled, or no-show reservations
        if (in_array($reservation->status, ['completed', 'cancelled', 'no_show'])) {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can delete the reservation.
     */
    public function delete(User $user, Reservation $reservation): bool
    {
        // Only allow deletion of pending reservations
        if ($reservation->status !== 'pending') {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can restore the reservation.
     */
    public function restore(User $user, Reservation $reservation): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can permanently delete the reservation.
     */
    public function forceDelete(User $user, Reservation $reservation): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can export reservation data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view reservation statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can confirm reservations.
     */
    public function confirm(User $user, Reservation $reservation): bool
    {
        // Can only confirm pending reservations
        if ($reservation->status !== 'pending') {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can cancel reservations.
     */
    public function cancel(User $user, Reservation $reservation): bool
    {
        // Cannot cancel completed reservations
        if (in_array($reservation->status, ['completed', 'cancelled', 'no_show'])) {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can mark reservations as completed.
     */
    public function complete(User $user, Reservation $reservation): bool
    {
        // Can only complete confirmed or in-progress reservations
        if (!in_array($reservation->status, ['confirmed', 'in_progress'])) {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can mark reservations as no-show.
     */
    public function markNoShow(User $user, Reservation $reservation): bool
    {
        // Can only mark confirmed reservations as no-show
        if ($reservation->status !== 'confirmed') {
            return false;
        }

        // Must be past the reservation time
        if (!$reservation->is_past) {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can modify reservation times.
     */
    public function modifyTime(User $user, Reservation $reservation): bool
    {
        // Cannot modify completed, cancelled, or no-show reservations
        if (in_array($reservation->status, ['completed', 'cancelled', 'no_show'])) {
            return false;
        }

        // Must be modifiable (4+ hours before start time)
        if (!$reservation->can_modify) {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can process payments for reservations.
     */
    public function processPayment(User $user, Reservation $reservation): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can refund reservations.
     */
    public function refund(User $user, Reservation $reservation): bool
    {
        // Must have payments to refund
        if ($reservation->payment_status === 'unpaid') {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can view reservation calendar.
     */
    public function viewCalendar(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can create recurring reservations.
     */
    public function createRecurring(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }
}
