<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\File;

class Translation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'group',
        'text_en',
        'text_ar',
        'is_active',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Cache key prefix for translations
     */
    const CACHE_PREFIX = 'translation_';

    /**
     * Cache duration in seconds (24 hours)
     */
    const CACHE_DURATION = 86400;

    /**
     * Get translation by key and locale
     * Note: This method is for the translation management panel only
     * It does not interfere with <PERSON><PERSON>'s built-in translation system
     *
     * @param string $key
     * @param string $locale
     * @return string|null
     */
    public static function getTranslation(string $key, string $locale = 'en'): ?string
    {
        $cacheKey = self::CACHE_PREFIX . $key . '_' . $locale;

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($key, $locale) {
            $translation = self::where('key', $key)->where('is_active', true)->first();

            if (!$translation) {
                return null;
            }

            return $locale === 'ar' ? $translation->text_ar : $translation->text_en;
        });
    }

    /**
     * Set translation for a key
     *
     * @param string $key
     * @param string $group
     * @param string $textEn
     * @param string $textAr
     * @param int|null $userId
     * @return bool
     */
    public static function setTranslation(string $key, string $group, string $textEn, string $textAr, int $userId = null): bool
    {
        $translation = self::firstOrNew(['key' => $key]);

        $translation->group = $group;
        $translation->text_en = $textEn;
        $translation->text_ar = $textAr;
        $translation->is_active = true;

        if ($translation->exists) {
            $translation->updated_by = $userId;
        } else {
            $translation->created_by = $userId;
        }

        $saved = $translation->save();

        if ($saved) {
            // Clear cache
            Cache::forget(self::CACHE_PREFIX . $key . '_en');
            Cache::forget(self::CACHE_PREFIX . $key . '_ar');
        }

        return $saved;
    }

    /**
     * Get all translations grouped by group
     *
     * @return array
     */
    public static function getAllGrouped(): array
    {
        return self::where('is_active', true)
            ->orderBy('group')
            ->orderBy('key')
            ->get()
            ->groupBy('group')
            ->toArray();
    }

    /**
     * Get translations by group
     *
     * @param string $group
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getByGroup(string $group)
    {
        return self::where('group', $group)
            ->where('is_active', true)
            ->orderBy('key')
            ->get();
    }

    /**
     * Get available groups
     *
     * @return array
     */
    public static function getGroups(): array
    {
        return self::where('is_active', true)
            ->distinct()
            ->pluck('group')
            ->sort()
            ->values()
            ->toArray();
    }

    /**
     * Search translations
     *
     * @param string $search
     * @param string|null $group
     * @param string|null $locale
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function search(string $search, string $group = null, string $locale = null)
    {
        $query = self::where('is_active', true);

        if ($group) {
            $query->where('group', $group);
        }

        if ($locale) {
            $column = $locale === 'ar' ? 'text_ar' : 'text_en';
            $query->where(function ($q) use ($search, $column) {
                $q->where('key', 'like', "%{$search}%")
                    ->orWhere($column, 'like', "%{$search}%");
            });
        } else {
            $query->where(function ($q) use ($search) {
                $q->where('key', 'like', "%{$search}%")
                    ->orWhere('text_en', 'like', "%{$search}%")
                    ->orWhere('text_ar', 'like', "%{$search}%");
            });
        }

        return $query->orderBy('group')->orderBy('key')->get();
    }

    /**
     * Export translations to language files
     *
     * @param string|null $group
     * @return bool
     */
    public static function exportToFiles(string $group = null): bool
    {
        try {
            $translations = $group ? self::getByGroup($group) : self::where('is_active', true)->get();

            $enTranslations = [];
            $arTranslations = [];

            foreach ($translations as $translation) {
                $groupKey = $translation->group;
                $key = $translation->key;

                if (!isset($enTranslations[$groupKey])) {
                    $enTranslations[$groupKey] = [];
                    $arTranslations[$groupKey] = [];
                }

                $enTranslations[$groupKey][$key] = $translation->text_en;
                $arTranslations[$groupKey][$key] = $translation->text_ar;
            }

            // Write English files
            foreach ($enTranslations as $groupName => $groupTranslations) {
                $filePath = resource_path("lang/en/{$groupName}.php");
                $content = "<?php\n\nreturn " . var_export($groupTranslations, true) . ";\n";
                File::put($filePath, $content);
            }

            // Write Arabic files
            foreach ($arTranslations as $groupName => $groupTranslations) {
                $filePath = resource_path("lang/ar/{$groupName}.php");
                $content = "<?php\n\nreturn " . var_export($groupTranslations, true) . ";\n";
                File::put($filePath, $content);
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Translation export failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Import translations from language files
     *
     * @param string|null $group
     * @return bool
     */
    public static function importFromFiles(string $group = null): bool
    {
        try {
            $langPath = resource_path('lang');
            $groups = $group ? [$group] : collect(File::files($langPath . '/en'))
                ->map(fn($file) => pathinfo($file, PATHINFO_FILENAME))
                ->toArray();

            foreach ($groups as $groupName) {
                $enFile = $langPath . "/en/{$groupName}.php";
                $arFile = $langPath . "/ar/{$groupName}.php";

                if (File::exists($enFile) && File::exists($arFile)) {
                    $enTranslations = include $enFile;
                    $arTranslations = include $arFile;

                    foreach ($enTranslations as $key => $enText) {
                        $arText = $arTranslations[$key] ?? '';
                        self::setTranslation($key, $groupName, $enText, $arText);
                    }
                }
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Translation import failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get missing translations
     *
     * @return array
     */
    public static function getMissingTranslations(): array
    {
        return self::where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('text_en')
                    ->orWhere('text_en', '')
                    ->orWhereNull('text_ar')
                    ->orWhere('text_ar', '');
            })
            ->orderBy('group')
            ->orderBy('key')
            ->get()
            ->toArray();
    }

    /**
     * Get statistics
     *
     * @return array
     */
    public static function getStatistics(): array
    {
        $total = self::where('is_active', true)->count();
        $complete = self::where('is_active', true)
            ->whereNotNull('text_en')
            ->where('text_en', '!=', '')
            ->whereNotNull('text_ar')
            ->where('text_ar', '!=', '')
            ->count();

        return [
            'total' => $total,
            'complete' => $complete,
            'incomplete' => $total - $complete,
            'completion_percentage' => $total > 0 ? round(($complete / $total) * 100, 2) : 0,
            'groups' => count(self::getGroups()),
        ];
    }
}
