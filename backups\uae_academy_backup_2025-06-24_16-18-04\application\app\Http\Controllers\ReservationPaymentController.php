<?php

namespace App\Http\Controllers;

use App\Models\ReservationPayment;
use App\Models\Reservation;
use App\Models\Customer;
use App\Models\Venue;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class ReservationPaymentController extends Controller
{
    /**
     * Display a listing of reservation payments with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', ReservationPayment::class);

        $query = ReservationPayment::with(['reservation', 'customer', 'venue', 'processedBy']);

        // Search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('payment_number', 'like', "%{$search}%")
                    ->orWhere('reference_number', 'like', "%{$search}%")
                    ->orWhere('receipt_number', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($customerQuery) use ($search) {
                        $customerQuery->where('full_name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%")
                            ->orWhere('phone', 'like', "%{$search}%");
                    });
            });
        }

        // Reservation filter
        if ($request->filled('reservation_id')) {
            $query->where('reservation_id', $request->get('reservation_id'));
        }

        // Customer filter
        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->get('customer_id'));
        }

        // Venue filter
        if ($request->filled('venue_id')) {
            $query->where('venue_id', $request->get('venue_id'));
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Payment type filter
        if ($request->filled('payment_type')) {
            $query->where('payment_type', $request->get('payment_type'));
        }

        // Payment method filter
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->get('date_to'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'payment_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $payments = $query->paginate(15)->withQueryString();

        // Get filter options
        $venues = Venue::active()->orderBy('name')->get();
        $customers = Customer::active()->orderBy('full_name')->get();

        if ($request->ajax()) {
            return response()->json([
                'payments' => $payments,
                'html' => view('reservation-payments.partials.table', compact('payments'))->render()
            ]);
        }

        return view('reservation-payments.index', compact('payments', 'venues', 'customers'));
    }

    /**
     * Show the form for creating a new reservation payment.
     */
    public function create(Request $request): View
    {
        Gate::authorize('create', ReservationPayment::class);

        $reservations = Reservation::with(['customer', 'venue', 'field'])
            ->whereIn('payment_status', ['pending', 'partial'])
            ->orderBy('reservation_date')
            ->get();

        $customers = Customer::active()->orderBy('full_name')->get();
        $venues = Venue::active()->orderBy('name')->get();

        // Pre-fill reservation data if reservation_id is provided
        $prefilledReservation = null;
        if ($request->has('reservation_id')) {
            $prefilledReservation = Reservation::with(['customer', 'venue', 'field'])
                ->find($request->get('reservation_id'));
        }

        return view('reservation-payments.create', compact('reservations', 'customers', 'venues', 'prefilledReservation'));
    }

    /**
     * Store a newly created reservation payment in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', ReservationPayment::class);

        $validator = Validator::make($request->all(), [
            'reservation_id' => 'required|exists:reservations,id',
            'customer_id' => 'required|exists:customers,id',
            'venue_id' => 'required|exists:venues,id',
            'payment_type' => 'required|in:deposit,full_payment,partial_payment,balance,refund',
            'amount' => 'required|numeric|min:0.01',
            'vat_rate' => 'required|numeric|min:0|max:100',
            'vat_inclusive' => 'boolean',
            'currency' => 'required|string|size:3',
            'payment_method' => 'required|in:cash,card,bank_transfer,online,cheque',
            'card_type' => 'nullable|string|max:50',
            'card_last_four' => 'nullable|string|size:4',
            'transaction_id' => 'nullable|string|max:100',
            'authorization_code' => 'nullable|string|max:100',
            'reference_number' => 'nullable|string|max:100',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
            'notes_ar' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $validator->validated();

            // Generate payment number
            $data['payment_number'] = ReservationPayment::generatePaymentNumber();
            $data['payment_datetime'] = now();
            $data['status'] = 'pending';
            $data['processed_by'] = Auth::id();

            // Calculate VAT
            $payment = new ReservationPayment($data);
            $payment->calculateVAT();
            $payment->save();

            // Process the payment immediately for cash payments
            if ($data['payment_method'] === 'cash') {
                $payment->process();
            }

            // Update reservation payment status
            $reservation = Reservation::find($data['reservation_id']);
            $this->updateReservationPaymentStatus($reservation);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment recorded successfully',
                    'payment' => $payment
                ]);
            }

            return redirect()->route('reservation-payments.show', $payment)
                ->with('success', 'Payment recorded successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating reservation payment: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error recording payment'
                ], 500);
            }

            return back()->with('error', 'Error recording payment')->withInput();
        }
    }

    /**
     * Display the specified reservation payment.
     */
    public function show(ReservationPayment $reservationPayment, Request $request): View|JsonResponse
    {
        Gate::authorize('view', $reservationPayment);

        $reservationPayment->load([
            'reservation.venue',
            'reservation.field',
            'customer',
            'venue',
            'processedBy',
            'refundedBy'
        ]);

        if ($request->ajax()) {
            return response()->json([
                'payment' => $reservationPayment
            ]);
        }

        return view('reservation-payments.show', compact('reservationPayment'));
    }

    /**
     * Show the form for editing the specified reservation payment.
     */
    public function edit(ReservationPayment $reservationPayment): View|RedirectResponse
    {
        Gate::authorize('update', $reservationPayment);

        if ($reservationPayment->status === 'completed') {
            return redirect()->route('reservation-payments.show', $reservationPayment)
                ->with('error', 'Completed payments cannot be edited');
        }

        $reservations = Reservation::with(['customer', 'venue', 'field'])
            ->orderBy('reservation_date')
            ->get();

        $customers = Customer::active()->orderBy('full_name')->get();
        $venues = Venue::active()->orderBy('name')->get();

        return view('reservation-payments.edit', compact('reservationPayment', 'reservations', 'customers', 'venues'));
    }

    /**
     * Update the specified reservation payment in storage.
     */
    public function update(Request $request, ReservationPayment $reservationPayment): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $reservationPayment);

        if ($reservationPayment->status === 'completed') {
            $message = 'Completed payments cannot be edited';

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $message
                ], 422);
            }

            return back()->with('error', $message);
        }

        $validator = Validator::make($request->all(), [
            'reservation_id' => 'required|exists:reservations,id',
            'customer_id' => 'required|exists:customers,id',
            'venue_id' => 'required|exists:venues,id',
            'payment_type' => 'required|in:deposit,full_payment,partial_payment,balance,refund',
            'amount' => 'required|numeric|min:0.01',
            'vat_rate' => 'required|numeric|min:0|max:100',
            'vat_inclusive' => 'boolean',
            'currency' => 'required|string|size:3',
            'payment_method' => 'required|in:cash,card,bank_transfer,online,cheque',
            'card_type' => 'nullable|string|max:50',
            'card_last_four' => 'nullable|string|size:4',
            'transaction_id' => 'nullable|string|max:100',
            'authorization_code' => 'nullable|string|max:100',
            'reference_number' => 'nullable|string|max:100',
            'payment_date' => 'required|date',
            'notes' => 'nullable|string|max:1000',
            'notes_ar' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $validator->validated();

            $reservationPayment->update($data);

            // Recalculate VAT
            $reservationPayment->calculateVAT();
            $reservationPayment->save();

            // Update reservation payment status
            $reservation = Reservation::find($data['reservation_id']);
            $this->updateReservationPaymentStatus($reservation);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment updated successfully',
                    'payment' => $reservationPayment->fresh()
                ]);
            }

            return redirect()->route('reservation-payments.show', $reservationPayment)
                ->with('success', 'Payment updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating reservation payment: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error updating payment'
                ], 500);
            }

            return back()->with('error', 'Error updating payment')->withInput();
        }
    }

    /**
     * Remove the specified reservation payment from storage.
     */
    public function destroy(ReservationPayment $reservationPayment): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $reservationPayment);

        try {
            // Check if payment can be deleted
            if ($reservationPayment->status === 'completed') {
                $message = 'Cannot delete completed payments. Consider refunding instead.';

                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 422);
                }

                return back()->with('error', $message);
            }

            $reservationId = $reservationPayment->reservation_id;
            $reservationPayment->delete();

            // Update reservation payment status
            $reservation = Reservation::find($reservationId);
            if ($reservation) {
                $this->updateReservationPaymentStatus($reservation);
            }

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment deleted successfully'
                ]);
            }

            return redirect()->route('reservation-payments.index')
                ->with('success', 'Payment deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting reservation payment: ' . $e->getMessage());

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error deleting payment'
                ], 500);
            }

            return back()->with('error', 'Error deleting payment');
        }
    }

    /**
     * Process a pending payment.
     */
    public function process(ReservationPayment $reservationPayment): JsonResponse
    {
        Gate::authorize('update', $reservationPayment);

        try {
            if ($reservationPayment->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending payments can be processed'
                ], 422);
            }

            $reservationPayment->process();

            // Update reservation payment status
            $this->updateReservationPaymentStatus($reservationPayment->reservation);

            return response()->json([
                'success' => true,
                'message' => 'Payment processed successfully',
                'status' => $reservationPayment->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error processing payment: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error processing payment'
            ], 500);
        }
    }

    /**
     * Fail a pending payment.
     */
    public function fail(Request $request, ReservationPayment $reservationPayment): JsonResponse
    {
        Gate::authorize('update', $reservationPayment);

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if ($reservationPayment->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending payments can be failed'
                ], 422);
            }

            $reservationPayment->fail($request->get('reason'));

            return response()->json([
                'success' => true,
                'message' => 'Payment marked as failed',
                'status' => $reservationPayment->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error failing payment: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error updating payment'
            ], 500);
        }
    }

    /**
     * Refund a completed payment.
     */
    public function refund(Request $request, ReservationPayment $reservationPayment): JsonResponse
    {
        Gate::authorize('update', $reservationPayment);

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01|max:' . $reservationPayment->net_amount,
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if (!$reservationPayment->is_refundable) {
                return response()->json([
                    'success' => false,
                    'message' => 'This payment is not refundable'
                ], 422);
            }

            $reservationPayment->refund(
                $request->get('amount'),
                $request->get('reason')
            );

            return response()->json([
                'success' => true,
                'message' => 'Payment refunded successfully',
                'refund_amount' => $reservationPayment->refund_amount
            ]);
        } catch (\Exception $e) {
            Log::error('Error refunding payment: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error processing refund'
            ], 500);
        }
    }

    /**
     * Generate and display payment receipt for printing.
     */
    public function receipt(ReservationPayment $reservationPayment): View
    {
        Gate::authorize('view', $reservationPayment);

        $reservationPayment->load([
            'reservation.venue',
            'reservation.field',
            'customer',
            'venue',
            'processedBy'
        ]);

        return view('reservation-payments.receipt', compact('reservationPayment'));
    }

    /**
     * API endpoint for reservation payments (AJAX requests).
     */
    public function apiIndex(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', ReservationPayment::class);

        $query = ReservationPayment::with(['reservation', 'customer', 'venue']);

        // Apply filters
        if ($request->filled('reservation_id')) {
            $query->where('reservation_id', $request->get('reservation_id'));
        }

        if ($request->filled('customer_id')) {
            $query->where('customer_id', $request->get('customer_id'));
        }

        if ($request->filled('venue_id')) {
            $query->where('venue_id', $request->get('venue_id'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('date_from')) {
            $query->whereDate('payment_date', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('payment_date', '<=', $request->get('date_to'));
        }

        $payments = $query->orderBy('payment_date', 'desc')->get();

        return response()->json($payments);
    }

    /**
     * Update reservation payment status based on payments.
     */
    private function updateReservationPaymentStatus(Reservation $reservation): void
    {
        $totalPaid = $reservation->payments()->completed()->sum('total_amount');
        $totalAmount = $reservation->total_amount;

        if ($totalPaid >= $totalAmount) {
            $reservation->update(['payment_status' => 'paid']);
        } elseif ($totalPaid > 0) {
            $reservation->update(['payment_status' => 'partial']);
        } else {
            $reservation->update(['payment_status' => 'unpaid']);
        }
    }
}
