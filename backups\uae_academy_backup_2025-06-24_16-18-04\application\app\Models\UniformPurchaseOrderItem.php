<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class UniformPurchaseOrderItem extends Model
{
    protected $fillable = [
        'uniform_purchase_order_id',
        'uniform_inventory_id',
        'item_name',
        'item_sku',
        'size',
        'color',
        'description',
        'quantity_ordered',
        'quantity_received',
        'quantity_pending',
        'quantity_cancelled',
        'unit_cost',
        'total_cost',
        'discount_amount',
        'discount_percentage',
        'net_cost',
        'status',
        'expected_date',
        'received_date',
        'receiving_notes',
        'quantity_accepted',
        'quantity_rejected',
        'rejection_reason',
    ];

    protected $casts = [
        'quantity_ordered' => 'integer',
        'quantity_received' => 'integer',
        'quantity_pending' => 'integer',
        'quantity_cancelled' => 'integer',
        'quantity_accepted' => 'integer',
        'quantity_rejected' => 'integer',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'net_cost' => 'decimal:2',
        'expected_date' => 'date',
        'received_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_unit_cost',
        'formatted_total_cost',
        'formatted_net_cost',
        'status_text',
        'status_badge_class',
        'completion_percentage',
        'is_fully_received',
        'is_partially_received',
        'outstanding_quantity',
        'formatted_expected_date',
        'formatted_received_date',
        'quality_acceptance_rate',
    ];

    /**
     * Get the purchase order that owns this item.
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(UniformPurchaseOrder::class, 'uniform_purchase_order_id');
    }

    /**
     * Get the inventory item.
     */
    public function inventoryItem(): BelongsTo
    {
        return $this->belongsTo(UniformInventory::class, 'uniform_inventory_id');
    }

    // Computed Properties

    /**
     * Get formatted unit cost in AED.
     */
    public function getFormattedUnitCostAttribute(): string
    {
        return 'AED ' . number_format($this->unit_cost, 2);
    }

    /**
     * Get formatted total cost in AED.
     */
    public function getFormattedTotalCostAttribute(): string
    {
        return 'AED ' . number_format($this->total_cost, 2);
    }

    /**
     * Get formatted net cost in AED.
     */
    public function getFormattedNetCostAttribute(): string
    {
        return 'AED ' . number_format($this->net_cost, 2);
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'Pending',
            'partial' => 'Partially Received',
            'received' => 'Fully Received',
            'cancelled' => 'Cancelled',
            default => 'Unknown'
        };
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'partial' => 'bg-orange-100 text-orange-800',
            'received' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get completion percentage.
     */
    public function getCompletionPercentageAttribute(): float
    {
        if ($this->quantity_ordered <= 0) {
            return 0;
        }
        
        return round(($this->quantity_received / $this->quantity_ordered) * 100, 1);
    }

    /**
     * Check if fully received.
     */
    public function getIsFullyReceivedAttribute(): bool
    {
        return $this->quantity_received >= $this->quantity_ordered;
    }

    /**
     * Check if partially received.
     */
    public function getIsPartiallyReceivedAttribute(): bool
    {
        return $this->quantity_received > 0 && $this->quantity_received < $this->quantity_ordered;
    }

    /**
     * Get outstanding quantity.
     */
    public function getOutstandingQuantityAttribute(): int
    {
        return max(0, $this->quantity_ordered - $this->quantity_received - $this->quantity_cancelled);
    }

    /**
     * Get formatted expected date.
     */
    public function getFormattedExpectedDateAttribute(): ?string
    {
        return $this->expected_date?->format('M d, Y');
    }

    /**
     * Get formatted received date.
     */
    public function getFormattedReceivedDateAttribute(): ?string
    {
        return $this->received_date?->format('M d, Y');
    }

    /**
     * Get quality acceptance rate.
     */
    public function getQualityAcceptanceRateAttribute(): float
    {
        if ($this->quantity_received <= 0) {
            return 0;
        }
        
        return round(($this->quantity_accepted / $this->quantity_received) * 100, 1);
    }

    // Query Scopes

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending items.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get received items.
     */
    public function scopeReceived(Builder $query): Builder
    {
        return $query->where('status', 'received');
    }

    /**
     * Scope to get partially received items.
     */
    public function scopePartial(Builder $query): Builder
    {
        return $query->where('status', 'partial');
    }

    /**
     * Scope to filter by inventory item.
     */
    public function scopeByInventoryItem(Builder $query, int $inventoryId): Builder
    {
        return $query->where('uniform_inventory_id', $inventoryId);
    }

    // Utility Methods

    /**
     * Receive quantity for this item.
     */
    public function receiveQuantity(int $quantity, array $qualityData = []): bool
    {
        if ($quantity <= 0 || $quantity > $this->outstanding_quantity) {
            return false;
        }

        $this->quantity_received += $quantity;
        $this->quantity_pending = max(0, $this->quantity_ordered - $this->quantity_received - $this->quantity_cancelled);
        
        // Handle quality control data
        if (!empty($qualityData)) {
            $this->quantity_accepted += $qualityData['accepted'] ?? $quantity;
            $this->quantity_rejected += $qualityData['rejected'] ?? 0;
            $this->rejection_reason = $qualityData['rejection_reason'] ?? null;
        } else {
            $this->quantity_accepted += $quantity;
        }

        // Update status
        if ($this->quantity_received >= $this->quantity_ordered) {
            $this->status = 'received';
        } elseif ($this->quantity_received > 0) {
            $this->status = 'partial';
        }

        $this->received_date = now()->toDateString();
        $this->save();

        // Update inventory stock
        if ($this->inventoryItem) {
            $this->inventoryItem->increment('current_stock', $quantity);
            $this->inventoryItem->update([
                'available_stock' => $this->inventoryItem->current_stock - $this->inventoryItem->reserved_stock,
                'last_restocked_at' => now()->toDateString(),
            ]);

            // Create stock movement
            UniformStockMovement::create([
                'uniform_inventory_id' => $this->uniform_inventory_id,
                'branch_id' => $this->purchaseOrder->branch_id,
                'academy_id' => $this->purchaseOrder->academy_id,
                'user_id' => auth()->id(),
                'reference_number' => UniformStockMovement::generateReferenceNumber(),
                'movement_type' => 'purchase',
                'quantity' => $quantity,
                'stock_before' => $this->inventoryItem->current_stock - $quantity,
                'stock_after' => $this->inventoryItem->current_stock,
                'unit_cost' => $this->unit_cost,
                'total_cost' => $quantity * $this->unit_cost,
                'uniform_purchase_order_id' => $this->uniform_purchase_order_id,
                'movement_date' => now()->toDateString(),
                'reason' => "Stock received from PO: {$this->purchaseOrder->po_number}",
            ]);
        }

        // Update purchase order status
        $this->purchaseOrder->checkAndUpdateReceivalStatus();

        return true;
    }

    /**
     * Cancel quantity for this item.
     */
    public function cancelQuantity(int $quantity, string $reason = null): bool
    {
        if ($quantity <= 0 || $quantity > $this->outstanding_quantity) {
            return false;
        }

        $this->quantity_cancelled += $quantity;
        $this->quantity_pending = max(0, $this->quantity_ordered - $this->quantity_received - $this->quantity_cancelled);
        
        if ($this->quantity_cancelled >= $this->quantity_ordered) {
            $this->status = 'cancelled';
        }

        if ($reason) {
            $this->receiving_notes = ($this->receiving_notes ? $this->receiving_notes . "\n" : '') . 
                                   "Cancelled {$quantity} units: {$reason}";
        }

        $this->save();

        // Update purchase order status
        $this->purchaseOrder->checkAndUpdateReceivalStatus();

        return true;
    }

    /**
     * Calculate costs based on quantity and unit cost.
     */
    public function calculateCosts(): void
    {
        $this->total_cost = $this->quantity_ordered * $this->unit_cost;
        $this->net_cost = $this->total_cost - $this->discount_amount;
        $this->save();
    }
}
