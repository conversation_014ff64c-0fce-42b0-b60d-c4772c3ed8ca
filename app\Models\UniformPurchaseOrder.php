<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;

class UniformPurchaseOrder extends Model
{
    protected $fillable = [
        'branch_id',
        'academy_id',
        'uniform_supplier_id',
        'created_by',
        'approved_by',
        'po_number',
        'supplier_reference',
        'order_date',
        'expected_delivery_date',
        'actual_delivery_date',
        'status',
        'subtotal',
        'discount_amount',
        'discount_percentage',
        'tax_amount',
        'tax_rate',
        'shipping_cost',
        'total_amount',
        'currency',
        'payment_status',
        'payment_method',
        'payment_due_date',
        'paid_amount',
        'delivery_address',
        'delivery_address_ar',
        'delivery_contact',
        'delivery_phone',
        'delivery_instructions',
        'terms_conditions',
        'notes',
        'notes_ar',
        'internal_notes',
        'approved_at',
        'ordered_at',
        'received_at',
        'closed_at',
    ];

    protected $casts = [
        'order_date' => 'date',
        'expected_delivery_date' => 'date',
        'actual_delivery_date' => 'date',
        'payment_due_date' => 'date',
        'subtotal' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'tax_rate' => 'decimal:2',
        'shipping_cost' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'approved_at' => 'datetime',
        'ordered_at' => 'datetime',
        'received_at' => 'datetime',
        'closed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'formatted_subtotal',
        'formatted_total_amount',
        'formatted_paid_amount',
        'outstanding_amount',
        'formatted_outstanding_amount',
        'status_text',
        'status_badge_class',
        'payment_status_text',
        'payment_status_badge_class',
        'payment_method_text',
        'days_until_due',
        'is_overdue',
        'completion_percentage',
        'delivery_status',
        'formatted_order_date',
        'formatted_expected_delivery',
        'formatted_actual_delivery',
    ];

    /**
     * Get the branch that owns this purchase order.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the academy that owns this purchase order.
     */
    public function academy(): BelongsTo
    {
        return $this->belongsTo(Academy::class);
    }

    /**
     * Get the supplier for this purchase order.
     */
    public function supplier(): BelongsTo
    {
        return $this->belongsTo(UniformSupplier::class, 'uniform_supplier_id');
    }

    /**
     * Get the user who created this purchase order.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved this purchase order.
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get the items for this purchase order.
     */
    public function items(): HasMany
    {
        return $this->hasMany(UniformPurchaseOrderItem::class);
    }

    /**
     * Get the stock movements related to this purchase order.
     */
    public function stockMovements(): HasMany
    {
        return $this->hasMany(UniformStockMovement::class);
    }

    // Computed Properties

    /**
     * Get formatted subtotal in AED.
     */
    public function getFormattedSubtotalAttribute(): string
    {
        return 'AED ' . number_format($this->subtotal, 2);
    }

    /**
     * Get formatted total amount in AED.
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        return 'AED ' . number_format($this->total_amount, 2);
    }

    /**
     * Get formatted paid amount in AED.
     */
    public function getFormattedPaidAmountAttribute(): string
    {
        return 'AED ' . number_format($this->paid_amount, 2);
    }

    /**
     * Get outstanding amount.
     */
    public function getOutstandingAmountAttribute(): float
    {
        return $this->total_amount - $this->paid_amount;
    }

    /**
     * Get formatted outstanding amount in AED.
     */
    public function getFormattedOutstandingAmountAttribute(): string
    {
        return 'AED ' . number_format($this->outstanding_amount, 2);
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'draft' => 'Draft',
            'pending' => 'Pending Approval',
            'approved' => 'Approved',
            'ordered' => 'Ordered',
            'partial' => 'Partially Received',
            'received' => 'Fully Received',
            'cancelled' => 'Cancelled',
            'closed' => 'Closed',
            default => 'Unknown'
        };
    }

    /**
     * Get status badge class for UI.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'draft' => 'bg-gray-100 text-gray-800',
            'pending' => 'bg-yellow-100 text-yellow-800',
            'approved' => 'bg-blue-100 text-blue-800',
            'ordered' => 'bg-purple-100 text-purple-800',
            'partial' => 'bg-orange-100 text-orange-800',
            'received' => 'bg-green-100 text-green-800',
            'cancelled' => 'bg-red-100 text-red-800',
            'closed' => 'bg-gray-100 text-gray-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get payment status text.
     */
    public function getPaymentStatusTextAttribute(): string
    {
        return match ($this->payment_status) {
            'pending' => 'Payment Pending',
            'partial' => 'Partially Paid',
            'paid' => 'Fully Paid',
            'overdue' => 'Overdue',
            default => 'Unknown'
        };
    }

    /**
     * Get payment status badge class for UI.
     */
    public function getPaymentStatusBadgeClassAttribute(): string
    {
        return match ($this->payment_status) {
            'pending' => 'bg-yellow-100 text-yellow-800',
            'partial' => 'bg-orange-100 text-orange-800',
            'paid' => 'bg-green-100 text-green-800',
            'overdue' => 'bg-red-100 text-red-800',
            default => 'bg-gray-100 text-gray-800'
        };
    }

    /**
     * Get payment method text.
     */
    public function getPaymentMethodTextAttribute(): string
    {
        return match ($this->payment_method) {
            'cash' => 'Cash',
            'bank_transfer' => 'Bank Transfer',
            'cheque' => 'Cheque',
            'credit' => 'Credit Terms',
            default => 'Not specified'
        };
    }

    /**
     * Get days until payment due.
     */
    public function getDaysUntilDueAttribute(): ?int
    {
        if (!$this->payment_due_date) {
            return null;
        }
        
        return now()->diffInDays($this->payment_due_date, false);
    }

    /**
     * Check if payment is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        if (!$this->payment_due_date || $this->payment_status === 'paid') {
            return false;
        }
        
        return now()->isAfter($this->payment_due_date);
    }

    /**
     * Get completion percentage based on received items.
     */
    public function getCompletionPercentageAttribute(): float
    {
        $totalOrdered = $this->items()->sum('quantity_ordered');
        $totalReceived = $this->items()->sum('quantity_received');
        
        if ($totalOrdered <= 0) {
            return 0;
        }
        
        return round(($totalReceived / $totalOrdered) * 100, 1);
    }

    /**
     * Get delivery status.
     */
    public function getDeliveryStatusAttribute(): string
    {
        if ($this->completion_percentage >= 100) {
            return 'delivered';
        } elseif ($this->completion_percentage > 0) {
            return 'partial';
        } else {
            return 'pending';
        }
    }

    /**
     * Get formatted order date.
     */
    public function getFormattedOrderDateAttribute(): string
    {
        return $this->order_date->format('M d, Y');
    }

    /**
     * Get formatted expected delivery date.
     */
    public function getFormattedExpectedDeliveryAttribute(): ?string
    {
        return $this->expected_delivery_date?->format('M d, Y');
    }

    /**
     * Get formatted actual delivery date.
     */
    public function getFormattedActualDeliveryAttribute(): ?string
    {
        return $this->actual_delivery_date?->format('M d, Y');
    }

    // Query Scopes

    /**
     * Scope to filter by status.
     */
    public function scopeByStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by payment status.
     */
    public function scopeByPaymentStatus(Builder $query, string $paymentStatus): Builder
    {
        return $query->where('payment_status', $paymentStatus);
    }

    /**
     * Scope to get overdue purchase orders.
     */
    public function scopeOverdue(Builder $query): Builder
    {
        return $query->where('payment_due_date', '<', now())
                    ->where('payment_status', '!=', 'paid');
    }

    /**
     * Scope to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): Builder
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Scope to filter by academy.
     */
    public function scopeByAcademy(Builder $query, int $academyId): Builder
    {
        return $query->where('academy_id', $academyId);
    }

    /**
     * Scope to filter by supplier.
     */
    public function scopeBySupplier(Builder $query, int $supplierId): Builder
    {
        return $query->where('uniform_supplier_id', $supplierId);
    }

    /**
     * Scope to search purchase orders.
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($q) use ($search) {
            $q->where('po_number', 'like', "%{$search}%")
              ->orWhere('supplier_reference', 'like', "%{$search}%")
              ->orWhereHas('supplier', function ($sq) use ($search) {
                  $sq->where('name', 'like', "%{$search}%");
              });
        });
    }

    // Utility Methods

    /**
     * Generate next PO number.
     */
    public static function generatePoNumber(): string
    {
        $year = now()->year;
        $lastPo = self::where('po_number', 'like', "PO-{$year}-%")
                     ->orderBy('id', 'desc')
                     ->first();
        
        if ($lastPo) {
            $lastNumber = (int) substr($lastPo->po_number, -3);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }
        
        return "PO-{$year}-" . str_pad($nextNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Calculate totals based on items.
     */
    public function calculateTotals(): void
    {
        $this->subtotal = $this->items()->sum('net_cost');
        $this->tax_amount = $this->subtotal * ($this->tax_rate / 100);
        $this->total_amount = $this->subtotal - $this->discount_amount + $this->tax_amount + $this->shipping_cost;
        $this->save();
    }

    /**
     * Approve the purchase order.
     */
    public function approve(User $user): bool
    {
        if ($this->status !== 'pending') {
            return false;
        }
        
        $this->update([
            'status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);
        
        return true;
    }

    /**
     * Mark as ordered.
     */
    public function markAsOrdered(): bool
    {
        if ($this->status !== 'approved') {
            return false;
        }
        
        $this->update([
            'status' => 'ordered',
            'ordered_at' => now(),
        ]);
        
        return true;
    }

    /**
     * Check if fully received and update status.
     */
    public function checkAndUpdateReceivalStatus(): void
    {
        $totalOrdered = $this->items()->sum('quantity_ordered');
        $totalReceived = $this->items()->sum('quantity_received');
        
        if ($totalReceived >= $totalOrdered) {
            $this->update([
                'status' => 'received',
                'received_at' => now(),
            ]);
        } elseif ($totalReceived > 0) {
            $this->update(['status' => 'partial']);
        }
    }
}
