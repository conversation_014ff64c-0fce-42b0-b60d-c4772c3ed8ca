import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            fontFamily: {
                'sans': ['IBM Plex Sans', ...defaultTheme.fontFamily.sans],
                'arabic': ['IBM Plex Sans Arabic', 'IBM Plex Sans', ...defaultTheme.fontFamily.sans],
                'century': ['Century Gothic', 'IBM Plex Sans', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                // UAE English Sports Academy Brand Colors
                'leaders': {
                    'red': '#E53E3E',
                    'deep-red': '#C53030',
                    'gold': '#D69E2E',
                },
                'neutral': {
                    'white': '#FFFFFF',
                    'off-white': '#FAFAFA',
                    'light-gray': '#F7FAFC',
                    'medium-gray': '#E2E8F0',
                    'dark-gray': '#4A5568',
                    'charcoal': '#1A202C',
                },
                'status': {
                    'success': '#38A169',
                    'warning': '#DD6B20',
                    'error': '#E53E3E',
                    'info': '#3182CE',
                }
            },
            spacing: {
                '18': '4.5rem',
                '88': '22rem',
                '128': '32rem',
            },
            boxShadow: {
                'card': '0 2px 4px rgba(0, 0, 0, 0.1)',
                'hover': '0 4px 8px rgba(0, 0, 0, 0.15)',
                'bank': '0 8px 32px rgba(0, 0, 0, 0.12)',
            },
            borderRadius: {
                'card': '8px',
                'button': '6px',
            },
            animation: {
                'fade-in-up': 'fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1)',
                'scale-in': 'scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
                'slide-in-left': 'slideInLeft 0.3s ease-out',
                'slide-in-right': 'slideInRight 0.3s ease-out',
            },
            keyframes: {
                fadeInUp: {
                    '0%': { opacity: '0', transform: 'translateY(30px)' },
                    '100%': { opacity: '1', transform: 'translateY(0)' },
                },
                scaleIn: {
                    '0%': { opacity: '0', transform: 'scale(0.9)' },
                    '100%': { opacity: '1', transform: 'scale(1)' },
                },
                slideInLeft: {
                    '0%': { transform: 'translateX(-100%)' },
                    '100%': { transform: 'translateX(0)' },
                },
                slideInRight: {
                    '0%': { transform: 'translateX(100%)' },
                    '100%': { transform: 'translateX(0)' },
                },
            },
        },
    },

    plugins: [forms],
};
