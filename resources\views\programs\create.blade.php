@extends('layouts.dashboard')

@section('title', 'Create Program - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Create New Program</h1>
                @if (isset($prefilledStudent))
                    <p class="text-lg text-dark-gray">Creating program for student: <span
                            class="font-semibold text-red-600">{{ $prefilledStudent->full_name }}</span></p>
                    <p class="text-sm text-gray-600">This program will be automatically assigned to the student upon
                        creation</p>
                @else
                    <p class="text-lg text-dark-gray">Add a new program to the academy system</p>
                @endif
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @if (isset($prefilledStudent))
                <a href="{{ route('students.show', $prefilledStudent) }}" class="btn-bank btn-bank-outline">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18">
                        </path>
                    </svg>
                    Back to Student
                </a>
            @else
                <a href="{{ route('programs.index') }}" class="btn-bank btn-bank-outline">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18">
                        </path>
                    </svg>
                    Back to Programs
                </a>
            @endif
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto" x-data="programForm()" x-init="init()">

        @if (isset($prefilledAcademy) && $academyProgramContext)
            <!-- Academy Context Information -->
            <div class="bank-card bg-gradient-to-r from-red-50 to-red-100 border-red-200 mb-6">
                <div class="bank-card-header">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 14l9-5-9-5-9 5 9 5z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="bank-card-title text-red-800">Creating Program for Academy:
                                {{ $academyProgramContext['academy_name'] }}</h3>
                            <p class="text-sm text-red-600">{{ $academyProgramContext['branch_name'] }} Branch</p>
                        </div>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div class="bg-white rounded-lg p-3 border border-red-200">
                            <div class="text-sm font-medium text-red-700">Total Students</div>
                            <div class="text-lg font-bold text-red-900">{{ $academyProgramContext['total_students'] }}</div>
                        </div>
                        <div class="bg-white rounded-lg p-3 border border-red-200">
                            <div class="text-sm font-medium text-red-700">Current Programs</div>
                            <div class="text-lg font-bold text-red-900">{{ $academyProgramContext['total_programs'] }}</div>
                        </div>
                        <div class="bg-white rounded-lg p-3 border border-red-200">
                            <div class="text-sm font-medium text-red-700">Available Students</div>
                            <div class="text-lg font-bold text-red-900">{{ $availableStudents->count() }}</div>
                        </div>
                        <div class="bg-white rounded-lg p-3 border border-red-200">
                            <div class="text-sm font-medium text-red-700">Academy</div>
                            <div class="text-lg font-bold text-red-900">{{ $prefilledAcademy->name }}</div>
                        </div>
                    </div>

                    <div class="mt-4 text-sm text-red-700">
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            ✨ Form pre-filled with academy data
                        </span>
                    </div>
                </div>
            </div>
        @endif

        <!-- Student Assignment Notice -->
        @if (isset($prefilledStudent))
            <div class="bank-card bg-blue-50 border-blue-200 mb-6">
                <div class="bank-card-body">
                    <div class="flex items-start">
                        <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-blue-800 mb-2">Auto-Assignment Enabled</h4>
                            <p class="text-blue-700 mb-2">
                                This program will be automatically assigned to
                                <strong>{{ $prefilledStudent->full_name }}</strong>
                                (Student ID: {{ $prefilledStudent->id }}) upon creation.
                            </p>
                            <p class="text-sm text-blue-600">
                                The student's academy (<strong>{{ $prefilledStudent->academy->name ?? 'N/A' }}</strong>)
                                has been pre-selected for this program.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        <form method="POST" action="{{ route('programs.store') }}" class="space-y-6">
            @csrf

            <!-- Hidden student_id if coming from student page -->
            @if (request('student_id'))
                <input type="hidden" name="student_id" value="{{ request('student_id') }}">
            @endif

            <!-- Basic Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Basic Information</h3>
                        <p class="bank-card-subtitle">Enter the program's basic details</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Program Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label-bank">Program Name <span
                                    class="text-error-red">*</span></label>

                            @if (isset($prefilledStudent) && $availablePrograms->count() > 0)
                                <!-- Dropdown for existing programs when student_id is provided -->
                                <select name="name" id="name"
                                    class="form-input-bank @error('name') border-error-red @enderror" required>
                                    <option value="">Select an existing program for
                                        {{ $prefilledStudent->full_name }}</option>
                                    @foreach ($availablePrograms as $program)
                                        <option value="{{ $program->name }}"
                                            {{ old('name') == $program->name ? 'selected' : '' }}>
                                            {{ $program->name }}
                                            ({{ $program->student_count }}/{{ $program->max_students ?? '∞' }} students)
                                            - {{ $program->formatted_price }}
                                        </option>
                                    @endforeach
                                    <option value="__new_program__"
                                        {{ old('name') == '__new_program__' ? 'selected' : '' }}>
                                        + Create New Program
                                    </option>
                                </select>

                                <!-- Hidden text input for new program name -->
                                <div id="new-program-name-container" style="display: none;" class="mt-3">
                                    <label for="new_program_name" class="form-label-bank">New Program Name <span
                                            class="text-error-red">*</span></label>
                                    <input type="text" name="new_program_name" id="new_program_name"
                                        value="{{ old('new_program_name') }}"
                                        class="form-input-bank @error('new_program_name') border-error-red @enderror"
                                        placeholder="Enter new program name">
                                </div>

                                <p class="text-sm text-dark-gray mt-2">
                                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Programs available for <strong>{{ $prefilledStudent->academy->name }}</strong>
                                    ({{ $prefilledStudent->branch->name }})
                                </p>
                            @else
                                <!-- Regular text input for new program -->
                                <input type="text" name="name" id="name" value="{{ old('name') }}"
                                    class="form-input-bank @error('name') border-error-red @enderror"
                                    placeholder="Enter program name" required>
                            @endif

                            @error('name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            @error('new_program_name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Branch Selection -->
                        <div>
                            <label for="branch_id" class="form-label-bank">Branch <span
                                    class="text-error-red">*</span></label>
                            <select name="branch_id" id="branch_id" @change="onBranchChange($event)"
                                class="form-select-bank @error('branch_id') border-error-red @enderror" required>
                                <option value="">Select Branch</option>
                                @foreach ($branches as $branch)
                                    <option value="{{ $branch->id }}"
                                        {{ old('branch_id', isset($prefilledStudent) ? $prefilledStudent->branch_id : (isset($prefilledAcademy) ? $prefilledAcademy->branch_id : '')) == $branch->id ? 'selected' : '' }}>
                                        {{ $branch->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('branch_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Academy Selection -->
                        <div>
                            <label for="academy_id" class="form-label-bank">Academy <span
                                    class="text-error-red">*</span></label>
                            <select name="academy_id" id="academy_id"
                                class="form-select-bank @error('academy_id') border-error-red @enderror" required>
                                <option value="">Select Academy</option>
                                @foreach ($academies as $academy)
                                    <option value="{{ $academy->id }}"
                                        {{ old('academy_id', isset($prefilledStudent) ? $prefilledStudent->academy_id : (isset($prefilledAcademy) ? $prefilledAcademy->id : '')) == $academy->id ? 'selected' : '' }}
                                        data-branch-id="{{ $academy->branch_id }}">
                                        {{ $academy->name }} ({{ $academy->branch->name }})
                                    </option>
                                @endforeach
                            </select>
                            @error('academy_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="description" class="form-label-bank">Description</label>
                            <textarea name="description" id="description" rows="3"
                                class="form-textarea-bank @error('description') border-error-red @enderror"
                                placeholder="Enter program description (optional)">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Schedule & Pricing Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Schedule & Pricing</h3>
                        <p class="bank-card-subtitle">Set program schedule and pricing details</p>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-6">
                        <!-- Days Selection -->
                        <div>
                            <label class="form-label-bank">Program Days <span class="text-error-red">*</span></label>
                            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3 mt-2">
                                @php
                                    $days = [
                                        'SUN' => 'Sunday',
                                        'MON' => 'Monday',
                                        'TUE' => 'Tuesday',
                                        'WED' => 'Wednesday',
                                        'THU' => 'Thursday',
                                        'FRI' => 'Friday',
                                        'SAT' => 'Saturday',
                                    ];
                                    $selectedDays = old('days', []);
                                @endphp
                                @foreach ($days as $value => $label)
                                    <label
                                        class="flex items-center p-3 border border-medium-gray rounded-lg cursor-pointer hover:bg-off-white transition-colors duration-200 {{ in_array($value, $selectedDays) ? 'bg-leaders-red bg-opacity-10 border-leaders-red' : '' }}">
                                        <input type="checkbox" name="days[]" value="{{ $value }}"
                                            {{ in_array($value, $selectedDays) ? 'checked' : '' }}
                                            class="form-checkbox text-leaders-red border-medium-gray rounded focus:ring-leaders-red focus:ring-offset-0">
                                        <span class="ml-2 text-sm font-medium text-dark-gray">{{ $label }}</span>
                                    </label>
                                @endforeach
                            </div>
                            @error('days')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Time Schedule -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="start_time" class="form-label-bank">Start Time</label>
                                <input type="time" name="start_time" id="start_time" value="{{ old('start_time') }}"
                                    class="form-input-bank @error('start_time') border-error-red @enderror">
                                @error('start_time')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="end_time" class="form-label-bank">End Time</label>
                                <input type="time" name="end_time" id="end_time" value="{{ old('end_time') }}"
                                    class="form-input-bank @error('end_time') border-error-red @enderror">
                                @error('end_time')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Classes and Pricing -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="classes" class="form-label-bank">Number of Classes <span
                                        class="text-error-red">*</span></label>
                                <input type="number" name="classes" id="classes" value="{{ old('classes', 1) }}"
                                    min="1" max="50"
                                    class="form-input-bank @error('classes') border-error-red @enderror" required>
                                @error('classes')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="price" class="form-label-bank">Price <span
                                        class="text-error-red">*</span></label>
                                <div class="relative">
                                    <input type="number" name="price" id="price" value="{{ old('price') }}"
                                        min="0" step="0.01"
                                        class="form-input-bank pr-12 @error('price') border-error-red @enderror"
                                        placeholder="0.00" required>
                                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                        <span class="text-dark-gray text-sm">AED</span>
                                    </div>
                                </div>
                                @error('price')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="max_students" class="form-label-bank">Max Students</label>
                                <input type="number" name="max_students" id="max_students"
                                    value="{{ old('max_students') }}" min="1" max="1000"
                                    class="form-input-bank @error('max_students') border-error-red @enderror"
                                    placeholder="Unlimited">
                                @error('max_students')
                                    <p class="form-error-bank">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label class="form-label-bank">Status</label>
                            <div class="flex items-center space-x-6 mt-2">
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="1"
                                        {{ old('status', '1') == '1' ? 'checked' : '' }}
                                        class="form-radio text-leaders-red border-medium-gray focus:ring-leaders-red focus:ring-offset-0">
                                    <span class="ml-2 text-sm font-medium text-dark-gray">Active</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="status" value="0"
                                        {{ old('status') == '0' ? 'checked' : '' }}
                                        class="form-radio text-leaders-red border-medium-gray focus:ring-leaders-red focus:ring-offset-0">
                                    <span class="ml-2 text-sm font-medium text-dark-gray">Inactive</span>
                                </label>
                            </div>
                            @error('status')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            @if (isset($availableStudents) && $availableStudents->count() > 0)
                <!-- Student Assignment Section -->
                <div class="bank-card">
                    <div class="bank-card-header">
                        <div>
                            <h3 class="bank-card-title">Assign Students to Program</h3>
                            <p class="bank-card-subtitle">Select students from {{ $prefilledAcademy->name }} to assign to
                                this program</p>
                        </div>
                    </div>
                    <div class="bank-card-body">
                        <div class="space-y-3">
                            <div class="flex items-center justify-between mb-4">
                                <span class="text-sm font-medium text-dark-gray">Available Students
                                    ({{ $availableStudents->count() }})</span>
                                <div class="flex space-x-2">
                                    <button type="button" onclick="selectAllStudents()"
                                        class="text-sm text-leaders-red hover:text-leaders-deep-red">
                                        Select All
                                    </button>
                                    <span class="text-light-gray">|</span>
                                    <button type="button" onclick="deselectAllStudents()"
                                        class="text-sm text-leaders-red hover:text-leaders-deep-red">
                                        Deselect All
                                    </button>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                @foreach ($availableStudents as $student)
                                    <label
                                        class="flex items-center p-3 bg-white rounded-lg border border-light-gray hover:border-leaders-red transition-colors duration-200 cursor-pointer">
                                        <input type="checkbox" name="assign_students[]" value="{{ $student->id }}"
                                            class="form-checkbox h-4 w-4 text-leaders-red border-medium-gray rounded focus:ring-leaders-red focus:ring-offset-0">
                                        <div class="ml-3 flex-1">
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <p class="text-sm font-medium text-charcoal-black">
                                                        {{ $student->full_name }}</p>
                                                    <p class="text-xs text-dark-gray">{{ $student->phone ?? 'No phone' }}
                                                    </p>
                                                </div>
                                                <div class="text-right">
                                                    <p class="text-xs text-dark-gray">ID: {{ $student->id }}</p>
                                                    <p class="text-xs text-dark-gray">Joined:
                                                        {{ $student->join_date ? $student->join_date->format('M d, Y') : 'N/A' }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                @endforeach
                            </div>

                            <div class="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                                <div class="flex items-start">
                                    <svg class="w-5 h-5 text-blue-600 mt-0.5 mr-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div class="text-sm text-blue-700">
                                        <p class="font-medium">Note:</p>
                                        <p>Only students without an assigned program are shown. Selected students will be
                                            automatically assigned to this program upon creation.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Form Actions -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-end space-x-4">
                        <a href="{{ route('programs.index') }}" class="btn-bank btn-bank-outline">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12">
                                </path>
                            </svg>
                            Cancel
                        </a>
                        <button type="submit" class="btn-bank">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7">
                                </path>
                            </svg>
                            Create Program
                            @if (isset($availableStudents) && $availableStudents->count() > 0)
                                <span class="ml-1" id="selected-count"></span>
                            @endif
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function programForm() {
            return {
                init() {
                    // Initialize form functionality
                    @if (isset($prefilledStudent))
                        // Handle pre-filled data from student
                        const branchId = '{{ $prefilledStudent->branch_id }}';
                        if (branchId) {
                            this.filterAcademiesByBranch(branchId);
                        }

                        // Initialize program name dropdown behavior
                        this.initProgramNameDropdown();
                    @elseif (isset($prefilledAcademy))
                        // Handle pre-filled data from academy
                        const branchId = '{{ $prefilledAcademy->branch_id }}';
                        if (branchId) {
                            this.filterAcademiesByBranch(branchId);
                        }
                    @else
                        this.filterAcademiesByBranch();
                    @endif
                },

                initProgramNameDropdown() {
                    const nameSelect = document.getElementById('name');
                    const newProgramContainer = document.getElementById('new-program-name-container');
                    const newProgramInput = document.getElementById('new_program_name');

                    if (nameSelect && newProgramContainer) {
                        nameSelect.addEventListener('change', function() {
                            if (this.value === '__new_program__') {
                                newProgramContainer.style.display = 'block';
                                newProgramInput.required = true;
                                nameSelect.required = false;
                            } else {
                                newProgramContainer.style.display = 'none';
                                newProgramInput.required = false;
                                nameSelect.required = true;
                            }
                        });

                        // Check initial state
                        if (nameSelect.value === '__new_program__') {
                            newProgramContainer.style.display = 'block';
                            newProgramInput.required = true;
                            nameSelect.required = false;
                        }
                    }
                },

                onBranchChange(event) {
                    this.filterAcademiesByBranch(event.target.value);
                },

                filterAcademiesByBranch(branchId = null) {
                    const academySelect = document.getElementById('academy_id');
                    const academyOptions = academySelect.querySelectorAll('option');

                    academyOptions.forEach(option => {
                        if (option.value === '') {
                            option.style.display = 'block';
                            return;
                        }

                        const optionBranchId = option.dataset.branchId;
                        if (!branchId || optionBranchId === branchId) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                            if (option.selected) {
                                option.selected = false;
                                academySelect.value = '';
                            }
                        }
                    });
                }
            }
        }

        // Student selection functions
        function selectAllStudents() {
            const checkboxes = document.querySelectorAll('input[name="assign_students[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
            updateSelectedCount();
        }

        function deselectAllStudents() {
            const checkboxes = document.querySelectorAll('input[name="assign_students[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
            updateSelectedCount();
        }

        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('input[name="assign_students[]"]:checked');
            const countElement = document.getElementById('selected-count');
            if (countElement) {
                if (checkboxes.length > 0) {
                    countElement.textContent = `(${checkboxes.length} selected)`;
                } else {
                    countElement.textContent = '';
                }
            }
        }

        // Add event listeners for checkboxes
        document.addEventListener('DOMContentLoaded', function() {
            const checkboxes = document.querySelectorAll('input[name="assign_students[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedCount);
            });
            updateSelectedCount(); // Initial count
        });
    </script>
@endpush
