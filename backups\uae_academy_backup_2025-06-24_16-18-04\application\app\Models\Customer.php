<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class Customer extends Model
{
    protected $fillable = [
        'customer_number',
        'full_name',
        'full_name_ar',
        'first_name',
        'first_name_ar',
        'last_name',
        'last_name_ar',
        'email',
        'phone',
        'phone_secondary',
        'nationality',
        'nationality_ar',
        'address',
        'address_ar',
        'city',
        'emirate',
        'birth_date',
        'gender',
        'id_type',
        'id_number',
        'id_expiry_date',
        'customer_type',
        'company_name',
        'company_name_ar',
        'trade_license',
        'tax_number',
        'preferred_language',
        'preferred_contact_method',
        'emergency_contact',
        'credit_limit',
        'payment_terms',
        'vip_status',
        'registration_date',
        'last_booking_date',
        'total_bookings',
        'total_spent',
        'status',
        'notes',
        'notes_ar',
        'profile_image',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'id_expiry_date' => 'date',
        'registration_date' => 'date',
        'last_booking_date' => 'date',
        'emergency_contact' => 'array',
        'credit_limit' => 'decimal:2',
        'total_spent' => 'decimal:2',
        'total_bookings' => 'integer',
        'vip_status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'localized_name',
        'localized_first_name',
        'localized_last_name',
        'localized_nationality',
        'localized_address',
        'localized_notes',
        'localized_company_name',
        'age',
        'formatted_phone',
        'formatted_phone_secondary',
        'status_text',
        'status_badge_class',
        'customer_type_text',
        'payment_terms_text',
        'formatted_total_spent',
        'formatted_credit_limit',
        'days_since_registration',
        'days_since_last_booking',
        'profile_image_url',
        'is_id_expired',
        'customer_tier',
    ];

    /**
     * Get the reservations for this customer.
     */
    public function reservations(): HasMany
    {
        return $this->hasMany(Reservation::class);
    }

    /**
     * Get the reservation payments for this customer.
     */
    public function reservationPayments(): HasMany
    {
        return $this->hasMany(ReservationPayment::class);
    }

    // Computed Properties

    /**
     * Get localized name based on current locale.
     */
    public function getLocalizedNameAttribute(): string
    {
        return app()->getLocale() === 'ar' && $this->full_name_ar ? $this->full_name_ar : $this->full_name;
    }

    /**
     * Get localized first name based on current locale.
     */
    public function getLocalizedFirstNameAttribute(): string
    {
        return app()->getLocale() === 'ar' && $this->first_name_ar ? $this->first_name_ar : $this->first_name;
    }

    /**
     * Get localized last name based on current locale.
     */
    public function getLocalizedLastNameAttribute(): string
    {
        return app()->getLocale() === 'ar' && $this->last_name_ar ? $this->last_name_ar : $this->last_name;
    }

    /**
     * Get localized nationality based on current locale.
     */
    public function getLocalizedNationalityAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->nationality_ar ? $this->nationality_ar : $this->nationality;
    }

    /**
     * Get localized address based on current locale.
     */
    public function getLocalizedAddressAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->address_ar ? $this->address_ar : $this->address;
    }

    /**
     * Get localized notes based on current locale.
     */
    public function getLocalizedNotesAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->notes_ar ? $this->notes_ar : $this->notes;
    }

    /**
     * Get localized company name based on current locale.
     */
    public function getLocalizedCompanyNameAttribute(): ?string
    {
        return app()->getLocale() === 'ar' && $this->company_name_ar ? $this->company_name_ar : $this->company_name;
    }

    /**
     * Get customer's age based on birth date.
     */
    public function getAgeAttribute(): ?int
    {
        return $this->birth_date ? $this->birth_date->age : null;
    }

    /**
     * Get formatted phone number.
     */
    public function getFormattedPhoneAttribute(): string
    {
        return $this->formatUAEPhone($this->phone);
    }

    /**
     * Get formatted secondary phone number.
     */
    public function getFormattedPhoneSecondaryAttribute(): ?string
    {
        return $this->phone_secondary ? $this->formatUAEPhone($this->phone_secondary) : null;
    }

    /**
     * Get status text.
     */
    public function getStatusTextAttribute(): string
    {
        return match ($this->status) {
            'active' => 'Active',
            'inactive' => 'Inactive',
            'blocked' => 'Blocked',
            default => 'Unknown',
        };
    }

    /**
     * Get status badge class.
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status) {
            'active' => 'badge-success',
            'inactive' => 'badge-warning',
            'blocked' => 'badge-danger',
            default => 'badge-secondary',
        };
    }

    /**
     * Get customer type text.
     */
    public function getCustomerTypeTextAttribute(): string
    {
        return match ($this->customer_type) {
            'individual' => 'Individual',
            'corporate' => 'Corporate',
            'group' => 'Group',
            default => 'Unknown',
        };
    }

    /**
     * Get payment terms text.
     */
    public function getPaymentTermsTextAttribute(): string
    {
        return match ($this->payment_terms) {
            'cash' => 'Cash',
            'credit_7' => '7 Days Credit',
            'credit_15' => '15 Days Credit',
            'credit_30' => '30 Days Credit',
            default => 'Unknown',
        };
    }

    /**
     * Get formatted total spent.
     */
    public function getFormattedTotalSpentAttribute(): string
    {
        return 'AED ' . number_format($this->total_spent, 2);
    }

    /**
     * Get formatted credit limit.
     */
    public function getFormattedCreditLimitAttribute(): string
    {
        return 'AED ' . number_format($this->credit_limit, 2);
    }

    /**
     * Get days since registration.
     */
    public function getDaysSinceRegistrationAttribute(): int
    {
        return $this->registration_date->diffInDays(now());
    }

    /**
     * Get days since last booking.
     */
    public function getDaysSinceLastBookingAttribute(): ?int
    {
        return $this->last_booking_date ? $this->last_booking_date->diffInDays(now()) : null;
    }

    /**
     * Get profile image URL.
     */
    public function getProfileImageUrlAttribute(): string
    {
        if ($this->profile_image) {
            return asset('storage/' . $this->profile_image);
        }

        // Return default avatar based on first letter of name
        return $this->getDefaultAvatarUrl();
    }

    /**
     * Check if ID is expired.
     */
    public function getIsIdExpiredAttribute(): bool
    {
        return $this->id_expiry_date && $this->id_expiry_date->isPast();
    }

    /**
     * Get customer tier based on total spent.
     */
    public function getCustomerTierAttribute(): string
    {
        if ($this->vip_status) {
            return 'VIP';
        }

        if ($this->total_spent >= 10000) {
            return 'Platinum';
        } elseif ($this->total_spent >= 5000) {
            return 'Gold';
        } elseif ($this->total_spent >= 1000) {
            return 'Silver';
        } else {
            return 'Bronze';
        }
    }

    // Query Scopes

    /**
     * Scope to get only active customers.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get VIP customers.
     */
    public function scopeVip(Builder $query): Builder
    {
        return $query->where('vip_status', true);
    }

    /**
     * Scope to filter by customer type.
     */
    public function scopeByType(Builder $query, string $type): Builder
    {
        return $query->where('customer_type', $type);
    }

    /**
     * Scope to search customers.
     */
    public function scopeSearch(Builder $query, ?string $search): Builder
    {
        if (!$search) {
            return $query;
        }

        return $query->where(function ($q) use ($search) {
            $q->where('full_name', 'like', "%{$search}%")
                ->orWhere('full_name_ar', 'like', "%{$search}%")
                ->orWhere('customer_number', 'like', "%{$search}%")
                ->orWhere('phone', 'like', "%{$search}%")
                ->orWhere('email', 'like', "%{$search}%")
                ->orWhere('company_name', 'like', "%{$search}%");
        });
    }

    // Utility Methods

    /**
     * Format UAE phone number.
     */
    private function formatUAEPhone(string $phone): string
    {
        // Format +971XXXXXXXXX to +971 XX XXX XXXX
        if (preg_match('/^\+971(\d{9})$/', $phone, $matches)) {
            $number = $matches[1];
            return '+971 ' . substr($number, 0, 2) . ' ' . substr($number, 2, 3) . ' ' . substr($number, 5);
        }
        return $phone;
    }

    /**
     * Generate unique customer number.
     */
    public static function generateCustomerNumber(): string
    {
        $year = now()->year;
        $counter = self::whereYear('created_at', $year)->count() + 1;
        
        return 'CUS' . $year . str_pad($counter, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get default avatar URL based on name.
     */
    private function getDefaultAvatarUrl(): string
    {
        $initial = strtoupper(substr($this->full_name, 0, 1));
        return "https://ui-avatars.com/api/?name={$initial}&size=200&background=dc2626&color=ffffff";
    }

    /**
     * Update customer statistics after booking.
     */
    public function updateBookingStats(float $amount): void
    {
        $this->increment('total_bookings');
        $this->increment('total_spent', $amount);
        $this->update(['last_booking_date' => now()]);
    }

    /**
     * Get customer statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_reservations' => $this->reservations()->count(),
            'confirmed_reservations' => $this->reservations()->where('status', 'confirmed')->count(),
            'completed_reservations' => $this->reservations()->where('status', 'completed')->count(),
            'cancelled_reservations' => $this->reservations()->where('status', 'cancelled')->count(),
            'total_payments' => $this->reservationPayments()->where('status', 'completed')->sum('total_amount'),
            'pending_payments' => $this->reservationPayments()->where('status', 'pending')->sum('total_amount'),
            'this_month_bookings' => $this->reservations()->whereMonth('reservation_date', now()->month)->count(),
            'this_month_spending' => $this->reservationPayments()
                ->where('status', 'completed')
                ->whereMonth('payment_date', now()->month)
                ->sum('total_amount'),
        ];
    }
}
