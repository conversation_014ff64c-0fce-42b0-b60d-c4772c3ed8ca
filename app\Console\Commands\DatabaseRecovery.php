<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Artisan;
use App\Models\User;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Student;
use App\Models\Payment;

class DatabaseRecovery extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'db:recover {--backup-first : Create backup before recovery} {--force : Force recovery without confirmation}';

    /**
     * The console command description.
     */
    protected $description = 'Recover database from critical errors and inconsistencies';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🚨 Database Recovery Tool');
        $this->newLine();

        if (!$this->option('force')) {
            if (!$this->confirm('This will attempt to fix critical database issues. Continue?')) {
                $this->info('Recovery cancelled.');
                return 0;
            }
        }

        $backupCreated = false;
        if ($this->option('backup-first')) {
            $backupCreated = $this->createBackup();
        }

        $this->info('🔧 Starting database recovery...');
        $this->newLine();

        $recoverySteps = [
            'checkDatabaseConnection',
            'validateMigrations',
            'fixForeignKeyViolations',
            'removeDuplicateRecords',
            'repairOrphanedRecords',
            'validateDataIntegrity',
            'optimizeDatabase',
        ];

        $successCount = 0;
        $totalSteps = count($recoverySteps);

        foreach ($recoverySteps as $step) {
            try {
                $this->info("🔄 Executing: {$step}");
                $result = $this->$step();
                
                if ($result) {
                    $this->info("✅ {$step} completed successfully");
                    $successCount++;
                } else {
                    $this->warn("⚠️  {$step} completed with warnings");
                    $successCount++;
                }
                
            } catch (\Exception $e) {
                $this->error("❌ {$step} failed: " . $e->getMessage());
                
                if (!$this->confirm("Continue with remaining recovery steps?")) {
                    break;
                }
            }
        }

        $this->newLine();
        $this->info("📊 Recovery Summary:");
        $this->info("- {$successCount}/{$totalSteps} steps completed");
        $this->info("- Backup created: " . ($backupCreated ? 'Yes' : 'No'));
        
        if ($successCount === $totalSteps) {
            $this->info("🎉 Database recovery completed successfully!");
            return 0;
        } else {
            $this->error("💥 Database recovery completed with issues.");
            return 1;
        }
    }

    private function createBackup(): bool
    {
        try {
            $this->info('📦 Creating database backup...');
            
            $timestamp = now()->format('Y-m-d_H-i-s');
            $backupFile = storage_path("backups/db_backup_{$timestamp}.sql");
            
            // Create backups directory if it doesn't exist
            if (!is_dir(dirname($backupFile))) {
                mkdir(dirname($backupFile), 0755, true);
            }

            $database = config('database.connections.mysql.database');
            $username = config('database.connections.mysql.username');
            $password = config('database.connections.mysql.password');
            $host = config('database.connections.mysql.host');

            $command = "mysqldump -h{$host} -u{$username}";
            if ($password) {
                $command .= " -p{$password}";
            }
            $command .= " {$database} > {$backupFile}";

            exec($command, $output, $returnCode);

            if ($returnCode === 0) {
                $this->info("✅ Backup created: {$backupFile}");
                return true;
            } else {
                $this->warn("⚠️  Backup creation failed, continuing without backup");
                return false;
            }
            
        } catch (\Exception $e) {
            $this->warn("⚠️  Backup creation failed: " . $e->getMessage());
            return false;
        }
    }

    private function checkDatabaseConnection(): bool
    {
        try {
            DB::connection()->getPdo();
            $this->line('   Database connection: OK');
            return true;
        } catch (\Exception $e) {
            throw new \Exception('Database connection failed: ' . $e->getMessage());
        }
    }

    private function validateMigrations(): bool
    {
        try {
            // Check if migrations table exists
            if (!Schema::hasTable('migrations')) {
                $this->line('   Creating migrations table...');
                Artisan::call('migrate:install');
            }

            // Run pending migrations
            $this->line('   Running pending migrations...');
            Artisan::call('migrate');
            
            return true;
        } catch (\Exception $e) {
            throw new \Exception('Migration validation failed: ' . $e->getMessage());
        }
    }

    private function fixForeignKeyViolations(): bool
    {
        $this->line('   Checking foreign key violations...');
        
        // Fix users with invalid branch_id
        $invalidUsers = User::whereNotNull('branch_id')
            ->whereNotExists(function ($query) {
                $query->select('id')->from('branches')->whereColumn('branches.id', 'users.branch_id');
            })->get();

        if ($invalidUsers->count() > 0) {
            $this->line("   Fixing {$invalidUsers->count()} users with invalid branch_id");
            foreach ($invalidUsers as $user) {
                $user->update(['branch_id' => null]);
            }
        }

        // Fix payments with invalid student_id
        $invalidPayments = Payment::whereNotExists(function ($query) {
            $query->select('id')->from('students')->whereColumn('students.id', 'payments.student_id');
        })->get();

        if ($invalidPayments->count() > 0) {
            $this->line("   Removing {$invalidPayments->count()} payments with invalid student_id");
            foreach ($invalidPayments as $payment) {
                $payment->delete();
            }
        }

        return true;
    }

    private function removeDuplicateRecords(): bool
    {
        $this->line('   Checking for duplicate records...');
        
        // Remove duplicate users by email
        $duplicateEmails = User::select('email')
            ->groupBy('email')
            ->havingRaw('COUNT(*) > 1')
            ->pluck('email');

        foreach ($duplicateEmails as $email) {
            $users = User::where('email', $email)->orderBy('id')->get();
            $keepUser = $users->first();
            $deleteUsers = $users->skip(1);
            
            $this->line("   Removing " . $deleteUsers->count() . " duplicate users for {$email}");
            foreach ($deleteUsers as $user) {
                $user->delete();
            }
        }

        return true;
    }

    private function repairOrphanedRecords(): bool
    {
        $this->line('   Checking for orphaned records...');
        
        // Remove academies without branches
        $orphanedAcademies = Academy::whereDoesntHave('branch')->get();
        if ($orphanedAcademies->count() > 0) {
            $this->line("   Removing {$orphanedAcademies->count()} orphaned academies");
            foreach ($orphanedAcademies as $academy) {
                $academy->delete();
            }
        }

        // Remove students without valid branch/academy
        $orphanedStudents = Student::where(function ($query) {
            $query->whereDoesntHave('branch')->orWhereDoesntHave('academy');
        })->get();
        
        if ($orphanedStudents->count() > 0) {
            $this->line("   Removing {$orphanedStudents->count()} orphaned students");
            foreach ($orphanedStudents as $student) {
                $student->delete();
            }
        }

        return true;
    }

    private function validateDataIntegrity(): bool
    {
        $this->line('   Running data integrity validation...');
        
        try {
            Artisan::call('db:validate-integrity');
            return true;
        } catch (\Exception $e) {
            $this->line("   Data integrity validation completed with warnings");
            return true; // Don't fail recovery for validation warnings
        }
    }

    private function optimizeDatabase(): bool
    {
        $this->line('   Optimizing database...');
        
        try {
            // Get all tables
            $tables = DB::select('SHOW TABLES');
            $database = config('database.connections.mysql.database');
            $tableKey = "Tables_in_{$database}";
            
            foreach ($tables as $table) {
                $tableName = $table->$tableKey;
                DB::statement("OPTIMIZE TABLE {$tableName}");
            }
            
            return true;
        } catch (\Exception $e) {
            $this->line("   Database optimization failed: " . $e->getMessage());
            return true; // Don't fail recovery for optimization issues
        }
    }
}
