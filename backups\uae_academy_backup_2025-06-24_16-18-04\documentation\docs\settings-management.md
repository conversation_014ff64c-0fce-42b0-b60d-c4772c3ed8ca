# Settings Management Module

## Overview
The Settings Management module provides a comprehensive system for configuring and managing all aspects of the UAE English Sports Academy system. It features a user-friendly interface with categorized settings, role-based access control, and advanced features like import/export and caching.

## Features

### Core Features
- **Categorized Settings**: Organized into logical categories (General, Academy, Payment, Notification, Security, System)
- **Multiple Input Types**: Support for text, textarea, number, boolean, select, file upload, and JSON data
- **Role-Based Access**: Admin-only access with proper authorization
- **Real-time Validation**: Client-side and server-side validation
- **Settings Caching**: Automatic caching for improved performance
- **Import/Export**: JSON-based backup and restore functionality

### Advanced Features
- **Encrypted Settings**: Sensitive data encryption support
- **Public Settings**: Settings accessible to non-admin users
- **Audit Trail**: Track changes with timestamps
- **File Upload**: Support for logo and document uploads
- **Multi-select Options**: Checkbox-based multi-value settings
- **Responsive Design**: Mobile-friendly interface

## Architecture

### Database Schema
```sql
CREATE TABLE settings (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT NULL,
    type VARCHAR(50) DEFAULT 'string',
    category VARCHAR(50) DEFAULT 'general',
    label VARCHAR(255) NOT NULL,
    description TEXT NULL,
    validation_rules JSON NULL,
    options JSON NULL,
    is_public BOOLEAN DEFAULT FALSE,
    is_encrypted BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### Model Structure
- **Setting Model**: Core model with caching, validation, and type casting
- **SettingPolicy**: Authorization policy for admin-only access
- **SettingController**: Full CRUD operations with advanced features
- **UpdateSettingsRequest**: Form validation for settings updates

## Settings Categories

### 1. General Settings
- **app_name**: Application name
- **app_logo**: Academy logo upload
- **app_timezone**: Default timezone
- **default_language**: System language (English/Arabic)
- **app_description**: Academy description

### 2. Academy Settings
- **default_program_duration**: Default program length in days
- **max_students_per_program**: Maximum students per program
- **auto_generate_student_id**: Automatic student ID generation

### 3. Payment Settings
- **default_currency**: System currency (AED default)
- **payment_methods**: Available payment methods
- **late_payment_fee**: Late payment penalty amount

### 4. Notification Settings
- **email_notifications**: Enable/disable email notifications
- **sms_notifications**: Enable/disable SMS notifications

### 5. Security Settings
- **session_timeout**: Session timeout duration
- **password_min_length**: Minimum password requirements

### 6. System Settings
- **maintenance_mode**: System maintenance toggle
- **backup_frequency**: Automatic backup schedule

## Usage

### Accessing Settings
```php
// Get a setting value
$appName = Setting::get('app_name', 'Default Name');

// Set a setting value
Setting::set('app_name', 'UAE English Sports Academy');

// Get settings by category
$generalSettings = Setting::getByCategory('general');

// Get all settings grouped by category
$allSettings = Setting::getAllGrouped();
```

### Validation
```php
// Validate setting value
$setting = Setting::where('key', 'app_name')->first();
$isValid = $setting->validateValue('New Name');
```

### Caching
Settings are automatically cached for 24 hours. Cache is cleared when settings are modified.

## Routes

### Web Routes
```php
Route::prefix('settings')->name('settings.')->middleware('role:admin')->group(function () {
    Route::get('/', [SettingController::class, 'index'])->name('index');
    Route::get('/edit/{category?}', [SettingController::class, 'edit'])->name('edit');
    Route::put('/update/{category?}', [SettingController::class, 'update'])->name('update');
    Route::post('/store', [SettingController::class, 'store'])->name('store');
    Route::delete('/{setting}', [SettingController::class, 'destroy'])->name('destroy');
    Route::get('/export', [SettingController::class, 'export'])->name('export');
    Route::post('/import', [SettingController::class, 'import'])->name('import');
});
```

## Views

### Main Views
- **settings/index.blade.php**: Settings dashboard with category overview
- **settings/edit.blade.php**: Category-specific settings editor
- **settings/partials/input.blade.php**: Dynamic input rendering

### Layout Integration
Settings pages use the `layouts.dashboard` layout which provides:
- **Sidebar Navigation**: Full left sidebar with all system modules
- **Header Section**: Page title and action buttons
- **Footer Component**: Professional footer with branding
- **Responsive Design**: Mobile-optimized layout with collapsible sidebar
- **Consistent Styling**: Unified design language across all pages

### Navigation
Settings are accessible from the sidebar navigation with a dedicated "Settings" menu item (Admin only). The Settings pages include:
- **Full Sidebar Navigation**: Complete left sidebar with all system modules
- **Footer Integration**: Professional footer with academy branding and links
- **Breadcrumb Navigation**: Clear navigation path within settings categories
- **Responsive Layout**: Sidebar collapses on mobile devices

## Security

### Authorization
- Only users with 'admin' role can access settings
- Policy-based authorization using SettingPolicy
- CSRF protection on all forms

### Data Protection
- Sensitive settings can be encrypted
- File uploads are validated and stored securely
- Input validation prevents malicious data

## Performance

### Caching Strategy
- Settings cached for 24 hours
- Cache automatically cleared on updates
- Grouped caching for category-based access

### Optimization
- Lazy loading of setting relationships
- Efficient database queries
- Minimal memory footprint

## Import/Export

### Export Format
```json
{
    "exported_at": "2025-06-17T12:00:00.000Z",
    "category": "general",
    "settings": [
        {
            "key": "app_name",
            "value": "UAE English Sports Academy",
            "type": "string",
            "category": "general",
            "label": "Application Name",
            "description": "The name of your sports academy system",
            "validation_rules": ["required", "string", "max:255"],
            "is_public": true,
            "sort_order": 1
        }
    ]
}
```

### Import Process
1. Upload JSON file through web interface
2. Validate file format and structure
3. Option to overwrite existing settings
4. Batch import with transaction safety

## Customization

### Adding New Settings
1. Create migration to add setting record
2. Update default settings in migration
3. Add validation rules and options
4. Update documentation

### Custom Input Types
Extend the input partial to support new field types:
```php
// In settings/partials/input.blade.php
@case('custom_type')
    // Custom input implementation
    @break
```

## Testing

### Unit Tests
- Setting model methods
- Policy authorization
- Validation rules

### Feature Tests
- Settings CRUD operations
- Import/export functionality
- Authorization checks

### Browser Tests
- Settings form interactions
- File upload functionality
- Category navigation

## Troubleshooting

### Common Issues
1. **Cache not clearing**: Check cache configuration
2. **File upload fails**: Verify storage permissions
3. **Validation errors**: Check validation rules format
4. **Authorization denied**: Verify user role and policy

### Debug Mode
Enable debug mode to see validation rules in the interface:
```php
// In .env
APP_DEBUG=true
```

## Future Enhancements

### Planned Features
- Settings versioning and rollback
- Advanced validation rules
- Settings templates
- API endpoints for external access
- Real-time settings sync
- Settings search and filtering

### Integration Opportunities
- Integration with notification system
- Connection to payment gateways
- Academy management automation
- Reporting system configuration
