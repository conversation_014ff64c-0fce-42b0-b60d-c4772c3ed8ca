import './bootstrap';
import './rtl-helpers';
import './language-switcher';
import './auto-translation';
import './name-translation';

import Alpine from 'alpinejs';

window.Alpine = Alpine;

// UAE English Sports Academy - Bank-Style Dashboard JavaScript

// Unified Button Text Color Fix
function ensureButtonTextStyling() {
    // Find all buttons in the system
    const buttons = document.querySelectorAll(
        'button, .btn, input[type="button"], input[type="submit"], input[type="reset"], a.btn, a.button, [role="button"]'
    );

    console.log(`Found ${buttons.length} buttons to check`);

    buttons.forEach((button, index) => {
        // Get computed styles
        const computedStyle = window.getComputedStyle(button);
        const backgroundColor = computedStyle.backgroundColor;
        const backgroundImage = computedStyle.backgroundImage;
        const textColor = computedStyle.color;

        // Check if button has red background
        const hasRedBackground = isRedBackground(backgroundColor, backgroundImage, button);

        if (hasRedBackground) {
            // Check if text color is dark/black
            const isDarkText = isDarkTextColor(textColor);

            if (isDarkText) {
                // Force white text
                button.style.setProperty('color', 'white', 'important');

                // Also apply to child elements (but don't override button background)
                const childElements = button.querySelectorAll('*');
                childElements.forEach(child => {
                    child.style.setProperty('color', 'white', 'important');
                    // Only set background to transparent for child elements, not the button itself
                    if (child !== button) {
                        child.style.setProperty('background', 'transparent', 'important');
                    }
                });

                // Fix SVG icons
                const svgElements = button.querySelectorAll('svg');
                svgElements.forEach(svg => {
                    svg.style.setProperty('color', 'white', 'important');
                    svg.style.setProperty('fill', 'white', 'important');
                    svg.style.setProperty('stroke', 'white', 'important');
                });

                console.log(`Fixed button ${index + 1}: "${button.textContent.trim()}" - Changed from ${textColor} to white`);
            }
        }
    });
}

// Helper function to detect red backgrounds
function isRedBackground(backgroundColor, backgroundImage, element) {
    // Check for red color values
    const redPatterns = [
        /rgb\((\d+),\s*(\d+),\s*(\d+)\)/, // rgb(r, g, b)
        /rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/, // rgba(r, g, b, a)
        /#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})/ // hex colors
    ];

    // Check background color
    if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent') {
        // Check for RGB red values
        const rgbMatch = backgroundColor.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
        if (rgbMatch) {
            const [, r, g, b] = rgbMatch.map(Number);
            // Consider it red if red component is significantly higher than green and blue
            if (r > 150 && r > g + 50 && r > b + 50) {
                return true;
            }
        }

        // Check for RGBA red values
        const rgbaMatch = backgroundColor.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/);
        if (rgbaMatch) {
            const [, r, g, b] = rgbaMatch.map(Number);
            if (r > 150 && r > g + 50 && r > b + 50) {
                return true;
            }
        }
    }

    // Check background image for gradients
    if (backgroundImage && backgroundImage !== 'none') {
        if (backgroundImage.includes('red') ||
            backgroundImage.includes('#e53e3e') ||
            backgroundImage.includes('#dc2626') ||
            backgroundImage.includes('#b91c1c')) {
            return true;
        }
    }

    // Check class names for red indicators
    const className = element.className || '';
    const redClassPatterns = [
        'red', 'Red', 'RED', 'primary', 'danger', 'error', 'warning',
        'leaders', 'Leaders', 'btn-bank', 'btn-primary', 'btn-danger',
        'bg-leaders-red', 'bg-leaders-deep-red', 'bg-red'
    ];

    return redClassPatterns.some(pattern => className.includes(pattern));
}

// Helper function to detect dark text colors
function isDarkTextColor(color) {
    if (!color || color === 'transparent') return false;

    // Check for RGB values
    const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (rgbMatch) {
        const [, r, g, b] = rgbMatch.map(Number);
        // Calculate luminance
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
        return luminance < 0.5; // Dark if luminance is less than 50%
    }

    // Check for RGBA values
    const rgbaMatch = color.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/);
    if (rgbaMatch) {
        const [, r, g, b] = rgbaMatch.map(Number);
        const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
        return luminance < 0.5;
    }

    // Check for common dark color keywords
    const darkColors = ['black', 'dark', 'gray', 'grey'];
    return darkColors.some(darkColor => color.includes(darkColor));
}

// Specific fix for Quick Actions section
function fixQuickActionsButtons() {
    // Target Quick Actions buttons specifically
    const quickActionButtons = document.querySelectorAll(
        '.bg-leaders-red, .bg-leaders-deep-red, a.bg-leaders-red, a.bg-leaders-deep-red, .btn-bank.bg-leaders-red'
    );

    console.log(`Found ${quickActionButtons.length} Quick Actions buttons to fix`);

    quickActionButtons.forEach((button, index) => {
        // Force white text
        button.style.setProperty('color', 'white', 'important');

        // Fix all child elements (but preserve button's own background)
        const allChildren = button.querySelectorAll('*');
        allChildren.forEach(child => {
            // Only set text color to white, don't touch background of the main button
            child.style.setProperty('color', 'white', 'important');
            // Only set background to transparent for child elements, not the button itself
            if (child !== button) {
                child.style.setProperty('background', 'transparent', 'important');
            }
        });

        // Fix SVG icons specifically
        const svgElements = button.querySelectorAll('svg');
        svgElements.forEach(svg => {
            svg.style.setProperty('color', 'white', 'important');
            svg.style.setProperty('fill', 'white', 'important');
            svg.style.setProperty('stroke', 'white', 'important');
        });

        // Fix text nodes directly
        const textNodes = [];
        const walker = document.createTreeWalker(
            button,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        let node;
        while (node = walker.nextNode()) {
            if (node.nodeValue.trim()) {
                textNodes.push(node);
            }
        }

        // Ensure parent elements of text nodes have white color
        textNodes.forEach(textNode => {
            if (textNode.parentElement) {
                textNode.parentElement.style.setProperty('color', 'white', 'important');
            }
        });

        console.log(`Fixed Quick Action button ${index + 1}: "${button.textContent.trim()}"`);
    });
}

// Unified Table Header Styling Fix
function ensureTableHeaderStyling() {
    // Find all table headers and ensure they have white text
    const tableHeaders = document.querySelectorAll(
        'table thead th, .table-bank thead th, .table thead th, thead th, .min-w-full thead th'
    );

    console.log(`Found ${tableHeaders.length} table headers to fix`);

    tableHeaders.forEach((header, index) => {
        // Force white text color
        header.style.setProperty('color', 'white', 'important');
        header.style.setProperty('background', 'transparent', 'important');

        // Remove any conflicting text color classes
        const textClasses = ['text-dark', 'text-black', 'text-gray-800', 'text-gray-900', 'text-dark-gray'];
        textClasses.forEach(className => {
            header.classList.remove(className);
        });

        // Also apply to any child elements
        const childElements = header.querySelectorAll('*');
        childElements.forEach(child => {
            child.style.setProperty('color', 'white', 'important');
            child.style.setProperty('background', 'transparent', 'important');

            // Remove conflicting classes from children too
            textClasses.forEach(className => {
                child.classList.remove(className);
            });
        });

        console.log(`Fixed header ${index + 1}: ${header.textContent.trim()}`);
    });

    // Ensure table head has proper background
    const tableHeads = document.querySelectorAll('table thead, .table-bank thead, .table thead, thead, .min-w-full thead, .bg-off-white');
    tableHeads.forEach((thead, index) => {
        thead.style.setProperty('background', 'linear-gradient(135deg, #1f2937 0%, #374151 100%)', 'important');
        thead.classList.remove('bg-off-white');
        console.log(`Fixed thead ${index + 1}`);
    });
}

// Enhanced Dashboard Layout Management with Backend Integration
window.dashboardLayout = function() {
    return {
        sidebarOpen: false,
        sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
        currentLang: localStorage.getItem('language') || 'EN',
        currentDirection: localStorage.getItem('direction') || 'ltr',

        init() {
            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024) {
                    this.sidebarOpen = false;
                }
            });

            // Listen for language change events from backend
            window.addEventListener('languageChanged', (event) => {
                this.handleLanguageChange(event.detail);
            });

            // Apply saved language state
            this.applyLanguageState();

            // Watch for sidebar collapse changes
            this.$watch('sidebarCollapsed', (value) => {
                localStorage.setItem('sidebarCollapsed', value);
            });
        },

        handleLanguageChange(data) {
            this.currentLang = data.locale.toUpperCase();
            this.currentDirection = data.direction;

            // Store preferences
            localStorage.setItem('language', this.currentLang);
            localStorage.setItem('direction', this.currentDirection);

            // Apply changes immediately
            this.applyLanguageState();

            // Update dynamic content
            this.updateRTLContent();
        },

        applyLanguageState() {
            const html = document.documentElement;
            html.setAttribute('lang', this.currentLang.toLowerCase());
            html.setAttribute('dir', this.currentDirection);

            if (this.currentDirection === 'rtl') {
                document.body.classList.add('arabic-text');
            } else {
                document.body.classList.remove('arabic-text');
            }
        },

        updateRTLContent() {
            // Update currency displays to maintain LTR for numbers
            const currencyElements = document.querySelectorAll('.currency-amount, .number-display');
            currencyElements.forEach(element => {
                if (this.currentDirection === 'rtl') {
                    element.style.direction = 'ltr';
                    element.style.display = 'inline-block';
                }
            });

            // Update table alignments
            const tables = document.querySelectorAll('.table-bank');
            tables.forEach(table => {
                if (this.currentDirection === 'rtl') {
                    table.style.direction = 'rtl';
                } else {
                    table.style.direction = 'ltr';
                }
            });
        },

        // Legacy support for old toggle method
        toggleLanguage() {
            const newLocale = this.currentLang === 'EN' ? 'ar' : 'en';
            this.switchLanguage(newLocale);
        },

        async switchLanguage(locale) {
            try {
                const response = await fetch('/api/language/switch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({ locale: locale })
                });

                const data = await response.json();

                if (data.success) {
                    // Dispatch language change event
                    window.dispatchEvent(new CustomEvent('languageChanged', {
                        detail: data
                    }));

                    // Reload page to apply translations
                    setTimeout(() => {
                        window.location.reload();
                    }, 500);
                }
            } catch (error) {
                console.error('Language switch error:', error);
            }
        }
    }
};

// Currency Formatting Utilities
window.formatCurrency = function(amount, currency = 'AED') {
    if (isNaN(amount) || amount === '') return '';
    return `${parseFloat(amount).toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    })} ${currency}`;
};

// Modal Management (Placeholder for future implementation)
window.openModal = function(action, type, id = null) {
    console.log(`Opening ${action} modal for ${type}`, id);
    // This will be implemented when modal system is created
    alert(`${action.charAt(0).toUpperCase() + action.slice(1)} ${type} modal will be implemented soon!`);
};

// Bank-style animations and interactions
document.addEventListener('DOMContentLoaded', function() {
    // Staggered animations for stats cards
    const statsCards = document.querySelectorAll('.stats-card');
    statsCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Enhanced hover effects for bank cards
    const bankCards = document.querySelectorAll('.bank-card');
    bankCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Table row hover effects
    const tableRows = document.querySelectorAll('.table-bank tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'var(--off-white)';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    console.log('UAE English Sports Academy - Bank-style dashboard loaded successfully');

    // Apply table header styling fix immediately
    ensureTableHeaderStyling();

    // Apply button text styling fix immediately
    ensureButtonTextStyling();

    // Apply Quick Actions specific fix immediately
    fixQuickActionsButtons();

    // Apply again after a short delay to catch any late-loading elements
    setTimeout(ensureTableHeaderStyling, 500);
    setTimeout(ensureButtonTextStyling, 500);
    setTimeout(fixQuickActionsButtons, 500);
    setTimeout(ensureTableHeaderStyling, 1000);
    setTimeout(ensureButtonTextStyling, 1000);
    setTimeout(fixQuickActionsButtons, 1000);
    setTimeout(ensureTableHeaderStyling, 2000);
    setTimeout(ensureButtonTextStyling, 2000);
    setTimeout(fixQuickActionsButtons, 2000);

    // Re-apply styling when content is dynamically loaded
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if any table elements were added
                const hasTableElements = Array.from(mutation.addedNodes).some(node => {
                    return node.nodeType === 1 && (
                        node.tagName === 'TABLE' ||
                        node.querySelector && node.querySelector('table')
                    );
                });

                // Check if any button elements were added
                const hasButtonElements = Array.from(mutation.addedNodes).some(node => {
                    return node.nodeType === 1 && (
                        node.tagName === 'BUTTON' ||
                        node.classList && node.classList.contains('btn') ||
                        node.querySelector && (node.querySelector('button') || node.querySelector('.btn'))
                    );
                });

                if (hasTableElements) {
                    setTimeout(ensureTableHeaderStyling, 100);
                }

                if (hasButtonElements) {
                    setTimeout(ensureButtonTextStyling, 100);
                    setTimeout(fixQuickActionsButtons, 100);
                }
            }
        });
    });

    // Start observing
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // Also apply on window load to catch everything
    window.addEventListener('load', function() {
        setTimeout(ensureTableHeaderStyling, 100);
        setTimeout(ensureButtonTextStyling, 100);
        setTimeout(fixQuickActionsButtons, 100);
    });
});

Alpine.start();
