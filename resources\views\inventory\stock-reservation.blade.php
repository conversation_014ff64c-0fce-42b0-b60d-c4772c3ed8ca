@extends('layouts.dashboard')

@section('title', __('Stock Reservation'))

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ __('Stock Reservation') }}</h1>
                <p class="text-lg text-dark-gray">{{ __('Reserve inventory for orders and customers') }}</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('inventory.show', $inventory) }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                {{ __('View Item') }}
            </a>
            <a href="{{ route('inventory.index') }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                {{ __('Back to Inventory') }}
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="stockReservationForm()">
        <!-- Item Information Card -->
        <div class="bank-card">
            <div class="bank-card-header">
                <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Item Information') }}</h3>
            </div>
            <div class="bank-card-body">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
                    <div>
                        <label class="form-label">{{ __('Item Name') }}</label>
                        <p class="font-medium">{{ $inventory->name }}</p>
                        <p class="text-sm text-gray-500">{{ $inventory->name_ar }}</p>
                    </div>
                    <div>
                        <label class="form-label">{{ __('SKU') }}</label>
                        <p class="font-medium">{{ $inventory->sku }}</p>
                    </div>
                    <div>
                        <label class="form-label">{{ __('Current Stock') }}</label>
                        <p class="font-medium text-lg">{{ $inventory->current_stock }}</p>
                    </div>
                    <div>
                        <label class="form-label">{{ __('Reserved Stock') }}</label>
                        <p class="font-medium text-lg text-yellow-600">{{ $inventory->reserved_stock ?? 0 }}</p>
                    </div>
                    <div>
                        <label class="form-label">{{ __('Available Stock') }}</label>
                        <p class="font-medium text-lg text-green-600">{{ $inventory->available_stock }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Reservation Form and Current Reservations -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Reservation Form -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Create Reservation') }}</h3>
                </div>
                <div class="bank-card-body">
                    <form method="POST" action="{{ route('inventory.reserve-stock', $inventory) }}" @submit="handleSubmit">
                        @csrf

                        <div class="space-y-6">
                            <!-- Quantity -->
                            <div>
                                <label for="quantity" class="form-label required">{{ __('Quantity to Reserve') }}</label>
                                <input type="number" id="quantity" name="quantity" x-model="quantity"
                                    class="form-input-bank" min="1" :max="availableStock" required>
                                <div class="text-sm text-gray-500 mt-1">
                                    {{ __('Available:') }} {{ $inventory->available_stock }}
                                </div>
                                @error('quantity')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Reservation Type -->
                            <div>
                                <label for="reservation_type" class="form-label required">{{ __('Reservation Type') }}</label>
                                <select id="reservation_type" name="reservation_type" x-model="reservationType" class="form-select-bank" required>
                                    <option value="">{{ __('Select Type') }}</option>
                                    <option value="order">{{ __('Uniform Order') }}</option>
                                    <option value="customer">{{ __('Customer Hold') }}</option>
                                    <option value="maintenance">{{ __('Maintenance/Repair') }}</option>
                                    <option value="other">{{ __('Other') }}</option>
                                </select>
                                @error('reservation_type')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Reference Information -->
                            <div x-show="reservationType === 'order' || reservationType === 'customer'">
                                <label for="reference_id" class="form-label">{{ __('Reference ID') }}</label>
                                <input type="number" id="reference_id" name="reference_id"
                                    class="form-input-bank" placeholder="Order ID or Customer ID">
                                @error('reference_id')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Expiry Date -->
                            <div>
                                <label for="expires_at" class="form-label">{{ __('Expiry Date') }}</label>
                                <input type="datetime-local" id="expires_at" name="expires_at"
                                    class="form-input-bank" :min="minDateTime">
                                <div class="text-sm text-gray-500 mt-1">
                                    {{ __('Leave empty for no expiry') }}
                                </div>
                                @error('expires_at')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Notes -->
                            <div>
                                <label for="notes" class="form-label">{{ __('Notes') }}</label>
                                <textarea id="notes" name="notes" rows="3" class="form-textarea-bank"
                                    placeholder="{{ __('Additional notes about this reservation...') }}"></textarea>
                                @error('notes')
                                    <div class="form-error">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Submit Button -->
                            <div class="flex items-center space-x-4 pt-6 border-t border-light-gray">
                                <button type="submit" class="btn-bank" :disabled="!quantity || !reservationType">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                                        </path>
                                    </svg>
                                    {{ __('Reserve Stock') }}
                                </button>
                                <a href="{{ route('inventory.show', $inventory) }}" class="btn-bank-outline">
                                    {{ __('Cancel') }}
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Current Reservations -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="text-lg font-semibold text-charcoal-black">{{ __('Active Reservations') }}</h3>
                </div>
                <div class="bank-card-body">
                    @if($reservations->count() > 0)
                        <div class="space-y-4">
                            @foreach($reservations as $reservation)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <div class="font-medium">
                                            {{ ucfirst(str_replace('_', ' ', $reservation->reservation_type)) }}
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded">
                                                {{ $reservation->quantity }} reserved
                                            </span>
                                            <form method="POST" action="{{ route('inventory.release-reservation', [$inventory, $reservation->id]) }}" 
                                                  onsubmit="return confirm('Are you sure you want to release this reservation?')" class="inline">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                                    Release
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                    
                                    <div class="text-sm text-gray-600 space-y-1">
                                        <div>{{ __('Created:') }} {{ $reservation->created_at->format('M j, Y H:i') }}</div>
                                        @if($reservation->expires_at)
                                            <div class="@if($reservation->expires_at->isPast()) text-red-600 @endif">
                                                {{ __('Expires:') }} {{ $reservation->expires_at->format('M j, Y H:i') }}
                                                @if($reservation->expires_at->isPast())
                                                    <span class="text-red-600 font-medium">(Expired)</span>
                                                @endif
                                            </div>
                                        @endif
                                        @if($reservation->reference_id)
                                            <div>{{ __('Reference:') }} #{{ $reservation->reference_id }}</div>
                                        @endif
                                        @if($reservation->notes)
                                            <div>{{ __('Notes:') }} {{ $reservation->notes }}</div>
                                        @endif
                                        <div>{{ __('By:') }} {{ $reservation->user->name ?? 'Unknown' }}</div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8 text-gray-500">
                            <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                            <p>{{ __('No active reservations') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
        <script>
            function stockReservationForm() {
                return {
                    quantity: '',
                    reservationType: '',
                    availableStock: {{ $inventory->available_stock }},
                    minDateTime: new Date().toISOString().slice(0, 16),

                    handleSubmit(event) {
                        if (!this.quantity || !this.reservationType) {
                            event.preventDefault();
                            alert('Please fill in all required fields');
                            return;
                        }

                        const qty = parseInt(this.quantity);
                        if (qty > this.availableStock) {
                            event.preventDefault();
                            alert(`Cannot reserve more than available stock (${this.availableStock})`);
                            return;
                        }

                        if (!confirm(`Are you sure you want to reserve ${qty} units?`)) {
                            event.preventDefault();
                        }
                    }
                }
            }
        </script>
    @endpush
@endsection
