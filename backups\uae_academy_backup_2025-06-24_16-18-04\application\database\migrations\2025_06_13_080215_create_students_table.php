<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('students', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('academy_id')->constrained()->onDelete('cascade');
            $table->string('full_name')->charset('utf8mb4');
            $table->string('email')->nullable();
            $table->string('phone', 15); // UAE format: +971XXXXXXXXX
            $table->string('nationality')->charset('utf8mb4')->nullable();
            $table->text('address')->charset('utf8mb4')->nullable();
            $table->date('birth_date')->nullable();
            $table->date('join_date');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->text('notes')->charset('utf8mb4')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['branch_id', 'academy_id', 'status']);
            $table->index('full_name');
            $table->index('phone');
            $table->index('join_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('students');
    }
};
