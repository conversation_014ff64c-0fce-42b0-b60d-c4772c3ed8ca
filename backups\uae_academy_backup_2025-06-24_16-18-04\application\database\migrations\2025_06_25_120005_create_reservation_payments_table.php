<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reservation_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('reservation_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('venue_id')->constrained()->onDelete('cascade');
            $table->string('payment_number')->unique(); // Auto-generated payment ID
            $table->string('reference_number')->nullable(); // External reference
            $table->enum('payment_type', ['deposit', 'full_payment', 'partial_payment', 'balance', 'refund'])->default('full_payment');
            $table->decimal('amount', 10, 2); // Payment amount
            $table->decimal('subtotal', 10, 2)->nullable(); // Amount before VAT
            $table->decimal('vat_rate', 5, 2)->default(5.00); // VAT rate
            $table->decimal('vat_amount', 10, 2)->default(0); // VAT amount
            $table->decimal('total_amount', 10, 2); // Total amount including VAT
            $table->boolean('vat_inclusive')->default(false); // Whether amount includes VAT
            $table->string('currency', 3)->default('AED');
            $table->enum('payment_method', ['cash', 'card', 'bank_transfer', 'online', 'cheque', 'credit'])->default('cash');
            $table->string('card_type')->nullable(); // Visa, Mastercard, etc.
            $table->string('card_last_four')->nullable(); // Last 4 digits of card
            $table->string('transaction_id')->nullable(); // Bank/gateway transaction ID
            $table->string('authorization_code')->nullable(); // Card authorization code
            $table->date('payment_date'); // Date of payment
            $table->datetime('payment_datetime'); // Exact time of payment
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->datetime('processed_at')->nullable(); // When payment was processed
            $table->datetime('failed_at')->nullable(); // When payment failed
            $table->text('failure_reason')->nullable(); // Reason for failure
            $table->datetime('refunded_at')->nullable(); // When refund was processed
            $table->decimal('refund_amount', 10, 2)->default(0); // Refund amount
            $table->text('refund_reason')->charset('utf8mb4')->nullable(); // Reason for refund
            $table->string('receipt_number')->nullable(); // Receipt number
            $table->boolean('receipt_printed')->default(false); // Receipt printed flag
            $table->boolean('receipt_emailed')->default(false); // Receipt emailed flag
            $table->json('payment_gateway_response')->nullable(); // Gateway response data
            $table->text('notes')->charset('utf8mb4')->nullable(); // Payment notes
            $table->text('notes_ar')->charset('utf8mb4')->nullable();
            $table->foreignId('processed_by')->nullable()->constrained('users'); // Staff who processed
            $table->foreignId('refunded_by')->nullable()->constrained('users'); // Staff who processed refund
            $table->timestamps();

            // Indexes
            $table->index(['reservation_id', 'payment_type']);
            $table->index(['customer_id', 'status']);
            $table->index(['payment_date', 'status']);
            $table->index('payment_number');
            $table->index('reference_number');
            $table->index(['payment_method', 'status']);
            $table->index(['venue_id', 'payment_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reservation_payments');
    }
};
