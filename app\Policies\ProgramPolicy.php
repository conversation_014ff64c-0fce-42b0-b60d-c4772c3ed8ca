<?php

namespace App\Policies;

use App\Models\Program;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProgramPolicy
{
    /**
     * Determine whether the user can view any programs.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the program.
     */
    public function view(User $user, Program $program): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => $this->userCanAccessProgram($user, $program),
            'academy_manager' => $this->userCanAccessProgram($user, $program),
            default => false,
        };
    }

    /**
     * Determine whether the user can create programs.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can update the program.
     */
    public function update(User $user, Program $program): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => $this->userCanAccessProgram($user, $program),
            'academy_manager' => $this->userCanAccessProgram($user, $program),
            default => false,
        };
    }

    /**
     * Determine whether the user can delete the program.
     */
    public function delete(User $user, Program $program): bool
    {
        // Only admin and branch managers can delete programs
        // Academy managers cannot delete programs to prevent accidental data loss
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            default => false,
        };
    }

    /**
     * Determine whether the user can restore the program.
     */
    public function restore(User $user, Program $program): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can permanently delete the program.
     */
    public function forceDelete(User $user, Program $program): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can export programs.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can perform bulk actions on programs.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can toggle program status.
     */
    public function toggleStatus(User $user, Program $program): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            'academy_manager' => $this->userCanAccessProgram($user, $program),
            default => false,
        };
    }

    /**
     * Determine whether the user can view program statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can manage program enrollments.
     */
    public function manageEnrollments(User $user, Program $program): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => true,
            'academy_manager' => $this->userCanAccessProgram($user, $program),
            default => false,
        };
    }

    /**
     * Check if the user can access the specific program.
     */
    private function userCanAccessProgram(User $user, Program $program): bool
    {
        // Load the program's academy and branch relationships
        $program->load(['academy.branch']);

        // Branch managers can only access programs in their assigned branch
        if ($user->role === 'branch_manager') {
            return $user->branch_id === $program->academy->branch_id;
        }

        // Academy managers can only access programs in their assigned academy
        if ($user->role === 'academy_manager') {
            return $user->academy_id === $program->academy_id;
        }

        return false;
    }

    /**
     * Determine if the user can create programs for a specific academy.
     */
    public function createForAcademy(User $user, int $academyId): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => $this->userCanAccessAcademy($user, $academyId),
            'academy_manager' => $this->userCanAccessAcademy($user, $academyId),
            default => false,
        };
    }

    /**
     * Check if the user can access a specific academy.
     */
    private function userCanAccessAcademy(User $user, int $academyId): bool
    {
        // Branch managers can access academies in their assigned branch
        if ($user->role === 'branch_manager') {
            // Load academy with branch relationship to check branch access
            $academy = \App\Models\Academy::with('branch')->find($academyId);
            return $academy && $user->branch_id === $academy->branch_id;
        }

        // Academy managers can only access their assigned academy
        if ($user->role === 'academy_manager') {
            return $user->academy_id === $academyId;
        }

        return false;
    }

    /**
     * Determine if the user can view programs from a specific branch.
     */
    public function viewByBranch(User $user, int $branchId): bool
    {
        return match ($user->role) {
            'admin' => true,
            'branch_manager' => $this->userCanAccessBranch($user, $branchId),
            'academy_manager' => $this->userCanAccessBranch($user, $branchId),
            default => false,
        };
    }

    /**
     * Check if the user can access a specific branch.
     */
    private function userCanAccessBranch(User $user, int $branchId): bool
    {
        // Branch managers can only access their assigned branch
        // Academy managers can only access their assigned branch
        return $user->branch_id === $branchId;
    }
}
