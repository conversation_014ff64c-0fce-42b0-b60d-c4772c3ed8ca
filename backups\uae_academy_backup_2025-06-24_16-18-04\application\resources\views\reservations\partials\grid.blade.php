<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
    @forelse($reservations as $reservation)
        <div class="bank-card hover:shadow-lg transition-shadow duration-200">
            <div class="bank-card-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <input type="checkbox" name="reservation_ids[]" value="{{ $reservation->id }}"
                            @change="toggleReservationSelection({{ $reservation->id }})"
                            :checked="selectedReservations.includes({{ $reservation->id }})"
                            class="rounded border-gray-300 text-leaders-red focus:ring-leaders-red">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div
                                class="h-10 w-10 rounded-full bg-gradient-to-br from-leaders-red to-leaders-deep-red flex items-center justify-center">
                                <span class="text-sm font-medium text-white">
                                    {{ substr($reservation->reservation_number, -3) }}
                                </span>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-charcoal-black">
                                <a href="{{ route('reservations.show', $reservation) }}" class="hover:text-leaders-red">
                                    {{ $reservation->reservation_number }}
                                </a>
                            </h3>
                            <p class="text-sm text-dark-gray">{{ $reservation->booking_type_text }}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $reservation->status_badge_class }}">
                            {{ $reservation->status_text }}
                        </span>
                    </div>
                </div>
            </div>

            <div class="bank-card-body">
                <!-- Customer Info -->
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-dark-gray mb-2">Customer</h4>
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <img class="h-8 w-8 rounded-full" src="{{ $reservation->customer->profile_image_url }}"
                                alt="{{ $reservation->customer->full_name }}">
                        </div>
                        <div>
                            <p class="text-sm font-medium text-charcoal-black">
                                <a href="{{ route('customers.show', $reservation->customer) }}"
                                    class="hover:text-leaders-red">
                                    {{ $reservation->customer->full_name }}
                                </a>
                            </p>
                            <p class="text-xs text-dark-gray">{{ $reservation->customer->phone }}</p>
                        </div>
                    </div>
                </div>

                <!-- Venue & Field Info -->
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-dark-gray mb-2">Venue & Field</h4>
                    <div class="space-y-1">
                        <p class="text-sm font-medium text-charcoal-black">{{ $reservation->venue->name }}</p>
                        <p class="text-sm text-dark-gray">{{ $reservation->field->name }}</p>
                        <p class="text-xs text-dark-gray">{{ $reservation->field->type_text }}</p>
                    </div>
                </div>

                <!-- Date & Time Info -->
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-dark-gray mb-2">Schedule</h4>
                    <div class="space-y-1">
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-8 0H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2">
                                </path>
                            </svg>
                            <span
                                class="text-sm text-charcoal-black">{{ $reservation->reservation_date->format('M d, Y') }}</span>
                            @if ($reservation->is_today)
                                <span
                                    class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Today
                                </span>
                            @elseif($reservation->is_upcoming)
                                <span
                                    class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Upcoming
                                </span>
                            @endif
                        </div>
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span class="text-sm text-charcoal-black">{{ $reservation->time_slot_text }}</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-dark-gray" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            <span class="text-sm text-charcoal-black">{{ $reservation->duration_text }}</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Info -->
                <div class="mb-4">
                    <h4 class="text-sm font-medium text-dark-gray mb-2">Payment</h4>
                    <div class="flex items-center justify-between">
                        <span
                            class="text-lg font-bold text-charcoal-black">{{ $reservation->formatted_total_amount }}</span>
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $reservation->payment_status_badge_class }}">
                            {{ $reservation->payment_status_text }}
                        </span>
                    </div>
                    @if ($reservation->discount_percentage > 0)
                        <p class="text-xs text-green-600 mt-1">{{ $reservation->discount_percentage }}% discount
                            applied</p>
                    @endif
                </div>

                <!-- Event Details -->
                @if ($reservation->event_name || $reservation->event_type)
                    <div class="mb-4">
                        <h4 class="text-sm font-medium text-dark-gray mb-2">Event Details</h4>
                        @if ($reservation->event_name)
                            <p class="text-sm font-medium text-charcoal-black">{{ $reservation->event_name }}</p>
                        @endif
                        @if ($reservation->event_type)
                            <p class="text-xs text-dark-gray">{{ $reservation->event_type_text }}</p>
                        @endif
                        @if ($reservation->expected_participants)
                            <p class="text-xs text-dark-gray">Expected participants:
                                {{ $reservation->expected_participants }}</p>
                        @endif
                    </div>
                @endif
            </div>

            <div class="bank-card-footer">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        @can('view', $reservation)
                            <a href="{{ route('reservations.show', $reservation) }}"
                                class="btn-bank btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                    </path>
                                </svg>
                                View
                            </a>
                        @endcan

                        @if ($reservation->status === 'pending')
                            @can('update', $reservation)
                                <button onclick="confirmReservation({{ $reservation->id }})"
                                    class="btn-bank btn-bank-sm bg-green-600 hover:bg-green-700 text-white">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    Confirm
                                </button>
                            @endcan
                        @endif

                        @if ($reservation->can_modify)
                            @can('update', $reservation)
                                <a href="{{ route('reservations.edit', $reservation) }}"
                                    class="btn-bank btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                    Edit
                                </a>
                            @endcan
                        @endif
                    </div>

                    <!-- Dropdown for more actions -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open"
                            class="btn-bank btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z">
                                </path>
                            </svg>
                        </button>
                        <div x-show="open" @click.away="open = false" x-transition
                            class="absolute right-0 bottom-full mb-2 w-48 bg-white rounded-md shadow-lg z-10 border border-light-gray">
                            <div class="py-1">
                                <a href="{{ route('reservations.invoice', $reservation) }}" target="_blank"
                                    class="block px-4 py-2 text-sm text-charcoal-black hover:bg-off-white">
                                    <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                        </path>
                                    </svg>
                                    View Invoice
                                </a>
                                @if ($reservation->payment_status !== 'paid')
                                    <a href="{{ route('reservation-payments.create', ['reservation_id' => $reservation->id]) }}"
                                        class="block px-4 py-2 text-sm text-charcoal-black hover:bg-off-white">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                            </path>
                                        </svg>
                                        Add Payment
                                    </a>
                                @endif
                                @if ($reservation->can_cancel)
                                    <button onclick="cancelReservation({{ $reservation->id }})"
                                        class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-off-white">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                        Cancel Reservation
                                    </button>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @empty
        <div class="col-span-full">
            <div class="text-center py-12">
                <svg class="w-12 h-12 text-dark-gray mx-auto mb-4" fill="none" stroke="currentColor"
                    viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-8 0H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2">
                    </path>
                </svg>
                <h3 class="text-lg font-medium text-charcoal-black mb-2">No reservations found</h3>
                <p class="text-dark-gray mb-4">Get started by creating your first reservation.</p>
                @can('create', App\Models\Reservation::class)
                    <a href="{{ route('reservations.create') }}" class="btn-bank">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Create Reservation
                    </a>
                @endcan
            </div>
        </div>
    @endforelse
</div>
