@extends('layouts.dashboard')

@section('title', ucfirst($category) . ' Settings - UAE English Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <a href="{{ route('settings.index') }}" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <div class="flex items-center space-x-2">
                <svg class="w-8 h-8 text-leaders-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                    </path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                    </path>
                </svg>
                <h1 class="text-2xl font-bold text-gray-900">{{ ucfirst($category) }} Settings</h1>
            </div>
            <div class="hidden md:flex items-center space-x-2 text-sm text-gray-500">
                <span>•</span>
                <span>{{ $allCategories[$category] }}</span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <button onclick="exportSettings('{{ $category }}')" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                    </path>
                </svg>
                Export {{ ucfirst($category) }}
            </button>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <div class="flex flex-col lg:flex-row gap-8">
            <!-- Settings Navigation -->
            <div class="lg:w-1/4">
                <div class="bank-card">
                    <div class="card-header">
                        <h3 class="card-title">Settings Categories</h3>
                    </div>
                    <div class="card-body p-0">
                        <nav class="settings-nav">
                            @foreach ($allCategories as $key => $name)
                                <a href="{{ route('settings.edit', $key) }}"
                                    class="settings-nav-item {{ $category === $key ? 'active' : '' }}">
                                    <div class="settings-nav-icon">
                                        @switch($key)
                                            @case('general')
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4">
                                                    </path>
                                                </svg>
                                            @break

                                            @case('academy')
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                                    </path>
                                                </svg>
                                            @break

                                            @case('payment')
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                                                    </path>
                                                </svg>
                                            @break

                                            @case('notification')
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V16a2 2 0 002 2h8a2 2 0 002-2v-5a1 1 0 00-.293-.707l-2-2A1 1 0 0011 8H9a1 1 0 01-1-1V4.828z">
                                                    </path>
                                                </svg>
                                            @break

                                            @case('security')
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                                                    </path>
                                                </svg>
                                            @break

                                            @case('system')
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                                    </path>
                                                </svg>
                                            @break

                                            @case('translation')
                                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
                                                    </path>
                                                </svg>
                                            @break
                                        @endswitch
                                    </div>
                                    <span class="settings-nav-text">{{ $name }}</span>
                                </a>
                            @endforeach
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Settings Form -->
            <div class="lg:w-3/4">
                <form action="{{ route('settings.update', $category) }}" method="POST" enctype="multipart/form-data"
                    id="settingsForm">
                    @csrf
                    @method('PUT')

                    <div class="bank-card">
                        <div class="card-header">
                            <h2 class="card-title">{{ $allCategories[$category] }}</h2>
                            <p class="card-subtitle">Configure settings for {{ strtolower($allCategories[$category]) }}</p>
                        </div>

                        <div class="card-body">
                            @if ($settings->isEmpty())
                                <div class="empty-state">
                                    <svg class="empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                                        </path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <h3 class="empty-title">No Settings Found</h3>
                                    <p class="empty-description">There are no settings configured for this category yet.
                                    </p>
                                </div>
                            @else
                                <div class="settings-grid">
                                    @foreach ($settings as $setting)
                                        <div class="setting-item">
                                            <div class="setting-header">
                                                <label for="{{ $setting->key }}" class="setting-label">
                                                    {{ $setting->label }}
                                                    @if (in_array('required', $setting->validation_rules ?? []))
                                                        <span class="text-red-500">*</span>
                                                    @endif
                                                </label>
                                                @if ($setting->description)
                                                    <p class="setting-description">{{ $setting->description }}</p>
                                                @endif
                                            </div>

                                            <div class="setting-input">
                                                @include('settings.partials.input', [
                                                    'setting' => $setting,
                                                ])
                                            </div>

                                            @error($setting->key)
                                                <div class="setting-error">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    @endforeach
                                </div>
                            @endif
                        </div>

                        @if ($settings->isNotEmpty())
                            <div class="card-footer">
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-500">
                                        <span class="font-medium">{{ $settings->count() }}</span> settings in this
                                        category
                                    </div>
                                    <div class="flex items-center space-x-3">
                                        <button type="button" onclick="resetForm()" class="btn-secondary">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                                                </path>
                                            </svg>
                                            Reset
                                        </button>
                                        <button type="submit" class="btn-primary">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M5 13l4 4L19 7"></path>
                                            </svg>
                                            Save Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        /* Settings Edit Enhanced Styles */

        /* Missing Button Classes */
        .btn-primary {
            background: linear-gradient(135deg, var(--leaders-red) 0%, var(--leaders-deep-red) 100%);
            color: var(--pure-white);
            border: 1px solid var(--leaders-deep-red);
            border-radius: var(--radius-button);
            padding: 0.75rem 1.5rem;
            font-family: var(--font-family-primary);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: var(--shadow-card);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--leaders-deep-red) 0%, #991B1B 100%);
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
            color: var(--pure-white);
            text-decoration: none;
        }

        .btn-secondary {
            background: var(--pure-white);
            color: var(--dark-gray);
            border: 1px solid var(--medium-gray);
            border-radius: var(--radius-button);
            padding: 0.75rem 1.5rem;
            font-family: var(--font-family-primary);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: var(--shadow-card);
        }

        .btn-secondary:hover {
            background: var(--light-gray);
            border-color: var(--leaders-red);
            color: var(--leaders-red);
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
            text-decoration: none;
        }

        /* Settings Navigation */
        .settings-nav {
            display: flex;
            flex-direction: column;
        }

        .settings-nav-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: #6b7280;
            text-decoration: none;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.2s ease;
        }

        .settings-nav-item:hover {
            background-color: #f9fafb;
            color: #374151;
        }

        .settings-nav-item.active {
            background-color: var(--leaders-red);
            color: white;
            font-weight: 500;
        }

        .settings-nav-icon {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }

        .settings-nav-icon svg {
            width: 100%;
            height: 100%;
        }

        .settings-nav-text {
            font-size: 0.875rem;
        }

        /* Settings Form */
        .settings-grid {
            display: grid;
            gap: 2rem;
        }

        .setting-item {
            border-bottom: 1px solid #f3f4f6;
            padding-bottom: 2rem;
        }

        .setting-item:last-child {
            border-bottom: none;
            padding-bottom: 0;
        }

        .setting-header {
            margin-bottom: 1rem;
        }

        .setting-label {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .setting-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .setting-input {
            position: relative;
        }

        .setting-error {
            margin-top: 0.5rem;
            font-size: 0.875rem;
            color: #ef4444;
        }

        /* Empty State */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .empty-icon {
            width: 4rem;
            height: 4rem;
            margin: 0 auto 1rem;
            color: #d1d5db;
        }

        .empty-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        .empty-description {
            color: #6b7280;
            margin: 0;
        }

        /* Form Controls */
        .form-control {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--medium-gray);
            border-radius: var(--radius-button);
            font-size: 0.875rem;
            color: var(--charcoal-black);
            background-color: var(--pure-white);
            transition: all var(--transition-fast);
            outline: none;
            font-family: var(--font-family-primary);
        }

        .form-control:focus {
            border-color: var(--leaders-red);
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
            background-color: var(--pure-white);
        }

        .form-control.is-invalid {
            border-color: var(--error-red);
            background-color: #fef2f2;
        }

        .form-control.is-invalid:focus {
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        .form-control::placeholder {
            color: var(--dark-gray);
            opacity: 0.7;
        }

        /* Checkbox and Radio */
        .checkbox-wrapper,
        .radio-wrapper {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-input,
        .radio-input {
            width: 1rem;
            height: 1rem;
            accent-color: var(--leaders-red);
        }

        /* File Upload */
        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }

        .file-upload-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-button {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1rem;
            background: #f9fafb;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            color: #374151;
            transition: all 0.2s ease;
        }

        .file-upload-button:hover {
            background: #f3f4f6;
        }

        .file-upload-text {
            margin-left: 0.5rem;
        }

        /* Multi-select */
        .multi-select {
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            padding: 0.5rem;
            max-height: 200px;
            overflow-y: auto;
        }

        .multi-select-option {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            border-radius: 0.25rem;
            transition: background-color 0.2s ease;
        }

        .multi-select-option:hover {
            background-color: #f9fafb;
        }

        .multi-select-checkbox {
            margin-right: 0.5rem;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .settings-nav {
                flex-direction: row;
                overflow-x: auto;
                border-bottom: 1px solid #e5e7eb;
            }

            .settings-nav-item {
                white-space: nowrap;
                border-bottom: none;
                border-right: 1px solid #f3f4f6;
            }

            .settings-nav-item:last-child {
                border-right: none;
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        function resetForm() {
            if (confirm('Are you sure you want to reset all changes?')) {
                document.getElementById('settingsForm').reset();
            }
        }

        function exportSettings(category) {
            let url = '{{ route('settings.export') }}';
            if (category) {
                url += '?category=' + category;
            }

            fetch(url)
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'settings-' + category + '-export-' + new Date().toISOString().slice(0, 19).replace(
                        /:/g, '-') + '.json';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                })
                .catch(error => {
                    console.error('Export failed:', error);
                    alert('Export failed. Please try again.');
                });
        }

        // Handle file uploads
        document.addEventListener('DOMContentLoaded', function() {
            const fileInputs = document.querySelectorAll('.file-upload-input');

            fileInputs.forEach(input => {
                input.addEventListener('change', function() {
                    const button = this.parentElement.querySelector('.file-upload-text');
                    if (this.files.length > 0) {
                        button.textContent = this.files[0].name;
                    } else {
                        button.textContent = 'Choose file';
                    }
                });
            });
        });

        // Handle multi-select checkboxes
        function updateMultiSelect(selectElement) {
            const checkboxes = selectElement.querySelectorAll('input[type="checkbox"]:checked');
            const values = Array.from(checkboxes).map(cb => cb.value);
            const hiddenInput = selectElement.parentElement.querySelector('input[type="hidden"]');
            if (hiddenInput) {
                hiddenInput.value = JSON.stringify(values);
            }
        }
    </script>
@endpush
