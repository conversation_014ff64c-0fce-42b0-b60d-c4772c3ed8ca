<?php

namespace App\Policies;

use App\Models\Uniform;
use App\Models\User;

class UniformPolicy
{
    /**
     * Determine whether the user can view any uniforms.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the uniform.
     */
    public function view(User $user, Uniform $uniform): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can view uniforms in their branch
                return $user->branch_id === $uniform->branch_id;
            case 'academy_manager':
                // Academy managers can view uniforms in their academy
                return $user->academy_id === $uniform->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can create uniforms.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can update the uniform.
     */
    public function update(User $user, Uniform $uniform): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can update uniforms in their branch
                return $user->branch_id === $uniform->branch_id;
            case 'academy_manager':
                // Academy managers can update uniforms in their academy
                return $user->academy_id === $uniform->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can delete the uniform.
     */
    public function delete(User $user, Uniform $uniform): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can delete uniforms in their branch
                return $user->branch_id === $uniform->branch_id;
            case 'academy_manager':
                // Academy managers can delete uniforms in their academy (only if not delivered)
                return $user->academy_id === $uniform->academy_id && 
                       $uniform->status !== 'delivered';
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can restore the uniform.
     */
    public function restore(User $user, Uniform $uniform): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can permanently delete the uniform.
     */
    public function forceDelete(User $user, Uniform $uniform): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can export uniform data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view uniform statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can toggle uniform status.
     */
    public function toggleStatus(User $user, Uniform $uniform): bool
    {
        return $this->update($user, $uniform);
    }

    /**
     * Determine whether the user can mark uniform as delivered.
     */
    public function markDelivered(User $user, Uniform $uniform): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                return $user->branch_id === $uniform->branch_id;
            case 'academy_manager':
                return $user->academy_id === $uniform->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can update uniform tracking status.
     */
    public function updateTracking(User $user, Uniform $uniform): bool
    {
        return $this->update($user, $uniform);
    }
}
