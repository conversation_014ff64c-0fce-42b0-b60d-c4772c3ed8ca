<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class NameTranslationService
{
    /**
     * Common Arabic name mappings for quick translation
     */
    private static array $commonNameMappings = [
        // Male Names
        'Ahmed' => 'أحمد',
        'Mohammed' => 'محمد',
        'Ali' => 'علي',
        'Omar' => 'عمر',
        'Hassan' => 'حسن',
        '<PERSON>' => 'حسين',
        'Khalid' => 'خالد',
        'Abdullah' => 'عبدالله',
        '<PERSON><PERSON>man' => 'عبدالرحمن',
        'Saeed' => 'سعيد',
        'Fahad' => 'فهد',
        'Faisal' => 'فيصل',
        'Nasser' => 'ناصر',
        'Mansour' => 'منصور',
        'Rashid' => 'راشد',
        'Sultan' => 'سلطان',
        'Hamad' => 'حمد',
        'Majid' => 'ماجد',
        'Tariq' => 'طارق',
        'Youssef' => 'يوسف',
        
        // Female Names
        'Fatima' => 'فاطمة',
        'Aisha' => 'عائشة',
        'Maryam' => 'مريم',
        'Khadija' => 'خديجة',
        'Amina' => 'آمنة',
        'Zahra' => 'زهراء',
        'Nour' => 'نور',
        'Sara' => 'سارة',
        'Layla' => 'ليلى',
        'Hala' => 'هالة',
        'Reem' => 'ريم',
        'Noura' => 'نورا',
        'Salma' => 'سلمى',
        'Dina' => 'دينا',
        'Rana' => 'رنا',
        'Lina' => 'لينا',
        'Maya' => 'مايا',
        'Yasmin' => 'ياسمين',
        'Rania' => 'رانيا',
        'Huda' => 'هدى',
        
        // Family Names
        'Al-Ahmed' => 'الأحمد',
        'Al-Mohammed' => 'المحمد',
        'Al-Ali' => 'العلي',
        'Al-Omar' => 'العمر',
        'Al-Hassan' => 'الحسن',
        'Al-Khalid' => 'الخالد',
        'Al-Saeed' => 'السعيد',
        'Al-Fahad' => 'الفهد',
        'Al-Faisal' => 'الفيصل',
        'Al-Nasser' => 'الناصر',
        'Al-Mansour' => 'المنصور',
        'Al-Rashid' => 'الراشد',
        'Al-Sultan' => 'السلطان',
        'Al-Hamad' => 'الحمد',
        'Al-Majid' => 'الماجد',
        'Al-Tariq' => 'الطارق',
        
        // Nationalities
        'Emirati' => 'إماراتي',
        'Saudi' => 'سعودي',
        'Egyptian' => 'مصري',
        'Lebanese' => 'لبناني',
        'Syrian' => 'سوري',
        'Jordanian' => 'أردني',
        'Palestinian' => 'فلسطيني',
        'Iraqi' => 'عراقي',
        'Kuwaiti' => 'كويتي',
        'Qatari' => 'قطري',
        'Bahraini' => 'بحريني',
        'Omani' => 'عماني',
        'Yemeni' => 'يمني',
        'Moroccan' => 'مغربي',
        'Tunisian' => 'تونسي',
        'Algerian' => 'جزائري',
        'Sudanese' => 'سوداني',
        'Pakistani' => 'باكستاني',
        'Indian' => 'هندي',
        'Bangladeshi' => 'بنغلاديشي',
        'Filipino' => 'فلبيني',
        'British' => 'بريطاني',
        'American' => 'أمريكي',
        'Canadian' => 'كندي',
        'Australian' => 'أسترالي',
        'German' => 'ألماني',
        'French' => 'فرنسي',
        'Italian' => 'إيطالي',
        'Spanish' => 'إسباني',
        'Russian' => 'روسي',
        'Chinese' => 'صيني',
        'Japanese' => 'ياباني',
        'Korean' => 'كوري',
    ];

    /**
     * Translate a name to Arabic using common mappings or transliteration
     */
    public static function translateName(string $name, string $type = 'name'): ?string
    {
        if (empty($name)) return null;

        // Check cache first
        $cacheKey = "name_translation_{$type}_" . md5($name);
        $cached = Cache::get($cacheKey);
        if ($cached) {
            return $cached;
        }

        // Check common mappings
        if (isset(self::$commonNameMappings[$name])) {
            $translation = self::$commonNameMappings[$name];
            Cache::put($cacheKey, $translation, now()->addDays(30));
            return $translation;
        }

        // Try to transliterate if it's already in Arabic script
        if (self::isArabicText($name)) {
            Cache::put($cacheKey, $name, now()->addDays(30));
            return $name;
        }

        // For complex names, try to break them down
        $translation = self::translateComplexName($name);
        if ($translation) {
            Cache::put($cacheKey, $translation, now()->addDays(30));
            return $translation;
        }

        // If no translation found, return null to use original
        return null;
    }

    /**
     * Check if text contains Arabic characters
     */
    private static function isArabicText(string $text): bool
    {
        return preg_match('/[\x{0600}-\x{06FF}]/u', $text);
    }

    /**
     * Translate complex names by breaking them into parts
     */
    private static function translateComplexName(string $name): ?string
    {
        $parts = explode(' ', $name);
        $translatedParts = [];

        foreach ($parts as $part) {
            $part = trim($part);
            if (empty($part)) continue;

            // Check if this part has a translation
            if (isset(self::$commonNameMappings[$part])) {
                $translatedParts[] = self::$commonNameMappings[$part];
            } else {
                // Try without prefixes like "Al-", "bin", "ibn"
                $cleanPart = preg_replace('/^(Al-|al-|bin|ibn|Abu|abu)/i', '', $part);
                if (isset(self::$commonNameMappings[$cleanPart])) {
                    $prefix = strtolower(substr($part, 0, strlen($part) - strlen($cleanPart)));
                    $arabicPrefix = match($prefix) {
                        'al-', 'Al-' => 'ال',
                        'bin', 'ibn' => 'بن',
                        'abu', 'Abu' => 'أبو',
                        default => ''
                    };
                    $translatedParts[] = $arabicPrefix . self::$commonNameMappings[$cleanPart];
                } else {
                    // If no translation found for this part, return null
                    return null;
                }
            }
        }

        return empty($translatedParts) ? null : implode(' ', $translatedParts);
    }

    /**
     * Auto-translate student names if Arabic versions are missing
     */
    public static function autoTranslateStudentNames(\App\Models\Student $student): array
    {
        $updates = [];

        // Translate full name
        if (empty($student->full_name_ar) && !empty($student->full_name)) {
            $arabicName = self::translateName($student->full_name);
            if ($arabicName) {
                $updates['full_name_ar'] = $arabicName;
            }
        }

        // Translate first name
        if (empty($student->first_name_ar) && !empty($student->first_name)) {
            $arabicFirstName = self::translateName($student->first_name);
            if ($arabicFirstName) {
                $updates['first_name_ar'] = $arabicFirstName;
            }
        }

        // Translate last name
        if (empty($student->last_name_ar) && !empty($student->last_name)) {
            $arabicLastName = self::translateName($student->last_name);
            if ($arabicLastName) {
                $updates['last_name_ar'] = $arabicLastName;
            }
        }

        // Translate nationality
        if (empty($student->nationality_ar) && !empty($student->nationality)) {
            $arabicNationality = self::translateName($student->nationality, 'nationality');
            if ($arabicNationality) {
                $updates['nationality_ar'] = $arabicNationality;
            }
        }

        return $updates;
    }

    /**
     * Bulk translate names for multiple students
     */
    public static function bulkTranslateStudents(array $studentIds): int
    {
        $count = 0;
        
        foreach ($studentIds as $studentId) {
            $student = \App\Models\Student::find($studentId);
            if (!$student) continue;

            $updates = self::autoTranslateStudentNames($student);
            
            if (!empty($updates)) {
                $student->update($updates);
                $count++;
            }
        }

        return $count;
    }

    /**
     * Get suggested Arabic name for a given English name
     */
    public static function getSuggestedArabicName(string $englishName): array
    {
        $suggestions = [];
        
        // Direct mapping
        if (isset(self::$commonNameMappings[$englishName])) {
            $suggestions[] = self::$commonNameMappings[$englishName];
        }

        // Partial matches
        foreach (self::$commonNameMappings as $english => $arabic) {
            if (stripos($english, $englishName) !== false || stripos($englishName, $english) !== false) {
                $suggestions[] = $arabic;
            }
        }

        return array_unique($suggestions);
    }
}
