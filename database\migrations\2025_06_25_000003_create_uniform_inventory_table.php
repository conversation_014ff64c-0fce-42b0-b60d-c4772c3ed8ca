<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('uniform_inventory', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('academy_id')->constrained()->onDelete('cascade');
            $table->foreignId('uniform_category_id')->constrained()->onDelete('cascade');
            $table->foreignId('uniform_supplier_id')->nullable()->constrained()->onDelete('set null');
            
            // Product Details
            $table->string('sku')->unique(); // Stock Keeping Unit
            $table->string('name'); // Product name
            $table->string('name_ar')->nullable();
            $table->text('description')->nullable();
            $table->text('description_ar')->nullable();
            $table->string('size'); // XS, S, M, L, XL, etc.
            $table->string('color')->nullable();
            $table->string('color_ar')->nullable();
            $table->string('material')->nullable();
            $table->string('brand')->nullable();
            $table->string('model')->nullable();
            $table->string('barcode')->nullable();
            
            // Inventory Management
            $table->integer('current_stock')->default(0);
            $table->integer('reserved_stock')->default(0); // Stock reserved for pending orders
            $table->integer('available_stock')->default(0); // current_stock - reserved_stock
            $table->integer('minimum_stock')->default(10); // Reorder point
            $table->integer('maximum_stock')->default(100); // Maximum stock level
            $table->integer('reorder_quantity')->default(50); // Quantity to reorder
            
            // Pricing
            $table->decimal('cost_price', 10, 2)->default(0); // Purchase cost in AED
            $table->decimal('selling_price', 10, 2)->default(0); // Selling price in AED
            $table->decimal('markup_percentage', 5, 2)->default(0); // Markup %
            $table->string('currency', 3)->default('AED');
            
            // Location & Organization
            $table->string('location')->nullable(); // Warehouse location
            $table->string('shelf')->nullable(); // Shelf number
            $table->string('bin')->nullable(); // Bin location
            
            // Status & Tracking
            $table->enum('status', ['active', 'inactive', 'discontinued', 'out_of_stock'])->default('active');
            $table->boolean('is_trackable')->default(true); // Track inventory levels
            $table->boolean('allow_backorder')->default(false); // Allow negative stock
            $table->date('last_restocked_at')->nullable();
            $table->date('last_sold_at')->nullable();
            
            // Supplier Information
            $table->string('supplier_sku')->nullable(); // Supplier's SKU
            $table->decimal('supplier_price', 10, 2)->nullable(); // Last purchase price
            $table->integer('supplier_lead_time')->nullable(); // Days
            $table->integer('supplier_minimum_order')->nullable();
            
            // Additional Fields
            $table->json('attributes')->nullable(); // Additional attributes (size chart, care instructions, etc.)
            $table->text('notes')->nullable();
            $table->text('notes_ar')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['branch_id', 'academy_id', 'status']);
            $table->index(['uniform_category_id', 'size', 'color']);
            $table->index(['current_stock', 'minimum_stock']);
            $table->index(['sku', 'barcode']);
            $table->index('status');
            $table->index('last_restocked_at');
            $table->index('last_sold_at');
            
            // Unique constraints
            $table->unique(['branch_id', 'academy_id', 'uniform_category_id', 'size', 'color'], 'unique_inventory_item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('uniform_inventory');
    }
};
