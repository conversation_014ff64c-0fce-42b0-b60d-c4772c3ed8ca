<?php

namespace App\Http\Controllers;

use App\Models\Reservation;
use App\Models\Venue;
use App\Models\Field;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class ReservationController extends Controller
{
    /**
     * Display a listing of reservations with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Reservation::class);

        $query = Reservation::with(['venue', 'field', 'customer', 'payments'])
            ->withCount(['payments']);

        // Search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('reservation_number', 'like', "%{$search}%")
                    ->orWhere('event_name', 'like', "%{$search}%")
                    ->orWhereHas('customer', function ($customerQuery) use ($search) {
                        $customerQuery->where('full_name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%")
                            ->orWhere('phone', 'like', "%{$search}%");
                    });
            });
        }

        // Venue filter
        if ($request->filled('venue_id')) {
            $query->where('venue_id', $request->get('venue_id'));
        }

        // Field filter
        if ($request->filled('field_id')) {
            $query->where('field_id', $request->get('field_id'));
        }

        // Status filter
        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        // Payment status filter
        if ($request->filled('payment_status')) {
            $query->where('payment_status', $request->get('payment_status'));
        }

        // Date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('reservation_date', '>=', $request->get('date_from'));
        }
        if ($request->filled('date_to')) {
            $query->whereDate('reservation_date', '<=', $request->get('date_to'));
        }

        // Today's reservations filter
        if ($request->filled('today')) {
            $query->today();
        }

        // Upcoming reservations filter
        if ($request->filled('upcoming')) {
            $query->upcoming();
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'reservation_date');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $reservations = $query->paginate(15)->withQueryString();

        // Get filter options
        $venues = Venue::active()->orderBy('name')->get();
        $fields = Field::active()->orderBy('name')->get();

        if ($request->ajax()) {
            return response()->json([
                'reservations' => $reservations,
                'html' => view('reservations.partials.table', compact('reservations'))->render()
            ]);
        }

        return view('reservations.index', compact('reservations', 'venues', 'fields'));
    }

    /**
     * Show the form for creating a new reservation.
     */
    public function create(Request $request): View
    {
        Gate::authorize('create', Reservation::class);

        $venues = Venue::active()->with('fields')->orderBy('name')->get();
        $customers = Customer::active()->orderBy('full_name')->get();

        // Get selected venue if venue_id is provided
        $selectedVenue = null;
        if ($request->filled('venue_id')) {
            $selectedVenue = Venue::with('fields')->find($request->get('venue_id'));
        }

        // Get selected field if field_id is provided
        $selectedField = null;
        if ($request->filled('field_id')) {
            $selectedField = Field::with('venue')->find($request->get('field_id'));
            // If field is selected but venue is not, set the venue from the field
            if ($selectedField && !$selectedVenue) {
                $selectedVenue = $selectedField->venue;
            }
        }

        return view('reservations.create', compact('venues', 'customers', 'selectedVenue', 'selectedField'));
    }

    /**
     * Store a newly created reservation in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Reservation::class);

        $validator = Validator::make($request->all(), [
            'venue_id' => 'required|exists:venues,id',
            'field_id' => 'required|exists:fields,id',
            'customer_id' => 'required|exists:customers,id',
            'reservation_date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'duration_hours' => 'required|integer|min:1',
            'hourly_rate' => 'required|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'vat_rate' => 'required|numeric|min:0|max:100',
            'currency' => 'required|string|size:3',
            'booking_type' => 'required|in:regular,recurring,event',
            'payment_method' => 'required|in:cash,card,bank_transfer,online',
            'deposit_amount' => 'nullable|numeric|min:0',
            'expected_participants' => 'nullable|integer|min:1',
            'event_type' => 'nullable|in:training,match,tournament,casual,corporate',
            'event_name' => 'nullable|string|max:255',
            'special_requirements' => 'nullable|string|max:1000',
            'special_requirements_ar' => 'nullable|string|max:1000',
            'equipment_requested' => 'nullable|array',
            'recurring_booking' => 'boolean',
            'recurring_pattern' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $validator->validated();

            // Generate reservation number
            $data['reservation_number'] = $this->generateReservationNumber();
            $data['booking_datetime'] = now();
            $data['status'] = 'pending';
            $data['payment_status'] = 'pending';

            // Check field availability
            $field = Field::find($data['field_id']);
            if (!$field->isAvailableForTimeSlot($data['reservation_date'], $data['start_time'], $data['end_time'])) {
                throw new \Exception('Selected time slot is not available');
            }

            $reservation = Reservation::create($data);

            // Calculate amounts
            $reservation->calculateTotalAmount();
            $reservation->save();

            // Update customer booking stats
            $customer = Customer::find($data['customer_id']);
            $customer->updateBookingStats($reservation->total_amount);

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Reservation created successfully',
                    'reservation' => $reservation
                ]);
            }

            return redirect()->route('reservations.show', $reservation)
                ->with('success', 'Reservation created successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating reservation: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 500);
            }

            return back()->with('error', $e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified reservation.
     */
    public function show(Reservation $reservation, Request $request): View|JsonResponse
    {
        Gate::authorize('view', $reservation);

        $reservation->load([
            'venue',
            'field',
            'customer',
            'payments.processedBy',
            'childReservations'
        ]);

        if ($request->ajax()) {
            return response()->json([
                'reservation' => $reservation
            ]);
        }

        return view('reservations.show', compact('reservation'));
    }

    /**
     * Show the form for editing the specified reservation.
     */
    public function edit(Reservation $reservation): View|RedirectResponse
    {
        Gate::authorize('update', $reservation);

        if (!$reservation->can_modify) {
            return redirect()->route('reservations.show', $reservation)
                ->with('error', 'This reservation cannot be modified');
        }

        $venues = Venue::active()->with('fields')->orderBy('name')->get();
        $customers = Customer::active()->orderBy('full_name')->get();

        return view('reservations.edit', compact('reservation', 'venues', 'customers'));
    }

    /**
     * Update the specified reservation in storage.
     */
    public function update(Request $request, Reservation $reservation): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $reservation);

        if (!$reservation->can_modify) {
            $message = 'This reservation cannot be modified';

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $message
                ], 422);
            }

            return back()->with('error', $message);
        }

        $validator = Validator::make($request->all(), [
            'venue_id' => 'required|exists:venues,id',
            'field_id' => 'required|exists:fields,id',
            'customer_id' => 'required|exists:customers,id',
            'reservation_date' => 'required|date|after_or_equal:today',
            'start_time' => 'required|date_format:H:i',
            'end_time' => 'required|date_format:H:i|after:start_time',
            'duration_hours' => 'required|integer|min:1',
            'hourly_rate' => 'required|numeric|min:0',
            'discount_percentage' => 'nullable|numeric|min:0|max:100',
            'vat_rate' => 'required|numeric|min:0|max:100',
            'currency' => 'required|string|size:3',
            'booking_type' => 'required|in:regular,recurring,event',
            'payment_method' => 'required|in:cash,card,bank_transfer,online',
            'deposit_amount' => 'nullable|numeric|min:0',
            'expected_participants' => 'nullable|integer|min:1',
            'event_type' => 'nullable|in:training,match,tournament,casual,corporate',
            'event_name' => 'nullable|string|max:255',
            'special_requirements' => 'nullable|string|max:1000',
            'special_requirements_ar' => 'nullable|string|max:1000',
            'equipment_requested' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()
                ], 422);
            }
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $data = $validator->validated();

            // Check field availability (excluding current reservation)
            $field = Field::find($data['field_id']);
            if (!$field->isAvailableForTimeSlot($data['reservation_date'], $data['start_time'], $data['end_time'], $reservation->id)) {
                throw new \Exception('Selected time slot is not available');
            }

            $reservation->update($data);

            // Recalculate amounts
            $reservation->calculateTotalAmount();
            $reservation->save();

            DB::commit();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Reservation updated successfully',
                    'reservation' => $reservation->fresh()
                ]);
            }

            return redirect()->route('reservations.show', $reservation)
                ->with('success', 'Reservation updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating reservation: ' . $e->getMessage());

            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 500);
            }

            return back()->with('error', $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified reservation from storage.
     */
    public function destroy(Reservation $reservation): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $reservation);

        try {
            // Check if reservation can be deleted
            if (in_array($reservation->status, ['completed', 'in_progress'])) {
                $message = 'Cannot delete completed or in-progress reservations';

                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 422);
                }

                return back()->with('error', $message);
            }

            // Check if reservation has payments
            if ($reservation->payments()->completed()->count() > 0) {
                $message = 'Cannot delete reservation with completed payments';

                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 422);
                }

                return back()->with('error', $message);
            }

            $reservation->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Reservation deleted successfully'
                ]);
            }

            return redirect()->route('reservations.index')
                ->with('success', 'Reservation deleted successfully');
        } catch (\Exception $e) {
            Log::error('Error deleting reservation: ' . $e->getMessage());

            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Error deleting reservation'
                ], 500);
            }

            return back()->with('error', 'Error deleting reservation');
        }
    }

    /**
     * Confirm the reservation.
     */
    public function confirm(Reservation $reservation): JsonResponse
    {
        Gate::authorize('update', $reservation);

        try {
            if ($reservation->status !== 'pending') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only pending reservations can be confirmed'
                ], 422);
            }

            $reservation->confirm();

            return response()->json([
                'success' => true,
                'message' => 'Reservation confirmed successfully',
                'status' => $reservation->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error confirming reservation: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error confirming reservation'
            ], 500);
        }
    }

    /**
     * Cancel the reservation.
     */
    public function cancel(Request $request, Reservation $reservation): JsonResponse
    {
        Gate::authorize('update', $reservation);

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500',
            'cancellation_fee' => 'nullable|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            if (!$reservation->can_cancel) {
                return response()->json([
                    'success' => false,
                    'message' => 'This reservation cannot be cancelled'
                ], 422);
            }

            $reservation->cancel(
                $request->get('reason'),
                Auth::user()->name,
                $request->get('cancellation_fee', 0)
            );

            return response()->json([
                'success' => true,
                'message' => 'Reservation cancelled successfully',
                'status' => $reservation->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error cancelling reservation: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error cancelling reservation'
            ], 500);
        }
    }

    /**
     * Complete the reservation.
     */
    public function complete(Reservation $reservation): JsonResponse
    {
        Gate::authorize('update', $reservation);

        try {
            if ($reservation->status !== 'confirmed') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only confirmed reservations can be completed'
                ], 422);
            }

            $reservation->complete();

            return response()->json([
                'success' => true,
                'message' => 'Reservation completed successfully',
                'status' => $reservation->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error completing reservation: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error completing reservation'
            ], 500);
        }
    }

    /**
     * Mark reservation as no-show.
     */
    public function markNoShow(Reservation $reservation): JsonResponse
    {
        Gate::authorize('update', $reservation);

        try {
            if (!in_array($reservation->status, ['confirmed', 'in_progress'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Only confirmed or in-progress reservations can be marked as no-show'
                ], 422);
            }

            $reservation->update(['status' => 'no_show']);

            return response()->json([
                'success' => true,
                'message' => 'Reservation marked as no-show',
                'status' => $reservation->status
            ]);
        } catch (\Exception $e) {
            Log::error('Error marking reservation as no-show: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error updating reservation'
            ], 500);
        }
    }

    /**
     * Generate and display reservation invoice for printing.
     */
    public function invoice(Reservation $reservation): View
    {
        Gate::authorize('view', $reservation);

        $reservation->load(['venue', 'field', 'customer', 'payments']);

        return view('reservations.invoice', compact('reservation'));
    }

    /**
     * Display calendar view of reservations.
     */
    public function calendar(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Reservation::class);

        if ($request->ajax()) {
            return $this->apiCalendar($request);
        }

        $venues = Venue::active()->orderBy('name')->get();
        $fields = Field::active()->orderBy('name')->get();

        return view('reservations.calendar', compact('venues', 'fields'));
    }

    /**
     * Export reservations to Excel.
     */
    public function exportExcel(Request $request): \Symfony\Component\HttpFoundation\Response
    {
        Gate::authorize('export', Reservation::class);

        // Implementation for Excel export would go here
        // For now, return a simple response
        return response()->make('Excel content', 200, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="reservations.xlsx"'
        ]);
    }

    /**
     * Export reservations to PDF.
     */
    public function exportPdf(Request $request): \Symfony\Component\HttpFoundation\Response
    {
        Gate::authorize('export', Reservation::class);

        // Implementation for PDF export would go here
        // For now, return a simple response
        return response()->make('PDF content', 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="reservations.pdf"'
        ]);
    }

    /**
     * API endpoint for reservations (AJAX requests).
     */
    public function apiIndex(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', Reservation::class);

        $query = Reservation::with(['venue', 'field', 'customer']);

        // Apply filters
        if ($request->filled('venue_id')) {
            $query->where('venue_id', $request->get('venue_id'));
        }

        if ($request->filled('field_id')) {
            $query->where('field_id', $request->get('field_id'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->get('status'));
        }

        if ($request->filled('date')) {
            $query->whereDate('reservation_date', $request->get('date'));
        }

        $reservations = $query->orderBy('reservation_date')->orderBy('start_time')->get();

        return response()->json($reservations);
    }

    /**
     * API endpoint for calendar data (AJAX requests).
     */
    public function apiCalendar(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', Reservation::class);

        $start = $request->get('start');
        $end = $request->get('end');

        $query = Reservation::with(['venue', 'field', 'customer']);

        if ($start && $end) {
            $query->whereBetween('reservation_date', [$start, $end]);
        }

        // Apply filters
        if ($request->filled('venue_id')) {
            $query->where('venue_id', $request->get('venue_id'));
        }

        if ($request->filled('field_id')) {
            $query->where('field_id', $request->get('field_id'));
        }

        $reservations = $query->get();

        // Format for calendar
        $events = $reservations->map(function ($reservation) {
            return [
                'id' => $reservation->id,
                'title' => $reservation->customer->full_name . ' - ' . $reservation->field->name,
                'start' => $reservation->reservation_date->format('Y-m-d') . 'T' . $reservation->start_time->format('H:i:s'),
                'end' => $reservation->reservation_date->format('Y-m-d') . 'T' . $reservation->end_time->format('H:i:s'),
                'backgroundColor' => $this->getStatusColor($reservation->status),
                'borderColor' => $this->getStatusColor($reservation->status),
                'extendedProps' => [
                    'reservation_number' => $reservation->reservation_number,
                    'customer' => $reservation->customer->full_name,
                    'venue' => $reservation->venue->name,
                    'field' => $reservation->field->name,
                    'status' => $reservation->status,
                    'total_amount' => $reservation->formatted_total_amount,
                ]
            ];
        });

        return response()->json($events);
    }

    /**
     * Get reservation statistics.
     */
    public function getStatistics(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', Reservation::class);

        $stats = [
            'total_reservations' => Reservation::count(),
            'today_reservations' => Reservation::today()->count(),
            'upcoming_reservations' => Reservation::upcoming()->count(),
            'confirmed_reservations' => Reservation::confirmed()->count(),
            'pending_reservations' => Reservation::where('status', 'pending')->count(),
            'cancelled_reservations' => Reservation::where('status', 'cancelled')->count(),
            'completed_reservations' => Reservation::where('status', 'completed')->count(),
            'total_revenue' => Reservation::where('payment_status', 'paid')->sum('total_amount'),
            'pending_revenue' => Reservation::where('payment_status', 'unpaid')->sum('total_amount'),
        ];

        return response()->json($stats);
    }

    /**
     * Generate a unique reservation number.
     */
    private function generateReservationNumber(): string
    {
        $prefix = 'RES';
        $year = date('Y');
        $month = date('m');

        // Get the last reservation number for this month
        $lastReservation = Reservation::where('reservation_number', 'like', "{$prefix}{$year}{$month}%")
            ->orderBy('reservation_number', 'desc')
            ->first();

        if ($lastReservation) {
            $lastNumber = (int) substr($lastReservation->reservation_number, -4);
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . $year . $month . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Execute bulk action on selected reservations.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        Gate::authorize('bulkAction', Reservation::class);

        $validator = Validator::make($request->all(), [
            'action' => 'required|in:confirm,cancel,delete',
            'reservation_ids' => 'required|array|min:1',
            'reservation_ids.*' => 'exists:reservations,id',
            'reason' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $action = $request->get('action');
            $reservationIds = $request->get('reservation_ids');
            $reason = $request->get('reason');
            $successCount = 0;
            $errors = [];

            foreach ($reservationIds as $reservationId) {
                try {
                    $reservation = Reservation::find($reservationId);

                    if (!$reservation) {
                        $errors[] = "Reservation {$reservationId} not found";
                        continue;
                    }

                    switch ($action) {
                        case 'confirm':
                            if ($reservation->status === 'pending') {
                                $reservation->confirm();
                                $successCount++;
                            } else {
                                $errors[] = "Reservation {$reservation->reservation_number} cannot be confirmed";
                            }
                            break;

                        case 'cancel':
                            if ($reservation->can_cancel) {
                                $reservation->cancel($reason ?: 'Bulk cancellation', Auth::user()->name);
                                $successCount++;
                            } else {
                                $errors[] = "Reservation {$reservation->reservation_number} cannot be cancelled";
                            }
                            break;

                        case 'delete':
                            if (
                                !in_array($reservation->status, ['completed', 'in_progress']) &&
                                $reservation->payments()->completed()->count() === 0
                            ) {
                                $reservation->delete();
                                $successCount++;
                            } else {
                                $errors[] = "Reservation {$reservation->reservation_number} cannot be deleted";
                            }
                            break;
                    }
                } catch (\Exception $e) {
                    $errors[] = "Error processing reservation {$reservationId}: " . $e->getMessage();
                }
            }

            DB::commit();

            $message = "{$successCount} reservation(s) processed successfully";
            if (!empty($errors)) {
                $message .= ". " . count($errors) . " error(s) occurred.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'processed_count' => $successCount,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error executing bulk action: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error executing bulk action'
            ], 500);
        }
    }

    /**
     * Get status color for calendar events.
     */
    private function getStatusColor(string $status): string
    {
        return match ($status) {
            'pending' => '#fbbf24',
            'confirmed' => '#10b981',
            'in_progress' => '#3b82f6',
            'completed' => '#6b7280',
            'cancelled' => '#ef4444',
            'no_show' => '#f59e0b',
            default => '#6b7280',
        };
    }
}
