<!-- Payments Table -->
<div class="overflow-x-auto">
    <table class="table-bank">
        <thead>
            <tr>
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bulkAction', App\Models\Payment::class)): ?>
                    <th class="w-12">
                        <input type="checkbox" class="form-checkbox" @change="toggleAllPayments($event.target.checked)" id="selectAllPayments">
                    </th>
                <?php endif; ?>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Payment Details
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Student & Location
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Amount & Method
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Dates & Period
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Status & Actions
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-light-gray">
            <?php $__empty_1 = true; $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr class="hover:bg-off-white transition-colors duration-200">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('bulkAction', App\Models\Payment::class)): ?>
                        <td class="px-6 py-4">
                            <input type="checkbox" name="payment_ids[]" value="<?php echo e($payment->id); ?>"
                                @change="togglePaymentSelection(<?php echo e($payment->id); ?>)" class="form-checkbox">
                        </td>
                    <?php endif; ?>

                    <!-- Payment Details -->
                    <td class="px-6 py-4">
                        <div class="space-y-1">
                            <div class="font-semibold text-charcoal-black">
                                <?php echo e($payment->reference_number ?? 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT)); ?>

                            </div>
                            <?php if($payment->description): ?>
                                <div class="text-sm text-dark-gray"><?php echo e($payment->description); ?></div>
                            <?php endif; ?>
                            <?php if($payment->reset_num): ?>
                                <div class="text-xs text-gray-500">Reset: <?php echo e($payment->reset_num); ?></div>
                            <?php endif; ?>
                        </div>
                    </td>

                    <!-- Student & Location -->
                    <td class="px-6 py-4">
                        <div class="space-y-1">
                            <div class="font-medium">
                                <?php if($payment->student): ?>
                                    <a href="<?php echo e(route('students.show', $payment->student)); ?>"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                        style="color: #dc2626 !important;">
                                        <?php echo e($payment->student->full_name); ?>

                                    </a>
                                <?php else: ?>
                                    <span class="text-gray-500">N/A</span>
                                <?php endif; ?>
                            </div>
                            <div class="text-sm">
                                <?php if($payment->branch): ?>
                                    <a href="<?php echo e(route('branches.show', $payment->branch)); ?>"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                        style="color: #dc2626 !important;">
                                        <?php echo e($payment->branch->name); ?>

                                    </a>
                                <?php else: ?>
                                    <span class="text-gray-500">N/A</span>
                                <?php endif; ?>
                            </div>
                            <div class="text-xs">
                                <?php if($payment->academy): ?>
                                    <a href="<?php echo e(route('academies.show', $payment->academy)); ?>"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                        style="color: #dc2626 !important;">
                                        <?php echo e($payment->academy->name); ?>

                                    </a>
                                <?php else: ?>
                                    <span class="text-gray-500">N/A</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </td>

                    <!-- Amount & Method -->
                    <td class="px-6 py-4">
                        <div class="space-y-1">
                            <div class="font-bold text-leaders-red text-lg">
                                AED <?php echo e(number_format($payment->amount, 2)); ?>

                            </div>
                            <?php if($payment->discount > 0): ?>
                                <div class="text-sm text-green-600">
                                    Discount: AED <?php echo e(number_format($payment->discount, 2)); ?>

                                </div>
                                <div class="text-sm font-medium text-charcoal-black">
                                    Net: AED <?php echo e(number_format($payment->net_amount, 2)); ?>

                                </div>
                            <?php endif; ?>
                            <div class="text-xs text-dark-gray">
                                <?php echo e($payment->method_text); ?>

                            </div>
                        </div>
                    </td>

                    <!-- Dates & Period -->
                    <td class="px-6 py-4">
                        <div class="space-y-1">
                            <div class="text-sm font-medium text-charcoal-black">
                                Paid: <?php echo e($payment->formatted_payment_date ?: 'Not paid'); ?>

                            </div>
                            <div class="text-xs text-dark-gray">
                                Period: <?php echo e($payment->formatted_start_date); ?> - <?php echo e($payment->formatted_end_date); ?>

                            </div>
                            <?php if($payment->class_time_from && $payment->class_time_to): ?>
                                <div class="text-xs text-gray-500">
                                    Time: <?php echo e($payment->class_time_from); ?> - <?php echo e($payment->class_time_to); ?>

                                </div>
                            <?php endif; ?>
                            <?php if($payment->renewal): ?>
                                <span
                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Renewal
                                </span>
                            <?php endif; ?>
                            <span
                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium <?php echo e($payment->payment_type_badge_class); ?>">
                                <?php echo e($payment->payment_type_text); ?>

                            </span>
                        </div>
                    </td>

                    <!-- Status & Actions -->
                    <td class="px-6 py-4">
                        <div class="space-y-2">
                            <span
                                class="badge-bank <?php echo e($payment->status_badge_class); ?> <?php echo e($payment->status === 'expired' ? 'expired-payment' : ''); ?>">
                                <?php echo e($payment->status_text); ?>

                            </span>
                            <?php if($payment->note): ?>
                                <div class="text-xs text-gray-500 max-w-xs truncate" title="<?php echo e($payment->note); ?>">
                                    Note: <?php echo e($payment->note); ?>

                                </div>
                            <?php endif; ?>
                        </div>
                    </td>

                    <!-- Actions -->
                    <td class="px-6 py-4 text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $payment)): ?>
                                <a href="<?php echo e(route('payments.show', $payment)); ?>"
                                    class="text-blue-600 hover:text-blue-900 transition-colors" title="View Details">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>

                                <a href="<?php echo e(route('payments.invoice', $payment)); ?>" target="_blank"
                                    class="text-green-600 hover:text-green-900 transition-colors" title="Print Invoice">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                                        </path>
                                    </svg>
                                </a>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $payment)): ?>
                                <a href="<?php echo e(route('payments.edit', $payment)); ?>"
                                    class="text-indigo-600 hover:text-indigo-900 transition-colors" title="Edit Payment">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>

                                <button @click="toggleStatus(<?php echo e($payment->id); ?>)"
                                    class="text-yellow-600 hover:text-yellow-900 transition-colors" title="Toggle Status">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                    </svg>
                                </button>
                            <?php endif; ?>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $payment)): ?>
                                <button @click="deletePayment(<?php echo e($payment->id); ?>)"
                                    class="text-red-500 hover:text-red-700 hover:bg-red-50 p-1 rounded transition-all duration-200" title="Delete Payment">
                                    <svg class="w-5 h-5" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                            <h3 class="text-lg font-medium text-dark-gray mb-2">No payments found</h3>
                            <p class="text-gray-500 mb-4">No payments match your current filters.</p>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\Payment::class)): ?>
                                <a href="<?php echo e(route('payments.create')); ?>" class="btn-bank">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add First Payment
                                </a>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>

<?php $__env->startPush('styles'); ?>
    <style>
        /* Enhanced Table Header Styling */
        .table-bank thead {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
        }

        .table-bank thead th {
            color: white !important;
            font-weight: 600 !important;
            font-size: 0.75rem !important;
            text-transform: uppercase !important;
            letter-spacing: 0.1em !important;
            padding: 1rem 1.5rem !important;
            border-bottom: 2px solid #e53e3e !important;
            background: transparent !important;
        }

        .table-bank thead th:first-child {
            border-top-left-radius: 0.5rem !important;
        }

        .table-bank thead th:last-child {
            border-top-right-radius: 0.5rem !important;
        }

        /* Ensure white text visibility */
        .table-bank thead th .text-white {
            color: white !important;
        }

        /* Enhanced table styling */
        .table-bank {
            border-radius: 0.5rem !important;
            overflow: hidden !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        }

        .table-bank tbody tr:hover {
            background-color: #f9fafb !important;
            transform: translateY(-1px) !important;
            transition: all 0.2s ease-in-out !important;
        }

        /* Payment Type Badge Styles */
        .badge-primary {
            background-color: #dbeafe !important;
            color: #1e40af !important;
        }

        .badge-info {
            background-color: #e0f2fe !important;
            color: #0369a1 !important;
        }

        .badge-secondary {
            background-color: #f3f4f6 !important;
            color: #374151 !important;
        }

        /* Enhanced Delete Button Styling */
        .delete-btn {
            color: #ef4444 !important;
            transition: all 0.2s ease-in-out !important;
        }

        .delete-btn:hover {
            color: #dc2626 !important;
            background-color: #fef2f2 !important;
            transform: scale(1.1) !important;
        }

        /* Select All Checkbox Styling */
        #selectAllPayments {
            accent-color: #ef4444 !important;
        }

        /* Individual Checkbox Styling */
        input[name="payment_ids[]"] {
            accent-color: #ef4444 !important;
        }
    </style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
    <script>


        async function toggleStatus(paymentId) {
            if (!confirm('Are you sure you want to toggle the payment status?')) {
                return;
            }

            try {
                const response = await fetch(`/payments/${paymentId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Reload the page to show updated status
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while updating the payment status.');
            }
        }


    </script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\xampp\htdocs\erp-login2\resources\views/payments/_table.blade.php ENDPATH**/ ?>