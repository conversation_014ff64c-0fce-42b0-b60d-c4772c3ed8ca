<?php

require_once 'vendor/autoload.php';

use App\Services\LanguageService;
use App\Helpers\TranslationHelper;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== UAE English Sports Academy - Backend Infrastructure Test ===" . PHP_EOL;
echo PHP_EOL;

try {
    // Test LanguageService
    echo "1. Testing LanguageService..." . PHP_EOL;
    $supportedLocales = LanguageService::getSupportedLocales();
    echo "   ✅ Supported locales: " . implode(', ', array_keys($supportedLocales)) . PHP_EOL;
    echo "   ✅ Current locale: " . LanguageService::getCurrentLocale() . PHP_EOL;
    echo "   ✅ Current direction: " . LanguageService::getCurrentDirection() . PHP_EOL;
    echo "   ✅ Is RTL: " . (LanguageService::isCurrentRtl() ? 'Yes' : 'No') . PHP_EOL;
    echo "   ✅ Currency format: " . LanguageService::formatCurrency(1234.56) . PHP_EOL;
    echo "   ✅ Number format: " . LanguageService::formatNumber(1234.56) . PHP_EOL;
    echo "   ✅ Phone format: " . LanguageService::formatPhone('501234567') . PHP_EOL;
    echo PHP_EOL;

    // Test Arabic locale
    echo "2. Testing Arabic locale..." . PHP_EOL;
    $arabicConfig = LanguageService::getLocaleConfig('ar');
    echo "   ✅ Arabic name: " . $arabicConfig['native'] . PHP_EOL;
    echo "   ✅ Arabic direction: " . $arabicConfig['direction'] . PHP_EOL;
    echo "   ✅ Arabic flag: " . $arabicConfig['flag'] . PHP_EOL;
    echo "   ✅ Arabic currency format: " . LanguageService::formatCurrency(1234.56, 'AED', 'ar') . PHP_EOL;
    echo PHP_EOL;

    // Test TranslationHelper
    echo "3. Testing TranslationHelper..." . PHP_EOL;
    $commonTranslations = TranslationHelper::getCommonTranslations();
    echo "   ✅ Common translations loaded: " . count($commonTranslations) . " categories" . PHP_EOL;
    echo "   ✅ Actions available: " . count($commonTranslations['actions']) . " actions" . PHP_EOL;
    echo "   ✅ Status options: " . count($commonTranslations['status']) . " statuses" . PHP_EOL;
    echo PHP_EOL;

    // Test Language Switcher Data
    echo "4. Testing Language Switcher..." . PHP_EOL;
    $languageData = LanguageService::getLanguageSwitcherData();
    foreach ($languageData as $lang) {
        echo "   ✅ {$lang['flag']} {$lang['native']} ({$lang['code']}) - {$lang['direction']}" . PHP_EOL;
    }
    echo PHP_EOL;

    // Test HTML Attributes
    echo "5. Testing HTML Attributes..." . PHP_EOL;
    $htmlAttrs = LanguageService::getHtmlAttributes();
    echo "   ✅ HTML lang: " . $htmlAttrs['lang'] . PHP_EOL;
    echo "   ✅ HTML dir: " . $htmlAttrs['dir'] . PHP_EOL;
    echo "   ✅ HTML class: " . $htmlAttrs['class'] . PHP_EOL;
    echo PHP_EOL;

    // Test Database Connection
    echo "6. Testing Database Connection..." . PHP_EOL;
    $userCount = \App\Models\User::count();
    echo "   ✅ Users in database: " . $userCount . PHP_EOL;
    
    // Check if language_preference column exists
    $hasLanguageColumn = \Illuminate\Support\Facades\Schema::hasColumn('users', 'language_preference');
    echo "   ✅ Language preference column: " . ($hasLanguageColumn ? 'Exists' : 'Missing') . PHP_EOL;
    echo PHP_EOL;

    echo "🚀 BACKEND INFRASTRUCTURE TEST COMPLETED SUCCESSFULLY!" . PHP_EOL;
    echo "✅ All components are working correctly" . PHP_EOL;
    echo "✅ Ready for Phase 2: Frontend Layout & Styling" . PHP_EOL;

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . PHP_EOL;
    echo "File: " . $e->getFile() . ":" . $e->getLine() . PHP_EOL;
}
