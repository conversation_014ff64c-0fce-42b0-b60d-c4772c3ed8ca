<!-- Payments Table -->
<div class="overflow-x-auto">
    <table class="table-bank">
        <thead>
            <tr>
                @can('bulkAction', App\Models\Payment::class)
                    <th class="w-12">
                        <input type="checkbox" class="form-checkbox" @change="toggleAllPayments($event)">
                    </th>
                @endcan
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Payment Details
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Student & Location
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Amount & Method
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Dates & Period
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                    Status & Actions
                </th>
                <th class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-light-gray">
            @forelse($payments as $payment)
                <tr class="hover:bg-off-white transition-colors duration-200">
                    @can('bulkAction', App\Models\Payment::class)
                        <td class="px-6 py-4">
                            <input type="checkbox" name="payment_ids[]" value="{{ $payment->id }}"
                                @change="togglePaymentSelection({{ $payment->id }})" class="form-checkbox">
                        </td>
                    @endcan

                    <!-- Payment Details -->
                    <td class="px-6 py-4">
                        <div class="space-y-1">
                            <div class="font-semibold text-charcoal-black">
                                {{ $payment->reference_number ?? 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}
                            </div>
                            @if ($payment->description)
                                <div class="text-sm text-dark-gray">{{ $payment->description }}</div>
                            @endif
                            @if ($payment->reset_num)
                                <div class="text-xs text-gray-500">Reset: {{ $payment->reset_num }}</div>
                            @endif
                        </div>
                    </td>

                    <!-- Student & Location -->
                    <td class="px-6 py-4">
                        <div class="space-y-1">
                            <div class="font-medium">
                                @if ($payment->student)
                                    <a href="{{ route('students.show', $payment->student) }}"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                        style="color: #dc2626 !important;">
                                        {{ $payment->student->full_name }}
                                    </a>
                                @else
                                    <span class="text-gray-500">N/A</span>
                                @endif
                            </div>
                            <div class="text-sm">
                                @if ($payment->branch)
                                    <a href="{{ route('branches.show', $payment->branch) }}"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                        style="color: #dc2626 !important;">
                                        {{ $payment->branch->name }}
                                    </a>
                                @else
                                    <span class="text-gray-500">N/A</span>
                                @endif
                            </div>
                            <div class="text-xs">
                                @if ($payment->academy)
                                    <a href="{{ route('academies.show', $payment->academy) }}"
                                        class="text-red-600 hover:text-red-800 underline transition-colors duration-200"
                                        style="color: #dc2626 !important;">
                                        {{ $payment->academy->name }}
                                    </a>
                                @else
                                    <span class="text-gray-500">N/A</span>
                                @endif
                            </div>
                        </div>
                    </td>

                    <!-- Amount & Method -->
                    <td class="px-6 py-4">
                        <div class="space-y-1">
                            <div class="font-bold text-leaders-red text-lg">
                                AED {{ number_format($payment->amount, 2) }}
                            </div>
                            @if ($payment->discount > 0)
                                <div class="text-sm text-green-600">
                                    Discount: AED {{ number_format($payment->discount, 2) }}
                                </div>
                                <div class="text-sm font-medium text-charcoal-black">
                                    Net: AED {{ number_format($payment->net_amount, 2) }}
                                </div>
                            @endif
                            <div class="text-xs text-dark-gray">
                                {{ $payment->method_text }}
                            </div>
                        </div>
                    </td>

                    <!-- Dates & Period -->
                    <td class="px-6 py-4">
                        <div class="space-y-1">
                            <div class="text-sm font-medium text-charcoal-black">
                                Paid: {{ $payment->formatted_payment_date ?: 'Not paid' }}
                            </div>
                            <div class="text-xs text-dark-gray">
                                Period: {{ $payment->formatted_start_date }} - {{ $payment->formatted_end_date }}
                            </div>
                            @if ($payment->class_time_from && $payment->class_time_to)
                                <div class="text-xs text-gray-500">
                                    Time: {{ $payment->class_time_from }} - {{ $payment->class_time_to }}
                                </div>
                            @endif
                            @if ($payment->renewal)
                                <span
                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    Renewal
                                </span>
                            @endif
                            <span
                                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $payment->payment_type_badge_class }}">
                                {{ $payment->payment_type_text }}
                            </span>
                        </div>
                    </td>

                    <!-- Status & Actions -->
                    <td class="px-6 py-4">
                        <div class="space-y-2">
                            <span class="badge-bank {{ $payment->status_badge_class }}">
                                {{ $payment->status_text }}
                            </span>
                            @if ($payment->note)
                                <div class="text-xs text-gray-500 max-w-xs truncate" title="{{ $payment->note }}">
                                    Note: {{ $payment->note }}
                                </div>
                            @endif
                        </div>
                    </td>

                    <!-- Actions -->
                    <td class="px-6 py-4 text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            @can('view', $payment)
                                <a href="{{ route('payments.show', $payment) }}"
                                    class="text-blue-600 hover:text-blue-900 transition-colors" title="View Details">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                        </path>
                                    </svg>
                                </a>

                                <a href="{{ route('payments.invoice', $payment) }}" target="_blank"
                                    class="text-green-600 hover:text-green-900 transition-colors" title="Print Invoice">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                                        </path>
                                    </svg>
                                </a>
                            @endcan

                            @can('update', $payment)
                                <a href="{{ route('payments.edit', $payment) }}"
                                    class="text-indigo-600 hover:text-indigo-900 transition-colors" title="Edit Payment">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                        </path>
                                    </svg>
                                </a>

                                <button @click="toggleStatus({{ $payment->id }})"
                                    class="text-yellow-600 hover:text-yellow-900 transition-colors" title="Toggle Status">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 9l4-4 4 4m0 6l-4 4-4-4"></path>
                                    </svg>
                                </button>
                            @endcan

                            @can('delete', $payment)
                                <button @click="deletePayment({{ $payment->id }})"
                                    class="text-red-600 hover:text-red-900 transition-colors" title="Delete Payment">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                                        </path>
                                    </svg>
                                </button>
                            @endcan
                        </div>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="7" class="px-6 py-12 text-center">
                        <div class="flex flex-col items-center justify-center">
                            <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z">
                                </path>
                            </svg>
                            <h3 class="text-lg font-medium text-dark-gray mb-2">No payments found</h3>
                            <p class="text-gray-500 mb-4">No payments match your current filters.</p>
                            @can('create', App\Models\Payment::class)
                                <a href="{{ route('payments.create') }}" class="btn-bank">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    Add First Payment
                                </a>
                            @endcan
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>
</div>

@push('styles')
    <style>
        /* Enhanced Table Header Styling */
        .table-bank thead {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%) !important;
        }

        .table-bank thead th {
            color: white !important;
            font-weight: 600 !important;
            font-size: 0.75rem !important;
            text-transform: uppercase !important;
            letter-spacing: 0.1em !important;
            padding: 1rem 1.5rem !important;
            border-bottom: 2px solid #e53e3e !important;
            background: transparent !important;
        }

        .table-bank thead th:first-child {
            border-top-left-radius: 0.5rem !important;
        }

        .table-bank thead th:last-child {
            border-top-right-radius: 0.5rem !important;
        }

        /* Ensure white text visibility */
        .table-bank thead th .text-white {
            color: white !important;
        }

        /* Enhanced table styling */
        .table-bank {
            border-radius: 0.5rem !important;
            overflow: hidden !important;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
        }

        .table-bank tbody tr:hover {
            background-color: #f9fafb !important;
            transform: translateY(-1px) !important;
            transition: all 0.2s ease-in-out !important;
        }

        /* Payment Type Badge Styles */
        .badge-primary {
            background-color: #dbeafe !important;
            color: #1e40af !important;
        }

        .badge-info {
            background-color: #e0f2fe !important;
            color: #0369a1 !important;
        }

        .badge-secondary {
            background-color: #f3f4f6 !important;
            color: #374151 !important;
        }
    </style>
@endpush

@push('scripts')
    <script>
        function toggleAllPayments(event) {
            const checkboxes = document.querySelectorAll('input[name="payment_ids[]"]');
            const isChecked = event.target.checked;

            checkboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
                const paymentId = parseInt(checkbox.value);

                if (isChecked) {
                    if (!this.selectedPayments.includes(paymentId)) {
                        this.selectedPayments.push(paymentId);
                    }
                } else {
                    const index = this.selectedPayments.indexOf(paymentId);
                    if (index > -1) {
                        this.selectedPayments.splice(index, 1);
                    }
                }
            });
        }

        async function toggleStatus(paymentId) {
            if (!confirm('Are you sure you want to toggle the payment status?')) {
                return;
            }

            try {
                const response = await fetch(`/payments/${paymentId}/toggle-status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Reload the page to show updated status
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while updating the payment status.');
            }
        }

        async function deletePayment(paymentId) {
            if (!confirm('Are you sure you want to delete this payment? This action cannot be undone.')) {
                return;
            }

            try {
                const response = await fetch(`/payments/${paymentId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                });

                const data = await response.json();

                if (data.success) {
                    // Reload the page to show updated list
                    window.location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('An error occurred while deleting the payment.');
            }
        }
    </script>
@endpush
