@extends('layouts.dashboard')

@section('title', 'Create New Reservation - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h4a2 2 0 012 2v1m-6 0h8m-8 0H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Create New Reservation</h1>
                <p class="text-lg text-dark-gray">Book a field for a customer</p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('reservations.index') }}"
                class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Reservations
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-6xl mx-auto" x-data="reservationForm()">
        <form method="POST" action="{{ route('reservations.store') }}" class="space-y-6">
            @csrf

            <!-- Basic Reservation Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                    <p class="bank-card-subtitle">Select venue, field, customer and booking details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Venue Selection -->
                        <div>
                            <label for="venue_id" class="form-label-bank required">Venue</label>
                            <select id="venue_id" name="venue_id"
                                class="form-select-bank @error('venue_id') border-red-500 @enderror"
                                @change="onVenueChange($event)" required>
                                <option value="">Select Venue</option>
                                @foreach ($venues as $venue)
                                    <option value="{{ $venue->id }}" data-vat-rate="{{ $venue->vat_rate }}"
                                        data-currency="{{ $venue->currency }}"
                                        {{ old('venue_id') == $venue->id ? 'selected' : '' }}>
                                        {{ $venue->localized_name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('venue_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Field Selection -->
                        <div>
                            <label for="field_id" class="form-label-bank required">Field</label>
                            <select id="field_id" name="field_id"
                                class="form-select-bank @error('field_id') border-red-500 @enderror"
                                @change="onFieldChange($event)" required>
                                <option value="">Select Field</option>
                                @foreach ($venues as $venue)
                                    @foreach ($venue->fields as $field)
                                        <option value="{{ $field->id }}" data-venue="{{ $venue->id }}"
                                            data-hourly-rate="{{ $field->hourly_rate }}"
                                            data-peak-rate="{{ $field->peak_hour_rate }}"
                                            data-weekend-rate="{{ $field->weekend_rate }}"
                                            data-min-hours="{{ $field->minimum_booking_hours }}"
                                            data-max-hours="{{ $field->maximum_booking_hours }}"
                                            data-requires-deposit="{{ $field->requires_deposit ? 'true' : 'false' }}"
                                            data-deposit-percentage="{{ $field->deposit_percentage }}"
                                            {{ old('field_id') == $field->id ? 'selected' : '' }} style="display: none;">
                                            {{ $field->localized_name }} - {{ $field->formatted_hourly_rate }}
                                        </option>
                                    @endforeach
                                @endforeach
                            </select>
                            @error('field_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Customer Selection -->
                        <div>
                            <label for="customer_id" class="form-label-bank required">Customer</label>
                            <select id="customer_id" name="customer_id"
                                class="form-select-bank @error('customer_id') border-red-500 @enderror" required>
                                <option value="">Select Customer</option>
                                @foreach ($customers as $customer)
                                    <option value="{{ $customer->id }}"
                                        {{ old('customer_id') == $customer->id ? 'selected' : '' }}>
                                        {{ $customer->full_name }} - {{ $customer->formatted_phone }}
                                    </option>
                                @endforeach
                            </select>
                            @error('customer_id')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Reservation Date -->
                        <div>
                            <label for="reservation_date" class="form-label-bank required">Reservation Date</label>
                            <input type="date" id="reservation_date" name="reservation_date"
                                value="{{ old('reservation_date', date('Y-m-d')) }}"
                                class="form-input-bank @error('reservation_date') border-red-500 @enderror"
                                min="{{ date('Y-m-d') }}" @change="calculatePricing()" required>
                            @error('reservation_date')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Start Time -->
                        <div>
                            <label for="start_time" class="form-label-bank required">Start Time</label>
                            <input type="time" id="start_time" name="start_time" value="{{ old('start_time') }}"
                                class="form-input-bank @error('start_time') border-red-500 @enderror"
                                @change="calculateDuration()" required>
                            @error('start_time')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- End Time -->
                        <div>
                            <label for="end_time" class="form-label-bank required">End Time</label>
                            <input type="time" id="end_time" name="end_time" value="{{ old('end_time') }}"
                                class="form-input-bank @error('end_time') border-red-500 @enderror"
                                @change="calculateDuration()" required>
                            @error('end_time')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Details -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Booking Details</h3>
                    <p class="bank-card-subtitle">Configure booking type and event information</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Duration Hours -->
                        <div>
                            <label for="duration_hours" class="form-label-bank required">Duration (Hours)</label>
                            <input type="number" id="duration_hours" name="duration_hours"
                                value="{{ old('duration_hours', 1) }}" min="1" max="12" step="1"
                                class="form-input-bank @error('duration_hours') border-red-500 @enderror"
                                x-model="durationHours" @input="calculatePricing()" required readonly>
                            @error('duration_hours')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Hourly Rate -->
                        <div>
                            <label for="hourly_rate" class="form-label-bank required">Hourly Rate</label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-gray"
                                    x-text="currency"></span>
                                <input type="number" id="hourly_rate" name="hourly_rate"
                                    value="{{ old('hourly_rate') }}" step="0.01" min="0"
                                    class="form-input-bank pl-12 @error('hourly_rate') border-red-500 @enderror"
                                    x-model="hourlyRate" @input="calculatePricing()" required readonly>
                            </div>
                            @error('hourly_rate')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Booking Type -->
                        <div>
                            <label for="booking_type" class="form-label-bank required">Booking Type</label>
                            <select id="booking_type" name="booking_type"
                                class="form-select-bank @error('booking_type') border-red-500 @enderror" required>
                                <option value="regular"
                                    {{ old('booking_type', 'regular') === 'regular' ? 'selected' : '' }}>
                                    Regular</option>
                                <option value="recurring" {{ old('booking_type') === 'recurring' ? 'selected' : '' }}>
                                    Recurring</option>
                                <option value="event" {{ old('booking_type') === 'event' ? 'selected' : '' }}>Event
                                </option>
                            </select>
                            @error('booking_type')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Event Type -->
                        <div>
                            <label for="event_type" class="form-label-bank">Event Type</label>
                            <select id="event_type" name="event_type"
                                class="form-select-bank @error('event_type') border-red-500 @enderror">
                                <option value="">Select Event Type</option>
                                <option value="training" {{ old('event_type') === 'training' ? 'selected' : '' }}>Training
                                </option>
                                <option value="match" {{ old('event_type') === 'match' ? 'selected' : '' }}>Match
                                </option>
                                <option value="tournament" {{ old('event_type') === 'tournament' ? 'selected' : '' }}>
                                    Tournament</option>
                                <option value="casual" {{ old('event_type', 'casual') === 'casual' ? 'selected' : '' }}>
                                    Casual</option>
                                <option value="corporate" {{ old('event_type') === 'corporate' ? 'selected' : '' }}>
                                    Corporate
                                </option>
                            </select>
                            @error('event_type')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Event Name -->
                        <div>
                            <label for="event_name" class="form-label-bank">Event Name</label>
                            <input type="text" id="event_name" name="event_name" value="{{ old('event_name') }}"
                                class="form-input-bank @error('event_name') border-red-500 @enderror"
                                placeholder="e.g., Weekly Training Session">
                            @error('event_name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Expected Participants -->
                        <div>
                            <label for="expected_participants" class="form-label-bank">Expected Participants</label>
                            <input type="number" id="expected_participants" name="expected_participants"
                                value="{{ old('expected_participants') }}" min="1" max="50"
                                class="form-input-bank @error('expected_participants') border-red-500 @enderror"
                                placeholder="Number of players">
                            @error('expected_participants')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pricing & Payment Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Pricing & Payment</h3>
                    <p class="bank-card-subtitle">Configure pricing, discounts and payment details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Discount Percentage -->
                        <div>
                            <label for="discount_percentage" class="form-label-bank">Discount (%)</label>
                            <input type="number" id="discount_percentage" name="discount_percentage"
                                value="{{ old('discount_percentage', 0) }}" min="0" max="100"
                                step="0.01"
                                class="form-input-bank @error('discount_percentage') border-red-500 @enderror"
                                x-model="discountPercentage" @input="calculatePricing()" placeholder="0.00">
                            @error('discount_percentage')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- VAT Rate -->
                        <div>
                            <label for="vat_rate" class="form-label-bank required">VAT Rate (%)</label>
                            <input type="number" id="vat_rate" name="vat_rate" value="{{ old('vat_rate', 5) }}"
                                min="0" max="100" step="0.01"
                                class="form-input-bank @error('vat_rate') border-red-500 @enderror" x-model="vatRate"
                                @input="calculatePricing()" required>
                            @error('vat_rate')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Currency -->
                        <div>
                            <label for="currency" class="form-label-bank required">Currency</label>
                            <select id="currency" name="currency"
                                class="form-select-bank @error('currency') border-red-500 @enderror" x-model="currency"
                                required>
                                <option value="AED" {{ old('currency', 'AED') === 'AED' ? 'selected' : '' }}>AED
                                </option>
                                <option value="USD" {{ old('currency') === 'USD' ? 'selected' : '' }}>USD</option>
                                <option value="EUR" {{ old('currency') === 'EUR' ? 'selected' : '' }}>EUR</option>
                            </select>
                            @error('currency')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label for="payment_method" class="form-label-bank required">Payment Method</label>
                            <select id="payment_method" name="payment_method"
                                class="form-select-bank @error('payment_method') border-red-500 @enderror" required>
                                <option value="">Select Payment Method</option>
                                <option value="cash" {{ old('payment_method') === 'cash' ? 'selected' : '' }}>Cash
                                </option>
                                <option value="card" {{ old('payment_method') === 'card' ? 'selected' : '' }}>
                                    Credit/Debit Card</option>
                                <option value="bank_transfer"
                                    {{ old('payment_method') === 'bank_transfer' ? 'selected' : '' }}>Bank Transfer
                                </option>
                                <option value="online" {{ old('payment_method') === 'online' ? 'selected' : '' }}>Online
                                    Payment</option>
                            </select>
                            @error('payment_method')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Deposit Amount -->
                        <div>
                            <label for="deposit_amount" class="form-label-bank">Deposit Amount</label>
                            <div class="relative">
                                <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-gray"
                                    x-text="currency"></span>
                                <input type="number" id="deposit_amount" name="deposit_amount"
                                    value="{{ old('deposit_amount') }}" step="0.01" min="0"
                                    class="form-input-bank pl-12 @error('deposit_amount') border-red-500 @enderror"
                                    x-model="depositAmount" placeholder="0.00">
                            </div>
                            @error('deposit_amount')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Pricing Summary -->
                    <div class="mt-6 p-4 bg-gray-50 rounded-lg" x-show="subtotal > 0">
                        <h4 class="text-sm font-medium text-charcoal-black mb-3">Pricing Summary</h4>
                        <div class="grid grid-cols-1 md:grid-cols-5 gap-4 text-sm">
                            <div>
                                <span class="text-dark-gray">Subtotal:</span>
                                <span class="font-medium text-charcoal-black"
                                    x-text="currency + ' ' + subtotal.toFixed(2)"></span>
                            </div>
                            <div>
                                <span class="text-dark-gray">Discount:</span>
                                <span class="font-medium text-charcoal-black"
                                    x-text="currency + ' ' + discountAmount.toFixed(2)"></span>
                            </div>
                            <div>
                                <span class="text-dark-gray">VAT (<span x-text="vatRate"></span>%):</span>
                                <span class="font-medium text-charcoal-black"
                                    x-text="currency + ' ' + vatAmount.toFixed(2)"></span>
                            </div>
                            <div>
                                <span class="text-dark-gray font-medium">Total:</span>
                                <span class="font-bold text-leaders-red"
                                    x-text="currency + ' ' + totalAmount.toFixed(2)"></span>
                            </div>
                            <div x-show="depositAmount > 0">
                                <span class="text-dark-gray">Remaining:</span>
                                <span class="font-medium text-charcoal-black"
                                    x-text="currency + ' ' + remainingAmount.toFixed(2)"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Additional Information</h3>
                    <p class="bank-card-subtitle">Special requirements and notes</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Special Requirements -->
                        <div>
                            <label for="special_requirements" class="form-label-bank">Special Requirements</label>
                            <textarea id="special_requirements" name="special_requirements" rows="4"
                                class="form-textarea-bank @error('special_requirements') border-red-500 @enderror"
                                placeholder="Any special setup, equipment, or requirements...">{{ old('special_requirements') }}</textarea>
                            @error('special_requirements')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Special Requirements (Arabic) -->
                        <div>
                            <label for="special_requirements_ar" class="form-label-bank">Special Requirements
                                (Arabic)</label>
                            <textarea id="special_requirements_ar" name="special_requirements_ar" rows="4"
                                class="form-textarea-bank @error('special_requirements_ar') border-red-500 @enderror"
                                placeholder="أي متطلبات خاصة أو معدات..." dir="rtl">{{ old('special_requirements_ar') }}</textarea>
                            @error('special_requirements_ar')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Equipment Requested -->
                        <div class="md:col-span-2">
                            <label class="form-label-bank">Equipment Requested</label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mt-2">
                                <label class="flex items-center">
                                    <input type="checkbox" name="equipment_requested[]" value="footballs"
                                        class="form-checkbox">
                                    <span class="ml-2 text-sm text-charcoal-black">Footballs</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="equipment_requested[]" value="cones"
                                        class="form-checkbox">
                                    <span class="ml-2 text-sm text-charcoal-black">Cones</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="equipment_requested[]" value="goals"
                                        class="form-checkbox">
                                    <span class="ml-2 text-sm text-charcoal-black">Goals</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="equipment_requested[]" value="bibs"
                                        class="form-checkbox">
                                    <span class="ml-2 text-sm text-charcoal-black">Training Bibs</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="equipment_requested[]" value="first_aid"
                                        class="form-checkbox">
                                    <span class="ml-2 text-sm text-charcoal-black">First Aid Kit</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="equipment_requested[]" value="water"
                                        class="form-checkbox">
                                    <span class="ml-2 text-sm text-charcoal-black">Water Bottles</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="equipment_requested[]" value="sound_system"
                                        class="form-checkbox">
                                    <span class="ml-2 text-sm text-charcoal-black">Sound System</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" name="equipment_requested[]" value="lighting"
                                        class="form-checkbox">
                                    <span class="ml-2 text-sm text-charcoal-black">Extra Lighting</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="flex items-center justify-end space-x-4">
                <a href="{{ route('reservations.index') }}"
                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                    Cancel
                </a>
                <button type="submit" class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Reservation
                </button>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function reservationForm() {
            return {
                // Form data
                durationHours: 1,
                hourlyRate: 0,
                discountPercentage: 0,
                vatRate: 5,
                currency: 'AED',
                depositAmount: 0,

                // Calculated values
                subtotal: 0,
                discountAmount: 0,
                vatAmount: 0,
                totalAmount: 0,
                remainingAmount: 0,

                // Field data
                selectedField: null,
                minHours: 1,
                maxHours: 12,

                init() {
                    // Initialize form if venue/field are pre-selected
                    this.$nextTick(() => {
                        const venueSelect = document.getElementById('venue_id');
                        if (venueSelect.value) {
                            this.onVenueChange({
                                target: venueSelect
                            });
                        }
                    });
                },

                onVenueChange(event) {
                    const venueId = event.target.value;
                    const fieldSelect = document.getElementById('field_id');
                    const fieldOptions = fieldSelect.querySelectorAll('option[data-venue]');

                    // Reset field selection
                    fieldSelect.value = '';
                    this.resetFieldData();

                    // Show/hide field options based on selected venue
                    fieldOptions.forEach(option => {
                        if (!venueId || option.dataset.venue === venueId) {
                            option.style.display = 'block';
                        } else {
                            option.style.display = 'none';
                        }
                    });

                    // Update VAT rate and currency from venue
                    if (venueId) {
                        const selectedOption = event.target.selectedOptions[0];
                        this.vatRate = parseFloat(selectedOption.dataset.vatRate) || 5;
                        this.currency = selectedOption.dataset.currency || 'AED';
                        document.getElementById('vat_rate').value = this.vatRate;
                        document.getElementById('currency').value = this.currency;
                    }
                },

                onFieldChange(event) {
                    const fieldId = event.target.value;

                    if (fieldId) {
                        const selectedOption = event.target.selectedOptions[0];
                        this.selectedField = {
                            id: fieldId,
                            hourlyRate: parseFloat(selectedOption.dataset.hourlyRate) || 0,
                            peakRate: parseFloat(selectedOption.dataset.peakRate) || 0,
                            weekendRate: parseFloat(selectedOption.dataset.weekendRate) || 0,
                            minHours: parseInt(selectedOption.dataset.minHours) || 1,
                            maxHours: parseInt(selectedOption.dataset.maxHours) || 12,
                            requiresDeposit: selectedOption.dataset.requiresDeposit === 'true',
                            depositPercentage: parseFloat(selectedOption.dataset.depositPercentage) || 0
                        };

                        this.hourlyRate = this.selectedField.hourlyRate;
                        this.minHours = this.selectedField.minHours;
                        this.maxHours = this.selectedField.maxHours;

                        // Update form fields
                        document.getElementById('hourly_rate').value = this.hourlyRate;
                        document.getElementById('duration_hours').min = this.minHours;
                        document.getElementById('duration_hours').max = this.maxHours;

                        // Calculate pricing
                        this.calculatePricing();
                    } else {
                        this.resetFieldData();
                    }
                },

                resetFieldData() {
                    this.selectedField = null;
                    this.hourlyRate = 0;
                    this.minHours = 1;
                    this.maxHours = 12;
                    this.subtotal = 0;
                    this.totalAmount = 0;
                    document.getElementById('hourly_rate').value = '';
                },

                calculateDuration() {
                    const startTime = document.getElementById('start_time').value;
                    const endTime = document.getElementById('end_time').value;

                    if (startTime && endTime) {
                        const start = new Date(`2000-01-01 ${startTime}`);
                        const end = new Date(`2000-01-01 ${endTime}`);

                        if (end > start) {
                            const diffMs = end - start;
                            const diffHours = diffMs / (1000 * 60 * 60);
                            this.durationHours = Math.round(diffHours * 100) / 100; // Round to 2 decimal places
                            document.getElementById('duration_hours').value = this.durationHours;
                            this.calculatePricing();
                        }
                    }
                },

                calculatePricing() {
                    if (!this.hourlyRate || !this.durationHours) {
                        this.resetPricing();
                        return;
                    }

                    // Determine rate based on date (weekend/peak hours logic can be added here)
                    let rate = this.hourlyRate;
                    const reservationDate = document.getElementById('reservation_date').value;

                    if (reservationDate && this.selectedField) {
                        const date = new Date(reservationDate);
                        const isWeekend = date.getDay() === 0 || date.getDay() === 6; // Sunday or Saturday

                        if (isWeekend && this.selectedField.weekendRate > 0) {
                            rate = this.selectedField.weekendRate;
                        }
                    }

                    // Calculate subtotal
                    this.subtotal = rate * this.durationHours;

                    // Calculate discount
                    this.discountAmount = (this.subtotal * this.discountPercentage) / 100;

                    // Calculate VAT
                    const discountedAmount = this.subtotal - this.discountAmount;
                    this.vatAmount = (discountedAmount * this.vatRate) / 100;

                    // Calculate total
                    this.totalAmount = discountedAmount + this.vatAmount;

                    // Calculate remaining amount after deposit
                    this.remainingAmount = this.totalAmount - (this.depositAmount || 0);

                    // Auto-calculate deposit if field requires it
                    if (this.selectedField && this.selectedField.requiresDeposit && this.selectedField.depositPercentage >
                        0) {
                        const autoDeposit = (this.totalAmount * this.selectedField.depositPercentage) / 100;
                        if (this.depositAmount === 0) {
                            this.depositAmount = autoDeposit;
                            document.getElementById('deposit_amount').value = this.depositAmount.toFixed(2);
                        }
                    }
                },

                resetPricing() {
                    this.subtotal = 0;
                    this.discountAmount = 0;
                    this.vatAmount = 0;
                    this.totalAmount = 0;
                    this.remainingAmount = 0;
                }
            }
        }

        // Initialize form validation and interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Time validation
            const startTimeInput = document.getElementById('start_time');
            const endTimeInput = document.getElementById('end_time');

            function validateTimes() {
                if (startTimeInput.value && endTimeInput.value) {
                    if (endTimeInput.value <= startTimeInput.value) {
                        endTimeInput.setCustomValidity('End time must be after start time');
                    } else {
                        endTimeInput.setCustomValidity('');
                    }
                }
            }

            startTimeInput.addEventListener('change', validateTimes);
            endTimeInput.addEventListener('change', validateTimes);
        });
    </script>
@endpush
