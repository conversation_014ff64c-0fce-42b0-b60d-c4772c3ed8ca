<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class Academy extends Model
{
    protected $fillable = [
        'branch_id',
        'name',
        'description',
        'coach_name',
        'coach_phone',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    protected $appends = [
        'student_count',
        'program_count',
        'active_student_count',
        'total_revenue',
        'pending_payments',
        'status_text',
        'formatted_coach_phone',
    ];

    /**
     * Get the branch that owns the academy.
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the programs for the academy.
     */
    public function programs(): HasMany
    {
        return $this->hasMany(Program::class);
    }

    /**
     * Get the active programs for the academy.
     */
    public function activePrograms(): HasMany
    {
        return $this->hasMany(Program::class)->where('status', true);
    }

    /**
     * Get the students for the academy.
     */
    public function students(): HasMany
    {
        return $this->hasMany(Student::class);
    }

    /**
     * Get the active students for the academy.
     */
    public function activeStudents(): HasMany
    {
        return $this->hasMany(Student::class)->where('status', 'active');
    }

    /**
     * Get the payments for the academy.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the completed payments for the academy.
     */
    public function completedPayments(): HasMany
    {
        return $this->hasMany(Payment::class)->where('status', 'completed');
    }

    /**
     * Get the uniforms for the academy.
     */
    public function uniforms(): HasMany
    {
        return $this->hasMany(Uniform::class);
    }

    /**
     * Get the users for the academy.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the academy managers for the academy.
     */
    public function academyManagers(): HasMany
    {
        return $this->hasMany(User::class)->where('role', 'academy_manager');
    }

    /**
     * Get the student count attribute.
     */
    protected function studentCount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->students()->count(),
        );
    }

    /**
     * Get the program count attribute.
     */
    protected function programCount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->programs()->count(),
        );
    }

    /**
     * Get the active student count attribute.
     */
    protected function activeStudentCount(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->activeStudents()->count(),
        );
    }

    /**
     * Get the total revenue attribute.
     */
    protected function totalRevenue(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->completedPayments()->sum('amount'),
        );
    }

    /**
     * Get the pending payments attribute.
     */
    protected function pendingPayments(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->payments()->where('status', 'pending')->sum('amount'),
        );
    }

    /**
     * Get the status text attribute.
     */
    protected function statusText(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->status ? 'Active' : 'Inactive',
        );
    }

    /**
     * Get the formatted coach phone attribute.
     */
    protected function formattedCoachPhone(): Attribute
    {
        return Attribute::make(
            get: function () {
                if (!$this->coach_phone) return null;

                // Format UAE phone number: +971XXXXXXXXX to +971 XX XXX XXXX
                $phone = preg_replace('/[^0-9]/', '', $this->coach_phone);
                if (strlen($phone) === 12 && substr($phone, 0, 3) === '971') {
                    return '+971 ' . substr($phone, 3, 2) . ' ' . substr($phone, 5, 3) . ' ' . substr($phone, 8, 4);
                }
                return $this->coach_phone;
            }
        );
    }

    /**
     * Scope a query to only include active academies.
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('status', true);
    }

    /**
     * Scope a query to only include inactive academies.
     */
    public function scopeInactive(Builder $query): void
    {
        $query->where('status', false);
    }

    /**
     * Scope a query to search academies.
     */
    public function scopeSearch(Builder $query, string $search): void
    {
        $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
                ->orWhere('description', 'like', "%{$search}%")
                ->orWhere('coach_name', 'like', "%{$search}%")
                ->orWhere('coach_phone', 'like', "%{$search}%")
                ->orWhereHas('branch', function ($branchQuery) use ($search) {
                    $branchQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('location', 'like', "%{$search}%");
                });
        });
    }

    /**
     * Scope a query to filter by branch.
     */
    public function scopeByBranch(Builder $query, int $branchId): void
    {
        $query->where('branch_id', $branchId);
    }

    /**
     * Get academy statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_programs' => $this->programs()->count(),
            'active_programs' => $this->activePrograms()->count(),
            'total_students' => $this->students()->count(),
            'active_students' => $this->activeStudents()->count(),
            'total_revenue' => $this->completedPayments()->sum('amount'),
            'pending_payments' => $this->payments()->where('status', 'pending')->sum('amount'),
            'total_uniforms' => $this->uniforms()->count(),
            'pending_uniforms' => $this->uniforms()->where('branch_status', 'pending')->count(),
            'average_age' => $this->students()->whereNotNull('birth_date')->avg(DB::raw('YEAR(CURDATE()) - YEAR(birth_date)')),
        ];
    }

    /**
     * Get students with their details for the academy.
     */
    public function getStudentsWithDetails()
    {
        return $this->students()
            ->with(['payments', 'uniforms'])
            ->select([
                'students.*',
                DB::raw('YEAR(CURDATE()) - YEAR(birth_date) as age'),
                DB::raw('DATEDIFF(CURDATE(), join_date) as days_enrolled')
            ])
            ->orderBy('join_date', 'desc')
            ->get();
    }

    /**
     * Get available programs for the academy.
     */
    public function getAvailablePrograms()
    {
        return $this->activePrograms()
            ->select([
                'programs.*',
                DB::raw('CASE
                    WHEN JSON_CONTAINS(days, \'"sunday"\') THEN "Sun " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"monday"\') THEN "Mon " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"tuesday"\') THEN "Tue " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"wednesday"\') THEN "Wed " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"thursday"\') THEN "Thu " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"friday"\') THEN "Fri " ELSE "" END ||
                    CASE
                    WHEN JSON_CONTAINS(days, \'"saturday"\') THEN "Sat " ELSE "" END
                    as formatted_days')
            ])
            ->get();
    }

    /**
     * Get revenue by month for the academy.
     */
    public function getMonthlyRevenue(int $months = 12): array
    {
        return $this->completedPayments()
            ->select([
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(amount) as total_amount'),
                DB::raw('COUNT(*) as payment_count')
            ])
            ->where('created_at', '>=', now()->subMonths($months))
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->get()
            ->toArray();
    }
}
