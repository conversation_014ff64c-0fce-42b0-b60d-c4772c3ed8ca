@extends('layouts.dashboard')

@section('title', $branch->name . ' - Branch Details')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div
                class="w-16 h-16 bg-gradient-to-br from-info-blue to-blue-600 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                    </path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">{{ $branch->name }}</h1>
                <p class="text-lg text-dark-gray">{{ $branch->location }}</p>
                <div class="flex items-center space-x-3 mt-2">
                    <span class="badge-bank {{ $branch->status ? 'badge-success' : 'badge-neutral' }}">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="{{ $branch->status ? 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                            </path>
                        </svg>
                        {{ $branch->status ? 'Active' : 'Inactive' }}
                    </span>
                    <span class="badge-bank badge-info">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                            </path>
                        </svg>
                        Created {{ $branch->created_at->format('M j, Y') }}
                    </span>
                </div>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            @can('update', $branch)
                <a href="{{ route('branches.edit', $branch) }}" class="btn-bank">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    Edit Branch
                </a>
            @endcan
            <a href="{{ route('branches.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Branches
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6" x-data="branchDetails()" x-init="init()">
        <!-- Branch Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
            <!-- Total Academies -->
            <a href="{{ route('academies.index', ['branch_id' => $branch->id]) }}"
                class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                style="animation-delay: 0.1s;">
                <div class="stats-icon">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z">
                        </path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_academies'] ?? 0 }}</div>
                <div class="stats-label">Total Academies</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                        </path>
                    </svg>
                    {{ $stats['active_academies'] ?? 0 }} Active
                </div>
            </a>

            <!-- Total Students -->
            <a href="{{ route('students.index', ['branch_id' => $branch->id]) }}"
                class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                style="animation-delay: 0.2s;">
                <div class="stats-icon bg-gradient-to-br from-success-green to-green-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 715 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_students'] ?? 0 }}</div>
                <div class="stats-label">Total Students</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z">
                        </path>
                    </svg>
                    {{ $stats['active_students'] ?? 0 }} Active
                </div>
            </a>

            <!-- Total Revenue -->
            <a href="{{ route('payments.index', ['branch_id' => $branch->id]) }}"
                class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                style="animation-delay: 0.3s;">
                <div class="stats-icon bg-gradient-to-br from-gold-yellow to-yellow-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                        </path>
                    </svg>
                </div>
                <div class="stats-value text-leaders-red">AED {{ number_format($stats['total_revenue'] ?? 0, 0) }}</div>
                <div class="stats-label">Total Revenue (AED)</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    {{ number_format($stats['pending_payments'] ?? 0, 0) }} Pending
                </div>
            </a>

            <!-- Total Programs -->
            <a href="{{ route('programs.index', ['branch_id' => $branch->id]) }}"
                class="stats-card scale-in hover:scale-105 transition-transform duration-200"
                style="animation-delay: 0.4s;">
                <div class="stats-icon bg-gradient-to-br from-info-blue to-blue-600">
                    <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01">
                        </path>
                    </svg>
                </div>
                <div class="stats-value">{{ $stats['total_programs'] ?? 0 }}</div>
                <div class="stats-label">Total Programs</div>
                <div class="stats-change positive">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    {{ $stats['active_programs'] ?? 0 }} Active Programs
                </div>
            </a>
        </div>

        <!-- Branch Information & Contact Details -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Branch Information -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Branch Information</h3>
                        <p class="bank-card-subtitle">Contact details and location</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                    </path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black">Location</h4>
                                <p class="text-dark-gray">{{ $branch->location }}</p>
                                @if ($branch->address)
                                    <p class="text-sm text-medium-gray mt-1">{{ $branch->address }}</p>
                                @endif
                            </div>
                        </div>

                        @if ($branch->phone)
                            <div class="flex items-start space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-success-green to-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">Phone Number</h4>
                                    <p class="text-dark-gray">+971 {{ $branch->formatted_phone }}</p>
                                    <a href="tel:+971{{ $branch->phone }}"
                                        class="text-sm text-leaders-red hover:text-leaders-deep-red">
                                        Call Now
                                    </a>
                                </div>
                            </div>
                        @endif

                        @if ($branch->email)
                            <div class="flex items-start space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-info-blue to-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">Email Address</h4>
                                    <p class="text-dark-gray">{{ $branch->email }}</p>
                                    <a href="mailto:{{ $branch->email }}"
                                        class="text-sm text-leaders-red hover:text-leaders-deep-red">
                                        Send Email
                                    </a>
                                </div>
                            </div>
                        @endif

                        <div class="flex items-start space-x-3">
                            <div
                                class="w-10 h-10 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                    </path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black">Established</h4>
                                <p class="text-dark-gray">{{ $branch->created_at->format('F j, Y') }}</p>
                                <p class="text-sm text-medium-gray">{{ $branch->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Quick Actions</h3>
                        <p class="bank-card-subtitle">Manage branch operations</p>
                    </div>
                    <div
                        class="w-12 h-12 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 gap-3">
                        @can('update', $branch)
                            <a href="{{ route('branches.edit', $branch) }}" class="btn-bank w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                                    </path>
                                </svg>
                                Edit Branch Details
                            </a>
                        @endcan

                        <button @click="toggleBranchStatus()" class="btn-bank btn-bank-secondary w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="{{ $branch->status ? 'M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z' : 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z' }}">
                                </path>
                            </svg>
                            {{ $branch->status ? 'Deactivate' : 'Activate' }} Branch
                        </button>

                        <a href="{{ route('academies.create', ['branch_id' => $branch->id]) }}" class="btn-bank w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add New Academy
                        </a>

                        @can('create', App\Models\Program::class)
                            <a href="{{ route('programs.create', ['branch_id' => $branch->id]) }}" class="btn-bank w-full">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Add Program
                            </a>
                        @endcan

                        <a href="{{ route('students.create', ['branch_id' => $branch->id]) }}" class="btn-bank w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            Add Student
                        </a>

                        <a href="#" class="btn-bank w-full">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2-2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                            View Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Academies and Students -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Academies List -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Academies & Programs</h3>
                        <p class="bank-card-subtitle">{{ $branch->academies->count() }} academies with their programs</p>
                    </div>
                    <a href="{{ route('academies.create', ['branch_id' => $branch->id]) }}" class="btn-bank btn-bank-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Academy
                    </a>
                </div>
                @php $globalProgramIndex = 0; @endphp
                <div class="bank-card-body">
                    @forelse($branch->academies as $academy)
                        <div class="mb-6 {{ !$loop->last ? 'border-b border-light-gray pb-6' : '' }}">
                            <!-- Academy Header -->
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-3">
                                    <div
                                        class="w-12 h-12 bg-gradient-to-br from-success-green to-green-600 rounded-lg flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <a href="{{ route('academies.show', $academy) }}"
                                            class="text-lg font-semibold text-leaders-red hover:text-leaders-deep-red transition-colors duration-200">
                                            {{ $academy->name }}
                                        </a>
                                        <p class="text-sm text-dark-gray">{{ $academy->programs->count() }} programs •
                                            {{ $academy->students->count() ?? 0 }} students</p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <span class="badge-bank {{ $academy->status ? 'badge-success' : 'badge-neutral' }}">
                                        {{ $academy->status ? 'Active' : 'Inactive' }}
                                    </span>
                                    <a href="{{ route('academies.show', $academy) }}" class="btn-bank btn-sm">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                            </path>
                                        </svg>
                                    </a>
                                </div>
                            </div>

                            <!-- Programs under this Academy -->
                            @if ($academy->programs->count() > 0)
                                <div class="ml-15 space-y-3">
                                    <h5 class="text-sm font-medium text-dark-gray uppercase tracking-wide">Programs</h5>
                                    <div class="grid grid-cols-1 gap-3">
                                        @foreach ($academy->programs as $program)
                                            <div
                                                class="{{ getProgramBackgroundColor($globalProgramIndex++) }} rounded-lg p-4 border border-light-gray hover:border-leaders-red transition-colors duration-200">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center space-x-3">
                                                        <div
                                                            class="w-8 h-8 bg-gradient-to-br from-info-blue to-blue-600 rounded-lg flex items-center justify-center">
                                                            <svg class="w-4 h-4 text-white" fill="none"
                                                                stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="2"
                                                                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                                                </path>
                                                            </svg>
                                                        </div>
                                                        <div>
                                                            <a href="{{ route('programs.show', $program) }}"
                                                                class="font-medium text-charcoal-black hover:text-leaders-red transition-colors duration-200">
                                                                {{ $program->name }}
                                                            </a>
                                                            <div
                                                                class="flex items-center space-x-4 text-xs text-dark-gray mt-1">
                                                                <span>{{ $program->students->count() ?? 0 }}
                                                                    students</span>
                                                                <span>AED {{ number_format($program->price, 0) }}</span>
                                                                <span>{{ $program->classes }} classes</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <span
                                                            class="badge-bank {{ $program->status ? 'badge-success' : 'badge-neutral' }} text-xs">
                                                            {{ $program->status ? 'Active' : 'Inactive' }}
                                                        </span>
                                                        <a href="{{ route('programs.show', $program) }}"
                                                            class="btn-bank btn-sm">
                                                            <svg class="w-3 h-3" fill="none" stroke="currentColor"
                                                                viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                                                                </path>
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="2"
                                                                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                                                                </path>
                                                            </svg>
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>

                                    <!-- Add Program Button for this Academy -->
                                    <div class="pt-2">
                                        <a href="{{ route('programs.create', ['academy_id' => $academy->id, 'branch_id' => $branch->id]) }}"
                                            class="inline-flex items-center text-sm text-leaders-red hover:text-leaders-deep-red font-medium">
                                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            Add Program to {{ $academy->name }}
                                        </a>
                                    </div>
                                </div>
                            @else
                                <div class="ml-15 bg-white rounded-lg p-4 border border-light-gray">
                                    <div class="text-center py-4">
                                        <svg class="w-8 h-8 mx-auto text-medium-gray mb-2" fill="none"
                                            stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253">
                                            </path>
                                        </svg>
                                        <p class="text-sm text-dark-gray mb-3">No programs in this academy yet.</p>
                                        <a href="{{ route('programs.create', ['academy_id' => $academy->id, 'branch_id' => $branch->id]) }}"
                                            class="btn-bank btn-sm">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                            </svg>
                                            Add First Program
                                        </a>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-medium-gray mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 14l9-5-9-5-9 5 9 5z"></path>
                            </svg>
                            <p class="text-dark-gray mb-4">No academies found in this branch.</p>
                            <a href="{{ route('academies.create', ['branch_id' => $branch->id]) }}"
                                class="btn-bank btn-bank-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create First Academy
                            </a>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Recent Students -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Recent Students</h3>
                        <p class="bank-card-subtitle">Latest student registrations</p>
                    </div>
                    <a href="#" class="btn-bank btn-bank-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add Student
                    </a>
                </div>
                <div class="bank-card-body">
                    @forelse($branch->students->sortByDesc('created_at')->take(5) as $student)
                        <div
                            class="flex items-center justify-between py-3 {{ !$loop->last ? 'border-b border-light-gray' : '' }}">
                            <div class="flex items-center space-x-3">
                                <div
                                    class="w-10 h-10 bg-gradient-to-br from-gold-yellow to-yellow-600 rounded-lg flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-charcoal-black">{{ $student->full_name ?? 'N/A' }}</h4>
                                    <p class="text-sm text-dark-gray">{{ $student->academy->name ?? 'N/A' }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span
                                    class="badge-bank {{ ($student->status ?? 'inactive') === 'active' ? 'badge-success' : 'badge-neutral' }}">
                                    {{ ucfirst($student->status ?? 'Inactive') }}
                                </span>
                                <p class="text-sm text-dark-gray mt-1">
                                    {{ $student->created_at ? $student->created_at->format('M d') : 'N/A' }}</p>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="w-12 h-12 mx-auto text-medium-gray mb-4" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <p class="text-dark-gray mb-4">No students found in this branch.</p>
                            <a href="#" class="btn-bank btn-bank-sm">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Register First Student
                            </a>
                        </div>
                    @endforelse

                    @if ($branch->students->count() > 5)
                        <div class="pt-4 border-t border-light-gray">
                            <a href="#" class="text-leaders-red hover:text-leaders-deep-red text-sm font-medium">
                                View All {{ $branch->students->count() }} Students →
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        function branchDetails() {
            return {
                init() {
                    // Initialize any branch details functionality
                },

                async toggleBranchStatus() {
                    const currentStatus = {{ $branch->status ? 'true' : 'false' }};
                    const newStatus = currentStatus ? 'deactivate' : 'activate';
                    const branchName = '{{ $branch->name }}';

                    let confirmMessage = `Are you sure you want to ${newStatus} "${branchName}"?`;

                    @if ($branch->academies->count() > 0 || $branch->students->count() > 0)
                        if (currentStatus) {
                            confirmMessage +=
                                '\n\nThis branch has {{ $branch->academies->count() }} academies and {{ $branch->students->count() }} students. Deactivating will affect operations.';
                        }
                    @endif

                    if (!confirm(confirmMessage)) {
                        return;
                    }

                    try {
                        const response = await fetch('{{ route('branches.toggle-status', $branch) }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            showNotification('success', result.message);
                            window.location.reload();
                        } else {
                            showNotification('error', result.message);
                        }
                    } catch (error) {
                        showNotification('error', 'An error occurred while updating the branch status.');
                    }
                }
            }
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            alert(message);
        }
    </script>
@endpush
