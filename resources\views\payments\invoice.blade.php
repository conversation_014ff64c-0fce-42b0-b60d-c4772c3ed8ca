<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Invoice - {{ $payment->reference_number ?? 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}
    </title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
            padding: 20px;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        .invoice-header {
            background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .logo-section {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }

        .logo {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
        }

        .company-name {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .company-subtitle {
            font-size: 16px;
            opacity: 0.9;
        }

        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            margin-top: 20px;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .invoice-body {
            padding: 40px;
        }

        .invoice-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }

        .info-section h3 {
            color: #d32f2f;
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid #d32f2f;
            padding-bottom: 5px;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            color: #555;
            display: inline-block;
            width: 120px;
        }

        .info-value {
            color: #333;
        }

        .payment-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 30px;
            margin: 30px 0;
            border-left: 5px solid #d32f2f;
        }

        .payment-details h3 {
            color: #d32f2f;
            font-size: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        .amount-breakdown {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 20px;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .amount-breakdown:last-child {
            border-bottom: none;
            font-weight: bold;
            font-size: 18px;
            color: #d32f2f;
            border-top: 2px solid #d32f2f;
            padding-top: 15px;
        }

        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .status-completed {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .status-pending {
            background: #fff3e0;
            color: #f57c00;
        }

        .status-failed {
            background: #ffebee;
            color: #d32f2f;
        }

        .status-cancelled {
            background: #f5f5f5;
            color: #757575;
        }

        .schedule-info {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .schedule-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e0e0e0;
        }

        .footer-note {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .contact-info {
            color: #888;
            font-size: 12px;
        }

        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #d32f2f;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
        }

        .print-button:hover {
            background: #b71c1c;
        }

        @media print {
            body {
                padding: 0;
                background: white;
            }

            .invoice-container {
                border: none;
                border-radius: 0;
                box-shadow: none;
                max-width: none;
            }

            .print-button {
                display: none;
            }

            .invoice-header {
                background: #d32f2f !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .payment-details {
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .schedule-info {
                background: #e3f2fd !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .footer {
                background: #f8f9fa !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }

        @media (max-width: 768px) {
            .invoice-info {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .amount-breakdown {
                grid-template-columns: 1fr;
                gap: 5px;
            }

            .invoice-body {
                padding: 20px;
            }

            .invoice-header {
                padding: 20px;
            }

            .company-name {
                font-size: 24px;
            }

            .invoice-title {
                font-size: 20px;
            }
        }
    </style>
</head>

<body>
    <button class="print-button" onclick="window.print()">
        🖨️ Print Invoice
    </button>

    <div class="invoice-container">
        <!-- Header -->
        <div class="invoice-header">
            <div class="logo-section">
                <div class="logo">
                    <img src="{{ asset('images/logo.jpg') }}" alt="LEADERS SPORTS SERVICES LLC SP" width="40"
                        height="40" style="object-fit: contain;">
                </div>
                <div>
                    <div class="company-name">LEADERS SPORTS SERVICES LLC SP</div>
                </div>
            </div>
            <div class="invoice-title">Payment Invoice</div>
        </div>

        <!-- Body -->
        <div class="invoice-body">
            <!-- Invoice Information -->
            <div class="invoice-info">
                <div class="info-section">
                    <h3>Invoice Details</h3>
                    <div class="info-item">
                        <span class="info-label">Invoice #:</span>
                        <span
                            class="info-value">{{ $payment->reference_number ?? 'PAY-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Issue Date:</span>
                        <span class="info-value">{{ $payment->created_at->format('M d, Y') }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Payment Date:</span>
                        <span class="info-value">{{ $payment->formatted_payment_date ?: 'Pending' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Status:</span>
                        <span class="status-badge status-{{ $payment->status }}">{{ $payment->status_text }}</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>Student Information</h3>
                    <div class="info-item">
                        <span class="info-label">Name:</span>
                        <span class="info-value">{{ $payment->student->full_name ?? 'N/A' }}</span>
                    </div>
                    @if ($payment->student && $payment->student->phone)
                        <div class="info-item">
                            <span class="info-label">Phone:</span>
                            <span class="info-value">{{ $payment->student->formatted_phone }}</span>
                        </div>
                    @endif
                    @if ($payment->student && $payment->student->email)
                        <div class="info-item">
                            <span class="info-label">Email:</span>
                            <span class="info-value">{{ $payment->student->email }}</span>
                        </div>
                    @endif
                    <div class="info-item">
                        <span class="info-label">Branch:</span>
                        <span class="info-value">{{ $payment->branch->name ?? 'N/A' }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Academy:</span>
                        <span class="info-value">{{ $payment->academy->name ?? 'N/A' }}</span>
                    </div>
                </div>

                <div class="info-section">
                    <h3>Company Information</h3>
                    <div class="info-item">
                        <span class="info-label">Address:</span>
                        <span
                            class="info-value">{{ \App\Models\Setting::get('company_address', 'LEADERS SPORTS SERVICES LLC SP SHARJAH AL MAMZAR') }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Phone:</span>
                        <span
                            class="info-value">{{ \App\Models\Setting::get('company_phone', '+971501051321') }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email:</span>
                        <span
                            class="info-value">{{ \App\Models\Setting::get('company_email', '<EMAIL>') }}</span>
                    </div>
                    @if (\App\Models\Setting::get('vat_enabled', true))
                        <div class="info-item">
                            <span class="info-label">VAT Number:</span>
                            <span
                                class="info-value">{{ \App\Models\Setting::get('vat_number', '100123456789012') }}</span>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Payment Details -->
            <div class="payment-details">
                <h3>Payment Breakdown</h3>

                @php
                    $vatDetails = $payment->getVatDetails();
                @endphp

                @if ($vatDetails['enabled'])
                    <div class="amount-breakdown">
                        <span>Subtotal:</span>
                        <span>{{ $vatDetails['formatted_subtotal'] }}</span>
                    </div>

                    <div class="amount-breakdown">
                        <span>VAT
                            ({{ $vatDetails['rate'] == floor($vatDetails['rate']) ? number_format($vatDetails['rate'], 0) : number_format($vatDetails['rate'], 1) }}%):</span>
                        <span>{{ $vatDetails['formatted_vat_amount'] }}</span>
                    </div>
                @else
                    <div class="amount-breakdown">
                        <span>Base Amount:</span>
                        <span>AED {{ number_format($payment->amount, 2) }}</span>
                    </div>
                @endif

                @if ($payment->discount > 0)
                    <div class="amount-breakdown">
                        <span>Discount Applied:</span>
                        <span>- AED {{ number_format($payment->discount, 2) }}</span>
                    </div>
                @endif

                <div class="amount-breakdown">
                    <span>Total Amount:</span>
                    <span>{{ $vatDetails['enabled'] ? $vatDetails['formatted_total_amount'] : 'AED ' . number_format($payment->net_amount, 2) }}</span>
                </div>
            </div>

            <!-- Payment Method & Period -->
            <div class="invoice-info">
                <div class="info-section">
                    <h3>Payment Information</h3>
                    <div class="info-item">
                        <span class="info-label">Method:</span>
                        <span class="info-value">{{ $payment->method_text }}</span>
                    </div>
                    @if ($payment->reset_num)
                        <div class="info-item">
                            <span class="info-label">Reset Number:</span>
                            <span class="info-value">{{ $payment->reset_num }}</span>
                        </div>
                    @endif
                    <div class="info-item">
                        <span class="info-label">Payment Type:</span>
                        <span class="info-value">{{ $payment->payment_type_text }}</span>
                    </div>
                    @if ($payment->renewal)
                        <div class="info-item">
                            <span class="info-label">Legacy Type:</span>
                            <span class="info-value">Renewal Payment</span>
                        </div>
                    @endif
                </div>

                <div class="info-section">
                    <h3>Service Period</h3>
                    <div class="info-item">
                        <span class="info-label">Start Date:</span>
                        <span class="info-value">{{ $payment->formatted_start_date }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">End Date:</span>
                        <span class="info-value">{{ $payment->formatted_end_date }}</span>
                    </div>
                    @if ($payment->class_time_from && $payment->class_time_to)
                        <div class="info-item">
                            <span class="info-label">Class Time:</span>
                            <span class="info-value">{{ $payment->class_time_from }} -
                                {{ $payment->class_time_to }}</span>
                        </div>
                    @endif
                </div>
            </div>

            @if ($payment->class_time_from && $payment->class_time_to)
                <!-- Schedule Information -->
                <div class="schedule-info">
                    <h4>📅 Class Schedule</h4>
                    <p><strong>Time:</strong> {{ $payment->class_time_from }} - {{ $payment->class_time_to }}</p>
                    <p><strong>Duration:</strong> {{ $payment->formatted_start_date }} to
                        {{ $payment->formatted_end_date }}</p>
                </div>
            @endif

            @if ($payment->description || $payment->note)
                <!-- Additional Information -->
                <div class="info-section" style="margin-top: 30px;">
                    <h3>Additional Information</h3>
                    @if ($payment->description)
                        <div class="info-item">
                            <span class="info-label">Description:</span>
                            <span class="info-value">{{ $payment->description }}</span>
                        </div>
                    @endif
                    @if ($payment->note)
                        <div class="info-item">
                            <span class="info-label">Note:</span>
                            <span class="info-value">{{ $payment->note }}</span>
                        </div>
                    @endif
                </div>
            @endif
        </div>

        <!-- Footer -->
        <div class="footer">
            <div class="footer-note">
                <strong>Thank you for choosing LEADERS SPORTS SERVICES!</strong><br>
                This invoice was generated on {{ now()->format('M d, Y \a\t H:i') }}
            </div>
            <div class="contact-info">
                Address: LEADERS SPORTS SERVICES LLC SP SHARJAH AL MAMZAR<br>
                Phone: +971501051321 | Email: <EMAIL><br>
                @if (\App\Models\Setting::get('vat_enabled', true))
                    VAT Number: {{ \App\Models\Setting::get('vat_number', '100123456789012') }}<br>
                @endif
                For any questions regarding this payment, please contact our administration office.
            </div>
        </div>
    </div>

    <script>
        // Auto-print functionality
        window.addEventListener('load', function() {
            // Add a small delay to ensure all content is loaded
            setTimeout(function() {
                // Focus the window to ensure print dialog appears
                window.focus();
            }, 500);
        });

        // Print function
        function printInvoice() {
            window.print();
        }

        // Keyboard shortcut for printing (Ctrl+P)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printInvoice();
            }
        });

        // Add print styles dynamically for better browser compatibility
        function addPrintStyles() {
            const style = document.createElement('style');
            style.textContent = `
                @media print {
                    @page {
                        margin: 0.5in;
                        size: A4;
                    }

                    body {
                        font-size: 12pt;
                        line-height: 1.4;
                    }

                    .invoice-container {
                        page-break-inside: avoid;
                    }

                    .invoice-info {
                        page-break-inside: avoid;
                    }

                    .payment-details {
                        page-break-inside: avoid;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // Initialize print styles
        addPrintStyles();
    </script>
</body>

</html>
