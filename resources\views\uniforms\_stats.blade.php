<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- Total Orders -->
    <div class="stats-card bg-gradient-to-br from-leaders-red to-leaders-deep-red text-white">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
            </svg>
        </div>
        <div class="stats-content">
            <div class="stats-value">{{ number_format($stats['total_uniforms']) }}</div>
            <div class="stats-label">Total Orders</div>
            <div class="stats-change">
                <span class="text-white/80">All time</span>
            </div>
        </div>
    </div>

    <!-- Ordered -->
    <div class="stats-card bg-gradient-to-br from-warning-orange to-yellow-600 text-white">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <div class="stats-content">
            <div class="stats-value">{{ number_format($stats['ordered_uniforms']) }}</div>
            <div class="stats-label">Ordered</div>
            <div class="stats-change">
                <span class="text-white/80">Pending processing</span>
            </div>
        </div>
    </div>

    <!-- Processing -->
    <div class="stats-card bg-gradient-to-br from-info-blue to-blue-600 text-white">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
        </div>
        <div class="stats-content">
            <div class="stats-value">{{ number_format($stats['processing_uniforms']) }}</div>
            <div class="stats-label">Processing</div>
            <div class="stats-change">
                <span class="text-white/80">In production</span>
            </div>
        </div>
    </div>

    <!-- Ready for Pickup -->
    <div class="stats-card bg-gradient-to-br from-purple-500 to-purple-700 text-white">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 8h14M5 8a2 2 0 110-4h1.586a1 1 0 01.707.293l1.414 1.414a1 1 0 00.707.293H15a2 2 0 012 2v2M5 8v10a2 2 0 002 2h10a2 2 0 002-2V10m-9 4h4"></path>
            </svg>
        </div>
        <div class="stats-content">
            <div class="stats-value">{{ number_format($stats['ready_uniforms']) }}</div>
            <div class="stats-label">Ready</div>
            <div class="stats-change">
                <span class="text-white/80">For pickup</span>
            </div>
        </div>
    </div>
</div>

<!-- Financial Statistics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- Total Revenue -->
    <div class="stats-card bg-gradient-to-br from-success-green to-green-600 text-white">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
        </div>
        <div class="stats-content">
            <div class="stats-value">{{ number_format($stats['total_amount'], 2) }}</div>
            <div class="stats-label">Total Revenue (AED)</div>
            <div class="stats-change">
                <span class="text-white/80">All orders</span>
            </div>
        </div>
    </div>

    <!-- Pending Revenue -->
    <div class="stats-card bg-gradient-to-br from-yellow-500 to-orange-500 text-white">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
        </div>
        <div class="stats-content">
            <div class="stats-value">{{ number_format($stats['pending_amount'], 2) }}</div>
            <div class="stats-label">Pending Revenue (AED)</div>
            <div class="stats-change">
                <span class="text-white/80">Not delivered</span>
            </div>
        </div>
    </div>

    <!-- Average Order -->
    <div class="stats-card bg-gradient-to-br from-indigo-500 to-purple-600 text-white">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
        </div>
        <div class="stats-content">
            <div class="stats-value">{{ number_format($stats['average_order'], 2) }}</div>
            <div class="stats-label">Average Order (AED)</div>
            <div class="stats-change">
                <span class="text-white/80">Per order</span>
            </div>
        </div>
    </div>

    <!-- This Month -->
    <div class="stats-card bg-gradient-to-br from-pink-500 to-rose-600 text-white">
        <div class="stats-icon">
            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
        </div>
        <div class="stats-content">
            <div class="stats-value">{{ number_format($stats['this_month_orders'], 2) }}</div>
            <div class="stats-label">This Month (AED)</div>
            <div class="stats-change">
                <span class="text-white/80">{{ now()->format('M Y') }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Delivery Status Overview -->
<div class="bank-card mb-6">
    <div class="bank-card-header">
        <h3 class="text-lg font-semibold text-charcoal-black">Delivery Status Overview</h3>
    </div>
    <div class="bank-card-body">
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-warning-orange">{{ number_format($stats['ordered_uniforms']) }}</div>
                <div class="text-sm text-dark-gray">Ordered</div>
                <div class="w-full bg-light-gray rounded-full h-2 mt-2">
                    <div class="bg-warning-orange h-2 rounded-full" style="width: {{ $stats['total_uniforms'] > 0 ? ($stats['ordered_uniforms'] / $stats['total_uniforms']) * 100 : 0 }}%"></div>
                </div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-info-blue">{{ number_format($stats['processing_uniforms']) }}</div>
                <div class="text-sm text-dark-gray">Processing</div>
                <div class="w-full bg-light-gray rounded-full h-2 mt-2">
                    <div class="bg-info-blue h-2 rounded-full" style="width: {{ $stats['total_uniforms'] > 0 ? ($stats['processing_uniforms'] / $stats['total_uniforms']) * 100 : 0 }}%"></div>
                </div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">{{ number_format($stats['ready_uniforms']) }}</div>
                <div class="text-sm text-dark-gray">Ready</div>
                <div class="w-full bg-light-gray rounded-full h-2 mt-2">
                    <div class="bg-purple-600 h-2 rounded-full" style="width: {{ $stats['total_uniforms'] > 0 ? ($stats['ready_uniforms'] / $stats['total_uniforms']) * 100 : 0 }}%"></div>
                </div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-success-green">{{ number_format($stats['delivered_uniforms']) }}</div>
                <div class="text-sm text-dark-gray">Delivered</div>
                <div class="w-full bg-light-gray rounded-full h-2 mt-2">
                    <div class="bg-success-green h-2 rounded-full" style="width: {{ $stats['total_uniforms'] > 0 ? ($stats['delivered_uniforms'] / $stats['total_uniforms']) * 100 : 0 }}%"></div>
                </div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-charcoal-black">{{ number_format($stats['total_uniforms']) }}</div>
                <div class="text-sm text-dark-gray">Total</div>
                <div class="w-full bg-charcoal-black rounded-full h-2 mt-2"></div>
            </div>
        </div>
    </div>
</div>
