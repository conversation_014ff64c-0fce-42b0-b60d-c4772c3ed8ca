@extends('layouts.dashboard')

@section('title', 'Settings Management - UAE English Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="flex items-center space-x-2">
                <svg class="w-8 h-8 text-leaders-red" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z">
                    </path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z">
                    </path>
                </svg>
                <h1 class="text-2xl font-bold text-gray-900">Settings Management</h1>
            </div>
            <div class="hidden md:flex items-center space-x-2 text-sm text-gray-500">
                <span>•</span>
                <span>System Configuration</span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <button onclick="exportSettings()" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                    </path>
                </svg>
                Export
            </button>
            <button onclick="openImportModal()" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                </svg>
                Import
            </button>
        </div>
    </div>
@endsection

@section('content')
    <div class="space-y-6">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="stats-card">
                <div class="stats-icon bg-blue-100 text-blue-600">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                        </path>
                    </svg>
                </div>
                <div class="stats-content">
                    <div class="stats-value">{{ $stats['total_settings'] }}</div>
                    <div class="stats-label">Total Settings</div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-icon bg-green-100 text-green-600">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10">
                        </path>
                    </svg>
                </div>
                <div class="stats-content">
                    <div class="stats-value">{{ $stats['categories_count'] }}</div>
                    <div class="stats-label">Categories</div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-icon bg-yellow-100 text-yellow-600">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                        </path>
                    </svg>
                </div>
                <div class="stats-content">
                    <div class="stats-value">{{ $stats['public_settings'] }}</div>
                    <div class="stats-label">Public Settings</div>
                </div>
            </div>

            <div class="stats-card">
                <div class="stats-icon bg-red-100 text-red-600">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                        </path>
                    </svg>
                </div>
                <div class="stats-content">
                    <div class="stats-value">{{ $stats['encrypted_settings'] }}</div>
                    <div class="stats-label">Encrypted Settings</div>
                </div>
            </div>
        </div>

        <!-- Settings Categories -->
        <div class="bank-card">
            <div class="card-header">
                <h2 class="card-title">Settings Categories</h2>
                <p class="card-subtitle">Configure your academy system settings by category</p>
            </div>

            <div class="card-body">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    @foreach ($categories as $key => $name)
                        <div class="category-card">
                            <div class="category-icon">
                                @switch($key)
                                    @case('general')
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4">
                                            </path>
                                        </svg>
                                    @break

                                    @case('academy')
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z">
                                            </path>
                                        </svg>
                                    @break

                                    @case('payment')
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z">
                                            </path>
                                        </svg>
                                    @break

                                    @case('notification')
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M15 17h5l-5 5v-5zM4.828 4.828A4 4 0 015.5 4H9v1H5.5a3 3 0 00-2.121.879l-.707.707A1 1 0 002 7.414V16a2 2 0 002 2h8a2 2 0 002-2v-5a1 1 0 00-.293-.707l-2-2A1 1 0 0011 8H9a1 1 0 01-1-1V4.828z">
                                            </path>
                                        </svg>
                                    @break

                                    @case('security')
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
                                            </path>
                                        </svg>
                                    @break

                                    @case('system')
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                            </path>
                                        </svg>
                                    @break

                                    @case('translation')
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129">
                                            </path>
                                        </svg>
                                    @break
                                @endswitch
                            </div>
                            <div class="category-content">
                                <h3 class="category-title">{{ $name }}</h3>
                                <p class="category-count">
                                    {{ isset($settingsGrouped[$key]) ? count($settingsGrouped[$key]) : 0 }} settings
                                </p>
                                <a href="{{ route('settings.edit', $key) }}" class="category-link">
                                    Configure Settings
                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- Import Modal -->
    <div id="importModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Import Settings</h3>
                <button onclick="closeImportModal()" class="modal-close">&times;</button>
            </div>
            <form id="importForm" action="{{ route('settings.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label for="import_file" class="form-label">Settings File (JSON)</label>
                        <input type="file" id="import_file" name="import_file" accept=".json" class="input-bank"
                            required>
                        <p class="form-help">Select a JSON file exported from the settings system.</p>
                    </div>
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="overwrite" value="1" class="checkbox-input">
                            <span class="checkbox-text">Overwrite existing settings</span>
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" onclick="closeImportModal()" class="btn-secondary">Cancel</button>
                    <button type="submit" class="btn-primary">Import Settings</button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        /* Settings Management Enhanced Styles */

        /* Missing Button Classes */
        .btn-primary {
            background: linear-gradient(135deg, var(--leaders-red) 0%, var(--leaders-deep-red) 100%);
            color: var(--pure-white);
            border: 1px solid var(--leaders-deep-red);
            border-radius: var(--radius-button);
            padding: 0.75rem 1.5rem;
            font-family: var(--font-family-primary);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: var(--shadow-card);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--leaders-deep-red) 0%, #991B1B 100%);
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
            color: var(--pure-white);
            text-decoration: none;
        }

        .btn-secondary {
            background: var(--pure-white);
            color: var(--dark-gray);
            border: 1px solid var(--medium-gray);
            border-radius: var(--radius-button);
            padding: 0.75rem 1.5rem;
            font-family: var(--font-family-primary);
            font-weight: 600;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            box-shadow: var(--shadow-card);
        }

        .btn-secondary:hover {
            background: var(--light-gray);
            border-color: var(--leaders-red);
            color: var(--leaders-red);
            transform: translateY(-1px);
            box-shadow: var(--shadow-hover);
            text-decoration: none;
        }

        /* Missing Form Classes */
        .form-group {
            margin-bottom: var(--space-lg);
        }

        .form-label {
            display: block;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--charcoal-black);
            margin-bottom: var(--space-sm);
        }

        .input-bank {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--medium-gray);
            border-radius: var(--radius-button);
            font-size: 0.875rem;
            color: var(--charcoal-black);
            background-color: var(--pure-white);
            transition: all var(--transition-fast);
            outline: none;
        }

        .input-bank:focus {
            border-color: var(--leaders-red);
            box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--charcoal-black);
            cursor: pointer;
        }

        .checkbox-input {
            width: 1rem;
            height: 1rem;
            accent-color: var(--leaders-red);
            border: 1px solid var(--medium-gray);
            border-radius: 0.25rem;
        }

        .checkbox-text {
            font-weight: 500;
        }

        .form-help {
            font-size: 0.75rem;
            color: var(--dark-gray);
            margin-top: 0.25rem;
        }

        /* Settings-specific styles */
        .category-card {
            background: var(--pure-white);
            border: 1px solid var(--medium-gray);
            border-radius: var(--radius-card);
            padding: var(--space-xl);
            transition: all var(--transition-normal);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--leaders-red), var(--deep-red));
            transform: scaleX(0);
            transition: transform var(--transition-normal);
        }

        .category-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-bank);
            border-color: var(--leaders-red);
        }

        .category-card:hover::before {
            transform: scaleX(1);
        }

        .category-icon {
            width: 3.5rem;
            height: 3.5rem;
            background: linear-gradient(135deg, var(--leaders-red), var(--deep-red));
            border-radius: var(--radius-card);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--space-lg);
            box-shadow: var(--shadow-card);
        }

        .category-icon svg {
            width: 1.75rem;
            height: 1.75rem;
            color: var(--pure-white);
        }

        .category-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--charcoal-black);
            margin-bottom: var(--space-sm);
            font-family: var(--font-family-primary);
        }

        .category-count {
            color: var(--dark-gray);
            font-size: 0.875rem;
            margin-bottom: var(--space-lg);
            font-weight: 500;
        }

        .category-link {
            display: inline-flex;
            align-items: center;
            color: var(--leaders-red);
            font-weight: 600;
            text-decoration: none;
            transition: all var(--transition-fast);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .category-link:hover {
            color: var(--deep-red);
            transform: translateX(4px);
            text-decoration: none;
        }

        .category-link svg {
            transition: transform var(--transition-fast);
        }

        .category-link:hover svg {
            transform: translateX(2px);
        }

        /* Modal styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            background: var(--pure-white);
            border-radius: var(--radius-card);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: var(--shadow-bank);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }

            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-xl);
            border-bottom: 1px solid var(--medium-gray);
            background: linear-gradient(135deg, var(--off-white) 0%, var(--light-gray) 100%);
        }

        .modal-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--charcoal-black);
            font-family: var(--font-family-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--dark-gray);
            cursor: pointer;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all var(--transition-fast);
        }

        .modal-close:hover {
            background: var(--error-red);
            color: var(--pure-white);
            transform: rotate(90deg);
        }

        .modal-body {
            padding: var(--space-xl);
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: var(--space-md);
            padding: var(--space-xl);
            border-top: 1px solid var(--medium-gray);
            background: var(--off-white);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .category-card {
                padding: var(--space-lg);
            }

            .category-icon {
                width: 3rem;
                height: 3rem;
            }

            .category-icon svg {
                width: 1.5rem;
                height: 1.5rem;
            }

            .category-title {
                font-size: 1.125rem;
            }

            .modal-content {
                width: 95%;
                margin: 1rem;
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: var(--space-lg);
            }

            .btn-primary,
            .btn-secondary {
                padding: 0.625rem 1.25rem;
                font-size: 0.8125rem;
            }
        }

        /* Animation for category cards */
        .category-card {
            animation: fadeInUp 0.6s ease-out;
        }

        .category-card:nth-child(1) {
            animation-delay: 0.1s;
        }

        .category-card:nth-child(2) {
            animation-delay: 0.2s;
        }

        .category-card:nth-child(3) {
            animation-delay: 0.3s;
        }

        .category-card:nth-child(4) {
            animation-delay: 0.4s;
        }

        .category-card:nth-child(5) {
            animation-delay: 0.5s;
        }

        .category-card:nth-child(6) {
            animation-delay: 0.6s;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        function openImportModal() {
            document.getElementById('importModal').style.display = 'flex';
        }

        function closeImportModal() {
            document.getElementById('importModal').style.display = 'none';
        }

        function exportSettings(category = null) {
            let url = '{{ route('settings.export') }}';
            if (category) {
                url += '?category=' + category;
            }

            fetch(url)
                .then(response => response.blob())
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.style.display = 'none';
                    a.href = url;
                    a.download = 'settings-export-' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') +
                        '.json';
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                })
                .catch(error => {
                    console.error('Export failed:', error);
                    alert('Export failed. Please try again.');
                });
        }

        // Close modal when clicking outside
        document.getElementById('importModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImportModal();
            }
        });
    </script>
@endpush
