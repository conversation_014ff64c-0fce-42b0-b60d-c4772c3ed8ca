@extends('layouts.dashboard')

@section('title', 'Edit Academy - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Edit Academy</h1>
                <p class="text-lg text-dark-gray">Update academy information</p>
                <span class="badge-bank badge-info">ID: #{{ $academy->id }}</span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('academies.show', $academy) }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                View Academy
            </a>
            <a href="{{ route('academies.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Academies
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto" x-data="academyEditForm()" x-init="init()">
        <form @submit.prevent="submitForm()" class="space-y-6">
            @csrf
            @method('PUT')

            <!-- Basic Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Basic Information</h3>
                    <p class="bank-card-subtitle">Update the academy's basic details</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Academy Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label-bank required">Academy Name</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   x-model="form.name"
                                   class="form-input-bank" 
                                   placeholder="Enter academy name"
                                   required>
                            <div x-show="errors.name" class="form-error" x-text="errors.name"></div>
                        </div>

                        <!-- Branch Selection -->
                        <div>
                            <label for="branch_id" class="form-label-bank required">Branch</label>
                            <select id="branch_id" 
                                    name="branch_id" 
                                    x-model="form.branch_id"
                                    class="form-select-bank" 
                                    required>
                                <option value="">Select a branch</option>
                                @foreach($branches as $branch)
                                    <option value="{{ $branch->id }}">{{ $branch->name }} - {{ $branch->location }}</option>
                                @endforeach
                            </select>
                            <div x-show="errors.branch_id" class="form-error" x-text="errors.branch_id"></div>
                        </div>

                        <!-- Status -->
                        <div>
                            <label for="status" class="form-label-bank">Status</label>
                            <select id="status" 
                                    name="status" 
                                    x-model="form.status"
                                    class="form-select-bank">
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                            <div x-show="errors.status" class="form-error" x-text="errors.status"></div>
                        </div>

                        <!-- Description -->
                        <div class="md:col-span-2">
                            <label for="description" class="form-label-bank">Description</label>
                            <textarea id="description" 
                                      name="description" 
                                      x-model="form.description"
                                      rows="4" 
                                      class="form-textarea-bank" 
                                      placeholder="Enter academy description (optional)"></textarea>
                            <div x-show="errors.description" class="form-error" x-text="errors.description"></div>
                            <p class="form-help">Provide a brief description of the academy and its programs</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coach Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <h3 class="bank-card-title">Coach Information</h3>
                    <p class="bank-card-subtitle">Update coach details for this academy</p>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Coach Name -->
                        <div>
                            <label for="coach_name" class="form-label-bank">Coach Name</label>
                            <input type="text" 
                                   id="coach_name" 
                                   name="coach_name" 
                                   x-model="form.coach_name"
                                   class="form-input-bank" 
                                   placeholder="Enter coach name">
                            <div x-show="errors.coach_name" class="form-error" x-text="errors.coach_name"></div>
                        </div>

                        <!-- Coach Phone -->
                        <div>
                            <label for="coach_phone" class="form-label-bank">Coach Phone</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-medium-gray text-sm">+971</span>
                                </div>
                                <input type="tel" 
                                       id="coach_phone" 
                                       name="coach_phone" 
                                       x-model="form.coach_phone"
                                       @input="formatPhoneNumber()"
                                       class="form-input-bank pl-16" 
                                       placeholder="XXXXXXXXX"
                                       pattern="^\+971[0-9]{9}$">
                            </div>
                            <div x-show="errors.coach_phone" class="form-error" x-text="errors.coach_phone"></div>
                            <p class="form-help">UAE phone format: +971XXXXXXXXX</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Change Tracking -->
            <div x-show="hasChanges()" x-transition class="bank-card bg-amber-50 border-amber-200">
                <div class="bank-card-body">
                    <div class="flex items-center space-x-3">
                        <svg class="w-5 h-5 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <div>
                            <p class="text-sm font-medium text-amber-800">You have unsaved changes</p>
                            <p class="text-xs text-amber-600">Make sure to save your changes before leaving this page</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button type="submit" 
                                    :disabled="loading || !hasChanges()"
                                    class="btn-bank"
                                    :class="{ 'opacity-50 cursor-not-allowed': loading || !hasChanges() }">
                                <svg x-show="!loading" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <svg x-show="loading" class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span x-text="loading ? 'Updating...' : 'Update Academy'">Update Academy</span>
                            </button>
                            <button type="button" 
                                    @click="resetForm()"
                                    :disabled="!hasChanges()"
                                    class="btn-bank btn-bank-outline"
                                    :class="{ 'opacity-50 cursor-not-allowed': !hasChanges() }">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                Reset Changes
                            </button>
                        </div>
                        <a href="{{ route('academies.index') }}" class="btn-bank btn-bank-secondary">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Cancel
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function academyEditForm() {
            return {
                loading: false,
                originalForm: {},
                form: {
                    name: '{{ $academy->name }}',
                    branch_id: '{{ $academy->branch_id }}',
                    description: '{{ $academy->description }}',
                    coach_name: '{{ $academy->coach_name }}',
                    coach_phone: '{{ $academy->coach_phone }}',
                    status: '{{ $academy->status ? '1' : '0' }}'
                },
                errors: {},

                init() {
                    this.originalForm = { ...this.form };
                },

                hasChanges() {
                    return JSON.stringify(this.form) !== JSON.stringify(this.originalForm);
                },

                formatPhoneNumber() {
                    let phone = this.form.coach_phone.replace(/[^0-9]/g, '');
                    if (phone.length > 0 && !phone.startsWith('971')) {
                        phone = '971' + phone;
                    }
                    if (phone.length > 12) {
                        phone = phone.substring(0, 12);
                    }
                    this.form.coach_phone = phone ? '+' + phone : '';
                },

                async submitForm() {
                    this.loading = true;
                    this.errors = {};

                    try {
                        const formData = new FormData();
                        Object.keys(this.form).forEach(key => {
                            if (this.form[key] !== null && this.form[key] !== '') {
                                formData.append(key, this.form[key]);
                            }
                        });

                        const response = await fetch('{{ route('academies.update', $academy) }}', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                                'Accept': 'application/json'
                            },
                            body: formData
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            showNotification('success', result.message || 'Academy updated successfully!');
                            this.originalForm = { ...this.form };
                            window.location.href = '{{ route('academies.index') }}';
                        } else {
                            if (result.errors) {
                                this.errors = result.errors;
                            } else {
                                showNotification('error', result.message || 'An error occurred while updating the academy.');
                            }
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showNotification('error', 'An unexpected error occurred. Please try again.');
                    } finally {
                        this.loading = false;
                    }
                },

                resetForm() {
                    this.form = { ...this.originalForm };
                    this.errors = {};
                }
            }
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            if (type === 'success') {
                alert('Success: ' + message);
            } else {
                alert('Error: ' + message);
            }
        }
    </script>
@endpush
