<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('uniform_suppliers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_ar')->nullable();
            $table->string('code')->unique(); // SUP001, SUP002, etc.
            $table->string('contact_person')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable(); // UAE format
            $table->string('phone_secondary')->nullable();
            $table->text('address')->nullable();
            $table->text('address_ar')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->default('UAE');
            $table->string('tax_number')->nullable(); // VAT number
            $table->string('trade_license')->nullable();
            $table->enum('payment_terms', ['cash', 'net_15', 'net_30', 'net_45', 'net_60'])->default('net_30');
            $table->decimal('credit_limit', 12, 2)->default(0); // AED
            $table->string('currency', 3)->default('AED');
            $table->integer('lead_time_days')->default(7); // Default lead time
            $table->decimal('minimum_order_amount', 10, 2)->default(0); // AED
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->decimal('rating', 3, 2)->default(0); // 0-5 rating
            $table->text('notes')->nullable();
            $table->text('notes_ar')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'rating']);
            $table->index('code');
            $table->index('email');
            $table->index('phone');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('uniform_suppliers');
    }
};
