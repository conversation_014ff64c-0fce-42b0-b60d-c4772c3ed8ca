<?php

namespace App\Policies;

use App\Models\Payment;
use App\Models\User;

class PaymentPolicy
{
    /**
     * Determine whether the user can view any payments.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the payment.
     */
    public function view(User $user, Payment $payment): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can view payments in their branch
                return $user->branch_id === $payment->branch_id;
            case 'academy_manager':
                // Academy managers can view payments in their academy
                return $user->academy_id === $payment->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can create payments.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can update the payment.
     */
    public function update(User $user, Payment $payment): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can update payments in their branch
                return $user->branch_id === $payment->branch_id;
            case 'academy_manager':
                // Academy managers can update payments in their academy
                return $user->academy_id === $payment->academy_id;
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can delete the payment.
     */
    public function delete(User $user, Payment $payment): bool
    {
        switch ($user->role) {
            case 'admin':
                return true;
            case 'branch_manager':
                // Branch managers can delete payments in their branch
                return $user->branch_id === $payment->branch_id;
            case 'academy_manager':
                // Academy managers can delete payments in their academy (only pending/failed)
                return $user->academy_id === $payment->academy_id && 
                       in_array($payment->status, ['pending', 'failed']);
            default:
                return false;
        }
    }

    /**
     * Determine whether the user can restore the payment.
     */
    public function restore(User $user, Payment $payment): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can permanently delete the payment.
     */
    public function forceDelete(User $user, Payment $payment): bool
    {
        // Only admin can permanently delete
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can export payment data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }
}
