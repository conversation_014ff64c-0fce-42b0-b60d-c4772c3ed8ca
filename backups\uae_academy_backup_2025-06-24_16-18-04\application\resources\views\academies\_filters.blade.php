<!-- Advanced Search & Filters -->
<div class="bank-card mb-6" x-data="academyFilters()" x-init="init()">
    <div class="bank-card-header">
        <div class="flex items-center justify-between">
            <h3 class="bank-card-title">Search & Filters</h3>
            <button @click="toggleFilters()"
                class="btn-bank btn-bank-sm bg-leaders-red hover:bg-leaders-deep-red text-white">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z">
                    </path>
                </svg>
                <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'">Show Filters</span>
            </button>
        </div>
    </div>

    <div class="bank-card-body">
        <!-- Quick Search -->
        <div class="mb-4">
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-medium-gray" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <input type="text" name="search" x-model="search" @input.debounce.300ms="applyFilters()"
                    class="form-input-bank pl-10"
                    placeholder="Search academies by name, description, coach, or branch...">
                <div x-show="search" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button @click="search = ''; applyFilters()" class="text-medium-gray hover:text-dark-gray">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showFilters" x-transition class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Branch Filter -->
            <div>
                <label class="form-label-bank">Branch</label>
                <select name="branch_id" x-model="branchId" @change="applyFilters()" class="form-select-bank">
                    <option value="">All Branches</option>
                    @foreach ($branches as $branch)
                        <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                            {{ $branch->name }}
                        </option>
                    @endforeach
                </select>
            </div>

            <!-- Status Filter -->
            <div>
                <label class="form-label-bank">Status</label>
                <select name="status" x-model="status" @change="applyFilters()" class="form-select-bank">
                    <option value="">All Status</option>
                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>

            <!-- Date From -->
            <div>
                <label class="form-label-bank">Created From</label>
                <input type="date" name="date_from" x-model="dateFrom" @change="applyFilters()"
                    class="form-input-bank" value="{{ request('date_from') }}">
            </div>

            <!-- Date To -->
            <div>
                <label class="form-label-bank">Created To</label>
                <input type="date" name="date_to" x-model="dateTo" @change="applyFilters()" class="form-input-bank"
                    value="{{ request('date_to') }}">
            </div>

            <!-- Sort By -->
            <div>
                <label class="form-label-bank">Sort By</label>
                <select name="sort_by" x-model="sortBy" @change="applyFilters()" class="form-select-bank">
                    <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Created Date
                    </option>
                    <option value="name" {{ request('sort_by') == 'name' ? 'selected' : '' }}>Name</option>
                    <option value="updated_at" {{ request('sort_by') == 'updated_at' ? 'selected' : '' }}>Last Updated
                    </option>
                </select>
            </div>

            <!-- Sort Order -->
            <div>
                <label class="form-label-bank">Sort Order</label>
                <select name="sort_order" x-model="sortOrder" @change="applyFilters()" class="form-select-bank">
                    <option value="desc" {{ request('sort_order') == 'desc' ? 'selected' : '' }}>Descending</option>
                    <option value="asc" {{ request('sort_order') == 'asc' ? 'selected' : '' }}>Ascending</option>
                </select>
            </div>

            <!-- Per Page -->
            <div>
                <label class="form-label-bank">Per Page</label>
                <select name="per_page" x-model="perPage" @change="applyFilters()" class="form-select-bank">
                    <option value="15" {{ request('per_page') == '15' ? 'selected' : '' }}>15</option>
                    <option value="25" {{ request('per_page') == '25' ? 'selected' : '' }}>25</option>
                    <option value="50" {{ request('per_page') == '50' ? 'selected' : '' }}>50</option>
                    <option value="100" {{ request('per_page') == '100' ? 'selected' : '' }}>100</option>
                </select>
            </div>

            <!-- Clear Filters -->
            <div class="flex items-end">
                <button @click="clearFilters()"
                    class="btn-bank bg-leaders-red hover:bg-leaders-deep-red text-white w-full">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                        </path>
                    </svg>
                    Clear Filters
                </button>
            </div>
        </div>

        <!-- Active Filters Display -->
        <div x-show="hasActiveFilters()" x-transition class="mt-4 flex flex-wrap gap-2">
            <span class="text-sm font-medium text-dark-gray">Active Filters:</span>

            <span x-show="search" class="badge-bank badge-neutral">
                Search: <span x-text="search"></span>
                <button @click="search = ''; applyFilters()" class="ml-1 text-red-500 hover:text-red-700">×</button>
            </span>

            <span x-show="branchId" class="badge-bank badge-neutral">
                Branch: <span x-text="getBranchName(branchId)"></span>
                <button @click="branchId = ''; applyFilters()" class="ml-1 text-red-500 hover:text-red-700">×</button>
            </span>

            <span x-show="status" class="badge-bank badge-neutral">
                Status: <span x-text="status"></span>
                <button @click="status = ''; applyFilters()" class="ml-1 text-red-500 hover:text-red-700">×</button>
            </span>

            <span x-show="dateFrom" class="badge-bank badge-neutral">
                From: <span x-text="dateFrom"></span>
                <button @click="dateFrom = ''; applyFilters()" class="ml-1 text-red-500 hover:text-red-700">×</button>
            </span>

            <span x-show="dateTo" class="badge-bank badge-neutral">
                To: <span x-text="dateTo"></span>
                <button @click="dateTo = ''; applyFilters()" class="ml-1 text-red-500 hover:text-red-700">×</button>
            </span>
        </div>
    </div>
</div>

<script>
    function academyFilters() {
        return {
            showFilters: localStorage.getItem('academyShowFilters') === 'true',
            search: '{{ request('search') }}',
            branchId: '{{ request('branch_id') }}',
            status: '{{ request('status') }}',
            dateFrom: '{{ request('date_from') }}',
            dateTo: '{{ request('date_to') }}',
            sortBy: '{{ request('sort_by', 'created_at') }}',
            sortOrder: '{{ request('sort_order', 'desc') }}',
            perPage: '{{ request('per_page', '15') }}',

            init() {
                this.$watch('showFilters', (value) => {
                    localStorage.setItem('academyShowFilters', value);
                });
            },

            toggleFilters() {
                this.showFilters = !this.showFilters;
            },

            applyFilters() {
                const params = new URLSearchParams();

                if (this.search) params.set('search', this.search);
                if (this.branchId) params.set('branch_id', this.branchId);
                if (this.status) params.set('status', this.status);
                if (this.dateFrom) params.set('date_from', this.dateFrom);
                if (this.dateTo) params.set('date_to', this.dateTo);
                if (this.sortBy !== 'created_at') params.set('sort_by', this.sortBy);
                if (this.sortOrder !== 'desc') params.set('sort_order', this.sortOrder);
                if (this.perPage !== '15') params.set('per_page', this.perPage);

                const url = new URL(window.location);
                url.search = params.toString();
                window.location.href = url.toString();
            },

            clearFilters() {
                this.search = '';
                this.branchId = '';
                this.status = '';
                this.dateFrom = '';
                this.dateTo = '';
                this.sortBy = 'created_at';
                this.sortOrder = 'desc';
                this.perPage = '15';

                window.location.href = window.location.pathname;
            },

            hasActiveFilters() {
                return this.search || this.branchId || this.status || this.dateFrom || this.dateTo;
            },

            getBranchName(branchId) {
                const branches = @json($branches);
                const branch = branches.find(b => b.id == branchId);
                return branch ? branch.name : '';
            }
        }
    }
</script>
