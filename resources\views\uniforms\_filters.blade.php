<!-- Advanced Search & Filters -->
<div class="bank-card mb-6">
    <div class="bank-card-header">
        <h3 class="text-lg font-semibold text-charcoal-black">Search & Filters</h3>
        <button @click="showFilters = !showFilters" class="btn-bank-outline">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                </path>
            </svg>
            <span x-text="showFilters ? 'Hide Filters' : 'Show Filters'"></span>
        </button>
    </div>

    <div class="bank-card-body" x-data="{ showFilters: false }">
        <!-- Quick Search -->
        <div class="mb-4">
            <form method="GET" action="{{ route('uniforms.index') }}" class="flex items-center space-x-4">
                <div class="flex-1">
                    <input type="text" name="search" value="{{ request('search') }}"
                        placeholder="Search by student name, email, phone, size, item, or reference number..."
                        class="form-input-bank w-full">
                </div>
                <button type="submit" class="btn-bank">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Search
                </button>
                @if(request()->hasAny(['search', 'branch_id', 'academy_id', 'status', 'size', 'item', 'payment_method', 'date_from', 'date_to']))
                    <a href="{{ route('uniforms.index') }}" class="btn-bank-outline">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Clear
                    </a>
                @endif
            </form>
        </div>

        <!-- Advanced Filters -->
        <div x-show="showFilters" x-transition class="border-t border-light-gray pt-4">
            <form method="GET" action="{{ route('uniforms.index') }}" class="space-y-4">
                <!-- Preserve search term -->
                @if(request('search'))
                    <input type="hidden" name="search" value="{{ request('search') }}">
                @endif

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Branch Filter -->
                    <div>
                        <label class="form-label-bank">Branch</label>
                        <select name="branch_id" class="form-select-bank">
                            <option value="">All Branches</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}" {{ request('branch_id') == $branch->id ? 'selected' : '' }}>
                                    {{ $branch->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Academy Filter -->
                    <div>
                        <label class="form-label-bank">Academy</label>
                        <select name="academy_id" class="form-select-bank">
                            <option value="">All Academies</option>
                            @foreach($academies as $academy)
                                <option value="{{ $academy->id }}" {{ request('academy_id') == $academy->id ? 'selected' : '' }}>
                                    {{ $academy->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <!-- Status Filter -->
                    <div>
                        <label class="form-label-bank">Status</label>
                        <select name="status" class="form-select-bank">
                            <option value="">All Statuses</option>
                            <option value="ordered" {{ request('status') == 'ordered' ? 'selected' : '' }}>Ordered</option>
                            <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Processing</option>
                            <option value="ready" {{ request('status') == 'ready' ? 'selected' : '' }}>Ready for Pickup</option>
                            <option value="delivered" {{ request('status') == 'delivered' ? 'selected' : '' }}>Delivered</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending (Legacy)</option>
                        </select>
                    </div>

                    <!-- Size Filter -->
                    <div>
                        <label class="form-label-bank">Size</label>
                        <select name="size" class="form-select-bank">
                            <option value="">All Sizes</option>
                            <option value="6xs-24-4" {{ request('size') == '6xs-24-4' ? 'selected' : '' }}>6XS (24-4)</option>
                            <option value="5xs-26-5" {{ request('size') == '5xs-26-5' ? 'selected' : '' }}>5XS (26-5)</option>
                            <option value="4xs-28-6" {{ request('size') == '4xs-28-6' ? 'selected' : '' }}>4XS (28-6)</option>
                            <option value="3xs-30-7" {{ request('size') == '3xs-30-7' ? 'selected' : '' }}>3XS (30-7)</option>
                            <option value="2xs-32-8" {{ request('size') == '2xs-32-8' ? 'selected' : '' }}>2XS (32-8)</option>
                            <option value="xs-34-9" {{ request('size') == 'xs-34-9' ? 'selected' : '' }}>XS (34-9)</option>
                            <option value="s-36-10" {{ request('size') == 's-36-10' ? 'selected' : '' }}>S (36-10)</option>
                            <option value="m-38-11" {{ request('size') == 'm-38-11' ? 'selected' : '' }}>M (38-11)</option>
                            <option value="l-40-12" {{ request('size') == 'l-40-12' ? 'selected' : '' }}>L (40-12)</option>
                            <option value="xl-42-13" {{ request('size') == 'xl-42-13' ? 'selected' : '' }}>XL (42-13)</option>
                            <option value="xxl-44-14" {{ request('size') == 'xxl-44-14' ? 'selected' : '' }}>XXL (44-14)</option>
                            <option value="3xl-46-15" {{ request('size') == '3xl-46-15' ? 'selected' : '' }}>3XL (46-15)</option>
                            <option value="4xl-48-16" {{ request('size') == '4xl-48-16' ? 'selected' : '' }}>4XL (48-16)</option>
                        </select>
                    </div>

                    <!-- Item Filter -->
                    <div>
                        <label class="form-label-bank">Item Type</label>
                        <select name="item" class="form-select-bank">
                            <option value="">All Items</option>
                            <option value="jersey" {{ request('item') == 'jersey' ? 'selected' : '' }}>Jersey</option>
                            <option value="shorts" {{ request('item') == 'shorts' ? 'selected' : '' }}>Shorts</option>
                            <option value="socks" {{ request('item') == 'socks' ? 'selected' : '' }}>Socks</option>
                            <option value="tracksuit" {{ request('item') == 'tracksuit' ? 'selected' : '' }}>Tracksuit</option>
                            <option value="jacket" {{ request('item') == 'jacket' ? 'selected' : '' }}>Jacket</option>
                            <option value="cap" {{ request('item') == 'cap' ? 'selected' : '' }}>Cap</option>
                            <option value="bag" {{ request('item') == 'bag' ? 'selected' : '' }}>Bag</option>
                            <option value="complete_set" {{ request('item') == 'complete_set' ? 'selected' : '' }}>Complete Set</option>
                        </select>
                    </div>

                    <!-- Payment Method Filter -->
                    <div>
                        <label class="form-label-bank">Payment Method</label>
                        <select name="payment_method" class="form-select-bank">
                            <option value="">All Methods</option>
                            <option value="cash" {{ request('payment_method') == 'cash' ? 'selected' : '' }}>Cash</option>
                            <option value="card" {{ request('payment_method') == 'card' ? 'selected' : '' }}>Credit/Debit Card</option>
                            <option value="bank_transfer" {{ request('payment_method') == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                        </select>
                    </div>

                    <!-- Date From -->
                    <div>
                        <label class="form-label-bank">Order Date From</label>
                        <input type="date" name="date_from" value="{{ request('date_from') }}" class="form-input-bank">
                    </div>

                    <!-- Date To -->
                    <div>
                        <label class="form-label-bank">Order Date To</label>
                        <input type="date" name="date_to" value="{{ request('date_to') }}" class="form-input-bank">
                    </div>
                </div>

                <!-- Sort Options -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t border-light-gray">
                    <div>
                        <label class="form-label-bank">Sort By</label>
                        <select name="sort_by" class="form-select-bank">
                            <option value="created_at" {{ request('sort_by') == 'created_at' ? 'selected' : '' }}>Created Date</option>
                            <option value="order_date" {{ request('sort_by') == 'order_date' ? 'selected' : '' }}>Order Date</option>
                            <option value="delivery_date" {{ request('sort_by') == 'delivery_date' ? 'selected' : '' }}>Delivery Date</option>
                            <option value="amount" {{ request('sort_by') == 'amount' ? 'selected' : '' }}>Amount</option>
                            <option value="status" {{ request('sort_by') == 'status' ? 'selected' : '' }}>Status</option>
                            <option value="size" {{ request('sort_by') == 'size' ? 'selected' : '' }}>Size</option>
                        </select>
                    </div>
                    <div>
                        <label class="form-label-bank">Sort Direction</label>
                        <select name="sort_direction" class="form-select-bank">
                            <option value="desc" {{ request('sort_direction') == 'desc' ? 'selected' : '' }}>Newest First</option>
                            <option value="asc" {{ request('sort_direction') == 'asc' ? 'selected' : '' }}>Oldest First</option>
                        </select>
                    </div>
                </div>

                <!-- Filter Actions -->
                <div class="flex items-center justify-between pt-4 border-t border-light-gray">
                    <div class="text-sm text-dark-gray">
                        @if(request()->hasAny(['branch_id', 'academy_id', 'status', 'size', 'item', 'payment_method', 'date_from', 'date_to']))
                            <span class="font-medium">{{ $uniforms->total() }}</span> results found with current filters
                        @else
                            Showing all <span class="font-medium">{{ $uniforms->total() }}</span> uniform orders
                        @endif
                    </div>
                    <div class="flex items-center space-x-3">
                        <button type="submit" class="btn-bank">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z">
                                </path>
                            </svg>
                            Apply Filters
                        </button>
                        <a href="{{ route('uniforms.index') }}" class="btn-bank-outline">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Reset All
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
