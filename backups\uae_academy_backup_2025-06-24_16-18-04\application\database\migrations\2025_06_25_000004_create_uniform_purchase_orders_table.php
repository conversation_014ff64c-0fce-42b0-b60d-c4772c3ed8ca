<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('uniform_purchase_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('branch_id')->constrained()->onDelete('cascade');
            $table->foreignId('academy_id')->constrained()->onDelete('cascade');
            $table->foreignId('uniform_supplier_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade'); // User who created the PO
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            
            // Purchase Order Details
            $table->string('po_number')->unique(); // PO-2025-001
            $table->string('supplier_reference')->nullable(); // Supplier's reference number
            $table->date('order_date');
            $table->date('expected_delivery_date')->nullable();
            $table->date('actual_delivery_date')->nullable();
            
            // Status Management
            $table->enum('status', [
                'draft',        // Being prepared
                'pending',      // Waiting for approval
                'approved',     // Approved, sent to supplier
                'ordered',      // Order placed with supplier
                'partial',      // Partially received
                'received',     // Fully received
                'cancelled',    // Cancelled
                'closed'        // Closed/Completed
            ])->default('draft');
            
            // Financial Information
            $table->decimal('subtotal', 12, 2)->default(0); // Before tax and discount
            $table->decimal('discount_amount', 10, 2)->default(0); // AED
            $table->decimal('discount_percentage', 5, 2)->default(0); // %
            $table->decimal('tax_amount', 10, 2)->default(0); // VAT amount
            $table->decimal('tax_rate', 5, 2)->default(5); // VAT rate %
            $table->decimal('shipping_cost', 10, 2)->default(0); // AED
            $table->decimal('total_amount', 12, 2)->default(0); // Final total
            $table->string('currency', 3)->default('AED');
            
            // Payment Information
            $table->enum('payment_status', ['pending', 'partial', 'paid', 'overdue'])->default('pending');
            $table->enum('payment_method', ['cash', 'bank_transfer', 'cheque', 'credit'])->nullable();
            $table->date('payment_due_date')->nullable();
            $table->decimal('paid_amount', 12, 2)->default(0);
            
            // Delivery Information
            $table->text('delivery_address')->nullable();
            $table->text('delivery_address_ar')->nullable();
            $table->string('delivery_contact')->nullable();
            $table->string('delivery_phone')->nullable();
            $table->text('delivery_instructions')->nullable();
            
            // Additional Information
            $table->text('terms_conditions')->nullable();
            $table->text('notes')->nullable();
            $table->text('notes_ar')->nullable();
            $table->text('internal_notes')->nullable(); // Internal notes not visible to supplier
            
            // Approval Workflow
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('ordered_at')->nullable();
            $table->timestamp('received_at')->nullable();
            $table->timestamp('closed_at')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->index(['branch_id', 'academy_id', 'status']);
            $table->index(['uniform_supplier_id', 'status']);
            $table->index(['po_number', 'order_date']);
            $table->index(['status', 'expected_delivery_date']);
            $table->index(['payment_status', 'payment_due_date']);
            $table->index('created_by');
            $table->index('approved_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('uniform_purchase_orders');
    }
};
