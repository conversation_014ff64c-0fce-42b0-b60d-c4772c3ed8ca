<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Branch;

class BranchSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if ($this->command) {
            $this->command->info('Creating branches...');
        }

        $branches = [
            [
                'name' => 'AJMAN HAMEDYA',
                'location' => 'Ajman, UAE',
                'phone' => '+971501234567',
                'email' => '<EMAIL>',
                'address' => 'Hamedya Area, Ajman, UAE',
                'status' => true,
            ],
            [
                'name' => 'DUBAI AL QUSAIS',
                'location' => 'Dubai, UAE',
                'phone' => '+971502345678',
                'email' => '<EMAIL>',
                'address' => 'Al Qusais Area, Dubai, UAE',
                'status' => true,
            ],
            [
                'name' => 'SHARJAH CITY CENTER',
                'location' => 'Sharjah, UAE',
                'phone' => '+971503456789',
                'email' => '<EMAIL>',
                'address' => 'City Center, Sharjah, UAE',
                'status' => true,
            ],
        ];

        foreach ($branches as $branchData) {
            Branch::firstOrCreate(
                ['name' => $branchData['name']],
                $branchData
            );
        }

        if ($this->command) {
            $this->command->info('Branches created successfully.');
        }
    }
}
