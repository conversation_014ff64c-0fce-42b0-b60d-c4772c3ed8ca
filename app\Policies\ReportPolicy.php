<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class ReportPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any reports.
     */
    public function viewAny(User $user, string $reportType = 'reports'): bool
    {
        // All authenticated users with management roles can view reports
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view financial reports.
     */
    public function view(User $user, string $reportType): bool
    {
        // All management roles can view all report types
        if (in_array($user->role, ['admin', 'branch_manager', 'academy_manager'])) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can export reports.
     */
    public function export(User $user, string $reportType): bool
    {
        // Same permissions as viewing
        return $this->view($user, $reportType);
    }

    /**
     * Determine whether the user can view financial reports.
     */
    public function financial(User $user): bool
    {
        return $this->view($user, 'financial');
    }

    /**
     * Determine whether the user can view uniform reports.
     */
    public function uniform(User $user): bool
    {
        return $this->view($user, 'uniform');
    }

    /**
     * Determine whether the user can view program reports.
     */
    public function program(User $user): bool
    {
        return $this->view($user, 'program');
    }

    /**
     * Determine whether the user can view status reports.
     */
    public function status(User $user): bool
    {
        return $this->view($user, 'status');
    }

    /**
     * Determine whether the user can view daily reports.
     */
    public function daily(User $user): bool
    {
        return $this->view($user, 'daily');
    }
}
