@extends('layouts.dashboard')

@section('page-header')
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-charcoal-black">
                Edit Uniform Order #{{ str_pad($uniform->id, 4, '0', STR_PAD_LEFT) }}
            </h1>
            <p class="mt-1 text-sm text-dark-gray">Update uniform order information</p>
        </div>
        <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            <a href="{{ route('uniforms.show', $uniform) }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z">
                    </path>
                </svg>
                View Order
            </a>
            <a href="{{ route('uniforms.index') }}" class="btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18">
                    </path>
                </svg>
                Back to Orders
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="bank-card">
        <div class="bank-card-header">
            <h3 class="text-lg font-semibold text-charcoal-black">Order Information</h3>
        </div>
        <div class="bank-card-body">
            <form method="POST" action="{{ route('uniforms.update', $uniform) }}" class="space-y-6">
                @csrf
                @method('PUT')
                
                <!-- Student Selection -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="form-label-bank">Student <span class="text-error-red">*</span></label>
                        <select name="student_id" required class="form-select-bank @error('student_id') border-error-red @enderror">
                            <option value="">Select Student</option>
                            @foreach($students as $student)
                                <option value="{{ $student->id }}" {{ $uniform->student_id == $student->id ? 'selected' : '' }}>
                                    {{ $student->full_name }} - {{ $student->email }}
                                </option>
                            @endforeach
                        </select>
                        @error('student_id')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Branch <span class="text-error-red">*</span></label>
                        <select name="branch_id" required class="form-select-bank @error('branch_id') border-error-red @enderror">
                            <option value="">Select Branch</option>
                            @foreach($branches as $branch)
                                <option value="{{ $branch->id }}" {{ $uniform->branch_id == $branch->id ? 'selected' : '' }}>
                                    {{ $branch->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('branch_id')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Academy <span class="text-error-red">*</span></label>
                        <select name="academy_id" required class="form-select-bank @error('academy_id') border-error-red @enderror">
                            <option value="">Select Academy</option>
                            @foreach($academies as $academy)
                                <option value="{{ $academy->id }}" {{ $uniform->academy_id == $academy->id ? 'selected' : '' }}>
                                    {{ $academy->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('academy_id')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Item Details -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div>
                        <label class="form-label-bank">Item Type <span class="text-error-red">*</span></label>
                        <select name="item" required class="form-select-bank @error('item') border-error-red @enderror">
                            <option value="">Select Item</option>
                            <option value="jersey" {{ $uniform->item == 'jersey' ? 'selected' : '' }}>Jersey</option>
                            <option value="shorts" {{ $uniform->item == 'shorts' ? 'selected' : '' }}>Shorts</option>
                            <option value="socks" {{ $uniform->item == 'socks' ? 'selected' : '' }}>Socks</option>
                            <option value="tracksuit" {{ $uniform->item == 'tracksuit' ? 'selected' : '' }}>Tracksuit</option>
                            <option value="jacket" {{ $uniform->item == 'jacket' ? 'selected' : '' }}>Jacket</option>
                            <option value="cap" {{ $uniform->item == 'cap' ? 'selected' : '' }}>Cap</option>
                            <option value="bag" {{ $uniform->item == 'bag' ? 'selected' : '' }}>Bag</option>
                            <option value="complete_set" {{ $uniform->item == 'complete_set' ? 'selected' : '' }}>Complete Set</option>
                        </select>
                        @error('item')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Size <span class="text-error-red">*</span></label>
                        <select name="size" required class="form-select-bank @error('size') border-error-red @enderror">
                            <option value="">Select Size</option>
                            <optgroup label="Kids Sizes">
                                <option value="6xs-24-4" {{ $uniform->size == '6xs-24-4' ? 'selected' : '' }}>6XS (24-4)</option>
                                <option value="5xs-26-5" {{ $uniform->size == '5xs-26-5' ? 'selected' : '' }}>5XS (26-5)</option>
                                <option value="4xs-28-6" {{ $uniform->size == '4xs-28-6' ? 'selected' : '' }}>4XS (28-6)</option>
                                <option value="3xs-30-7" {{ $uniform->size == '3xs-30-7' ? 'selected' : '' }}>3XS (30-7)</option>
                                <option value="2xs-32-8" {{ $uniform->size == '2xs-32-8' ? 'selected' : '' }}>2XS (32-8)</option>
                            </optgroup>
                            <optgroup label="Standard Sizes">
                                <option value="xs-34-9" {{ $uniform->size == 'xs-34-9' ? 'selected' : '' }}>XS (34-9)</option>
                                <option value="s-36-10" {{ $uniform->size == 's-36-10' ? 'selected' : '' }}>S (36-10)</option>
                                <option value="m-38-11" {{ $uniform->size == 'm-38-11' ? 'selected' : '' }}>M (38-11)</option>
                                <option value="l-40-12" {{ $uniform->size == 'l-40-12' ? 'selected' : '' }}>L (40-12)</option>
                                <option value="xl-42-13" {{ $uniform->size == 'xl-42-13' ? 'selected' : '' }}>XL (42-13)</option>
                                <option value="xxl-44-14" {{ $uniform->size == 'xxl-44-14' ? 'selected' : '' }}>XXL (44-14)</option>
                            </optgroup>
                            <optgroup label="Large Sizes">
                                <option value="3xl-46-15" {{ $uniform->size == '3xl-46-15' ? 'selected' : '' }}>3XL (46-15)</option>
                                <option value="4xl-48-16" {{ $uniform->size == '4xl-48-16' ? 'selected' : '' }}>4XL (48-16)</option>
                            </optgroup>
                        </select>
                        @error('size')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Quantity <span class="text-error-red">*</span></label>
                        <input type="number" name="quantity" min="1" max="50" value="{{ $uniform->quantity }}" required 
                               class="form-input-bank @error('quantity') border-error-red @enderror" id="quantity-input">
                        @error('quantity')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Unit Price (AED) <span class="text-error-red">*</span></label>
                        <input type="number" name="amount" step="0.01" min="0" max="999999.99" value="{{ $uniform->amount }}" required 
                               class="form-input-bank @error('amount') border-error-red @enderror" id="amount-input">
                        @error('amount')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Total Amount Display -->
                <div class="bg-light-gray p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-medium text-charcoal-black">Total Amount:</span>
                        <span class="text-2xl font-bold text-success-green" id="total-amount">{{ $uniform->formatted_total_amount }}</span>
                    </div>
                </div>

                <!-- Payment & Status -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="form-label-bank">Payment Method <span class="text-error-red">*</span></label>
                        <select name="payment_method" required class="form-select-bank @error('payment_method') border-error-red @enderror">
                            <option value="">Select Method</option>
                            <option value="cash" {{ $uniform->payment_method == 'cash' ? 'selected' : '' }}>Cash</option>
                            <option value="card" {{ $uniform->payment_method == 'card' ? 'selected' : '' }}>Credit/Debit Card</option>
                            <option value="bank_transfer" {{ $uniform->payment_method == 'bank_transfer' ? 'selected' : '' }}>Bank Transfer</option>
                        </select>
                        @error('payment_method')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Status <span class="text-error-red">*</span></label>
                        <select name="status" required class="form-select-bank @error('status') border-error-red @enderror">
                            <option value="ordered" {{ $uniform->status == 'ordered' ? 'selected' : '' }}>Ordered</option>
                            <option value="processing" {{ $uniform->status == 'processing' ? 'selected' : '' }}>Processing</option>
                            <option value="ready" {{ $uniform->status == 'ready' ? 'selected' : '' }}>Ready for Pickup</option>
                            <option value="delivered" {{ $uniform->status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                            <option value="cancelled" {{ $uniform->status == 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                        </select>
                        @error('status')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Order Date <span class="text-error-red">*</span></label>
                        <input type="date" name="order_date" value="{{ $uniform->order_date->format('Y-m-d') }}" required 
                               class="form-input-bank @error('order_date') border-error-red @enderror">
                        @error('order_date')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Tracking Status -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <label class="form-label-bank">Branch Status <span class="text-error-red">*</span></label>
                        <select name="branch_status" required class="form-select-bank @error('branch_status') border-error-red @enderror">
                            <option value="pending" {{ $uniform->branch_status == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="received" {{ $uniform->branch_status == 'received' ? 'selected' : '' }}>Received</option>
                            <option value="delivered" {{ $uniform->branch_status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                        </select>
                        @error('branch_status')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Office Status <span class="text-error-red">*</span></label>
                        <select name="office_status" required class="form-select-bank @error('office_status') border-error-red @enderror">
                            <option value="pending" {{ $uniform->office_status == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="received" {{ $uniform->office_status == 'received' ? 'selected' : '' }}>Received</option>
                            <option value="delivered" {{ $uniform->office_status == 'delivered' ? 'selected' : '' }}>Delivered</option>
                        </select>
                        @error('office_status')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Delivery Date</label>
                        <input type="date" name="delivery_date" value="{{ $uniform->delivery_date ? $uniform->delivery_date->format('Y-m-d') : '' }}" 
                               class="form-input-bank @error('delivery_date') border-error-red @enderror">
                        @error('delivery_date')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="form-label-bank">Reference Number</label>
                        <input type="text" name="reference_number" maxlength="50" value="{{ $uniform->reference_number }}" 
                               class="form-input-bank @error('reference_number') border-error-red @enderror">
                        @error('reference_number')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>

                    <div>
                        <label class="form-label-bank">Description</label>
                        <input type="text" name="description" maxlength="1000" value="{{ $uniform->description }}" 
                               class="form-input-bank @error('description') border-error-red @enderror">
                        @error('description')
                            <div class="form-error-bank">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <!-- Notes -->
                <div>
                    <label class="form-label-bank">Notes</label>
                    <textarea name="note" rows="3" maxlength="1000" 
                              class="form-textarea-bank @error('note') border-error-red @enderror">{{ $uniform->note }}</textarea>
                    @error('note')
                        <div class="form-error-bank">{{ $message }}</div>
                    @enderror
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-4 pt-6 border-t border-light-gray">
                    <a href="{{ route('uniforms.show', $uniform) }}" class="btn-bank-outline">
                        Cancel
                    </a>
                    <button type="submit" class="btn-bank">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                            </path>
                        </svg>
                        Update Order
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('quantity-input');
    const amountInput = document.getElementById('amount-input');
    const totalAmountDisplay = document.getElementById('total-amount');

    // Calculate total amount
    function updateTotalAmount() {
        const quantity = parseInt(quantityInput.value) || 0;
        const amount = parseFloat(amountInput.value) || 0;
        const total = quantity * amount;
        totalAmountDisplay.textContent = 'AED ' + total.toFixed(2);
    }

    if (quantityInput && amountInput) {
        quantityInput.addEventListener('input', updateTotalAmount);
        amountInput.addEventListener('input', updateTotalAmount);
    }
});
</script>
@endpush
