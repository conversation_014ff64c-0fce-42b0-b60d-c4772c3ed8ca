<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\Student;
use App\Models\Branch;
use App\Models\Academy;
use App\Models\Program;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments with advanced search and filter capabilities.
     */
    public function index(Request $request): View|JsonResponse
    {
        Gate::authorize('viewAny', Payment::class);

        $query = Payment::with(['student', 'branch', 'academy'])
            ->withCount(['student']);

        // Apply branch-level filtering for non-admin users
        $user = Auth::user();
        if ($user->role === 'branch_manager') {
            $query->where('branch_id', $user->branch_id);
        } elseif ($user->role === 'academy_manager') {
            $query->where('academy_id', $user->academy_id);
        }

        // Advanced search filter
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhere('note', 'like', "%{$search}%")
                    ->orWhereHas('student', function ($studentQuery) use ($search) {
                        $studentQuery->where('full_name', 'like', "%{$search}%")
                            ->orWhere('phone', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                    });
            });
        }

        // Branch filter
        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }

        // Academy filter
        if ($request->filled('academy_id')) {
            $query->byAcademy($request->get('academy_id'));
        }

        // Status filter
        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'completed') {
                $query->completed();
            } elseif ($status === 'pending') {
                $query->pending();
            } else {
                $query->where('status', $status);
            }
        }

        // Payment method filter
        if ($request->filled('payment_method')) {
            $query->byMethod($request->get('payment_method'));
        }

        // Payment type filter
        if ($request->filled('payment_type')) {
            $query->byPaymentType($request->get('payment_type'));
        }

        // Amount range filter
        if ($request->filled('min_amount') && $request->filled('max_amount')) {
            $query->whereBetween('amount', [$request->get('min_amount'), $request->get('max_amount')]);
        }

        // Date range filter
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $query->byDateRange($request->get('start_date'), $request->get('end_date'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $payments = $query->paginate($perPage)->withQueryString();

        // Get branches and academies for filter dropdowns
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        // Calculate statistics
        $stats = [
            'total_payments' => Payment::count(),
            'completed_payments' => Payment::completed()->count(),
            'pending_payments' => Payment::pending()->count(),
            'failed_payments' => Payment::where('status', 'failed')->count(),
            'total_amount' => Payment::completed()->sum('amount'),
            'pending_amount' => Payment::pending()->sum('amount'),
            'average_payment' => Payment::completed()->avg('amount'),
            'this_month_payments' => Payment::completed()
                ->whereMonth('payment_date', now()->month)
                ->whereYear('payment_date', now()->year)
                ->sum('amount'),
            // Payment type statistics
            'new_entry_payments' => Payment::newEntry()->count(),
            'renewal_payments' => Payment::renewal()->count(),
            'regular_payments' => Payment::regular()->count(),
            'new_entry_amount' => Payment::newEntry()->completed()->sum('amount'),
            'renewal_amount' => Payment::renewal()->completed()->sum('amount'),
            'regular_amount' => Payment::regular()->completed()->sum('amount'),
        ];

        if ($request->ajax()) {
            return response()->json([
                'payments' => $payments,
                'stats' => $stats
            ]);
        }

        return view('payments.index', compact('payments', 'branches', 'academies', 'stats'));
    }

    /**
     * Show the form for creating a new payment.
     */
    public function create(Request $request): View
    {
        Gate::authorize('create', Payment::class);

        $students = Student::active()->with(['branch', 'academy', 'program'])->orderBy('full_name')->get();
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        // Pre-fill student data if student_id is provided
        $prefilledStudent = null;
        $studentPaymentContext = null;
        $prefilledProgram = null;
        $programPaymentContext = null;

        if ($request->has('student_id')) {
            $prefilledStudent = Student::with(['branch', 'academy', 'program', 'payments' => function ($query) {
                $query->orderBy('created_at', 'desc')->limit(3);
            }])
                ->find($request->get('student_id'));

            if ($prefilledStudent) {
                // Get payment context for better UX
                $studentPaymentContext = [
                    'total_payments' => $prefilledStudent->total_payments,
                    'pending_payments' => $prefilledStudent->pending_payments,
                    'last_payment' => $prefilledStudent->payments->first(),
                    'payment_count' => $prefilledStudent->payments()->count(),
                    'suggested_payment_type' => $prefilledStudent->payments()->count() > 0 ? 'renewal' : 'new_entry'
                ];
            }
        }

        // Pre-fill program data if program_id is provided
        if ($request->has('program_id')) {
            $prefilledProgram = Program::with(['academy.branch', 'students'])->find($request->get('program_id'));

            if ($prefilledProgram) {
                // Get program payment context
                $programStats = $prefilledProgram->getPaymentStatistics();
                $programPaymentContext = [
                    'program_name' => $prefilledProgram->name,
                    'program_price' => $prefilledProgram->price,
                    'program_classes' => $prefilledProgram->classes,
                    'program_duration' => $prefilledProgram->duration_hours,
                    'academy_name' => $prefilledProgram->academy->name,
                    'branch_name' => $prefilledProgram->academy->branch->name,
                    'total_revenue' => $programStats['total_revenue'],
                    'total_payments' => $programStats['total_payments'],
                    'average_payment' => $programStats['average_payment'],
                    'suggested_amount' => $prefilledProgram->price,
                    'suggested_start_date' => now()->format('Y-m-d'),
                    'suggested_end_date' => now()->addDays(30)->format('Y-m-d'), // Default 30 days
                    'class_time_from' => $prefilledProgram->start_time ? $prefilledProgram->start_time->format('H:i') : null,
                    'class_time_to' => $prefilledProgram->end_time ? $prefilledProgram->end_time->format('H:i') : null,
                ];

                // Get students enrolled in this program for easy selection
                $students = $prefilledProgram->students()->with(['branch', 'academy', 'program'])->orderBy('full_name')->get();

                // Filter academies to include only the program's academy
                $academies = Academy::where('id', $prefilledProgram->academy_id)->with('branch')->get();

                // Filter branches to include only the program's branch
                $branches = Branch::where('id', $prefilledProgram->academy->branch_id)->get();

                // Debug: Log the data being passed
                Log::info('Program payment form data:', [
                    'program_id' => $prefilledProgram->id,
                    'program_name' => $prefilledProgram->name,
                    'academy_id' => $prefilledProgram->academy_id,
                    'branch_id' => $prefilledProgram->academy->branch_id,
                    'students_count' => $students->count(),
                    'academies_count' => $academies->count(),
                    'branches_count' => $branches->count(),
                ]);
            }
        }

        return view('payments.create', compact(
            'students',
            'branches',
            'academies',
            'prefilledStudent',
            'studentPaymentContext',
            'prefilledProgram',
            'programPaymentContext'
        ));
    }

    /**
     * Store a newly created payment in storage.
     */
    public function store(Request $request): RedirectResponse|JsonResponse
    {
        Gate::authorize('create', Payment::class);

        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'amount' => 'required|numeric|min:0|max:999999.99',
            'vat_inclusive' => 'boolean',
            'vat_rate' => 'nullable|numeric|min:0|max:100',
            'discount' => 'nullable|numeric|min:0|max:999999.99',
            'payment_method' => 'required|in:cash,card,bank_transfer,online,cheque',
            'payment_date' => 'required|date',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:active,expired,pending,failed,refunded,cancelled',
            'reset_num' => 'nullable|string|max:50',
            'class_time_from' => 'nullable|date_format:H:i',
            'class_time_to' => 'nullable|date_format:H:i|after:class_time_from',
            'renewal' => 'boolean',
            'payment_type' => 'nullable|in:new_entry,renewal,regular',
            'note' => 'nullable|string|max:1000',
            'description' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();

            // Generate reference number if not provided
            if (empty($data['reference_number'])) {
                $data['reference_number'] = Payment::generateReferenceNumber();
            }

            // Set currency to AED
            $data['currency'] = 'AED';

            $payment = Payment::create($data);

            // Calculate VAT amounts
            $payment->calculateVat();
            $payment->save();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment created successfully',
                    'payment' => $payment->load(['student', 'branch', 'academy'])
                ]);
            }

            return redirect()->route('payments.index')
                ->with('success', 'Payment created successfully');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create payment: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to create payment: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified payment with detailed information.
     */
    public function show(Payment $payment): View|JsonResponse
    {
        Gate::authorize('view', $payment);

        $payment->load(['student', 'branch', 'academy']);

        if (request()->ajax()) {
            return response()->json([
                'payment' => $payment
            ]);
        }

        return view('payments.show', compact('payment'));
    }

    /**
     * Generate and display payment invoice for printing.
     */
    public function invoice(Payment $payment): View
    {
        Gate::authorize('view', $payment);

        $payment->load(['student', 'branch', 'academy']);

        return view('payments.invoice', compact('payment'));
    }

    /**
     * Show the form for editing the specified payment.
     */
    public function edit(Payment $payment): View
    {
        Gate::authorize('update', $payment);

        $students = Student::active()->with(['branch', 'academy'])->orderBy('full_name')->get();
        $branches = Branch::active()->orderBy('name')->get();
        $academies = Academy::active()->orderBy('name')->get();

        return view('payments.edit', compact('payment', 'students', 'branches', 'academies'));
    }

    /**
     * Update the specified payment in storage.
     */
    public function update(Request $request, Payment $payment): RedirectResponse|JsonResponse
    {
        Gate::authorize('update', $payment);

        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:students,id',
            'branch_id' => 'required|exists:branches,id',
            'academy_id' => 'required|exists:academies,id',
            'amount' => 'required|numeric|min:0|max:999999.99',
            'vat_inclusive' => 'boolean',
            'vat_rate' => 'nullable|numeric|min:0|max:100',
            'discount' => 'nullable|numeric|min:0|max:999999.99',
            'payment_method' => 'required|in:cash,card,bank_transfer,online,cheque',
            'payment_date' => 'required|date',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:active,expired,pending,failed,refunded,cancelled',
            'reset_num' => 'nullable|string|max:50',
            'class_time_from' => 'nullable|date_format:H:i',
            'class_time_to' => 'nullable|date_format:H:i|after:class_time_from',
            'renewal' => 'boolean',
            'payment_type' => 'nullable|in:new_entry,renewal,regular',
            'note' => 'nullable|string|max:1000',
            'description' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        try {
            $data = $validator->validated();

            // Keep currency as AED
            $data['currency'] = 'AED';

            $payment->update($data);

            // Recalculate VAT amounts
            $payment->calculateVat();
            $payment->save();

            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment updated successfully',
                    'payment' => $payment->load(['student', 'branch', 'academy'])
                ]);
            }

            return redirect()->route('payments.show', $payment)
                ->with('success', 'Payment updated successfully');
        } catch (\Exception $e) {
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update payment: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to update payment: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified payment from storage.
     */
    public function destroy(Payment $payment): RedirectResponse|JsonResponse
    {
        Gate::authorize('delete', $payment);

        try {
            // Check if payment can be deleted (only pending or failed payments)
            if (in_array($payment->status, ['completed', 'refunded'])) {
                $message = 'Cannot delete completed or refunded payments. Consider marking as cancelled instead.';

                if (request()->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => $message
                    ], 422);
                }

                return redirect()->back()->with('error', $message);
            }

            $payment->delete();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Payment deleted successfully'
                ]);
            }

            return redirect()->route('payments.index')
                ->with('success', 'Payment deleted successfully');
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete payment: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Failed to delete payment: ' . $e->getMessage());
        }
    }

    /**
     * Toggle payment status (AJAX).
     */
    public function toggleStatus(Payment $payment): JsonResponse
    {
        Gate::authorize('update', $payment);

        try {
            $newStatus = match ($payment->status) {
                'pending' => 'active',
                'active' => 'pending',
                'failed' => 'pending',
                'cancelled' => 'pending',
                'expired' => 'active',
                default => 'pending'
            };

            $payment->update(['status' => $newStatus]);

            if ($newStatus === 'active' && !$payment->payment_date) {
                $payment->update(['payment_date' => now()]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment status updated successfully',
                'status' => $payment->status,
                'status_text' => $payment->status_text
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle bulk actions on payments.
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:complete,cancel,delete,mark_pending',
            'payment_ids' => 'required|array|min:1',
            'payment_ids.*' => 'exists:payments,id'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $paymentIds = $request->get('payment_ids');
            $action = $request->get('action');
            $count = 0;

            foreach ($paymentIds as $paymentId) {
                $payment = Payment::find($paymentId);
                if (!$payment) continue;

                Gate::authorize('update', $payment);

                switch ($action) {
                    case 'complete':
                        if ($payment->status === 'pending') {
                            $payment->update([
                                'status' => 'active',
                                'payment_date' => now()
                            ]);
                            $count++;
                        }
                        break;
                    case 'cancel':
                        if (in_array($payment->status, ['pending', 'failed'])) {
                            $payment->update(['status' => 'cancelled']);
                            $count++;
                        }
                        break;
                    case 'mark_pending':
                        if (in_array($payment->status, ['failed', 'cancelled'])) {
                            $payment->update(['status' => 'pending']);
                            $count++;
                        }
                        break;
                    case 'delete':
                        if (in_array($payment->status, ['pending', 'failed', 'cancelled', 'expired'])) {
                            $payment->delete();
                            $count++;
                        }
                        break;
                }
            }

            $actionText = match ($action) {
                'complete' => 'completed',
                'cancel' => 'cancelled',
                'mark_pending' => 'marked as pending',
                'delete' => 'deleted',
                default => 'processed'
            };

            return response()->json([
                'success' => true,
                'message' => "{$count} payments {$actionText} successfully"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export payments to Excel.
     */
    public function exportExcel(Request $request)
    {
        Gate::authorize('export', Payment::class);

        try {
            $query = Payment::with(['student', 'branch', 'academy']);

            // Apply same filters as index method
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('reference_number', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%")
                        ->orWhereHas('student', function ($studentQuery) use ($search) {
                            $studentQuery->where('full_name', 'like', "%{$search}%");
                        });
                });
            }
            if ($request->filled('branch_id')) {
                $query->byBranch($request->get('branch_id'));
            }
            if ($request->filled('academy_id')) {
                $query->byAcademy($request->get('academy_id'));
            }
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'completed') $query->completed();
                elseif ($status === 'pending') $query->pending();
                else $query->where('status', $status);
            }
            if ($request->filled('payment_type')) {
                $query->byPaymentType($request->get('payment_type'));
            }

            $payments = $query->get();

            $filename = 'payments_' . date('Y-m-d_H-i-s') . '.csv';

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            ];

            $callback = function () use ($payments) {
                $file = fopen('php://output', 'w');

                // CSV Headers
                fputcsv($file, [
                    'ID',
                    'Reference Number',
                    'Student Name',
                    'Branch',
                    'Academy',
                    'Amount (AED)',
                    'Discount (AED)',
                    'Net Amount (AED)',
                    'Payment Method',
                    'Payment Date',
                    'Start Date',
                    'End Date',
                    'Status',
                    'Class Time From',
                    'Class Time To',
                    'Renewal',
                    'Description',
                    'Note',
                    'Created At'
                ]);

                // CSV Data
                foreach ($payments as $payment) {
                    fputcsv($file, [
                        $payment->id,
                        $payment->reference_number,
                        $payment->student->full_name ?? 'N/A',
                        $payment->branch->name ?? 'N/A',
                        $payment->academy->name ?? 'N/A',
                        number_format($payment->amount, 2),
                        number_format($payment->discount ?? 0, 2),
                        number_format($payment->net_amount, 2),
                        $payment->method_text,
                        $payment->formatted_payment_date,
                        $payment->formatted_start_date,
                        $payment->formatted_end_date,
                        $payment->status_text,
                        $payment->class_time_from,
                        $payment->class_time_to,
                        $payment->renewal ? 'Yes' : 'No',
                        $payment->description,
                        $payment->note,
                        $payment->created_at->format('M d, Y H:i')
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to export payments: ' . $e->getMessage());
        }
    }

    /**
     * Export payments to PDF.
     */
    public function exportPdf(Request $request)
    {
        Gate::authorize('export', Payment::class);

        try {
            $query = Payment::with(['student', 'branch', 'academy']);

            // Apply same filters as index method
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function ($q) use ($search) {
                    $q->where('reference_number', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%")
                        ->orWhereHas('student', function ($studentQuery) use ($search) {
                            $studentQuery->where('full_name', 'like', "%{$search}%");
                        });
                });
            }
            if ($request->filled('branch_id')) {
                $query->byBranch($request->get('branch_id'));
            }
            if ($request->filled('academy_id')) {
                $query->byAcademy($request->get('academy_id'));
            }
            if ($request->filled('status')) {
                $status = $request->get('status');
                if ($status === 'completed') $query->completed();
                elseif ($status === 'pending') $query->pending();
                else $query->where('status', $status);
            }
            if ($request->filled('payment_type')) {
                $query->byPaymentType($request->get('payment_type'));
            }

            $payments = $query->get();

            // Calculate summary statistics
            $stats = [
                'total_payments' => $payments->count(),
                'total_amount' => $payments->sum('amount'),
                'completed_amount' => $payments->where('status', 'completed')->sum('amount'),
                'pending_amount' => $payments->where('status', 'pending')->sum('amount'),
            ];

            $html = view('payments.pdf', compact('payments', 'stats'))->render();

            // For now, return HTML view (can be enhanced with actual PDF library later)
            return response($html, 200, [
                'Content-Type' => 'text/html',
                'Content-Disposition' => 'inline; filename="payments_' . date('Y-m-d') . '.html"'
            ]);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to export PDF: ' . $e->getMessage());
        }
    }

    /**
     * Get academies by branch (AJAX).
     */
    public function getAcademiesByBranch(Request $request): JsonResponse
    {
        $branchId = $request->get('branch_id');

        if (!$branchId) {
            return response()->json(['academies' => []]);
        }

        $academies = Academy::where('branch_id', $branchId)
            ->active()
            ->orderBy('name')
            ->get(['id', 'name']);

        return response()->json(['academies' => $academies]);
    }

    /**
     * Get students by academy (AJAX).
     */
    public function getStudentsByAcademy(Request $request): JsonResponse
    {
        $academyId = $request->get('academy_id');

        if (!$academyId) {
            return response()->json(['students' => []]);
        }

        $students = Student::where('academy_id', $academyId)
            ->active()
            ->orderBy('full_name')
            ->get(['id', 'full_name', 'phone']);

        return response()->json(['students' => $students]);
    }

    /**
     * API endpoint for payments listing (AJAX).
     */
    public function apiIndex(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', Payment::class);

        $query = Payment::with(['student', 'branch', 'academy'])
            ->withCount(['student']);

        // Apply branch-level filtering for non-admin users
        $user = Auth::user();
        if ($user->role === 'branch_manager') {
            $query->where('branch_id', $user->branch_id);
        } elseif ($user->role === 'academy_manager') {
            $query->where('academy_id', $user->academy_id);
        }

        // Apply same filters as index method
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('reference_number', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhereHas('student', function ($studentQuery) use ($search) {
                        $studentQuery->where('full_name', 'like', "%{$search}%");
                    });
            });
        }

        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }

        if ($request->filled('academy_id')) {
            $query->byAcademy($request->get('academy_id'));
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'completed') $query->completed();
            elseif ($status === 'pending') $query->pending();
            else $query->where('status', $status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->get('payment_method'));
        }

        if ($request->filled('payment_type')) {
            $query->byPaymentType($request->get('payment_type'));
        }

        if ($request->filled('date_from')) {
            $query->where('payment_date', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('payment_date', '<=', $request->get('date_to'));
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $payments = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'payments' => $payments,
            'success' => true
        ]);
    }

    /**
     * Get payment statistics (AJAX).
     */
    public function getStatistics(Request $request): JsonResponse
    {
        Gate::authorize('viewAny', Payment::class);

        $query = Payment::query();

        // Apply filters if provided
        if ($request->filled('branch_id')) {
            $query->byBranch($request->get('branch_id'));
        }

        if ($request->filled('academy_id')) {
            $query->byAcademy($request->get('academy_id'));
        }

        if ($request->filled('date_from')) {
            $query->where('payment_date', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->where('payment_date', '<=', $request->get('date_to'));
        }

        $stats = [
            'total_payments' => $query->count(),
            'completed_payments' => $query->clone()->completed()->count(),
            'pending_payments' => $query->clone()->pending()->count(),
            'failed_payments' => $query->clone()->where('status', 'failed')->count(),
            'cancelled_payments' => $query->clone()->where('status', 'cancelled')->count(),
            'refunded_payments' => $query->clone()->where('status', 'refunded')->count(),
            'total_amount' => $query->clone()->sum('amount'),
            'completed_amount' => $query->clone()->completed()->sum('amount'),
            'pending_amount' => $query->clone()->pending()->sum('amount'),
            'average_payment' => $query->clone()->completed()->avg('amount'),
            'this_month_payments' => $query->clone()->completed()
                ->whereMonth('payment_date', now()->month)
                ->whereYear('payment_date', now()->year)
                ->sum('amount'),
            // Payment type statistics
            'new_entry_payments' => $query->clone()->newEntry()->count(),
            'renewal_payments' => $query->clone()->renewal()->count(),
            'regular_payments' => $query->clone()->regular()->count(),
            'new_entry_amount' => $query->clone()->newEntry()->completed()->sum('amount'),
            'renewal_amount' => $query->clone()->renewal()->completed()->sum('amount'),
            'regular_amount' => $query->clone()->regular()->completed()->sum('amount'),
            'last_month_payments' => $query->clone()->completed()
                ->whereMonth('payment_date', now()->subMonth()->month)
                ->whereYear('payment_date', now()->subMonth()->year)
                ->sum('amount'),
        ];

        // Calculate growth percentage
        $stats['monthly_growth'] = 0;
        if ($stats['last_month_payments'] > 0) {
            $stats['monthly_growth'] = (($stats['this_month_payments'] - $stats['last_month_payments']) / $stats['last_month_payments']) * 100;
        }

        return response()->json([
            'stats' => $stats,
            'success' => true
        ]);
    }
}
