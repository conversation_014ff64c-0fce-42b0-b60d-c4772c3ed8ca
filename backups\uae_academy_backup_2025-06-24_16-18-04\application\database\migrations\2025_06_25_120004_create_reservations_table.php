<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('reservations', function (Blueprint $table) {
            $table->id();
            $table->string('reservation_number')->unique(); // Auto-generated reservation ID
            $table->foreignId('venue_id')->constrained()->onDelete('cascade');
            $table->foreignId('field_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->date('reservation_date'); // Date of the booking
            $table->time('start_time'); // Start time
            $table->time('end_time'); // End time
            $table->integer('duration_hours'); // Duration in hours
            $table->decimal('hourly_rate', 8, 2); // Rate per hour at time of booking
            $table->decimal('subtotal', 10, 2); // Amount before VAT and discounts
            $table->decimal('discount_percentage', 5, 2)->default(0); // Discount percentage
            $table->decimal('discount_amount', 10, 2)->default(0); // Discount amount in AED
            $table->decimal('vat_rate', 5, 2)->default(5.00); // VAT rate at time of booking
            $table->decimal('vat_amount', 10, 2)->default(0); // VAT amount
            $table->decimal('total_amount', 10, 2); // Final total amount
            $table->string('currency', 3)->default('AED');
            $table->enum('booking_type', ['regular', 'peak', 'weekend', 'special'])->default('regular');
            $table->enum('status', ['pending', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'])->default('pending');
            $table->enum('payment_status', ['unpaid', 'partial', 'paid', 'refunded'])->default('unpaid');
            $table->decimal('deposit_amount', 10, 2)->default(0); // Deposit paid
            $table->decimal('remaining_amount', 10, 2)->default(0); // Amount still owed
            $table->enum('payment_method', ['cash', 'card', 'bank_transfer', 'online', 'credit'])->nullable();
            $table->datetime('booking_datetime'); // When the booking was made
            $table->datetime('confirmed_at')->nullable(); // When booking was confirmed
            $table->datetime('cancelled_at')->nullable(); // When booking was cancelled
            $table->string('cancelled_by')->nullable(); // Who cancelled (customer/admin)
            $table->text('cancellation_reason')->charset('utf8mb4')->nullable();
            $table->decimal('cancellation_fee', 8, 2)->default(0);
            $table->json('participants')->nullable(); // List of participants/players
            $table->integer('expected_participants')->nullable(); // Number of expected players
            $table->enum('event_type', ['training', 'match', 'tournament', 'casual', 'corporate'])->default('casual');
            $table->string('event_name')->charset('utf8mb4')->nullable(); // Name of event/match
            $table->text('special_requirements')->charset('utf8mb4')->nullable(); // Special equipment, setup, etc.
            $table->text('special_requirements_ar')->charset('utf8mb4')->nullable();
            $table->json('equipment_requested')->nullable(); // Requested equipment
            $table->boolean('recurring_booking')->default(false); // Is this a recurring booking?
            $table->json('recurring_pattern')->nullable(); // Recurring pattern details
            $table->foreignId('parent_reservation_id')->nullable()->constrained('reservations'); // For recurring bookings
            $table->boolean('reminder_sent')->default(false); // Reminder notification sent
            $table->datetime('reminder_sent_at')->nullable();
            $table->integer('rating')->nullable(); // Customer rating (1-5)
            $table->text('feedback')->charset('utf8mb4')->nullable(); // Customer feedback
            $table->text('internal_notes')->charset('utf8mb4')->nullable(); // Staff notes
            $table->text('internal_notes_ar')->charset('utf8mb4')->nullable();
            $table->foreignId('created_by')->nullable()->constrained('users'); // Staff who created booking
            $table->foreignId('confirmed_by')->nullable()->constrained('users'); // Staff who confirmed
            $table->timestamps();

            // Indexes
            $table->index(['venue_id', 'field_id', 'reservation_date']);
            $table->index(['customer_id', 'status']);
            $table->index(['reservation_date', 'start_time', 'end_time']);
            $table->index(['status', 'payment_status']);
            $table->index('reservation_number');
            $table->index(['booking_datetime', 'status']);
            $table->index(['recurring_booking', 'parent_reservation_id']);

            // Unique constraint to prevent double booking
            $table->unique(['field_id', 'reservation_date', 'start_time', 'end_time'], 'unique_field_time_slot');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('reservations');
    }
};
