<?php

namespace App\Policies;

use App\Models\ReservationPayment;
use App\Models\User;

class ReservationPaymentPolicy
{
    /**
     * Determine whether the user can view any reservation payments.
     */
    public function viewAny(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view the reservation payment.
     */
    public function view(User $user, ReservationPayment $payment): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can create reservation payments.
     */
    public function create(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can update the reservation payment.
     */
    public function update(User $user, ReservationPayment $payment): bool
    {
        // Cannot update completed or refunded payments
        if (in_array($payment->status, ['completed', 'refunded'])) {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can delete the reservation payment.
     */
    public function delete(User $user, ReservationPayment $payment): bool
    {
        // Only allow deletion of pending payments
        if ($payment->status !== 'pending') {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can restore the reservation payment.
     */
    public function restore(User $user, ReservationPayment $payment): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can permanently delete the reservation payment.
     */
    public function forceDelete(User $user, ReservationPayment $payment): bool
    {
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can perform bulk actions.
     */
    public function bulkAction(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can export payment data.
     */
    public function export(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view payment statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can process payments.
     */
    public function process(User $user, ReservationPayment $payment): bool
    {
        // Can only process pending payments
        if ($payment->status !== 'pending') {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can fail payments.
     */
    public function fail(User $user, ReservationPayment $payment): bool
    {
        // Can only fail pending payments
        if ($payment->status !== 'pending') {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can refund payments.
     */
    public function refund(User $user, ReservationPayment $payment): bool
    {
        // Can only refund completed payments
        if ($payment->status !== 'completed') {
            return false;
        }

        // Must be refundable
        if (!$payment->is_refundable) {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can print receipts.
     */
    public function printReceipt(User $user, ReservationPayment $payment): bool
    {
        // Can only print receipts for completed payments
        if ($payment->status !== 'completed') {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can email receipts.
     */
    public function emailReceipt(User $user, ReservationPayment $payment): bool
    {
        // Can only email receipts for completed payments
        if ($payment->status !== 'completed') {
            return false;
        }

        return in_array($user->role, ['admin', 'branch_manager', 'academy_manager']);
    }

    /**
     * Determine whether the user can view payment gateway responses.
     */
    public function viewGatewayResponse(User $user, ReservationPayment $payment): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }

    /**
     * Determine whether the user can manage payment methods.
     */
    public function managePaymentMethods(User $user): bool
    {
        return in_array($user->role, ['admin', 'branch_manager']);
    }
}
