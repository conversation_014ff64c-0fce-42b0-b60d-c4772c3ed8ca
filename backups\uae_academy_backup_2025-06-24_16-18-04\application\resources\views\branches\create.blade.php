@extends('layouts.dashboard')

@section('title', 'Create New Branch - Sports Academy')

@section('header')
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-br from-success-green to-green-600 rounded-full flex items-center justify-center">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </div>
            <div>
                <h1 class="text-3xl font-bold text-charcoal-black">Create New Branch</h1>
                <p class="text-lg text-dark-gray">Add a new academy branch location</p>
                <span class="badge-bank badge-info">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    New Branch Setup
                </span>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ route('branches.index') }}" class="btn-bank btn-bank-outline">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Back to Branches
            </a>
        </div>
    </div>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto" x-data="branchForm()" x-init="init()">
        <form action="{{ route('branches.store') }}" method="POST" @submit="handleSubmit" class="space-y-6">
            @csrf
            
            <!-- Main Information Card -->
            <div class="bank-card">
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Branch Information</h3>
                        <p class="bank-card-subtitle">Basic details about the new branch</p>
                    </div>
                    <div class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                            </path>
                        </svg>
                    </div>
                </div>
                <div class="bank-card-body">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Branch Name -->
                        <div class="md:col-span-2">
                            <label for="name" class="form-label-bank required">Branch Name</label>
                            <input type="text" 
                                   id="name" 
                                   name="name" 
                                   value="{{ old('name') }}"
                                   class="form-input-bank @error('name') border-error-red @enderror"
                                   placeholder="Enter branch name (e.g., Dubai Marina Branch)"
                                   x-model="form.name"
                                   @input="validateField('name')"
                                   required>
                            @error('name')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Choose a descriptive name that identifies the branch location</p>
                        </div>

                        <!-- Location -->
                        <div class="md:col-span-2">
                            <label for="location" class="form-label-bank required">Location</label>
                            <input type="text" 
                                   id="location" 
                                   name="location" 
                                   value="{{ old('location') }}"
                                   class="form-input-bank @error('location') border-error-red @enderror"
                                   placeholder="Enter location (e.g., Dubai Marina, Abu Dhabi Downtown)"
                                   x-model="form.location"
                                   @input="validateField('location')"
                                   required>
                            @error('location')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Specify the city area or district where the branch is located</p>
                        </div>

                        <!-- Address -->
                        <div class="md:col-span-2">
                            <label for="address" class="form-label-bank">Full Address</label>
                            <textarea id="address" 
                                      name="address" 
                                      rows="3"
                                      class="form-textarea-bank @error('address') border-error-red @enderror"
                                      placeholder="Enter complete address including building, street, and landmarks"
                                      x-model="form.address">{{ old('address') }}</textarea>
                            @error('address')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: Provide detailed address for better identification</p>
                        </div>

                        <!-- Phone Number -->
                        <div>
                            <label for="phone" class="form-label-bank">Phone Number</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-dark-gray text-sm">+971</span>
                                </div>
                                <input type="tel" 
                                       id="phone" 
                                       name="phone" 
                                       value="{{ old('phone') }}"
                                       class="form-input-bank pl-12 @error('phone') border-error-red @enderror"
                                       placeholder="50 123 4567"
                                       pattern="[0-9]{2}[0-9]{3}[0-9]{4}"
                                       x-model="form.phone"
                                       @input="validateField('phone')">
                            </div>
                            @error('phone')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">UAE phone number format: 50 123 4567</p>
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="form-label-bank">Email Address</label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   value="{{ old('email') }}"
                                   class="form-input-bank @error('email') border-error-red @enderror"
                                   placeholder="<EMAIL>"
                                   x-model="form.email"
                                   @input="validateField('email')">
                            @error('email')
                                <p class="form-error-bank">{{ $message }}</p>
                            @enderror
                            <p class="form-help-bank">Optional: Contact email for this branch</p>
                        </div>

                        <!-- Status -->
                        <div class="md:col-span-2">
                            <label class="form-label-bank">Branch Status</label>
                            <div class="flex items-center space-x-6">
                                <label class="flex items-center">
                                    <input type="radio" 
                                           name="status" 
                                           value="1" 
                                           class="form-radio-bank"
                                           x-model="form.status"
                                           {{ old('status', '1') == '1' ? 'checked' : '' }}>
                                    <span class="ml-2 text-charcoal-black">Active</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" 
                                           name="status" 
                                           value="0" 
                                           class="form-radio-bank"
                                           x-model="form.status"
                                           {{ old('status') == '0' ? 'checked' : '' }}>
                                    <span class="ml-2 text-charcoal-black">Inactive</span>
                                </label>
                            </div>
                            <p class="form-help-bank">Set the initial status of the branch</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preview Card -->
            <div class="bank-card" x-show="showPreview" x-transition>
                <div class="bank-card-header">
                    <div>
                        <h3 class="bank-card-title">Branch Preview</h3>
                        <p class="bank-card-subtitle">Review the branch information before saving</p>
                    </div>
                    <button type="button" @click="showPreview = false" class="btn-bank btn-bank-outline btn-bank-sm">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Hide Preview
                    </button>
                </div>
                <div class="bank-card-body">
                    <div class="bg-off-white rounded-lg p-6">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-leaders-red to-leaders-deep-red rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                    </path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-semibold text-charcoal-black" x-text="form.name || 'Branch Name'"></h4>
                                <p class="text-dark-gray" x-text="form.location || 'Location'"></p>
                            </div>
                            <span class="badge-bank" :class="form.status === '1' ? 'badge-success' : 'badge-neutral'">
                                <span x-text="form.status === '1' ? 'Active' : 'Inactive'"></span>
                            </span>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div x-show="form.address">
                                <strong class="text-charcoal-black">Address:</strong>
                                <p class="text-dark-gray" x-text="form.address"></p>
                            </div>
                            <div x-show="form.phone">
                                <strong class="text-charcoal-black">Phone:</strong>
                                <p class="text-dark-gray">+971 <span x-text="form.phone"></span></p>
                            </div>
                            <div x-show="form.email">
                                <strong class="text-charcoal-black">Email:</strong>
                                <p class="text-dark-gray" x-text="form.email"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="bank-card">
                <div class="bank-card-body">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <button type="button" 
                                    @click="showPreview = !showPreview" 
                                    class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                                <span x-text="showPreview ? 'Hide Preview' : 'Show Preview'"></span>
                            </button>
                        </div>
                        
                        <div class="flex items-center space-x-3">
                            <a href="{{ route('branches.index') }}" class="btn-bank btn-bank-outline">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="btn-bank"
                                    :disabled="isSubmitting"
                                    :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" x-show="!isSubmitting">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <svg class="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24" x-show="isSubmitting">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                <span x-text="isSubmitting ? 'Creating Branch...' : 'Create Branch'"></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
@endsection

@push('scripts')
    <script>
        function branchForm() {
            return {
                form: {
                    name: '{{ old("name") }}',
                    location: '{{ old("location") }}',
                    address: '{{ old("address") }}',
                    phone: '{{ old("phone") }}',
                    email: '{{ old("email") }}',
                    status: '{{ old("status", "1") }}'
                },
                showPreview: false,
                isSubmitting: false,
                errors: {},

                init() {
                    // Auto-show preview if form has data
                    if (this.form.name || this.form.location) {
                        this.showPreview = true;
                    }
                },

                validateField(field) {
                    // Clear previous error
                    delete this.errors[field];

                    // Basic validation
                    switch (field) {
                        case 'name':
                            if (!this.form.name.trim()) {
                                this.errors[field] = 'Branch name is required';
                            } else if (this.form.name.length < 3) {
                                this.errors[field] = 'Branch name must be at least 3 characters';
                            }
                            break;
                        case 'location':
                            if (!this.form.location.trim()) {
                                this.errors[field] = 'Location is required';
                            }
                            break;
                        case 'email':
                            if (this.form.email && !this.isValidEmail(this.form.email)) {
                                this.errors[field] = 'Please enter a valid email address';
                            }
                            break;
                        case 'phone':
                            if (this.form.phone && !this.isValidUAEPhone(this.form.phone)) {
                                this.errors[field] = 'Please enter a valid UAE phone number';
                            }
                            break;
                    }
                },

                isValidEmail(email) {
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    return emailRegex.test(email);
                },

                isValidUAEPhone(phone) {
                    const phoneRegex = /^[0-9]{2}[0-9]{3}[0-9]{4}$/;
                    return phoneRegex.test(phone.replace(/\s/g, ''));
                },

                handleSubmit(event) {
                    this.isSubmitting = true;
                    
                    // Validate all fields
                    this.validateField('name');
                    this.validateField('location');
                    this.validateField('email');
                    this.validateField('phone');

                    // If there are errors, prevent submission
                    if (Object.keys(this.errors).length > 0) {
                        event.preventDefault();
                        this.isSubmitting = false;
                        showNotification('error', 'Please fix the form errors before submitting.');
                        return;
                    }
                }
            }
        }

        function showNotification(type, message) {
            // This will be implemented with the notification system
            alert(message);
        }
    </script>
@endpush
